package bagAndInfo.cell
{
   import com.pickgliss.events.InteractiveEvent;
   import com.pickgliss.utils.DoubleClickManager;
   import ddt.data.goods.InventoryItemInfo;
   import ddt.data.goods.ItemTemplateInfo;
   import ddt.events.CellEvent;
   import ddt.interfaces.IDragable;
   import ddt.manager.PlayerManager;
   import ddt.manager.SoundManager;
   import flash.display.Sprite;
   import flash.events.Event;
   
   public class LinkedBagCell extends BagCell
   {
      
      protected var _bagCell:BagCell;
      
      public var DoubleClickEnabled:Boolean = true;
      
      public function LinkedBagCell(_arg_1:Sprite)
      {
         super(0,null,true,_arg_1);
      }
      
      override protected function init() : void
      {
         addEventListener("interactive_click",this.__clickHandler);
         addEventListener("interactive_double_click",this.__doubleClickHandler);
         DoubleClickManager.Instance.enableDoubleClick(this);
         super.init();
      }
      
      private function __clickHandler(_arg_1:InteractiveEvent) : void
      {
         if(_info && !locked && stage && allowDrag)
         {
            SoundManager.instance.play("008");
         }
         dragStart();
      }
      
      public function get bagCell() : BagCell
      {
         return this._bagCell;
      }
      
      public function set bagCell(_arg_1:BagCell) : void
      {
         if(Boolean(this._bagCell))
         {
            this._bagCell.removeEventListener("change",this.__changed);
            if(Boolean(this._bagCell.itemInfo) && this._bagCell.itemInfo.BagType == 0)
            {
               PlayerManager.Instance.Self.Bag.unlockItem(this._bagCell.itemInfo);
            }
            else if(Boolean(this._bagCell.itemInfo))
            {
               PlayerManager.Instance.Self.PropBag.unlockItem(this._bagCell.itemInfo);
            }
            this._bagCell.locked = false;
            info = null;
         }
         this._bagCell = _arg_1;
         if(Boolean(this._bagCell))
         {
            this._bagCell.addEventListener("change",this.__changed);
            this.info = this._bagCell.info;
            if(Boolean(_info) && _info.CategoryID == 74)
            {
               tipData = this._bagCell.tipData;
               updateCellStar();
            }
         }
      }
      
      override public function get place() : int
      {
         if(Boolean(this._bagCell))
         {
            return this._bagCell.itemInfo.Place;
         }
         return -1;
      }
      
      protected function __doubleClickHandler(_arg_1:InteractiveEvent) : void
      {
         if(!this.DoubleClickEnabled)
         {
            return;
         }
         if((_arg_1.currentTarget as BagCell).info != null)
         {
            if((_arg_1.currentTarget as BagCell).info != null)
            {
               dispatchEvent(new CellEvent("doubleclick",this,true));
            }
         }
      }
      
      override public function dragStop(_arg_1:DragEffect) : void
      {
         if(PlayerManager.Instance.Self.bagLocked)
         {
            return;
         }
         if(Boolean(this._bagCell))
         {
            if(_arg_1.action != "none" || Boolean(_arg_1.target))
            {
               this._bagCell.dragStop(_arg_1);
               this._bagCell.removeEventListener("change",this.__changed);
               this._bagCell = null;
               info = null;
            }
            else
            {
               this.locked = false;
            }
         }
      }
      
      private function __changed(_arg_1:Event) : void
      {
         this.info = this._bagCell == null ? null : this._bagCell.info;
         if(this._bagCell == null || this._bagCell.info == null)
         {
            this.clearLinkCell();
         }
         else
         {
            this._bagCell.locked = true;
         }
      }
      
      override public function getSource() : IDragable
      {
         return this._bagCell;
      }
      
      public function clearLinkCell() : void
      {
         if(Boolean(this._bagCell))
         {
            this._bagCell.removeEventListener("change",this.__changed);
            if(Boolean(this._bagCell.itemInfo) && this._bagCell.itemInfo.lock)
            {
               if(Boolean(this._bagCell.itemInfo) && this._bagCell.itemInfo.BagType == 0)
               {
                  PlayerManager.Instance.Self.Bag.unlockItem(this._bagCell.itemInfo);
               }
               else
               {
                  PlayerManager.Instance.Self.PropBag.unlockItem(this._bagCell.itemInfo);
               }
            }
            this._bagCell.locked = false;
         }
         this.bagCell = null;
      }
      
      override public function set locked(_arg_1:Boolean) : void
      {
      }
      
      override public function dispose() : void
      {
         removeEventListener("interactive_click",this.__clickHandler);
         removeEventListener("interactive_double_click",this.__doubleClickHandler);
         DoubleClickManager.Instance.disableDoubleClick(this);
         this.clearLinkCell();
         if(info is InventoryItemInfo)
         {
            info["lock"] = false;
         }
         super.dispose();
         this.bagCell = null;
      }
   }
}

