package bagAndInfo.bag.ring
{
   import bagAndInfo.BagAndInfoManager;
   import bagAndInfo.bag.ring.data.RingSystemData;
   import bagAndInfo.cell.BagCell;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.controls.BaseButton;
   import com.pickgliss.ui.controls.Frame;
   import com.pickgliss.ui.image.ScaleFrameImage;
   import com.pickgliss.ui.text.FilterFrameText;
   import com.pickgliss.utils.ObjectUtils;
   import consortion.ConsortionModelManager;
   import ddt.events.PlayerPropertyEvent;
   import ddt.manager.LanguageMgr;
   import ddt.manager.PlayerManager;
   import ddt.manager.SocketManager;
   import ddt.utils.HelpFrameUtils;
   import ddt.utils.PositionUtils;
   import flash.display.Bitmap;
   import flash.events.MouseEvent;
   
   public class RingSystemView extends Frame
   {
      
      private var _helpBtn:BaseButton;
      
      private var _bg:Bitmap;
      
      private var _progress:RingSystemLevel;
      
      private var _ringCell:BagCell;
      
      private var _currentData:FilterFrameText;
      
      private var _nextData:FilterFrameText;
      
      private var _infoText:FilterFrameText;
      
      private var _coupleNum:RingSystemFilterInfo;
      
      private var _dungeonNum:RingSystemFilterInfo;
      
      private var _propsNum:RingSystemFilterInfo;
      
      private var _skill:ScaleFrameImage;
      
      public function RingSystemView()
      {
         super();
         this.initView();
         this.initEvent();
         this.sendPkg();
      }
      
      private function initView() : void
      {
         titleText = LanguageMgr.GetTranslation("ddt.bagandinfo.ringSystem.titleText");
         this._helpBtn = ComponentFactory.Instance.creatComponentByStylename("bagAndInfo.bag.RingSystem.helpBtn");
         addToContent(this._helpBtn);
         this._bg = ComponentFactory.Instance.creat("asset.bagAndInfo.bag.RingSystemView.bg");
         addToContent(this._bg);
         this._progress = new RingSystemLevel();
         PositionUtils.setPos(this._progress,"bagAndInfo.RingSystem.ProgressPos");
         addToContent(this._progress);
         this._ringCell = new BagCell(0,PlayerManager.Instance.Self.Bag.items[16]);
         this._ringCell.setBgVisible(false);
         this._ringCell.setContentSize(70,70);
         this._ringCell.deleteEnchantMc();
         PositionUtils.setPos(this._ringCell,"bagAndInfo.RingSystem.ringPos");
         addToContent(this._ringCell);
         this._currentData = ComponentFactory.Instance.creatComponentByStylename("bagAndInfo.bag.RingSystemView.currentData");
         addToContent(this._currentData);
         this._nextData = ComponentFactory.Instance.creatComponentByStylename("bagAndInfo.bag.RingSystemView.nextData");
         addToContent(this._nextData);
         this._skill = ComponentFactory.Instance.creatComponentByStylename("bagAndInfo.bag.RingSystemView.skill");
         this._skill.width = 73;
         this._skill.height = 73;
         addToContent(this._skill);
         this._infoText = ComponentFactory.Instance.creatComponentByStylename("bagAndInfo.bag.RingSystemView.infoText");
         this._infoText.text = LanguageMgr.GetTranslation("ddt.bagandinfo.ringSystem.infoText2");
         this._infoText.width = 180;
         PositionUtils.setPos(this._infoText,"bagAndInfo.RingSystem.skillPos");
         addToContent(this._infoText);
         this._coupleNum = new RingSystemFilterInfo(1);
         PositionUtils.setPos(this._coupleNum,"bagAndInfo.RingSystem.couplePos");
         addToContent(this._coupleNum);
         this._dungeonNum = new RingSystemFilterInfo(2);
         PositionUtils.setPos(this._dungeonNum,"bagAndInfo.RingSystem.dungeonPos");
         addToContent(this._dungeonNum);
         this._propsNum = new RingSystemFilterInfo(2);
         PositionUtils.setPos(this._propsNum,"bagAndInfo.RingSystem.propsPos");
         addToContent(this._propsNum);
         this.setViewInfo();
         this.setSkillTipData();
      }
      
      private function initEvent() : void
      {
         this._helpBtn.addEventListener("click",this.__onHelpClick);
         PlayerManager.Instance.Self.addEventListener("propertychange",this.__onUpdateProperty);
      }
      
      protected function __onHelpClick(_arg_1:MouseEvent) : void
      {
         HelpFrameUtils.Instance.simpleHelpFrame(LanguageMgr.GetTranslation("ddt.consortia.bossFrame.helpTitle"),ComponentFactory.Instance.creat("asset.bagAndInfo.bag.RingSystem.heopInfo"),408,488);
      }
      
      protected function setViewInfo() : void
      {
         var _local_3:RingSystemData = BagAndInfoManager.Instance.getCurrentRingData();
         this._currentData.text = _local_3.Attack + "%\n" + _local_3.Defence + "%\n" + _local_3.Agility + "%\n" + _local_3.Luck + "%";
         var _local_1:RingSystemData = BagAndInfoManager.Instance.RingData[_local_3.Level + 1];
         if(_local_1 != null)
         {
            this._nextData.text = _local_1.Attack + "%\n" + _local_1.Defence + "%\n" + _local_1.Agility + "%\n" + _local_1.Luck + "%";
         }
         else
         {
            PlayerManager.Instance.Self.RingExp = _local_3.Exp;
         }
         var _local_2:int = Boolean(_local_1) ? _local_1.Exp : _local_3.Exp;
         this._progress.setProgress(PlayerManager.Instance.Self.RingExp - _local_3.Exp,_local_3.Level,_local_2 - _local_3.Exp);
      }
      
      private function setSkillTipData() : void
      {
         var _local_2:* = null;
         var _local_4:* = null;
         var _local_3:* = null;
         var _local_1:int = int(int(BagAndInfoManager.Instance.getCurrentRingData().Level / 10));
         if(_local_1 == 0)
         {
            _local_2 = ConsortionModelManager.Instance.model.getskillInfoWithTypeAndLevel(0,1)[0];
            _local_4 = {};
            _local_4["name"] = _local_2.name;
            _local_4["content"] = LanguageMgr.GetTranslation("tank.bagAndInfo.ringSkill.notGet");
         }
         else
         {
            _local_2 = ConsortionModelManager.Instance.model.getskillInfoWithTypeAndLevel(0,_local_1)[0];
            _local_4 = {};
            _local_4["name"] = _local_2.name + "Lv" + _local_1;
            _local_4["content"] = _local_2.descript.replace("{0}",_local_2.value);
         }
         if(_local_1 < RingSystemData.TotalLevel * 0.1)
         {
            _local_3 = ConsortionModelManager.Instance.model.getskillInfoWithTypeAndLevel(0,_local_1 + 1)[0];
            _local_4["nextLevel"] = LanguageMgr.GetTranslation("tank.bagAndInfo.ringSkill.nextLevel",_local_3.name,_local_1 + 1,_local_3.descript.replace("{0}",_local_3.value));
            _local_4["limitLevel"] = LanguageMgr.GetTranslation("tank.bagAndInfo.ringSkill.nextUnLock",(_local_1 + 1) * 10);
         }
         else
         {
            _local_4["nextLevel"] = LanguageMgr.GetTranslation("tank.bagAndInfo.ringSkill.fullLevel");
            _local_4["limitLevel"] = "";
         }
         this._skill.tipData = _local_4;
         if(_local_1 <= 0)
         {
            this._skill.filters = ComponentFactory.Instance.creatFilters("grayFilter");
         }
      }
      
      private function __onUpdateProperty(_arg_1:PlayerPropertyEvent) : void
      {
         var _local_2:* = null;
         if(Boolean(_arg_1.changedProperties["ringUseNum"]))
         {
            _local_2 = PlayerManager.Instance.Self.ringUseNum;
            this._coupleNum.setInfoText({
               "info":this.getSurplusCount(_local_2[0],20) + "/20",
               "tipData":LanguageMgr.GetTranslation("ddt.bagandinfo.ringSystem.infoText3")
            });
            this._dungeonNum.setInfoText({
               "info":this.getSurplusCount(_local_2[1],4) + "/4",
               "tipData":LanguageMgr.GetTranslation("ddt.bagandinfo.ringSystem.infoText4")
            });
            this._propsNum.setInfoText({
               "info":this.getSurplusCount(_local_2[2],5) + "/5",
               "tipData":LanguageMgr.GetTranslation("ddt.bagandinfo.ringSystem.infoText5")
            });
         }
      }
      
      private function getSurplusCount(_arg_1:int, _arg_2:int) : int
      {
         return _arg_2 - _arg_1;
      }
      
      private function sendPkg() : void
      {
         SocketManager.Instance.out.getPlayerSpecialProperty(2);
      }
      
      private function removeEvent() : void
      {
         PlayerManager.Instance.Self.removeEventListener("propertychange",this.__onUpdateProperty);
      }
      
      override public function dispose() : void
      {
         this.removeEvent();
         super.dispose();
         ObjectUtils.disposeAllChildren(this);
      }
   }
}

