package bones.display
{
   import bones.BoneMovieFactory;
   import bones.loader.BonesLoaderEvent;
   import bones.loader.BonesLoaderManager;
   import com.pickgliss.ui.core.Disposeable;
   import com.pickgliss.utils.ObjectUtils;
   import dragonBones.Armature;
   import dragonBones.Bone;
   import dragonBones.animation.AnimationState;
   import dragonBones.animation.WorldClock;
   import dragonBones.objects.AnimationData;
   import dragonBones.objects.ArmatureData;
   import dragonBones.objects.BoneData;
   import flash.display.Bitmap;
   import flash.display.DisplayObject;
   import flash.display.Sprite;
   import flash.events.Event;
   import road7th.data.DictionaryData;
   
   public class BoneMovieFlash extends Sprite implements IBoneMovie, Disposeable
   {
      
      private var _direction:int = 1;
      
      private var _armature:Armature = null;
      
      private var _armatureData:ArmatureData = null;
      
      private var _styleName:String;
      
      private var _loadComplete:Boolean = true;
      
      private var _movie:DisplayObject;
      
      private var _defaultBoneActionType:String;
      
      private var _deafultImage:Bitmap;
      
      private var _argsData:DictionaryData;
      
      private var _callBack:Function;
      
      public function BoneMovieFlash(_arg_1:String = "")
      {
         super();
         this._styleName = _arg_1;
      }
      
      public function setArmature(_arg_1:Armature, _arg_2:ArmatureData = null) : IBoneMovie
      {
         this.clearArmature();
         this._armature = _arg_1;
         this._armatureData = _arg_2 || _arg_1.armatureData;
         if(Boolean(this._argsData))
         {
            this._argsData.clear();
         }
         this._argsData = BoneMovieFactory.instance.analysisFrameArgs(this);
         this._movie = this._armature.display as DisplayObject;
         addChildAt(this._movie,0);
         var _local_3:AnimationData = this._armatureData.animationDataList[0];
         if(this._defaultBoneActionType == null || this._defaultBoneActionType == "")
         {
            this._defaultBoneActionType = _local_3.name;
         }
         this.gotoAndPlay(this._defaultBoneActionType);
         this.startClock();
         return this;
      }
      
      public function loadWait() : void
      {
         this._loadComplete = false;
         var _local_1:String = BoneMovieFactory.instance.model.getBonesStyle(this._styleName).boneType;
         if(_local_1 != "none")
         {
            this._deafultImage.x = -60;
            this._deafultImage.y = -100;
            addChild(this._deafultImage);
         }
         BonesLoaderManager.instance.addEventListener("complete",this.__onLoaderComplete);
      }
      
      public function gotoAndPlay(_arg_1:String, _arg_2:Number = -1, _arg_3:Number = -1, _arg_4:Number = NaN, _arg_5:int = 0, _arg_6:String = null, _arg_7:String = "sameLayerAndGroup", _arg_8:Boolean = true, _arg_9:Boolean = true) : AnimationState
      {
         return this._armature.animation.gotoAndPlay(_arg_1,_arg_2,_arg_3,_arg_4,_arg_5,_arg_6,_arg_7,_arg_8,_arg_9);
      }
      
      public function play(_arg_1:String = "") : void
      {
         if(Boolean(this._armature))
         {
            this.gotoAndPlay(_arg_1 == "" ? this._defaultBoneActionType : _arg_1);
         }
         this._defaultBoneActionType = _arg_1;
      }
      
      public function stop() : void
      {
         if(this._armature != null && this._armature.animation != null)
         {
            this._armature.animation.stop();
         }
      }
      
      public function changeBone(_arg_1:String, _arg_2:Bone) : void
      {
         var _local_4:String = null;
         var _local_3:Bone = null;
         if(this._armature != null && this._armature.animation != null)
         {
            _local_3 = this._armature.getBone(_arg_1);
            if(_local_3.parent != null)
            {
               _local_4 = _local_3.parent.name;
            }
            this._armature.addBone(_arg_2,_local_4);
            this._armature.removeBone(_local_3);
         }
      }
      
      public function getBoneByName(_arg_1:String) : Bone
      {
         return this._armature.getBone(_arg_1);
      }
      
      public function getBoneDataByName(_arg_1:String) : BoneData
      {
         var _local_2:BoneData = null;
         if(this._armatureData != null)
         {
            _local_2 = this._armatureData.getBoneData(name);
            if(_local_2 != null)
            {
               return _local_2;
            }
         }
         return null;
      }
      
      public function get boneMovieName() : String
      {
         if(this._armature != null)
         {
            return this._armature.name;
         }
         return null;
      }
      
      public function get boneMovieLabels() : Vector.<String>
      {
         if(this._armature != null && this._armature.animation != null)
         {
            return this._armature.animation.movementList;
         }
         return null;
      }
      
      public function get currentLabel() : String
      {
         if(this._armature != null && this._armature.animation != null)
         {
            return this._armature.animation.lastAnimationName;
         }
         return null;
      }
      
      public function get armature() : Armature
      {
         return this._armature;
      }
      
      public function set direction(_arg_1:int) : void
      {
         if(this._direction == _arg_1)
         {
            return;
         }
         this._direction = _arg_1;
         this.scaleX = this._direction;
      }
      
      public function get direction() : int
      {
         return this._direction;
      }
      
      public function get styleName() : String
      {
         return this._styleName;
      }
      
      public function stopClock() : void
      {
         WorldClock.clock.remove(this._armature);
      }
      
      public function startClock() : void
      {
         WorldClock.clock.add(this._armature);
      }
      
      private function clearArmature() : void
      {
         if(Boolean(this._armature))
         {
            this.stopClock();
            if(Boolean(this._armature.animation))
            {
               this._armature.animation.stop();
            }
            this._armature.dispose();
            this._armature = null;
            this._armatureData = null;
            ObjectUtils.disposeObject(this._movie);
            this._movie = null;
         }
         if(Boolean(this._deafultImage) && Boolean(this._deafultImage.parent))
         {
            this._deafultImage.parent.removeChild(this._deafultImage);
         }
         this._deafultImage = null;
      }
      
      public function get loadComplete() : Boolean
      {
         return this._loadComplete;
      }
      
      private function __onLoaderComplete(_arg_1:BonesLoaderEvent) : void
      {
         if(this._styleName == _arg_1.vo.styleName)
         {
            this._loadComplete = true;
            BonesLoaderManager.instance.removeEventListener("complete",this.__onLoaderComplete);
            this.setArmature(BoneMovieFactory.instance.getArmature(this._styleName,BoneMovieFactory.instance.flashFactory));
            dispatchEvent(new Event("complete"));
            if(this._callBack != null)
            {
               this._callBack.call();
            }
         }
      }
      
      public function set onLoadComplete(_arg_1:Function) : void
      {
         this._callBack = _arg_1;
      }
      
      public function getValueByAttribute(_arg_1:String) : String
      {
         return this._argsData[_arg_1];
      }
      
      public function dispose() : void
      {
         if(Boolean(this._argsData))
         {
            this._argsData.clear();
         }
         this._argsData = null;
         this._callBack = null;
         this.clearArmature();
         BonesLoaderManager.instance.removeEventListener("complete",this.__onLoaderComplete);
      }
   }
}

