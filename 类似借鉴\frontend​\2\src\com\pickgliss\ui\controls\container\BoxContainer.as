package com.pickgliss.ui.controls.container
{
   import com.pickgliss.events.ComponentEvent;
   import com.pickgliss.ui.core.Component;
   import com.pickgliss.utils.ObjectUtils;
   import flash.display.DisplayObject;
   
   public class BoxContainer extends Component
   {
      
      public static const P_childRefresh:String = "childRefresh";
      
      public static const P_childResize:String = "childResize";
      
      public static const P_isReverAdd:String = "isReverAdd";
      
      public static const P_spacing:String = "spacing";
      
      public static const P_strictSize:String = "strictSize";
      
      public static const P_autoSize:String = "autoSize";
      
      public static const LEFT_OR_TOP:int = 0;
      
      public static const RIGHT_OR_BOTTOM:int = 1;
      
      public static const CENTER:int = 2;
      
      public static const NONE:int = 3;
      
      protected var _childrenList:Vector.<DisplayObject> = new Vector.<DisplayObject>();
      
      protected var _isReverAdd:Boolean;
      
      protected var _isReverAddList:Boolean;
      
      protected var _spacing:Number = 0;
      
      protected var _strictSize:Number = -1;
      
      protected var _autoSize:int = 0;
      
      public function BoxContainer()
      {
         super();
      }
      
      override public function addChild(_arg_1:DisplayObject) : DisplayObject
      {
         if(this._childrenList.indexOf(_arg_1) > -1)
         {
            return _arg_1;
         }
         if(!this._isReverAdd)
         {
            this._childrenList.push(super.addChild(_arg_1));
         }
         else if(!this._isReverAddList)
         {
            this._childrenList.push(super.addChildAt(_arg_1,0));
         }
         else
         {
            this._childrenList.splice(0,0,super.addChildAt(_arg_1,0));
         }
         _arg_1.addEventListener("propertiesChanged",this.__onResize);
         onPropertiesChanged("childRefresh");
         return _arg_1;
      }
      
      override public function dispose() : void
      {
         this.disposeAllChildren();
         this._childrenList = null;
         super.dispose();
      }
      
      public function disposeAllChildren() : void
      {
         var _local_2:int = 0;
         var _local_1:* = null;
         _local_2 = 0;
         while(_local_2 < numChildren)
         {
            _local_1 = getChildAt(_local_2);
            _local_1.removeEventListener("propertiesChanged",this.__onResize);
            _local_2++;
         }
         ObjectUtils.disposeAllChildren(this);
      }
      
      public function set isReverAdd(_arg_1:Boolean) : void
      {
         if(this._isReverAdd == _arg_1)
         {
            return;
         }
         this._isReverAdd = _arg_1;
         onPropertiesChanged("isReverAdd");
      }
      
      public function set isReverAddList(_arg_1:Boolean) : void
      {
         if(this._isReverAddList == _arg_1)
         {
            return;
         }
         this._isReverAddList = _arg_1;
      }
      
      public function refreshChildPos() : void
      {
         onPropertiesChanged("childResize");
      }
      
      public function removeAllChild() : void
      {
         while(numChildren > 0)
         {
            removeChildAt(0);
         }
         this._childrenList.length = 0;
      }
      
      public function clearAllChild() : void
      {
         while(numChildren > 0)
         {
            ObjectUtils.disposeObject(this.removeChild(getChildAt(0)));
         }
         this._childrenList.length = 0;
      }
      
      override public function removeChild(_arg_1:DisplayObject) : DisplayObject
      {
         _arg_1.removeEventListener("propertiesChanged",this.__onResize);
         this._childrenList.splice(this._childrenList.indexOf(_arg_1),1);
         super.removeChild(_arg_1);
         onPropertiesChanged("childRefresh");
         return _arg_1;
      }
      
      public function reverChildren() : void
      {
         var _local_2:int = 0;
         var _local_1:Vector.<DisplayObject> = new Vector.<DisplayObject>();
         while(numChildren > 0)
         {
            _local_1.push(removeChildAt(0));
         }
         _local_2 = 0;
         while(_local_2 < _local_1.length)
         {
            this.addChild(_local_1[_local_2]);
            _local_2++;
         }
      }
      
      public function set autoSize(_arg_1:int) : void
      {
         if(this._autoSize == _arg_1)
         {
            return;
         }
         this._autoSize = _arg_1;
         onPropertiesChanged("autoSize");
      }
      
      public function get spacing() : Number
      {
         return this._spacing;
      }
      
      public function set spacing(_arg_1:Number) : void
      {
         if(this._spacing == _arg_1)
         {
            return;
         }
         this._spacing = _arg_1;
         onPropertiesChanged("spacing");
      }
      
      public function set strictSize(_arg_1:Number) : void
      {
         if(this._strictSize == _arg_1)
         {
            return;
         }
         this._strictSize = _arg_1;
         onPropertiesChanged("strictSize");
      }
      
      public function arrange() : void
      {
      }
      
      protected function get isStrictSize() : Boolean
      {
         return this._strictSize > 0;
      }
      
      override protected function onProppertiesUpdate() : void
      {
         this.arrange();
      }
      
      private function __onResize(_arg_1:ComponentEvent) : void
      {
         if(Boolean(_arg_1.changedProperties["height"]) || Boolean(_arg_1.changedProperties["width"]))
         {
            onPropertiesChanged("childRefresh");
         }
      }
      
      protected function getItemWidth(_arg_1:DisplayObject) : Number
      {
         if(this.isStrictSize)
         {
            return this._strictSize;
         }
         return _arg_1.width;
      }
      
      public function get realWidth() : Number
      {
         var _local_2:int = 0;
         var _local_1:* = 0;
         _local_2 = 0;
         while(_local_2 < this._childrenList.length)
         {
            _local_1 += this.getItemWidth(this._childrenList[_local_2]) + this._spacing;
            _local_2++;
         }
         return _local_1 - this._spacing;
      }
   }
}

