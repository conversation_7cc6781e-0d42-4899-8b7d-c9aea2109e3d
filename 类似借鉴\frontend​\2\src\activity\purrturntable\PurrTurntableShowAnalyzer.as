package activity.purrturntable
{
   import com.pickgliss.loader.DataAnalyzer;
   import com.pickgliss.utils.ObjectUtils;
   import road7th.data.DictionaryData;
   
   public class PurrTurntableShowAnalyzer extends DataAnalyzer
   {
      
      private var _dic:DictionaryData;
      
      private var _arrType:Array;
      
      private var _dicReward:DictionaryData;
      
      public function PurrTurntableShowAnalyzer(_arg_1:Function)
      {
         super(_arg_1);
      }
      
      override public function analyze(_arg_1:*) : void
      {
         var _local_8:int = 0;
         var _local_6:* = null;
         var _local_7:* = null;
         var _local_3:* = null;
         var _local_2:* = null;
         var _local_4:* = null;
         this._dic = new DictionaryData();
         this._arrType = [];
         this._dicReward = new DictionaryData();
         var _local_5:XML = new XML(_arg_1);
         if(_local_5.@value == "true")
         {
            _local_6 = _local_5..Item;
            _local_8 = 0;
            while(_local_8 < _local_6.length())
            {
               _local_7 = new PurrTurntableShowTemp();
               ObjectUtils.copyPorpertiesByXML(_local_7,_local_6[_local_8]);
               if(!this._dic[_local_7.Type])
               {
                  _local_3 = [];
                  this._dic.add(_local_7.Type,_local_3);
                  this._arrType.push(_local_7.Type);
               }
               this._dic[_local_7.Type].unshift(_local_7);
               _local_8++;
            }
            _local_8 = 0;
            while(_local_8 < _local_6.length())
            {
               _local_7 = new PurrTurntableShowTemp();
               ObjectUtils.copyPorpertiesByXML(_local_7,_local_6[_local_8]);
               if(!this._dicReward[_local_7.Type])
               {
                  _local_2 = new DictionaryData();
                  this._dicReward.add(_local_7.Type,_local_2);
               }
               if(!this._dicReward[_local_7.Type][_local_7.Level])
               {
                  _local_4 = new DictionaryData();
                  this._dicReward[_local_7.Type].add(_local_7.Level,_local_4);
               }
               this._dicReward[_local_7.Type][_local_7.Level].add(_local_7.Index,_local_7);
               _local_8++;
            }
            onAnalyzeComplete();
         }
         else
         {
            message = _local_5.@message;
            onAnalyzeError();
         }
      }
      
      public function get data() : DictionaryData
      {
         return this._dic;
      }
      
      public function get dataType() : Array
      {
         return this._arrType;
      }
      
      public function get dataReward() : DictionaryData
      {
         return this._dicReward;
      }
   }
}

