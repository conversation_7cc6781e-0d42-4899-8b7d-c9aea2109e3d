package activity.dayActive.data
{
   import activity.dayActive.DayActiveManager;
   import ddt.manager.ServerConfigManager;
   import road7th.data.DictionaryData;
   
   public class DayActiveModel
   {
      
      public var activeOldValue:int;
      
      private var _activeValue:int;
      
      private var _resetQuestCount:int;
      
      private var _canResetQuest:<PERSON>olean;
      
      private var _activeQuestData:DictionaryData;
      
      private var _activeRewardData:DictionaryData;
      
      private var _list:Array;
      
      public var fastComlateTaskCount:int;
      
      public var shopList:Array;
      
      public var shopScore:int;
      
      public var lastRefreshDate:Date;
      
      public function DayActiveModel()
      {
         super();
         this._list = [];
         this._activeRewardData = new DictionaryData();
      }
      
      public function clearList() : void
      {
         this._list.splice(0,this._list.length);
      }
      
      public function sortList() : void
      {
         this._list.sortOn(["isGetReward","isComplete","IsMust","sort"],[16,0x10 | 2,0x10 | 2,0x10 | 2]);
      }
      
      public function get activeValue() : int
      {
         return this._activeValue;
      }
      
      public function set activeValue(_arg_1:int) : void
      {
         this._activeValue = _arg_1;
      }
      
      public function set activeQuestData(_arg_1:DictionaryData) : void
      {
         this._activeQuestData = _arg_1;
      }
      
      public function set resetQuestCount(_arg_1:int) : void
      {
         this._resetQuestCount = _arg_1;
         this._canResetQuest = this._resetQuestCount < ServerConfigManager.instance.dayActiveResetTotal;
      }
      
      public function getActiveQuestInfoByID(_arg_1:int) : DayActiveQuestInfo
      {
         return this._activeQuestData[_arg_1];
      }
      
      public function addRewardData(_arg_1:String) : void
      {
         var _local_2:Array = _arg_1.split(",");
         var _local_3:DayActiveRewardInfo = new DayActiveRewardInfo();
         _local_3.targetActive = _local_2[0];
         _local_3.rewardID = _local_2[1];
         _local_3.count = _local_2[2];
         _local_3.validDate = _local_2[3];
         _local_3.isBand = _local_2[4] == "1";
         this._activeRewardData.add(_local_3.targetActive,_local_3);
      }
      
      public function getActiveRewardInfoByate(_arg_1:int) : DayActiveRewardInfo
      {
         return this._activeRewardData[_arg_1];
      }
      
      public function addInfo(_arg_1:DayActiveInfo) : void
      {
         this._list.push(_arg_1);
      }
      
      public function getInfoByID(_arg_1:int) : DayActiveInfo
      {
         var _local_2:DayActiveInfo = null;
         for each(_local_2 in this._list)
         {
            if(_local_2.id == _arg_1)
            {
               return _local_2;
            }
         }
         return null;
      }
      
      public function updateRewardData(_arg_1:String) : void
      {
         var _local_3:int = 0;
         var _local_4:* = null;
         var _local_2:Array = _arg_1.split("|");
         _local_3 = 0;
         while(_local_3 < _local_2.length)
         {
            _local_4 = this._activeRewardData[DayActiveManager.REWARD_CONDITION[_local_3]];
            _local_4.isGetReward = _local_2[_local_3] == "1";
            _local_3++;
         }
      }
      
      public function get canResetQuest() : Boolean
      {
         return this._canResetQuest;
      }
      
      public function get activeList() : Array
      {
         return this._list;
      }
      
      public function checkIsRedDot() : Boolean
      {
         var _local_7:int = 0;
         var _local_5:int = 0;
         var _local_3:Boolean = false;
         var _local_6:* = null;
         var _local_1:* = null;
         _local_7 = 0;
         while(_local_7 < this._list.length)
         {
            _local_6 = this._list[_local_7];
            if(!_local_6.isGetReward && Boolean(_local_6.isComplete))
            {
               return true;
            }
            _local_7++;
         }
         var _local_4:String = DayActiveManager.Instance.rewardData;
         var _local_2:Array = _local_4.split("|");
         _local_5 = 0;
         while(_local_5 < _local_2.length)
         {
            _local_1 = this._activeRewardData[DayActiveManager.REWARD_CONDITION[_local_5]];
            if(this.activeValue >= _local_1.targetActive && _local_2[_local_5] != "1")
            {
               return true;
            }
            _local_5++;
         }
         return false;
      }
   }
}

