package consortion.view.selfConsortia.consortiaTask
{
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.controls.BaseButton;
   import com.pickgliss.ui.core.Disposeable;
   import com.pickgliss.ui.image.ScaleBitmapImage;
   import com.pickgliss.ui.image.ScaleFrameImage;
   import com.pickgliss.ui.text.FilterFrameText;
   import com.pickgliss.utils.ObjectUtils;
   import consortion.ConsortionModelManager;
   import ddt.manager.ConsortiaDutyManager;
   import ddt.manager.LanguageMgr;
   import ddt.manager.MessageTipManager;
   import ddt.manager.PlayerManager;
   import ddt.manager.SoundManager;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class ConsortiaMyTaskFinishItem extends Sprite implements Disposeable
   {
      
      public static const DONATE_TYPE:int = 5;
      
      private var _noFinishValue:int;
      
      private var _bg:ScaleBitmapImage;
      
      private var _donateBtn:BaseButton;
      
      private var _finishTxt:FilterFrameText;
      
      private var _myFinishTxt:FilterFrameText;
      
      private var lockImg:ScaleFrameImage;
      
      private var _isLock:Boolean = false;
      
      private var _lockId:int = 0;
      
      private var _taskId:int;
      
      public function ConsortiaMyTaskFinishItem()
      {
         super();
         this.initView();
         this.initEvents();
      }
      
      public function get isLock() : Boolean
      {
         return this._isLock;
      }
      
      public function set isLock(_arg_1:Boolean) : void
      {
         this._isLock = _arg_1;
      }
      
      public function get lockId() : int
      {
         return this._lockId;
      }
      
      public function set lockId(_arg_1:int) : void
      {
         this._lockId = _arg_1;
      }
      
      private function initView() : void
      {
         var _local_3:* = undefined;
         var _local_1:* = null;
         this._bg = ComponentFactory.Instance.creatComponentByStylename("consortion.task.bg1");
         this._donateBtn = ComponentFactory.Instance.creatComponentByStylename("consortion.task.donateBtn");
         this._finishTxt = ComponentFactory.Instance.creatComponentByStylename("consortion.task.finishTxt");
         this._myFinishTxt = ComponentFactory.Instance.creatComponentByStylename("consortion.task.finishNumberTxt");
         this.lockImg = ComponentFactory.Instance.creatComponentByStylename("consortion.task.luckImg");
         addChild(this._bg);
         addChild(this._donateBtn);
         addChild(this._finishTxt);
         addChild(this._myFinishTxt);
         this._donateBtn.visible = false;
         addChild(this.lockImg);
         this.lockImg.setFrame(1);
         _local_3 = 0.8;
         this.lockImg.scaleY = _local_3;
         this.lockImg.scaleX = _local_3;
         var _local_2:int = PlayerManager.Instance.Self.Right;
         if(ConsortiaDutyManager.GetRight(_local_2,512))
         {
            this.lockImg.filters = null;
            this.lockImg.buttonMode = true;
            this.lockImg.addEventListener("click",this.__onLockImgChange);
         }
         else
         {
            this.lockImg.buttonMode = false;
            _local_1 = ComponentFactory.Instance.creatFilters("grayFilter");
            this.lockImg.filters = _local_1;
         }
      }
      
      private function __onLockImgChange(_arg_1:MouseEvent) : void
      {
         var _local_2:* = null;
         SoundManager.instance.playButtonSound();
         if(!this._isLock && ConsortionModelManager.Instance.TaskModel.lockNum >= 2)
         {
            _local_2 = LanguageMgr.GetTranslation("consortia.task.lockNum");
            MessageTipManager.getInstance().show(_local_2,0,true,1);
            return;
         }
         this._isLock = !this._isLock;
         if(this._isLock)
         {
            this._lockId = this._taskId;
            ++ConsortionModelManager.Instance.TaskModel.lockNum;
         }
         else
         {
            this._lockId = 0;
            --ConsortionModelManager.Instance.TaskModel.lockNum;
         }
         this.lockImg.setFrame(this._isLock ? 2 : 1);
      }
      
      private function initEvents() : void
      {
         addEventListener("mouseOver",this.__over);
         addEventListener("mouseOut",this.__out);
         this._donateBtn.addEventListener("click",this.__donateClick);
      }
      
      private function removeEvents() : void
      {
         removeEventListener("mouseOver",this.__over);
         removeEventListener("mouseOut",this.__out);
         this._donateBtn.removeEventListener("click",this.__donateClick);
      }
      
      private function __over(_arg_1:MouseEvent) : void
      {
         this._finishTxt.setFrame(2);
         this._myFinishTxt.setFrame(2);
      }
      
      private function __out(_arg_1:MouseEvent) : void
      {
         this._finishTxt.setFrame(1);
         this._myFinishTxt.setFrame(1);
      }
      
      private function __donateClick(_arg_1:MouseEvent) : void
      {
         var _local_2:* = null;
         SoundManager.instance.play("008");
         if(PlayerManager.Instance.Self.DDTMoney > 0)
         {
            _local_2 = ComponentFactory.Instance.creatComponentByStylename("DonateFrame");
            _local_2.targetValue = this._noFinishValue;
            _local_2.show();
         }
         else
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("consortia.task.noMedal"));
         }
      }
      
      public function update(_arg_1:int, _arg_2:String, _arg_3:int, _arg_4:int, _arg_5:int = 0) : void
      {
         this._isLock = false;
         this.lockImg.setFrame(1);
         this._noFinishValue = _arg_4 - _arg_3;
         this._finishTxt.text = _arg_2;
         this._taskId = _arg_5;
         this._myFinishTxt.text = _arg_3.toString();
         if(_arg_1 == 5)
         {
            if(_arg_3 < _arg_4)
            {
               this._donateBtn.visible = true;
               this._finishTxt.x = 45;
            }
            else
            {
               this._donateBtn.visible = false;
               this._finishTxt.x = 3;
            }
         }
         else
         {
            this._donateBtn.visible = false;
            this._finishTxt.x = 3;
         }
      }
      
      public function updateFinishTxt(_arg_1:int) : void
      {
         this._myFinishTxt.text = _arg_1.toString();
      }
      
      override public function get height() : Number
      {
         return this._bg.height;
      }
      
      public function get taskId() : int
      {
         return this._taskId;
      }
      
      public function dispose() : void
      {
         this.removeEvents();
         var _local_1:int = PlayerManager.Instance.Self.Right;
         if(ConsortiaDutyManager.GetRight(_local_1,512))
         {
            this.lockImg.removeEventListener("click",this.__onLockImgChange);
         }
         if(Boolean(this.lockImg))
         {
            ObjectUtils.disposeObject(this.lockImg);
            this.lockImg = null;
         }
         if(Boolean(this._bg))
         {
            ObjectUtils.disposeObject(this._bg);
         }
         this._bg = null;
         if(Boolean(this._donateBtn))
         {
            ObjectUtils.disposeObject(this._donateBtn);
         }
         this._donateBtn = null;
         if(Boolean(this._finishTxt))
         {
            ObjectUtils.disposeObject(this._finishTxt);
         }
         this._finishTxt = null;
         if(Boolean(this._myFinishTxt))
         {
            ObjectUtils.disposeObject(this._myFinishTxt);
         }
         this._myFinishTxt = null;
         ObjectUtils.disposeAllChildren(this);
         if(Boolean(parent))
         {
            parent.removeChild(this);
         }
      }
   }
}

