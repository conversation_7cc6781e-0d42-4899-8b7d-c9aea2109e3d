package bagAndInfo.bag
{
   import bagAndInfo.cell.BagCell;
   import com.pickgliss.events.FrameEvent;
   import com.pickgliss.toplevel.StageReferance;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.LayerManager;
   import com.pickgliss.ui.controls.Frame;
   import com.pickgliss.ui.controls.TextButton;
   import com.pickgliss.ui.controls.list.DropList;
   import com.pickgliss.ui.image.Image;
   import com.pickgliss.ui.text.FilterFrameText;
   import com.pickgliss.ui.text.TextArea;
   import consortion.ConsortionModelManager;
   import ddt.data.goods.InventoryItemInfo;
   import ddt.data.goods.ItemTemplateInfo;
   import ddt.manager.GameInSocketOut;
   import ddt.manager.ItemManager;
   import ddt.manager.LanguageMgr;
   import ddt.manager.MessageTipManager;
   import ddt.manager.PlayerManager;
   import ddt.manager.SoundManager;
   import ddt.utils.FilterWordManager;
   import ddt.utils.PositionUtils;
   import ddt.view.NameInputDropListTarget;
   import ddt.view.chat.ChatFriendListPanel;
   import flash.display.Bitmap;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   
   public class GetFriendPackFrame extends Frame
   {
      
      private var _bg:Image;
      
      private var _checkOutViewBg:Image;
      
      private var _cellBg:Bitmap;
      
      protected var _comboBoxLabel:FilterFrameText;
      
      protected var _nameInput:NameInputDropListTarget;
      
      protected var _dropList:DropList;
      
      protected var _friendList:ChatFriendListPanel;
      
      private var _textAreaBg:Image;
      
      private var _textArea:TextArea;
      
      protected var _chooseFriendBtn:TextButton;
      
      private var _certainBtn:TextButton;
      
      private var _deselectBtn:TextButton;
      
      private var _awardCell:BagCell;
      
      protected var _cellNameLabel:FilterFrameText;
      
      private var _tipTxt:FilterFrameText;
      
      private var _info:ItemTemplateInfo;
      
      private var _bagType:int;
      
      private var _place:int;
      
      public function GetFriendPackFrame()
      {
         super();
         this.initView();
         this.addEvent();
      }
      
      private function initView() : void
      {
         titleText = LanguageMgr.GetTranslation("shop.view.present");
         this._bg = ComponentFactory.Instance.creatComponentByStylename("bag.getFriendPackFrame.PresentFrameBg1");
         addToContent(this._bg);
         this._checkOutViewBg = ComponentFactory.Instance.creatComponentByStylename("bag.getFriendPackFrame.CheckOutViewBg");
         addToContent(this._checkOutViewBg);
         this._cellBg = ComponentFactory.Instance.creatBitmap("asset.getFriendPackFrame.cellBg1");
         addToContent(this._cellBg);
         this._comboBoxLabel = ComponentFactory.Instance.creatComponentByStylename("bag.getFriendPackFrame.ComboBoxLabel");
         this._comboBoxLabel.text = LanguageMgr.GetTranslation("shop.PresentFrame.ComboBoxLabel");
         addToContent(this._comboBoxLabel);
         this._nameInput = ComponentFactory.Instance.creatCustomObject("bag.getFriendPackFrame.nameInput");
         addToContent(this._nameInput);
         this._dropList = ComponentFactory.Instance.creatComponentByStylename("droplist.SimpleDropList");
         this._dropList.targetDisplay = this._nameInput;
         this._dropList.x = this._nameInput.x;
         this._dropList.y = this._nameInput.y + this._nameInput.height;
         this._friendList = new ChatFriendListPanel();
         this._friendList.setup(this.selectName,false);
         this._textAreaBg = ComponentFactory.Instance.creatComponentByStylename("bag.getFriendPackFrame.TextAreaBg");
         this._textArea = ComponentFactory.Instance.creatComponentByStylename("bag.getFriendPackFrame.PresentClearingTextArea");
         this._textArea.maxChars = 100;
         addToContent(this._textArea);
         this._textArea.addChild(this._textAreaBg);
         this._chooseFriendBtn = ComponentFactory.Instance.creatComponentByStylename("bag.getFriendPackFrame.ChooseFriendBtn");
         this._chooseFriendBtn.text = LanguageMgr.GetTranslation("shop.PresentFrame.ChooseFriendButtonText");
         addToContent(this._chooseFriendBtn);
         this._certainBtn = ComponentFactory.Instance.creat("bag.getFriendPackFrame.certainBtn");
         this._certainBtn.text = LanguageMgr.GetTranslation("tank.room.RoomIIView2.affirm");
         addToContent(this._certainBtn);
         this._deselectBtn = ComponentFactory.Instance.creat("bag.getFriendPackFrame.deselectBtn");
         this._deselectBtn.text = LanguageMgr.GetTranslation("tank.view.DefyAfficheView.cancel");
         addToContent(this._deselectBtn);
         this._awardCell = new BagCell(0,null,true,ComponentFactory.Instance.creatBitmap("asset.getFriendPackFrame.cellBg2"));
         PositionUtils.setPos(this._awardCell,"getFriendPackFrame.cellPos");
         addToContent(this._awardCell);
         this._cellNameLabel = ComponentFactory.Instance.creatComponentByStylename("bag.getFriendPackFrame.cellNameLabel");
         addToContent(this._cellNameLabel);
         this._tipTxt = ComponentFactory.Instance.creatComponentByStylename("bag.getFriendPackFrame.tipTxt");
         this._tipTxt.text = LanguageMgr.GetTranslation("bag.getFriendPackFrame.tipTxtMsg");
         addToContent(this._tipTxt);
      }
      
      public function updateView(_arg_1:ItemTemplateInfo, _arg_2:int, _arg_3:int) : void
      {
         this._info = _arg_1;
         this._bagType = _arg_2;
         this._place = _arg_3;
         var _local_4:InventoryItemInfo = new InventoryItemInfo();
         _local_4.TemplateID = this._info.TemplateID;
         ItemManager.fill(_local_4);
         _local_4.IsBinds = true;
         _local_4.MaxCount = 1;
         _local_4.Count = 1;
         this._awardCell.info = _local_4;
         this._cellNameLabel.text = _local_4.Name;
      }
      
      private function addEvent() : void
      {
         this._certainBtn.addEventListener("click",this.__certainBtnClick);
         this._deselectBtn.addEventListener("click",this.__deselectBtnClick);
         addEventListener("response",this.__frameEventHandler);
         this._nameInput.addEventListener("change",this.__onReceiverChange);
         this._chooseFriendBtn.addEventListener("click",this.__showFramePanel);
         StageReferance.stage.addEventListener("click",this.__hideDropList);
      }
      
      protected function selectName(_arg_1:String, _arg_2:int = 0) : void
      {
         this.setName(_arg_1);
         this._friendList.setVisible = false;
      }
      
      public function setName(_arg_1:String) : void
      {
         this._nameInput.text = _arg_1;
      }
      
      private function __certainBtnClick(_arg_1:MouseEvent) : void
      {
         SoundManager.instance.playButtonSound();
         var _local_2:String = this._nameInput.text;
         if(_local_2 == "")
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("shop.ShopIIPresentView.give"));
            return;
         }
         if(FilterWordManager.IsNullorEmpty(_local_2))
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("shop.ShopIIPresentView.space"));
            return;
         }
         if(_local_2 == PlayerManager.Instance.Self.NickName)
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("bag.getFriendPackFrame.getSelfMsg"));
            return;
         }
         GameInSocketOut.sendGetFriendPack(_local_2,this._textArea.text,this._bagType,this._place);
         this.dispose();
      }
      
      private function __deselectBtnClick(_arg_1:MouseEvent) : void
      {
         SoundManager.instance.playButtonSound();
         this.dispose();
      }
      
      public function show() : void
      {
         LayerManager.Instance.addToLayer(this,3,true,1);
      }
      
      private function __frameEventHandler(_arg_1:FrameEvent) : void
      {
         switch(_arg_1.responseCode)
         {
            case 0:
            case 1:
               SoundManager.instance.playButtonSound();
               this.dispose();
         }
      }
      
      protected function __hideDropList(_arg_1:Event) : void
      {
         if(_arg_1.target is FilterFrameText)
         {
            return;
         }
         if(Boolean(this._dropList) && Boolean(this._dropList.parent))
         {
            this._dropList.parent.removeChild(this._dropList);
         }
      }
      
      private function __showFramePanel(_arg_1:MouseEvent) : void
      {
         var _local_2:Point = null;
         SoundManager.instance.playButtonSound();
         _local_2 = this._chooseFriendBtn.localToGlobal(new Point(0,0));
         this._friendList.x = _local_2.x - 95;
         this._friendList.y = _local_2.y + this._chooseFriendBtn.height;
         this._friendList.setVisible = true;
         LayerManager.Instance.addToLayer(this._friendList,3);
      }
      
      protected function __onReceiverChange(_arg_1:Event) : void
      {
         if(this._nameInput.text == "")
         {
            this._dropList.dataList = null;
            return;
         }
         var _local_2:Array = PlayerManager.Instance.onlineFriendList.concat(PlayerManager.Instance.offlineFriendList).concat(ConsortionModelManager.Instance.model.onlineConsortiaMemberList).concat(ConsortionModelManager.Instance.model.offlineConsortiaMemberList);
         this._dropList.dataList = this.filterSearch(this.filterRepeatInArray(_local_2),this._nameInput.text);
      }
      
      private function filterRepeatInArray(_arg_1:Array) : Array
      {
         var _local_4:int = 0;
         var _local_3:int = 0;
         var _local_2:Array = [];
         _local_4 = 0;
         while(_local_4 < _arg_1.length)
         {
            if(_local_4 == 0)
            {
               _local_2.push(_arg_1[_local_4]);
            }
            _local_3 = 0;
            while(_local_3 < _local_2.length)
            {
               if(_local_2[_local_3].NickName == _arg_1[_local_4].NickName)
               {
                  break;
               }
               if(_local_3 == _local_2.length - 1)
               {
                  _local_2.push(_arg_1[_local_4]);
               }
               _local_3++;
            }
            _local_4++;
         }
         return _local_2;
      }
      
      private function filterSearch(_arg_1:Array, _arg_2:String) : Array
      {
         var _local_4:int = 0;
         var _local_3:Array = [];
         _local_4 = 0;
         while(_local_4 < _arg_1.length)
         {
            if(_arg_1[_local_4].NickName.indexOf(_arg_2) != -1)
            {
               _local_3.push(_arg_1[_local_4]);
            }
            _local_4++;
         }
         return _local_3;
      }
      
      private function removeEvent() : void
      {
         if(Boolean(this._certainBtn))
         {
            this._certainBtn.removeEventListener("click",this.__certainBtnClick);
         }
         if(Boolean(this._deselectBtn))
         {
            this._deselectBtn.removeEventListener("click",this.__deselectBtnClick);
         }
         removeEventListener("response",this.__frameEventHandler);
         if(Boolean(this._nameInput))
         {
            this._nameInput.removeEventListener("change",this.__onReceiverChange);
         }
         if(Boolean(this._chooseFriendBtn))
         {
            this._chooseFriendBtn.removeEventListener("click",this.__showFramePanel);
         }
         StageReferance.stage.removeEventListener("click",this.__hideDropList);
      }
      
      override public function dispose() : void
      {
         super.dispose();
         this.removeEvent();
         this._cellBg = null;
         this._certainBtn = null;
         this._deselectBtn = null;
         this._awardCell = null;
         this._info = null;
         this._comboBoxLabel = null;
         this._nameInput = null;
         this._dropList = null;
         this._friendList = null;
         this._textArea = null;
         this._textAreaBg = null;
         this._chooseFriendBtn = null;
         this._cellNameLabel = null;
         this._tipTxt = null;
         this._checkOutViewBg = null;
         if(Boolean(this.parent))
         {
            this.parent.removeChild(this);
         }
      }
   }
}

