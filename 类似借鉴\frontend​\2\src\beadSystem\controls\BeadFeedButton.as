package beadSystem.controls
{
   import bagAndInfo.cell.DragEffect;
   import baglocked.BaglockedManager;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.controls.SimpleBitmapButton;
   import com.pickgliss.ui.controls.cell.ICell;
   import ddt.interfaces.IDragable;
   import ddt.manager.DragManager;
   import ddt.manager.PlayerManager;
   import ddt.manager.SoundManager;
   import flash.display.Bitmap;
   import flash.events.Event;
   import flash.events.MouseEvent;
   
   public class BeadFeedButton extends SimpleBitmapButton implements IDragable
   {
      
      public static const stopFeed:String = "stopfeed";
      
      public var isActive:Boolean = false;
      
      public function BeadFeedButton()
      {
         super();
         this.addEvt();
      }
      
      private function addEvt() : void
      {
         this.addEventListener("click",this.clickthis);
      }
      
      private function removeEvt() : void
      {
         this.removeEventListener("click",this.clickthis);
      }
      
      private function clickthis(_arg_1:MouseEvent) : void
      {
         SoundManager.instance.play("008");
         this.dragStart(stage.mouseX,stage.mouseY);
      }
      
      override protected function init() : void
      {
         buttonMode = true;
         super.init();
      }
      
      public function dragAgain() : void
      {
         if(Boolean(stage))
         {
            this.dragStart(stage.mouseX,stage.mouseY);
         }
      }
      
      public function dragStop(_arg_1:DragEffect) : void
      {
         this.isActive = true;
         if(PlayerManager.Instance.Self.bagLocked && _arg_1.target is ICell)
         {
            BaglockedManager.Instance.show();
            return;
         }
         SoundManager.instance.play("008");
         if(_arg_1.target is BeadCell)
         {
            (_arg_1.target as BeadCell).FeedBead();
         }
         else
         {
            dispatchEvent(new Event("stopfeed"));
         }
      }
      
      public function dragStart(_arg_1:Number, _arg_2:Number) : void
      {
         var _local_3:Bitmap = ComponentFactory.Instance.creatBitmap("beadSystem.feedIcon");
         DragManager.startDrag(this,this,_local_3,_arg_1,_arg_2,"move",false);
      }
      
      public function getSource() : IDragable
      {
         return this;
      }
   }
}

