package consortion.analyze
{
   import com.pickgliss.loader.DataAnalyzer;
   
   public class ConsortiaTaskRankAnalyzer extends DataAnalyzer
   {
      
      private var _dataList:Array;
      
      public function ConsortiaTaskRankAnalyzer(_arg_1:Function)
      {
         super(_arg_1);
      }
      
      public function get dataList() : Array
      {
         return this._dataList;
      }
      
      override public function analyze(_arg_1:*) : void
      {
         var _local_3:int = 0;
         var _local_6:int = 0;
         var _local_4:* = null;
         var _local_5:* = null;
         var _local_2:XML = new XML(_arg_1);
         this._dataList = [];
         if(_local_2.@value == "true")
         {
            _local_4 = _local_2.children();
            _local_3 = int(_local_4.length());
            _local_6 = 0;
            while(_local_6 < _local_3)
            {
               _local_5 = {};
               _local_5.id = int(_local_4[_local_6].@ID);
               _local_5.name = String(_local_4[_local_6].@NicName);
               _local_5.rank = _local_6 + 1;
               _local_5.percent = Number(_local_4[_local_6].@Percentage);
               _local_5.contribute = Number(_local_4[_local_6].@AwardRichesoffer);
               this._dataList.push(_local_5);
               _local_6++;
            }
            onAnalyzeComplete();
         }
         else
         {
            message = _local_2.@message;
            onAnalyzeError();
            onAnalyzeError();
         }
      }
   }
}

