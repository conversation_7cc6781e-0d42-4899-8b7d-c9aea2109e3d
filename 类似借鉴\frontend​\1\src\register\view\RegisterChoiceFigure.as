package register.view
{
   import com.pickgliss.loader.BaseLoader;
   import com.pickgliss.loader.LoadResourceManager;
   import com.pickgliss.loader.LoaderEvent;
   import com.pickgliss.loader.RequestLoader;
   import com.pickgliss.ui.core.Disposeable;
   import com.pickgliss.utils.Base64;
   import com.pickgliss.utils.ClassUtils;
   import com.pickgliss.utils.MD5;
   import com.pickgliss.utils.ObjectUtils;
   import com.pickgliss.utils.StringUtils;
   import ddt.GlobalData;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.FocusEvent;
   import flash.events.MouseEvent;
   import flash.events.TimerEvent;
   import flash.net.URLVariables;
   import flash.text.TextField;
   import flash.utils.Timer;
   import register.RegisterLuncher;
   import register.RegisterSoundManager;
   
   public class RegisterChoiceFigure extends Sprite implements Disposeable
   {
      
      private const INPUT_MSG:String = "请点这里输入昵称";
      
      private var _view:MovieClip;
      
      private var _boyClickArea:Sprite;
      
      private var _girlClickArea:Sprite;
      
      private var _boyClickBtn:MovieClip;
      
      private var _girlClickBtn:MovieClip;
      
      private var _movie:MovieClip;
      
      private var _nameField:TextField;
      
      private var _clearbtn:Sprite;
      
      private var _randomBtn:Sprite;
      
      private var _nameState:TextField;
      
      private var _enterGameBtn:Sprite;
      
      private var _sex:Boolean = true;
      
      private var _luncher:RegisterLuncher;
      
      private var _typeClip:MovieClip;
      
      private var _bg:MovieClip;
      
      private var _phoneField:TextField;
      
      private var _codeField:TextField;
      
      private var _getCodeBtn:MovieClip;
      
      private var _phoneStr:String;
      
      private var _timeTxt:TextField;
      
      private var _isPassSms:Boolean = false;
      
      private var _isfocusIned:Boolean = false;
      
      private var _isClickEnterGame:Boolean;
      
      private var _isWaiting:Boolean = false;
      
      private var _remainTime:int = 60;
      
      private var _timer:Timer;
      
      public function RegisterChoiceFigure(param1:RegisterLuncher)
      {
         _luncher = param1;
         super();
         init();
         initEvent();
         updateSex(false,false);
         loadRandomNickName();
      }
      
      public static function solveRequestPath(param1:String = "") : String
      {
         return GlobalData.requestPath + param1;
      }
      
      private function init() : void
      {
         _view = ClassUtils.CreatInstance("asset.choicefigure.movie") as MovieClip;
         _bg = _view["bgClip"];
         _typeClip = _view["typeClip"];
         if(_luncher.type == 1)
         {
            _bg.gotoAndStop(1);
            _typeClip.gotoAndStop(1);
         }
         else
         {
            _bg.gotoAndStop(2);
            _typeClip.gotoAndStop(2);
            _phoneField = _typeClip["phoneTxt"];
            _codeField = _typeClip["codeTxt"];
            _getCodeBtn = _typeClip["codeBtn"];
            _phoneField.restrict = "0-9";
            _phoneField.maxChars = 11;
            _codeField.restrict = "0-9";
            _codeField.maxChars = 10;
            _getCodeBtn.buttonMode = true;
            _timeTxt = _typeClip["timeTxt"];
            _timeTxt.visible = false;
         }
         _boyClickArea = _view["boyArea"];
         _girlClickArea = _view["girlArea"];
         _boyClickBtn = _typeClip["boyBtn"];
         _girlClickBtn = _typeClip["girlBtn"];
         _movie = _view["playerMovie"];
         _clearbtn = _typeClip["clearbtn"];
         _randomBtn = _typeClip["dice"];
         _nameField = _typeClip["nickname"];
         _nameState = _typeClip["nameState"];
         _enterGameBtn = _typeClip["enterGame"];
         _boyClickArea.buttonMode = _girlClickArea.buttonMode = true;
         _boyClickBtn.buttonMode = _girlClickBtn.buttonMode = true;
         _clearbtn.buttonMode = _randomBtn.buttonMode = _enterGameBtn.buttonMode = true;
         addChild(_view);
      }
      
      private function loadRandomNickName() : void
      {
         updateMouseState(false);
         var _loc2_:URLVariables = new URLVariables();
         _loc2_["sex"] = _sex ? 1 : 0;
         _loc2_["rnd"] = Math.random();
         var _loc1_:RequestLoader = LoadResourceManager.Instance.createLoader(GlobalData.requestPath + "NickNameRandom.ashx",6,_loc2_);
         _loc1_.addEventListener("complete",__nickNameHandler);
         _loc1_.addEventListener("loadError",__onLoadNickError);
         LoadResourceManager.Instance.startLoad(_loc1_);
      }
      
      protected function __nickNameHandler(param1:LoaderEvent) : void
      {
         param1.loader.removeEventListener("complete",__nickNameHandler);
         param1.loader.removeEventListener("loadError",__onLoadNickError);
         var _loc3_:XML = new XML(param1.loader.content);
         var _loc2_:String = _loc3_.@name;
         _nameField.text = _loc2_;
         updateTextState();
         loadCheckNickname();
      }
      
      protected function __onLoadNickError(param1:LoaderEvent) : void
      {
         param1.loader.removeEventListener("complete",__nickNameHandler);
         param1.loader.removeEventListener("loadError",__onLoadNickError);
         _nameField.text = "请点这里输入昵称";
         updateTextState();
         updateMouseState(true);
      }
      
      private function __onClickClearName(param1:MouseEvent) : void
      {
         RegisterSoundManager.instance.playButton();
         _nameField.text = "";
         updateTextState();
      }
      
      private function __onClickRandomName(param1:MouseEvent) : void
      {
         RegisterSoundManager.instance.playButton();
         _isClickEnterGame = false;
         loadRandomNickName();
      }
      
      private function loadCheckNickname() : void
      {
         var _loc2_:URLVariables = new URLVariables();
         _loc2_["NickName"] = _nameField.text;
         _loc2_["rnd"] = Math.random();
         var _loc1_:RequestLoader = LoadResourceManager.Instance.createLoader(GlobalData.requestPath + "NickNameCheck.ashx",6,_loc2_);
         _loc1_.addEventListener("complete",__onLoadCheckNickName);
         _loc1_.addEventListener("loadError",__onLoadCheckNickError);
         LoadResourceManager.Instance.startLoad(_loc1_);
      }
      
      private function loadCheckSms() : void
      {
         var _loc4_:URLVariables = new URLVariables();
         var _loc1_:String = GlobalData.user + _phoneStr + _luncher.time + "9FItrZjdaoyrsJnb";
         _loc1_ = Base64.encode(_loc1_);
         var _loc2_:String = MD5.hash(_loc1_);
         _loc4_["userName"] = GlobalData.user;
         _loc4_["phone"] = _phoneField.text;
         _loc4_["token"] = _loc2_;
         _loc4_["smsKey"] = _codeField.text;
         _loc4_["rnd"] = Math.random();
         var _loc3_:RequestLoader = LoadResourceManager.Instance.createLoader(GlobalData.requestPath + "SMSCheck.ashx",6,_loc4_);
         _loc3_.addEventListener("complete",__onLoadCheckSms);
         _loc3_.addEventListener("loadError",__onLoadCheckSmsError);
         LoadResourceManager.Instance.startLoad(_loc3_);
      }
      
      protected function __onLoadCheckSmsError(param1:LoaderEvent) : void
      {
         param1.loader.removeEventListener("complete",__onLoadCheckSms);
         param1.loader.removeEventListener("loadError",__onLoadCheckSmsError);
         _nameField.text = "请点这里输入昵称";
         updateTextState();
      }
      
      protected function __onLoadCheckSms(param1:LoaderEvent) : void
      {
         param1.loader.removeEventListener("complete",__onLoadCheckSms);
         param1.loader.removeEventListener("loadError",__onLoadCheckSmsError);
         var _loc2_:XML = new XML(param1.loader.content);
         _timeTxt.visible = true;
         if(String(_loc2_.@value) == "true")
         {
            if(_timer)
            {
               _timer.stop();
            }
            _isPassSms = true;
            _timeTxt.text = "验证码正确！";
            if(_isClickEnterGame)
            {
               enterGame();
            }
            else
            {
               updateMouseState(true);
            }
         }
         else
         {
            _timeTxt.text = "验证码错误！";
            updateMouseState(true);
         }
      }
      
      protected function __onLoadCheckNickError(param1:LoaderEvent) : void
      {
         param1.loader.removeEventListener("complete",__onLoadCheckNickName);
         param1.loader.removeEventListener("loadError",__onLoadCheckNickError);
         _nameField.text = "请点这里输入昵称";
         updateTextState();
      }
      
      protected function __onLoadCheckNickName(param1:LoaderEvent) : void
      {
         param1.loader.removeEventListener("complete",__onLoadCheckNickName);
         param1.loader.removeEventListener("loadError",__onLoadCheckNickError);
         var _loc2_:XML = new XML(param1.loader.content);
         _nameState.text = String(_loc2_.@message);
         if(String(_loc2_.@value) == "true")
         {
            _nameState.textColor = 3593990;
            if(_isClickEnterGame)
            {
               if(_luncher.type == 2)
               {
                  loadCheckSms();
               }
               else
               {
                  enterGame();
               }
            }
            else
            {
               updateMouseState(true);
            }
         }
         else
         {
            _nameState.textColor = 15400960;
            updateMouseState(true);
         }
      }
      
      private function enterGame() : void
      {
         _luncher.nickName = _nameField.text;
         _luncher.sex = _sex;
         if(_luncher.type == 2)
         {
            _luncher.phone = _phoneStr;
            _luncher.smsKey = _codeField.text;
         }
         _luncher.login();
         _luncher.disposeFigure();
         _luncher = null;
      }
      
      public function reEnterGame(param1:Boolean = false) : void
      {
         if(!param1)
         {
            updateMouseState(true);
            if(_timer)
            {
               _timer.stop();
            }
            _isWaiting = false;
            _timeTxt.text = "验证码错误！";
         }
         else
         {
            _luncher.disposeFigure();
            _luncher = null;
         }
      }
      
      protected function __onTextChange(param1:Event) : void
      {
         updateTextState();
      }
      
      private function updateTextState() : void
      {
         if(_nameField.text == "" || _nameField.text == "请点这里输入昵称")
         {
            _clearbtn.visible = false;
            _nameState.text = "可输入中英文或数字，长度不超过14个字符";
            _nameState.textColor = 16445855;
         }
         else
         {
            _clearbtn.visible = true;
         }
      }
      
      private function __focusIn(param1:FocusEvent) : void
      {
         if(_nameField.text == "请点这里输入昵称")
         {
            _nameField.text = "";
         }
         updateTextState();
         _isfocusIned = true;
      }
      
      private function __focusOut(param1:FocusEvent) : void
      {
         if(_isfocusIned)
         {
            if(StringUtils.trim(_nameField.text) != "")
            {
               _isClickEnterGame = false;
               loadCheckNickname();
            }
         }
      }
      
      private function updateSex(param1:Boolean, param2:Boolean = true) : void
      {
         var _loc3_:String = null;
         if(_sex != param1)
         {
            _sex = param1;
            _loc3_ = "normal";
            if(_sex)
            {
               _boyClickBtn.gotoAndStop(2);
               _girlClickBtn.gotoAndStop(1);
               if(param2)
               {
                  _loc3_ = "boy";
               }
               else
               {
                  _loc3_ = "boyState";
               }
            }
            else
            {
               _boyClickBtn.gotoAndStop(1);
               _girlClickBtn.gotoAndStop(2);
               if(param2)
               {
                  _loc3_ = "girl";
               }
               else
               {
                  _loc3_ = "girlState";
               }
            }
            _movie.gotoAndPlay(_loc3_);
         }
      }
      
      private function updateMouseState(param1:Boolean) : void
      {
         _boyClickArea.mouseEnabled = _girlClickArea.mouseEnabled = param1;
         _boyClickBtn.mouseEnabled = _girlClickBtn.mouseEnabled = param1;
         _clearbtn.mouseEnabled = _randomBtn.mouseEnabled = _enterGameBtn.mouseEnabled = param1;
         _nameField.mouseEnabled = param1;
      }
      
      protected function __onClickGirl(param1:MouseEvent) : void
      {
         RegisterSoundManager.instance.playButton();
         updateSex(false);
      }
      
      protected function __onClickBoy(param1:MouseEvent) : void
      {
         RegisterSoundManager.instance.playButton();
         updateSex(true);
      }
      
      protected function __onClickEnterGame(param1:MouseEvent) : void
      {
         RegisterSoundManager.instance.playButton();
         _isClickEnterGame = true;
         updateMouseState(false);
         loadCheckNickname();
      }
      
      private function __onClickGetCode(param1:MouseEvent) : void
      {
         if(_isWaiting)
         {
            return;
         }
         _isWaiting = true;
         _timeTxt.visible = true;
         _remainTime = 60;
         _timeTxt.text = "60s";
         if(!_timer)
         {
            _timer = new Timer(1000);
            _timer.addEventListener("timer",__timer);
         }
         _timer.start();
         _phoneStr = _phoneField.text;
         var _loc5_:URLVariables = new URLVariables();
         var _loc2_:String = GlobalData.user + _phoneStr + _luncher.time + "9FItrZjdaoyrsJnb";
         _loc2_ = Base64.encode(_loc2_);
         var _loc3_:String = MD5.hash(_loc2_);
         _loc5_["userName"] = GlobalData.user;
         _loc5_["phone"] = _phoneField.text;
         _loc5_["token"] = _loc3_;
         var _loc4_:BaseLoader = new BaseLoader(1,solveRequestPath("SMSService.ashx"),_loc5_);
         _loc4_.loadFromWeb();
      }
      
      private function __timer(param1:TimerEvent) : void
      {
         _remainTime--;
         if(_remainTime <= 0)
         {
            _timer.stop();
            _isWaiting = false;
            _timeTxt.visible = false;
         }
         _timeTxt.text = _remainTime + "s";
      }
      
      private function initEvent() : void
      {
         _boyClickArea.addEventListener("click",__onClickBoy);
         _boyClickBtn.addEventListener("click",__onClickBoy);
         _girlClickArea.addEventListener("click",__onClickGirl);
         _girlClickBtn.addEventListener("click",__onClickGirl);
         _clearbtn.addEventListener("click",__onClickClearName);
         _randomBtn.addEventListener("click",__onClickRandomName);
         _enterGameBtn.addEventListener("click",__onClickEnterGame);
         _nameField.addEventListener("change",__onTextChange);
         _nameField.addEventListener("focusOut",__focusOut);
         _nameField.addEventListener("focusIn",__focusIn);
         if(_getCodeBtn)
         {
            _getCodeBtn.addEventListener("click",__onClickGetCode);
         }
      }
      
      private function removeEvent() : void
      {
         _boyClickArea.removeEventListener("click",__onClickBoy);
         _boyClickBtn.removeEventListener("click",__onClickBoy);
         _girlClickArea.removeEventListener("click",__onClickGirl);
         _girlClickBtn.removeEventListener("click",__onClickGirl);
         _clearbtn.removeEventListener("click",__onClickClearName);
         _randomBtn.removeEventListener("click",__onClickRandomName);
         _enterGameBtn.removeEventListener("click",__onClickEnterGame);
         _nameField.removeEventListener("change",__onTextChange);
         _nameField.removeEventListener("focusOut",__focusOut);
         _nameField.removeEventListener("focusIn",__focusIn);
         if(_getCodeBtn)
         {
            _getCodeBtn.removeEventListener("click",__onClickGetCode);
         }
         if(_timer)
         {
            _timer.stop();
            _timer.removeEventListener("timer",__timer);
            _timer = null;
         }
      }
      
      public function dispose() : void
      {
         removeEvent();
         _boyClickArea = null;
         _girlClickArea = null;
         _boyClickBtn = null;
         _girlClickBtn = null;
         _movie = null;
         _nameField = null;
         _clearbtn = null;
         _randomBtn = null;
         _nameState = null;
         _enterGameBtn = null;
         _phoneField = null;
         _codeField = null;
         _getCodeBtn = null;
         _timeTxt = null;
         ObjectUtils.disposeObject(_view);
         _view = null;
         if(this.parent)
         {
            this.parent.removeChild(this);
         }
      }
   }
}

