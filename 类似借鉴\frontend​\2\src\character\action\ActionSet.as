package character.action
{
   import flash.events.EventDispatcher;
   
   public class ActionSet extends EventDispatcher
   {
      
      private var _actions:Array;
      
      private var _currentAction:BaseAction;
      
      public function ActionSet(_arg_1:XML = null)
      {
         super();
         this._actions = [];
         if(<PERSON><PERSON><PERSON>(_arg_1))
         {
            this.parseFromXml(_arg_1);
         }
      }
      
      public function addAction(_arg_1:BaseAction) : void
      {
         if(<PERSON><PERSON><PERSON>(_arg_1))
         {
            this._actions.push(_arg_1);
         }
      }
      
      public function getAction(_arg_1:String) : BaseAction
      {
         var _local_2:BaseAction = null;
         for each(_local_2 in this._actions)
         {
            if(_local_2.name == _arg_1)
            {
               return _local_2;
            }
         }
         return null;
      }
      
      public function get next() : BaseAction
      {
         var _local_1:BaseAction = null;
         for each(_local_1 in this._actions)
         {
            if(_local_1.name == this._currentAction.name)
            {
               return _local_1;
            }
         }
         return this._currentAction;
      }
      
      public function get currentAction() : BaseAction
      {
         if(<PERSON><PERSON><PERSON>(this._currentAction))
         {
            return this._currentAction;
         }
         if(this._actions.length > 0)
         {
            this._currentAction = this._actions[0];
         }
         return this._currentAction;
      }
      
      public function get stringActions() : Array
      {
         var _local_2:BaseAction = null;
         var _local_1:Array = [];
         for each(_local_2 in this._actions)
         {
            _local_1.push(_local_2.name);
         }
         return _local_1;
      }
      
      public function get actions() : Array
      {
         return this._actions;
      }
      
      public function removeAction(_arg_1:String) : void
      {
         var _local_2:BaseAction = null;
         for each(_local_2 in this._actions)
         {
            if(_local_2.name == _arg_1)
            {
               this._actions.splice(this._actions.indexOf(_local_2),1);
               _local_2.dispose();
            }
         }
      }
      
      private function parseFromXml(_arg_1:XML) : void
      {
      }
      
      public function toXml() : XML
      {
         var _local_3:BaseAction = null;
         var _local_2:int = 0;
         var _local_1:XML = <actionSet></actionSet>;
         while(_local_2 < this._actions.length)
         {
            _local_3 = this._actions[_local_2];
            _local_1.appendChild(_local_3.toXml());
            _local_2++;
         }
         return _local_1;
      }
      
      public function dispose() : void
      {
         var _local_1:BaseAction = null;
         for each(_local_1 in this._actions)
         {
            _local_1.dispose();
         }
      }
   }
}

