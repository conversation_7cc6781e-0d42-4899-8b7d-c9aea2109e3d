package beadSystem.controls
{
   import bagAndInfo.cell.DragEffect;
   import baglocked.BaglockedManager;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.controls.SimpleBitmapButton;
   import ddt.interfaces.IDragable;
   import ddt.manager.DragManager;
   import ddt.manager.PlayerManager;
   import ddt.manager.SoundManager;
   import flash.display.Bitmap;
   import flash.events.MouseEvent;
   import flash.utils.setTimeout;
   import store.view.embed.EmbedStoneCell;
   
   public class BeadLockButton extends SimpleBitmapButton implements IDragable
   {
      
      public function BeadLockButton()
      {
         super();
         this.addEvt();
      }
      
      private function addEvt() : void
      {
         this.addEventListener("click",this.clickthis);
      }
      
      private function removeEvt() : void
      {
         this.removeEventListener("click",this.clickthis);
      }
      
      private function clickthis(_arg_1:MouseEvent) : void
      {
         SoundManager.instance.play("008");
         this.dragStart(stage.mouseX,stage.mouseY);
      }
      
      public function getSource() : IDragable
      {
         return this;
      }
      
      public function dragStop(_arg_1:DragEffect) : void
      {
         SoundManager.instance.play("008");
         if(PlayerManager.Instance.Self.bagLocked)
         {
            BaglockedManager.Instance.show();
            return;
         }
         if(_arg_1.target is BeadCell)
         {
            if((_arg_1.target as BeadCell).LockBead())
            {
               setTimeout(this.continueDrag,75);
            }
         }
         if(_arg_1.target is EmbedStoneCell)
         {
            if((_arg_1.target as EmbedStoneCell).LockBead())
            {
               setTimeout(this.continueDrag,75);
            }
         }
      }
      
      private function continueDrag() : void
      {
         if(Boolean(stage))
         {
            this.dragStart(stage.mouseX,stage.mouseY);
         }
      }
      
      public function dragStart(_arg_1:Number, _arg_2:Number) : void
      {
         var _local_3:Bitmap = ComponentFactory.Instance.creatBitmap("asset.beadSystem.beadInset.lockIcon");
         DragManager.startDrag(this,this,_local_3,_arg_1,_arg_2,"move",false);
      }
      
      override public function get width() : Number
      {
         return 81;
      }
      
      override public function get height() : Number
      {
         return 31;
      }
   }
}

