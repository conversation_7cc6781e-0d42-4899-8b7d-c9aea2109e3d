package consortion.guard
{
   import road7th.data.DictionaryData;
   
   public class ConsortiaGuardModel
   {
      
      private var _playerList:DictionaryData;
      
      private var _isOpen:Boolean;
      
      private var _openTime:Date;
      
      private var _bossHp:Array;
      
      private var _bossMaxHp:Array;
      
      private var _bossState:Array;
      
      private var _statueHp:Number = 0;
      
      private var _statueMaxHp:Number = 0;
      
      private var _isFight:Boolean = true;
      
      private var _isWin:Boolean;
      
      private var _endTime:Number;
      
      private var _openLevel:int;
      
      private var _buffLevel:int;
      
      private var _rankList:DictionaryData;
      
      private var _rankBossList:DictionaryData;
      
      public function ConsortiaGuardModel()
      {
         super();
         this.reset();
      }
      
      public function reset() : void
      {
         if(<PERSON><PERSON><PERSON>(this._playerList))
         {
            this._playerList.clear();
         }
         this._playerList = new DictionaryData();
         this._bossHp = [100,100,100,100];
         this._bossMaxHp = [100,100,100,100];
         this._bossState = [];
         if(<PERSON><PERSON><PERSON>(this._rankList))
         {
            this._rankList.clear();
         }
         this._rankList = new DictionaryData();
         if(<PERSON><PERSON><PERSON>(this._rankBossList))
         {
            this._rankBossList.clear();
         }
         this._rankBossList = new DictionaryData();
      }
      
      public function setBossHp(_arg_1:int, _arg_2:Number) : void
      {
         this._bossHp[_arg_1] = _arg_2;
      }
      
      public function setBossMaxHp(_arg_1:int, _arg_2:Number) : void
      {
         this._bossMaxHp[_arg_1] = _arg_2;
      }
      
      public function setBossState(_arg_1:int, _arg_2:int) : void
      {
         this._bossState[_arg_1] = _arg_2;
      }
      
      public function getBossHp(_arg_1:int) : Number
      {
         return this._bossHp[_arg_1];
      }
      
      public function getBossMaxHp(_arg_1:int) : Number
      {
         return this._bossMaxHp[_arg_1];
      }
      
      public function getBossState(_arg_1:int) : int
      {
         return this._bossState[_arg_1];
      }
      
      public function get playerList() : DictionaryData
      {
         return this._playerList;
      }
      
      public function set isOpen(_arg_1:Boolean) : void
      {
         this._isOpen = _arg_1;
      }
      
      public function get isOpen() : Boolean
      {
         return this._isOpen;
      }
      
      public function set openTime(_arg_1:Date) : void
      {
         this._openTime = _arg_1;
      }
      
      public function get openTime() : Date
      {
         return this._openTime;
      }
      
      public function get rankList() : DictionaryData
      {
         return this._rankList;
      }
      
      public function get statueHp() : Number
      {
         return this._statueHp;
      }
      
      public function set statueHp(_arg_1:Number) : void
      {
         this._statueHp = _arg_1;
      }
      
      public function get statueMaxHp() : Number
      {
         return this._statueMaxHp;
      }
      
      public function set statueMaxHp(_arg_1:Number) : void
      {
         this._statueMaxHp = _arg_1;
      }
      
      public function get isFight() : Boolean
      {
         return this._isFight;
      }
      
      public function set isFight(_arg_1:Boolean) : void
      {
         this._isFight = _arg_1;
      }
      
      public function get isWin() : Boolean
      {
         return this._isWin;
      }
      
      public function set isWin(_arg_1:Boolean) : void
      {
         this._isWin = _arg_1;
      }
      
      public function get endTime() : Number
      {
         return this._endTime;
      }
      
      public function set endTime(_arg_1:Number) : void
      {
         this._endTime = _arg_1;
      }
      
      public function get openLevel() : int
      {
         return this._openLevel;
      }
      
      public function set openLevel(_arg_1:int) : void
      {
         this._openLevel = _arg_1;
      }
      
      public function get buffLevel() : int
      {
         return this._buffLevel;
      }
      
      public function set buffLevel(_arg_1:int) : void
      {
         this._buffLevel = _arg_1;
      }
      
      public function get rankBossList() : DictionaryData
      {
         return this._rankBossList;
      }
   }
}

