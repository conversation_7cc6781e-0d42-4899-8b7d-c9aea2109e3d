package BombTurnTable
{
   import BombTurnTable.event.TurnTableEvent;
   import com.pickgliss.ui.controls.BaseButton;
   import ddt.CoreManager;
   import ddt.events.CEvent;
   import ddt.events.PkgEvent;
   import ddt.manager.LanguageMgr;
   import ddt.manager.MessageTipManager;
   import ddt.manager.SocketManager;
   import ddt.manager.SoundManager;
   import ddt.manager.TimeManager;
   import ddt.utils.HelperUIModuleLoad;
   import flash.events.IEventDispatcher;
   import hall.HallStateView;
   import hallIcon.HallIconManager;
   import road7th.comm.PackageIn;
   
   public class BombTurnTableManager extends CoreManager
   {
      
      public static const TURNTABLE_OPENVIEW:String = "TurnTableOpenView";
      
      public static const TURNTABLE_END:String = "TurnTableEnd";
      
      private static var _instance:BombTurnTableManager = null;
      
      private var _showFlag:Boolean = false;
      
      private var _iconBtn:BaseButton = null;
      
      private var _hall:HallStateView = null;
      
      private var _endDate:Date;
      
      private var _clickNum:Number = 0;
      
      public function BombTurnTableManager(_arg_1:IEventDispatcher = null)
      {
         super(_arg_1);
      }
      
      public static function get instance() : BombTurnTableManager
      {
         if(_instance == null)
         {
            _instance = new BombTurnTableManager();
         }
         return _instance;
      }
      
      public function setup() : void
      {
         SocketManager.Instance.addEventListener(PkgEvent.format(329,52),this._isOpen);
      }
      
      protected function _isOpen(_arg_1:PkgEvent) : void
      {
         var _local_2:PackageIn = _arg_1.pkg;
         this._showFlag = _local_2.readBoolean();
         this._endDate = _local_2.readDate();
         this.checkIcon();
      }
      
      private function checkIcon() : void
      {
         HallIconManager.instance.updateSwitchHandler("bombTurnTable",this._showFlag);
         if(this._showFlag == false)
         {
            this.dispatchEvent(new TurnTableEvent("TurnTableEnd",null));
         }
      }
      
      public function openView_Handler() : void
      {
         SoundManager.instance.playButtonSound();
         var _local_1:Number = new Date().time;
         if(_local_1 - this._clickNum < 1000)
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.storeIIStrength.startStrengthClickTimerMsg"));
            return;
         }
         this._clickNum = _local_1;
         show();
      }
      
      public function get isValid() : Boolean
      {
         var _local_1:Number = this._endDate.getTime() - 2000;
         var _local_2:Number = Number(TimeManager.Instance.Now().getTime());
         if(_local_1 < _local_2)
         {
            return true;
         }
         return false;
      }
      
      public function sendStartLottery() : void
      {
         SocketManager.Instance.out.sendBombTurnTableLottery();
      }
      
      public function requestViewData() : void
      {
         SocketManager.Instance.out.requestBombTurnTableData();
      }
      
      override protected function start() : void
      {
         new HelperUIModuleLoad().loadUIModule(["bombTurnTable"],function():void
         {
            dispatchEvent(new CEvent("TurnTableOpenView"));
         });
      }
   }
}

