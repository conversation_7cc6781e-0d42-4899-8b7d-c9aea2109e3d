package bombKing.event
{
   import flash.events.Event;
   
   public class BombKingEvent extends Event
   {
      
      public static const BOMBKING_OPENVIEW:String = "bombkingOpenView";
      
      public static const STARTLOADBATTLEXML:String = "startloadbattlexml";
      
      public static const RECORDING_MODIFYANGLE:String = "recordingModifyAngle";
      
      public var data:Object;
      
      public function BombKingEvent(_arg_1:String, _arg_2:Object = null)
      {
         this.data = _arg_2;
         super(_arg_1,bubbles,cancelable);
      }
   }
}

