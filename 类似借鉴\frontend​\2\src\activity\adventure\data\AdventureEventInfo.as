package activity.adventure.data
{
   public class AdventureEventInfo
   {
      
      public static const NO_ACTION:int = 0;
      
      public static const ADD_COUNT:int = 1;
      
      public static const ADD_BOX1:int = 2;
      
      public static const ADD_BOX2:int = 3;
      
      public static const ADD_BOX3:int = 4;
      
      public static const ADD_BOX4:int = 5;
      
      public static const RANDOM:int = 6;
      
      public static const SELECT:int = 7;
      
      public static const ACTION_GOOD:int = 8;
      
      public static const ACTION_BAD:int = 9;
      
      public static const BOSS:int = 10;
      
      public static const SHOP:int = 11;
      
      public static const GET_ITEM:int = 12;
      
      public var ID:int;
      
      public var Gate:int;
      
      public var GateName:String;
      
      public var EventType:int;
      
      public var EventValue:int;
      
      public var Option:String;
      
      public var Descr:String;
      
      public function AdventureEventInfo()
      {
         super();
      }
   }
}

