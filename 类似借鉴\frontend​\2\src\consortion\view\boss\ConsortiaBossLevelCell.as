package consortion.view.boss
{
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.core.Disposeable;
   import com.pickgliss.ui.text.FilterFrameText;
   import ddt.manager.LanguageMgr;
   import ddt.utils.PositionUtils;
   import flash.display.Bitmap;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class ConsortiaBossLevelCell extends Sprite implements Disposeable
   {
      
      private var _txt:FilterFrameText;
      
      private var _light:Bitmap;
      
      private var _level:int;
      
      public function ConsortiaBossLevelCell(_arg_1:int)
      {
         super();
         this._level = _arg_1;
         this.buttonMode = true;
         this._txt = ComponentFactory.Instance.creatComponentByStylename("consortion.bossFrame.levelShowTxt");
         PositionUtils.setPos(this._txt,"consortiaBoss.levelView.cellTxtPos");
         addChild(this._txt);
         this._light = ComponentFactory.Instance.creatBitmap("asset.consortionBossFrame.levelCellLight");
         this._light.visible = false;
         addChild(this._light);
         addEventListener("mouseOver",this.overHandler,false,0,true);
         addEventListener("mouseOut",this.outHandler,false,0,true);
      }
      
      public function judgeMaxLevel(_arg_1:int) : void
      {
         if(this._level > _arg_1)
         {
            this.mouseEnabled = false;
            this.filters = ComponentFactory.Instance.creatFilters("grayFilter");
         }
      }
      
      public function update(_arg_1:String) : void
      {
         this._txt.text = LanguageMgr.GetTranslation(_arg_1,this._level);
      }
      
      public function changeLightSizePos(_arg_1:int, _arg_2:int, _arg_3:int, _arg_4:int) : void
      {
         this._light.width = _arg_1;
         this._light.height = _arg_2;
         this._light.x = _arg_3;
         this._light.y = _arg_4;
      }
      
      public function get level() : int
      {
         return this._level;
      }
      
      private function overHandler(_arg_1:MouseEvent) : void
      {
         this._light.visible = true;
      }
      
      private function outHandler(_arg_1:MouseEvent) : void
      {
         this._light.visible = false;
      }
      
      public function dispose() : void
      {
      }
   }
}

