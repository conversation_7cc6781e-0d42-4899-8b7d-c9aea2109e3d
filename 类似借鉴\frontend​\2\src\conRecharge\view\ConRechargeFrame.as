package conRecharge.view
{
   import com.pickgliss.events.FrameEvent;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.controls.BaseButton;
   import com.pickgliss.ui.controls.Frame;
   import com.pickgliss.ui.text.FilterFrameText;
   import com.pickgliss.utils.ObjectUtils;
   import conRecharge.ConRechargeManager;
   import ddt.manager.LanguageMgr;
   import ddt.manager.LeavePageManager;
   import ddt.manager.SocketManager;
   import ddt.manager.SoundManager;
   import ddt.manager.TimeManager;
   import ddt.utils.PositionUtils;
   import ddt.view.bossbox.TimeCountDown;
   import flash.display.Bitmap;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import road7th.utils.DateUtils;
   import wonderfulActivity.WonderfulActivityManager;
   
   public class ConRechargeFrame extends Frame
   {
      
      private var _topBg:Bitmap;
      
      private var _rightBg:Bitmap;
      
      private var _leftBg:Bitmap;
      
      private var _activityTime:FilterFrameText;
      
      private var _totalRechargeTxt:FilterFrameText;
      
      private var _rechargeBtn:BaseButton;
      
      private var _leftView:ConRechargeLeftItem;
      
      private var _rightView:ConRechargeRightItem;
      
      private var _time:TimeCountDown;
      
      public function ConRechargeFrame()
      {
         super();
         this.initView();
         this.addEvent();
      }
      
      private function initView() : void
      {
         var _local_6:int = 0;
         var _local_9:int = 0;
         var _local_7:int = 0;
         var _local_8:* = null;
         var _local_3:* = null;
         var _local_1:* = null;
         this._time = new TimeCountDown(1000);
         this._topBg = ComponentFactory.Instance.creatBitmap("asset.conRecharge.topBg");
         addToContent(this._topBg);
         this._leftBg = ComponentFactory.Instance.creatBitmap("asset.conRecharge.leftBg");
         addToContent(this._leftBg);
         this._rightBg = ComponentFactory.Instance.creatBitmap("asset.conRecharge.rightBg");
         addToContent(this._rightBg);
         this._leftView = new ConRechargeLeftItem();
         addToContent(this._leftView);
         PositionUtils.setPos(this._leftView,"conRecharge.leftView.pos");
         this._rightView = new ConRechargeRightItem();
         addToContent(this._rightView);
         PositionUtils.setPos(this._rightView,"conRecharge.rightView.pos");
         this._activityTime = ComponentFactory.Instance.creatComponentByStylename("conRecharge.activityTime.txt");
         addToContent(this._activityTime);
         var _local_2:Array = WonderfulActivityManager.Instance.getActivityInitDataById(ConRechargeManager.instance.actId).statusArr;
         _local_9 = 0;
         while(_local_9 < _local_2.length)
         {
            if(_local_2[_local_9].statusID == 0)
            {
               _local_6 = int(_local_2[_local_9].statusValue);
               break;
            }
            _local_9++;
         }
         this._totalRechargeTxt = ComponentFactory.Instance.creatComponentByStylename("conRecharge.totalRecharge.txt");
         addToContent(this._totalRechargeTxt);
         this._totalRechargeTxt.text = LanguageMgr.GetTranslation("ddt.conRecharge.totalRecharge",_local_6);
         this._rechargeBtn = ComponentFactory.Instance.creatComponentByStylename("conRecharge.rechargeBtn");
         addToContent(this._rechargeBtn);
         var _local_5:String = LanguageMgr.GetTranslation("ddt.conRecharge.chargeTip");
         var _local_4:Array = WonderfulActivityManager.Instance.getActivityInitDataById(ConRechargeManager.instance.actId).statusArr;
         _local_7 = 0;
         while(_local_7 < _local_4.length)
         {
            _local_8 = _local_4[_local_7];
            if(_local_8.statusID != 0)
            {
               _local_3 = String(_local_8.statusID);
               _local_1 = _local_3.substr(0,4) + "/" + _local_3.substr(4,2) + "/" + _local_3.substr(6,2);
               _local_5 += "\n" + LanguageMgr.GetTranslation("ddt.conRecharge.moneyTxt",_local_1 + "--" + _local_8.statusValue);
            }
            _local_7++;
         }
         this._rechargeBtn.tipData = _local_5;
      }
      
      private function addEvent() : void
      {
         addEventListener("response",this._responseHandle);
         this._rechargeBtn.addEventListener("click",this.clickHandler);
         this._time.addEventListener("TIME_countdown_complete",this._timeOver);
         this._time.addEventListener("countdown_one",this._timeOne);
         var _local_1:int = DateUtils.getHourDifference(DateUtils.getDateByStr(ConRechargeManager.instance.beginTime).valueOf(),DateUtils.getDateByStr(ConRechargeManager.instance.endTime).valueOf());
         this._time.setTimeOnMinute(_local_1 * 60);
      }
      
      private function _timeOver(_arg_1:Event) : void
      {
      }
      
      private function clickHandler(_arg_1:MouseEvent) : void
      {
         LeavePageManager.leaveToFillPath();
      }
      
      private function _timeOne(_arg_1:Event) : void
      {
         var _local_3:Date = DateUtils.getDateByStr(ConRechargeManager.instance.endTime);
         var _local_2:String = TimeManager.Instance.getMaxRemainDateStr(_local_3);
         this._activityTime.text = LanguageMgr.GetTranslation("ddt.conRecharge.activityTime",_local_2);
      }
      
      private function removeEvent() : void
      {
         removeEventListener("response",this._responseHandle);
         this._rechargeBtn.removeEventListener("click",this.clickHandler);
         this._time.removeEventListener("TIME_countdown_complete",this._timeOver);
         this._time.removeEventListener("countdown_one",this._timeOne);
         this._time.dispose();
      }
      
      protected function _responseHandle(_arg_1:FrameEvent) : void
      {
         SoundManager.instance.play("008");
         switch(_arg_1.responseCode)
         {
            case 4:
               return;
            case 0:
            case 1:
               this.dispose();
         }
      }
      
      override public function dispose() : void
      {
         SocketManager.Instance.out.requestWonderfulActInit(2);
         this.removeEvent();
         super.dispose();
         ObjectUtils.disposeObject(this._topBg);
         this._topBg = null;
         ObjectUtils.disposeObject(this._leftBg);
         this._leftBg = null;
         ObjectUtils.disposeObject(this._rightBg);
         this._rightBg = null;
         ObjectUtils.disposeObject(this._activityTime);
         this._activityTime = null;
         ObjectUtils.disposeObject(this._totalRechargeTxt);
         this._totalRechargeTxt = null;
         ObjectUtils.disposeObject(this._rechargeBtn);
         this._rechargeBtn = null;
         ObjectUtils.disposeObject(this._leftView);
         this._leftView = null;
         ObjectUtils.disposeObject(this._rightView);
         this._rightView = null;
      }
   }
}

