package beadSystem.controls
{
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.core.Component;
   import com.pickgliss.ui.text.FilterFrameText;
   import com.pickgliss.utils.ClassUtils;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.data.goods.InventoryItemInfo;
   import ddt.manager.PlayerManager;
   import ddt.utils.PositionUtils;
   import flash.display.Bitmap;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.utils.Dictionary;
   
   public class BeadFeedProgress extends Component
   {
      
      protected var _background:Bitmap;
      
      protected var _thuck:Component;
      
      protected var _graphics_thuck:Bitmap;
      
      protected var _progressLabel:FilterFrameText;
      
      protected var _star:MovieClip;
      
      private var _progressBarMask:Sprite;
      
      private var _scaleValue:Number;
      
      private var _total:int = 50;
      
      private var _taskFrames:Dictionary;
      
      private var _currentExp:int;
      
      private var _upLevelExp:int;
      
      private var _currentLevel:int;
      
      private var _currentFrame:int;
      
      public function BeadFeedProgress()
      {
         super();
         this.intView();
      }
      
      private function intView() : void
      {
         this._background = ComponentFactory.Instance.creatBitmap("asset.ddtstore.StrengthenSpaceProgress");
         PositionUtils.setPos(this._background,"asset.ddtstore.StrengthenSpaceProgressBgPos");
         addChild(this._background);
         this._thuck = ComponentFactory.Instance.creatComponentByStylename("ddtstore.info.thunck");
         addChild(this._thuck);
         this._graphics_thuck = ComponentFactory.Instance.creatBitmap("asset.ddtstore.StrengthenColorStrip");
         addChild(this._graphics_thuck);
         this.initMask();
         this._star = ClassUtils.CreatInstance("asset.strengthen.star");
         this._star.y = this._progressBarMask.height / 2;
         addChild(this._star);
         this._progressLabel = ComponentFactory.Instance.creatComponentByStylename("ddtstore.info.StoreStrengthProgressText");
         addChild(this._progressLabel);
         this._scaleValue = this._graphics_thuck.width / this._total;
         this.resetProgress();
      }
      
      public function set currentExp(_arg_1:int) : void
      {
         this._currentExp = _arg_1;
      }
      
      public function set upLevelExp(_arg_1:int) : void
      {
         this._upLevelExp = _arg_1;
      }
      
      public function resetProgress() : void
      {
         tipData = "0/0";
         this._progressLabel.text = "0%";
         this._currentExp = 0;
         this._upLevelExp = 0;
         this._currentFrame = 0;
         this._currentLevel = -1;
         this.setMask(0);
         this.setStarVisible(false);
         this._taskFrames = new Dictionary();
      }
      
      public function intProgress(_arg_1:InventoryItemInfo) : void
      {
         var _local_2:Number = NaN;
         var _local_3:int = 0;
         this._currentFrame = 0;
         this._currentLevel = 5;
         if(this._upLevelExp > 0 && this._currentExp < this._upLevelExp)
         {
            _local_2 = this._currentExp / this._upLevelExp;
            _local_3 = Math.floor(_local_2 * this._total);
            if(_local_3 < 1 && _local_2 > 0)
            {
               _local_3 = 1;
            }
            this._currentFrame = _local_3;
         }
         this.setMask(this._currentFrame);
         this.setExpPercent();
         this.setStarVisible(false);
         this._taskFrames = new Dictionary();
      }
      
      public function setProgress(_arg_1:InventoryItemInfo) : void
      {
         if(this._currentLevel == 6)
         {
            this._taskFrames[0] = this._total;
            this._currentLevel = 6;
         }
         var _local_2:Number = this._currentExp / this._upLevelExp;
         var _local_3:int = Math.floor(_local_2 * this._total);
         if(_local_3 < 1 && _local_2 > 0)
         {
            _local_3 = 1;
         }
         if(this._currentFrame == _local_3)
         {
            if(Boolean(this._taskFrames[0]) && this._taskFrames[0] != 0)
            {
               this.setStarVisible(true);
               this._taskFrames[1] = _local_3;
               this.startProgress();
            }
         }
         else
         {
            this.setStarVisible(true);
            this._taskFrames[1] = _local_3;
            this.startProgress();
         }
         this.setExpPercent();
      }
      
      private function startProgress() : void
      {
         this.addEventListener("enterFrame",this.__startFrame);
      }
      
      private function __startFrame(_arg_1:Event) : void
      {
         var _local_2:int = 0;
         ++this._currentFrame;
         this.setMask(this._currentFrame);
         if(this._taskFrames.hasOwnProperty(0))
         {
            _local_2 = int(this._taskFrames[0]);
         }
         if(_local_2 == 0 && Boolean(this._taskFrames.hasOwnProperty(1)))
         {
            _local_2 = int(this._taskFrames[1]);
         }
         if(this._currentFrame >= _local_2)
         {
            if(_local_2 >= this._total)
            {
               this._currentFrame = 0;
               this._taskFrames[0] = 0;
            }
            else
            {
               this._taskFrames[1] = 0;
               this.removeEventListener("enterFrame",this.__startFrame);
               this.setStarVisible(false);
               _arg_1.stopImmediatePropagation();
            }
         }
      }
      
      private function setExpPercent() : void
      {
         var _local_1:Number = NaN;
         if(this._currentExp == 0 || PlayerManager.Instance.Self.embedUpLevelCell.itemInfo.Hole1 == 21)
         {
            this._progressLabel.text = "0%";
         }
         else
         {
            _local_1 = int(this._currentExp / this._upLevelExp * 100);
            if(isNaN(_local_1))
            {
               _local_1 = 0;
            }
            this._progressLabel.text = _local_1 + "%";
         }
         if(isNaN(this._currentExp))
         {
            this._currentExp = 0;
         }
         if(isNaN(this._upLevelExp))
         {
            this._upLevelExp = 0;
         }
         if(PlayerManager.Instance.Self.embedUpLevelCell.itemInfo.Hole1 == 21)
         {
            tipData = this._currentExp + "/" + 0;
         }
         else
         {
            tipData = this._currentExp + "/" + this._upLevelExp;
         }
      }
      
      private function setStarVisible(_arg_1:Boolean) : void
      {
         this._star.visible = _arg_1;
      }
      
      private function setMask(_arg_1:Number) : void
      {
         var _local_2:Number = _arg_1 * this._scaleValue;
         if(isNaN(_local_2) || _local_2 == 0)
         {
            this._progressBarMask.width = 0;
         }
         else
         {
            if(_local_2 >= this._graphics_thuck.width)
            {
               _local_2 %= this._graphics_thuck.width;
            }
            this._progressBarMask.width = _local_2;
         }
         this._star.x = this._progressBarMask.x + this._progressBarMask.width;
      }
      
      private function initMask() : void
      {
         this._progressBarMask = new Sprite();
         this._progressBarMask.graphics.beginFill(16777215,1);
         this._progressBarMask.graphics.drawRect(0,0,this._graphics_thuck.width,this._graphics_thuck.height);
         this._progressBarMask.graphics.endFill();
         this._graphics_thuck.cacheAsBitmap = true;
         this._graphics_thuck.mask = this._progressBarMask;
         addChild(this._progressBarMask);
      }
      
      override public function dispose() : void
      {
         ObjectUtils.disposeObject(this._thuck);
         this._thuck = null;
         ObjectUtils.disposeObject(this._background);
         this._background = null;
         ObjectUtils.disposeObject(this._graphics_thuck);
         this._graphics_thuck = null;
         ObjectUtils.disposeObject(this._progressLabel);
         this._progressLabel = null;
         ObjectUtils.disposeObject(this._progressBarMask);
         this._progressBarMask = null;
         ObjectUtils.disposeObject(this._star);
         this._star = null;
         if(this.hasEventListener("enterFrame"))
         {
            this.removeEventListener("enterFrame",this.__startFrame);
         }
         super.dispose();
      }
   }
}

