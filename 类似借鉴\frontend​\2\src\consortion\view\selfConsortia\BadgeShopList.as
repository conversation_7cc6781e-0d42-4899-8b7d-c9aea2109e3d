package consortion.view.selfConsortia
{
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.controls.ScrollPanel;
   import com.pickgliss.ui.controls.container.VBox;
   import com.pickgliss.ui.core.Disposeable;
   import consortion.data.BadgeInfo;
   import flash.display.Sprite;
   
   public class BadgeShopList extends Sprite implements Disposeable
   {
      
      private var _items:Array;
      
      private var _list:VBox;
      
      private var _panel:ScrollPanel;
      
      public function BadgeShopList()
      {
         super();
         this.init();
      }
      
      private function init() : void
      {
         this._items = [];
         this._list = ComponentFactory.Instance.creat("consortion.badgeShop.list");
         this._panel = ComponentFactory.Instance.creat("consortion.badgeShop.panel");
         this._panel.setView(this._list);
         addChild(this._panel);
      }
      
      public function setList(_arg_1:Array) : void
      {
         var _local_3:BadgeShopItem = null;
         var _local_4:BadgeInfo = null;
         var _local_2:* = null;
         for each(_local_3 in this._items)
         {
            _local_3.dispose();
         }
         this._items = [];
         for each(_local_4 in _arg_1)
         {
            _local_2 = new BadgeShopItem(_local_4);
            this._list.addChild(_local_2);
            this._items.push(_local_2);
         }
      }
      
      public function dispose() : void
      {
         var _local_1:BadgeShopItem = null;
         for each(_local_1 in this._items)
         {
            _local_1.dispose();
         }
         this._items = null;
         this._list.dispose();
         this._list = null;
         this._panel.dispose();
         this._panel = null;
         if(Boolean(parent))
         {
            parent.removeChild(this);
         }
      }
   }
}

