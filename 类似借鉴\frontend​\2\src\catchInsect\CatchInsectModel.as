package catchInsect
{
   import ddt.manager.ServerConfigManager;
   import road7th.utils.DateUtils;
   
   public class CatchInsectModel
   {
      
      public var isOpen:Boolean;
      
      public var isEnter:Boolean;
      
      public var beginDate:String;
      
      public var endDate:String;
      
      public var score:int;
      
      public var avaibleScore:int;
      
      public var prizeStatus:int;
      
      public function CatchInsectModel()
      {
         super();
      }
      
      public function get activityTime() : String
      {
         var _local_1:String = "";
         this.beginDate = ServerConfigManager.instance.catchInsectBeginTime.split(" ")[0];
         this.endDate = ServerConfigManager.instance.catchInsectEndTime.split(" ")[0];
         if(<PERSON>olean(this.beginDate) && Boolean(this.endDate))
         {
            _local_1 = this.beginDate.replace(/-/g,".") + "-" + this.endDate.replace(/-/g,".");
         }
         return _local_1;
      }
      
      public function get endTime() : Date
      {
         return DateUtils.getDateByStr(ServerConfigManager.instance.catchInsectEndTime);
      }
   }
}

