package bones.loader
{
   import bones.BoneMovieFactory;
   import bones.model.BoneVo;
   import com.crypto.NewCrypto;
   import com.pickgliss.loader.BaseLoader;
   import com.pickgliss.loader.LoadResourceManager;
   import com.pickgliss.loader.LoaderEvent;
   import com.pickgliss.ui.core.Disposeable;
   import deng.fzip.FZip;
   import flash.display.Bitmap;
   import flash.display.Loader;
   import flash.events.Event;
   import flash.events.EventDispatcher;
   
   public class BonesResourceLoader extends EventDispatcher implements Disposeable
   {
      
      protected var _vo:BoneVo;
      
      private var _skeletonList:Array;
      
      private var _loader:Loader;
      
      private var _loadRes:BaseLoader;
      
      private var _image:Bitmap = null;
      
      private var _atlas:XML;
      
      private var _fzip:FZip;
      
      public var module:String = "default";
      
      public function BonesResourceLoader(_arg_1:BoneVo)
      {
         super();
         this._vo = _arg_1;
         this._skeletonList = [];
      }
      
      public function load() : void
      {
         if(this._loadRes == null)
         {
            this._loadRes = LoadResourceManager.Instance.createLoader(this.getLoaderPath(this._vo),10);
            this._loadRes.addEventListener("complete",this.__onLoadComplete);
            LoadResourceManager.Instance.startLoad(this._loadRes);
         }
      }
      
      private function __onLoadComplete(_arg_1:LoaderEvent) : void
      {
         var _local_2:* = null;
         var _local_3:* = null;
         _arg_1.loader.removeEventListener("complete",this.__onLoadComplete);
         if(!BonesLoaderManager.instance.getBoneLoaderComplete(this._vo))
         {
            BonesLoaderManager.instance.saveBoneLoaderData(this.vo);
            _local_2 = NewCrypto.decry(this._loadRes.content);
            _local_3 = new FZip();
            _local_3.addEventListener("complete",this.__onZipParaComplete);
            _local_3.loadBytes(_local_2);
         }
         this._loadRes = null;
      }
      
      private function __onZipParaComplete(_arg_1:Event) : void
      {
         this._fzip = _arg_1.currentTarget as FZip;
         this._fzip.removeEventListener("complete",this.__onZipParaComplete);
         this._loader = new Loader();
         this._loader.contentLoaderInfo.addEventListener("complete",this.__onLoadBitmapComplete);
         this._loader.loadBytes(this._fzip.getFileByName(this._vo.atlasName + ".png").content);
      }
      
      private function __onLoadBitmapComplete(_arg_1:Event) : void
      {
         var _local_5:int = 0;
         var _local_3:* = null;
         var _local_2:* = null;
         this._loader.contentLoaderInfo.removeEventListener("complete",this.__onLoadBitmapComplete);
         this._image = this._loader.content as Bitmap;
         this._atlas = new XML(this._fzip.getFileByName(this._vo.atlasName + ".xml").content.toString());
         var _local_4:Array = BoneMovieFactory.instance.model.getBoneVoListByAtlasName(this._vo.atlasName);
         _local_5 = 0;
         while(_local_5 < _local_4.length)
         {
            _local_3 = (_local_4[_local_5] as BoneVo).styleName;
            _local_2 = this._fzip.getFileByName(_local_3 + ".json").content.toString();
            this._skeletonList.push({
               "name":_local_3,
               "data":_local_2
            });
            _local_5++;
         }
         this._fzip.close();
         this._loader.unload();
         this._loader = null;
         this.loaderComplete();
      }
      
      public function loaderComplete() : void
      {
         dispatchEvent(new Event("complete"));
      }
      
      private function getLoaderPath(_arg_1:BoneVo) : String
      {
         var _local_2:* = null;
         if(this._vo.loadType == 1)
         {
            _local_2 = BonesLoaderManager.RESOURCE_SITE;
         }
         else
         {
            _local_2 = BonesLoaderManager.SITE_MAIN;
         }
         return _local_2 + _arg_1.path + _arg_1.atlasName + _arg_1.ext;
      }
      
      public function get image() : Bitmap
      {
         return this._image;
      }
      
      public function get atlas() : XML
      {
         return this._atlas;
      }
      
      public function get skeletonList() : Array
      {
         return this._skeletonList;
      }
      
      public function get vo() : BoneVo
      {
         return this._vo;
      }
      
      public function dispose() : void
      {
         if(Boolean(this._loadRes))
         {
            this._loadRes.removeEventListener("complete",this.__onLoadComplete);
         }
         if(Boolean(this._loader))
         {
            this._loader.contentLoaderInfo.removeEventListener("complete",this.__onLoadBitmapComplete);
         }
         while(Boolean(this._skeletonList.length))
         {
            this._skeletonList.pop();
         }
         if(Boolean(this._image) && Boolean(this._image.bitmapData))
         {
            this._image.bitmapData.dispose();
         }
         this._vo = null;
         this._atlas = null;
         this._image = null;
         this._skeletonList = null;
         this._fzip = null;
         this._loadRes = null;
         this._loader = null;
      }
   }
}

