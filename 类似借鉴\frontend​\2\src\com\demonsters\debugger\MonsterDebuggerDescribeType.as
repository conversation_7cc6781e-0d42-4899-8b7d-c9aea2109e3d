package com.demonsters.debugger
{
   import flash.utils.describeType;
   import flash.utils.getQualifiedClassName;
   
   internal class MonsterDebuggerDescribeType
   {
      
      private static var cache:Object = {};
      
      public function MonsterDebuggerDescribeType()
      {
         super();
      }
      
      internal static function get(_arg_1:*) : XML
      {
         var _local_3:String = getQualifiedClassName(_arg_1);
         if(_local_3 in cache)
         {
            return cache[_local_3];
         }
         var _local_2:XML = describeType(_arg_1);
         cache[_local_3] = _local_2;
         return _local_2;
      }
   }
}

