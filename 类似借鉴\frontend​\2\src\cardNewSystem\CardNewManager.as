package cardNewSystem
{
   import cardNewSystem.analyzer.CarSuitAnalyzer;
   import cardNewSystem.analyzer.CardBookAnalyzer;
   import cardNewSystem.analyzer.CardMainInfoAnalyzer;
   import cardNewSystem.data.CardEPackageType;
   import cardNewSystem.data.CardEvent;
   import cardNewSystem.data.CardMainInfo;
   import cardNewSystem.data.CardModel;
   import ddt.data.player.SelfInfo;
   import ddt.events.PkgEvent;
   import ddt.manager.PlayerManager;
   import ddt.manager.SocketManager;
   import flash.events.EventDispatcher;
   import flash.utils.Dictionary;
   import road7th.comm.PackageIn;
   
   public class CardNewManager extends EventDispatcher
   {
      
      private static var _ins:CardNewManager;
      
      public static const CARD_MAXLEVEL:int = 90;
      
      private var _model:CardModel;
      
      public function CardNewManager()
      {
         super();
         this._model = new CardModel();
      }
      
      public static function get ins() : CardNewManager
      {
         if(!_ins)
         {
            _ins = new CardNewManager();
         }
         return _ins;
      }
      
      public function setup() : void
      {
         SocketManager.Instance.addEventListener(PkgEvent.format(648,CardEPackageType.CARD_INFO),this.__cardInfoHanlder);
         SocketManager.Instance.addEventListener(PkgEvent.format(648,CardEPackageType.CARD_MODPROPERTY),this.__cardMoproperty);
         SocketManager.Instance.addEventListener(PkgEvent.format(648,CardEPackageType.CARD_SOUL),this.__cardSoulChange);
      }
      
      private function __cardInfoHanlder(_arg_1:PkgEvent) : void
      {
         var _local_11:int = 0;
         var _local_9:int = 0;
         var _local_10:int = 0;
         var _local_7:int = 0;
         var _local_8:int = 0;
         var _local_3:* = null;
         var _local_5:PackageIn = _arg_1.pkg;
         var _local_12:CardMainInfo = this._model.cardMainInfo;
         _local_12.level = _local_5.readInt();
         _local_12.templateInfo = this._model.getCardMainTempInfoByLevel(_local_12.level);
         _local_12.point = _local_5.readInt();
         _local_12.armor = _local_5.readInt();
         _local_12.crits = _local_5.readInt();
         _local_12.trick = _local_5.readInt();
         _local_12.viole = _local_5.readInt();
         _local_12.xiAttack = _local_5.readInt();
         _local_12.xiDefence = _local_5.readInt();
         _local_12.xiAgility = _local_5.readInt();
         _local_12.xiLucky = _local_5.readInt();
         this._model.suitDic = new Dictionary();
         var _local_2:Dictionary = this._model.hasCards;
         var _local_6:int = _local_5.readInt();
         _local_11 = 0;
         while(_local_11 < _local_6)
         {
            _local_9 = _local_5.readInt();
            _local_10 = _local_5.readInt();
            _local_3 = this._model.getCardTemplateInfoById(_local_9,_local_10);
            this._model.hasCards[_local_9] = _local_3;
            _local_11++;
         }
         var _local_4:int = _local_5.readInt();
         _local_7 = 0;
         while(_local_7 < _local_4)
         {
            _local_8 = _local_5.readInt();
            this._model.suitDic[_local_8] = _local_5.readBoolean();
            _local_7++;
         }
         this._model.suitLevel = _local_5.readInt();
         this._model.suitPro = _local_5.readUTF();
         this._model.initPriSuitInfos();
         this.dispatchEvent(new CardEvent("update_cardInfo"));
      }
      
      private function __cardMoproperty(_arg_1:PkgEvent) : void
      {
         var _local_4:int = 0;
         var _local_3:PackageIn = _arg_1.pkg;
         var _local_2:Array = [];
         _local_4 = 0;
         while(_local_4 < 4)
         {
            _local_2.push(_local_3.readInt());
            _local_4++;
         }
         this.dispatchEvent(new CardEvent("card_xidian",{"proArr":_local_2}));
      }
      
      private function __cardSoulChange(_arg_1:PkgEvent) : void
      {
         var _local_3:SelfInfo = PlayerManager.Instance.Self;
         var _local_2:PackageIn = _arg_1.pkg;
         _local_3.CardSoul = _local_2.readInt();
      }
      
      public function get model() : CardModel
      {
         return this._model;
      }
      
      public function set model(_arg_1:CardModel) : void
      {
         this._model = _arg_1;
      }
      
      public function analyzerCardMainTemplate(_arg_1:CardMainInfoAnalyzer) : void
      {
         this._model.cardMainTempInfos = _arg_1.infoArr;
      }
      
      public function analyzerCardsuitTemplate(_arg_1:CarSuitAnalyzer) : void
      {
         this._model.cardSuitTempInfos = _arg_1.infoArr;
      }
      
      public function analyzerCardBookTemplate(_arg_1:CardBookAnalyzer) : void
      {
         this._model.cardBookTempInfos = _arg_1.infoArr;
      }
   }
}

