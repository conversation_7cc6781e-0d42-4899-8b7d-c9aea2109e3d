package academy
{
   import com.pickgliss.events.FrameEvent;
   import com.pickgliss.loader.BaseLoader;
   import com.pickgliss.loader.LoadResourceManager;
   import com.pickgliss.ui.AlertManager;
   import com.pickgliss.ui.controls.alert.BaseAlerFrame;
   import ddt.data.analyze.AcademyMemberListAnalyze;
   import ddt.data.analyze.MyAcademyPlayersAnalyze;
   import ddt.data.player.AcademyPlayerInfo;
   import ddt.data.player.BasePlayer;
   import ddt.data.player.PlayerInfo;
   import ddt.events.PkgEvent;
   import ddt.manager.AcademyFrameManager;
   import ddt.manager.LanguageMgr;
   import ddt.manager.MessageTipManager;
   import ddt.manager.PathManager;
   import ddt.manager.PlayerManager;
   import ddt.manager.ServerConfigManager;
   import ddt.manager.SharedManager;
   import ddt.manager.SocketManager;
   import ddt.manager.SoundManager;
   import ddt.manager.StateManager;
   import ddt.manager.TimeManager;
   import ddt.utils.RequestVairableCreater;
   import ddt.view.academyCommon.data.SimpleMessger;
   import flash.events.Event;
   import flash.events.EventDispatcher;
   import flash.net.URLVariables;
   import quest.TaskManager;
   import road7th.data.DictionaryData;
   import road7th.utils.DateUtils;
   
   public class AcademyManager extends EventDispatcher
   {
      
      private static var _instance:AcademyManager;
      
      public static const SELF_DESCRIBE:String = "selfDescribe";
      
      public static const LEVEL_GAP:int = 5;
      
      public static const TARGET_PLAYER_MIN_LEVEL:int = 8;
      
      public static const FARM_PLAYER_MIN_LEVEL:int = 25;
      
      public static const ACADEMY_LEVEL_MIN:int = 50;
      
      public static const APPRENTICE_LEVEL_MAX:int = 41;
      
      public static const ACADEMY_LEVEL_MAX:int = 8;
      
      public static const RECOMMEND_MAX_NUM:int = 3;
      
      public static const GRADUATE_NUM:Array = [1,5,10,50,99];
      
      public static const MASTER:Boolean = false;
      
      public static const APPSHIP:Boolean = true;
      
      public static const ACADEMY:Boolean = true;
      
      public static const RECOMMEND:Boolean = false;
      
      public static const NONE_STATE:int = 0;
      
      public static const APPRENTICE_STATE:int = 1;
      
      public static const MASTER_STATE:int = 2;
      
      public static const MASTER_FULL_STATE:int = 3;
      
      public var isShowRecommend:Boolean = true;
      
      public var freezesDate:Date;
      
      public var selfIsRegister:Boolean;
      
      public var isSelfPublishEquip:Boolean;
      
      private var _showMessage:Boolean = true;
      
      private var _recommendPlayers:Vector.<AcademyPlayerInfo>;
      
      private var _myAcademyPlayers:DictionaryData;
      
      private var _messgers:Vector.<SimpleMessger>;
      
      private var _selfDescribe:String;
      
      public function AcademyManager()
      {
         super();
      }
      
      public static function get Instance() : AcademyManager
      {
         if(_instance == null)
         {
            _instance = new AcademyManager();
         }
         return _instance;
      }
      
      public function setup() : void
      {
         this.initEvent();
         this._messgers = new Vector.<SimpleMessger>();
      }
      
      private function initEvent() : void
      {
         SocketManager.Instance.addEventListener(PkgEvent.format(141),this.__apprenticeSystemAnswer);
      }
      
      private function __apprenticeSystemAnswer(_arg_1:PkgEvent) : void
      {
         var _local_7:int = 0;
         var _local_15:int = 0;
         var _local_10:int = 0;
         var _local_16:int = 0;
         var _local_12:int = 0;
         var _local_2:int = 0;
         var _local_4:Boolean = false;
         var _local_14:* = null;
         var _local_8:* = null;
         var _local_9:* = null;
         var _local_3:* = null;
         var _local_11:* = null;
         var _local_13:* = null;
         var _local_5:* = null;
         var _local_6:int = _arg_1.pkg.readByte();
         switch(_local_6)
         {
            case 4:
               _local_7 = _arg_1.pkg.readInt();
               _local_14 = _arg_1.pkg.readUTF();
               _local_8 = _arg_1.pkg.readUTF();
               if(SharedManager.Instance.unAcceptAnswer.indexOf(_local_7) < 0)
               {
                  AcademyFrameManager.Instance.showAcademyAnswerMasterFrame(_local_7,_local_14,_local_8);
               }
               return;
            case 5:
               _local_15 = _arg_1.pkg.readInt();
               _local_9 = _arg_1.pkg.readUTF();
               _local_3 = _arg_1.pkg.readUTF();
               if(PlayerManager.Instance.Self.apprenticeshipState == 1)
               {
                  return;
               }
               if(SharedManager.Instance.unAcceptAnswer.indexOf(_local_15) < 0)
               {
                  AcademyFrameManager.Instance.showAcademyAnswerApprenticeFrame(_local_15,_local_9,_local_3);
               }
               return;
               break;
            case 10:
               PlayerManager.Instance.Self.apprenticeshipState = _arg_1.pkg.readInt();
               PlayerManager.Instance.Self.masterID = _arg_1.pkg.readInt();
               PlayerManager.Instance.Self.setMasterOrApprentices(_arg_1.pkg.readUTF());
               _local_10 = _arg_1.pkg.readInt();
               PlayerManager.Instance.Self.graduatesCount = _arg_1.pkg.readInt();
               PlayerManager.Instance.Self.honourOfMaster = _arg_1.pkg.readUTF();
               PlayerManager.Instance.Self.freezesDate = _arg_1.pkg.readDate();
               if(Boolean(this._myAcademyPlayers) && _local_10 != -1)
               {
                  this._myAcademyPlayers.remove(_local_10);
               }
               if(Boolean(this._myAcademyPlayers) && PlayerManager.Instance.Self.apprenticeshipState == 0)
               {
                  this._myAcademyPlayers.clear();
               }
               if(PlayerManager.Instance.Self.apprenticeshipState != 0)
               {
                  TaskManager.instance.requestCanAcceptTask();
               }
               return;
            case 11:
               _local_16 = _arg_1.pkg.readInt();
               _local_12 = _arg_1.pkg.readInt();
               _local_11 = _arg_1.pkg.readUTF();
               if(_local_16 == 0)
               {
                  AcademyFrameManager.Instance.showApprenticeGraduate();
               }
               else if(_local_16 == 1)
               {
                  AcademyFrameManager.Instance.showMasterGraduate(_local_11);
               }
               return;
            case 6:
            case 7:
               _local_2 = _arg_1.pkg.readInt();
               _local_13 = _arg_1.pkg.readUTF();
               return;
            case 17:
               _local_5 = _arg_1.pkg.readUTF();
               _local_4 = _arg_1.pkg.readBoolean();
               this.academyAlert(_local_5,_local_4);
         }
      }
      
      private function academyAlert(_arg_1:String, _arg_2:Boolean) : void
      {
         var _local_3:* = null;
         if(_arg_2)
         {
            AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("AlertDialog.Info"),_arg_1,LanguageMgr.GetTranslation("ok"),"",false,false,false,2).addEventListener("response",this.__onCancel);
         }
         else if(!this.isFighting())
         {
            _local_3 = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("AlertDialog.Info"),_arg_1,LanguageMgr.GetTranslation("ddt.manager.AcademyManager.alertSubmit"),"",false,false,false,2);
            _local_3.addEventListener("response",this.__frameEvent);
         }
         else if(StateManager.currentStateType == "hotSpringRoom" || StateManager.currentStateType == "churchRoom")
         {
            AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("AlertDialog.Info"),_arg_1,LanguageMgr.GetTranslation("ok"),"",false,false,false,2).addEventListener("response",this.__onCancel);
         }
      }
      
      private function __onCancel(_arg_1:FrameEvent) : void
      {
         SoundManager.instance.play("008");
         (_arg_1.currentTarget as BaseAlerFrame).removeEventListener("response",this.__frameEvent);
         (_arg_1.currentTarget as BaseAlerFrame).dispose();
      }
      
      private function __frameEvent(_arg_1:FrameEvent) : void
      {
         SoundManager.instance.play("008");
         (_arg_1.currentTarget as BaseAlerFrame).removeEventListener("response",this.__frameEvent);
         (_arg_1.currentTarget as BaseAlerFrame).dispose();
         switch(_arg_1.responseCode)
         {
            case 2:
            case 3:
               StateManager.setState("academyRegistration");
         }
      }
      
      public function loadAcademyMemberList(_arg_1:Function, _arg_2:Boolean = true, _arg_3:Boolean = false, _arg_4:int = 1, _arg_5:String = "", _arg_6:int = 0, _arg_7:Boolean = true, _arg_8:Boolean = false) : void
      {
         var _local_10:URLVariables = RequestVairableCreater.creatWidthKey(true);
         _local_10["requestType"] = _arg_2;
         _local_10["appshipStateType"] = _arg_3;
         _local_10["page"] = _arg_4;
         _local_10["name"] = _arg_5;
         _local_10["Grade"] = _arg_6;
         _local_10["sex"] = _arg_7;
         _local_10["isReturnSelf"] = _arg_8;
         var _local_9:BaseLoader = LoadResourceManager.Instance.createLoader(PathManager.solveRequestPath("ApprenticeshipClubList.ashx"),6,_local_10,"GET",null,true,true);
         _local_9.loadErrorMessage = LanguageMgr.GetTranslation("civil.frame.CivilRegisterFrame.infoError");
         _local_9.analyzer = new AcademyMemberListAnalyze(_arg_1);
         LoadResourceManager.Instance.startLoad(_local_9);
      }
      
      public function recommend() : void
      {
         if(!this.isRecommend())
         {
            return;
         }
         if(!DateUtils.isToday(new Date(PlayerManager.Instance.Self.LastDate)) || !SharedManager.Instance.isRecommend)
         {
            this._showMessage = false;
            if(PlayerManager.Instance.Self.Grade < 41 && PlayerManager.Instance.Self.apprenticeshipState != 1)
            {
               this.loadAcademyMemberList(this.__recommendPlayersComplete,false,false,1,"",PlayerManager.Instance.Self.Grade,PlayerManager.Instance.Self.Sex);
            }
            else if(PlayerManager.Instance.Self.Grade >= 50 && (PlayerManager.Instance.Self.apprenticeshipState == 0 || PlayerManager.Instance.Self.apprenticeshipState == 2))
            {
               this.loadAcademyMemberList(this.__recommendPlayersComplete,false,true,1,"",PlayerManager.Instance.Self.Grade,PlayerManager.Instance.Self.Sex);
            }
         }
      }
      
      public function recommends() : void
      {
         if(PlayerManager.Instance.Self.Grade < 41 && PlayerManager.Instance.Self.apprenticeshipState != 1)
         {
            this._showMessage = true;
            this.loadAcademyMemberList(this.__recommendPlayersComplete,false,false,1,"",PlayerManager.Instance.Self.Grade,PlayerManager.Instance.Self.Sex);
         }
         else if(PlayerManager.Instance.Self.Grade >= 50 && (PlayerManager.Instance.Self.apprenticeshipState == 0 || PlayerManager.Instance.Self.apprenticeshipState == 2))
         {
            this._showMessage = true;
            this.loadAcademyMemberList(this.__recommendPlayersComplete,false,true,1,"",PlayerManager.Instance.Self.Grade,PlayerManager.Instance.Self.Sex);
         }
      }
      
      private function __recommendPlayersComplete(_arg_1:AcademyMemberListAnalyze) : void
      {
         this._recommendPlayers = _arg_1.academyMemberList;
         if(this._recommendPlayers.length >= 3)
         {
            if(PlayerManager.Instance.Self.Grade < 50)
            {
               AcademyFrameManager.Instance.showAcademyApprenticeMainFrame();
            }
            else
            {
               AcademyFrameManager.Instance.showAcademyMasterMainFrame();
            }
         }
         else if(this._showMessage)
         {
            if(PlayerManager.Instance.Self.Grade >= 50)
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.data.analyze.AcademyMemberListAnalyze.info"));
            }
            else
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.data.analyze.AcademyMemberListAnalyze.infoI"));
            }
         }
         this.isShowRecommend = false;
      }
      
      public function get recommendPlayers() : Vector.<AcademyPlayerInfo>
      {
         return this._recommendPlayers;
      }
      
      public function get myAcademyPlayers() : DictionaryData
      {
         return this._myAcademyPlayers;
      }
      
      public function myAcademy() : void
      {
         var _local_2:URLVariables = RequestVairableCreater.creatWidthKey(true);
         _local_2["RelationshipID"] = PlayerManager.Instance.Self.masterID;
         var _local_1:BaseLoader = LoadResourceManager.Instance.createLoader(PathManager.solveRequestPath("UserApprenticeshipInfoList.ashx"),6,_local_2);
         _local_1.loadErrorMessage = LanguageMgr.GetTranslation("ddt.data.analyze.MyAcademyPlayersAnalyze");
         _local_1.analyzer = new MyAcademyPlayersAnalyze(this.myAcademyPlayersComplete);
         LoadResourceManager.Instance.startLoad(_local_1);
      }
      
      public function myAcademyPlayersComplete(_arg_1:MyAcademyPlayersAnalyze) : void
      {
         this._myAcademyPlayers = _arg_1.myAcademyPlayers;
      }
      
      public function getAppAddAttrPer(_arg_1:int) : String
      {
         var _local_2:Array = ServerConfigManager.instance.getAppAddAttrPer;
         return this.convertStrToPer(this.getValueByLVFromArr(_arg_1,_local_2));
      }
      
      public function getAppChargeRetPer(_arg_1:int) : String
      {
         var _local_2:Array = ServerConfigManager.instance.getAppChargeRetPer;
         return this.convertStrToPer(this.getValueByLVFromArr(_arg_1,_local_2));
      }
      
      public function getAppVitReducePer(_arg_1:int) : String
      {
         var _local_2:Array = ServerConfigManager.instance.getAppVitReducePer;
         return this.convertStrToPer(this.getValueByLVFromArr(_arg_1,_local_2));
      }
      
      private function getValueByLVFromArr(_arg_1:int, _arg_2:Array) : String
      {
         if(_arg_2.length <= 0)
         {
            return "";
         }
         _arg_1 = _arg_1 <= 0 ? 1 : (_arg_1 > _arg_2.length ? _arg_2.length - 1 : _arg_1);
         return _arg_2[_arg_1 - 1];
      }
      
      private function convertStrToPer(_arg_1:String) : String
      {
         var _local_2:Number = Number(_arg_1);
         return _local_2 * 100 + "%";
      }
      
      public function compareState(_arg_1:BasePlayer, _arg_2:PlayerInfo) : Boolean
      {
         if(_arg_2.Grade < 8)
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.manager.AcademyManager.warning6"));
            return false;
         }
         if(_arg_1.Grade < 8)
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.manager.AcademyManager.warningII"));
            return false;
         }
         if(_arg_2.apprenticeshipState == 1)
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.manager.AcademyManager.WarningApprenticeState"));
            return false;
         }
         if(_arg_2.apprenticeshipState == 3)
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.manager.AcademyManager.WarningMasterFullState"));
            return false;
         }
         if(_arg_2.Grade >= 50)
         {
            if(_arg_1.Grade >= 41)
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.manager.AcademyManager.warningIII"));
               return false;
            }
            if(_arg_2.Grade - _arg_1.Grade >= 5)
            {
               return true;
            }
            if(_arg_2.Grade - _arg_1.Grade < 5)
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.manager.AcademyManager.warning"));
               return false;
            }
         }
         else
         {
            if(_arg_1.Grade < 50)
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.manager.AcademyManager.warningIIII"));
               return false;
            }
            if(_arg_2.Grade >= 41)
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.manager.AcademyManager.warningIII"));
               return false;
            }
            if(_arg_1.Grade - _arg_2.Grade >= 5)
            {
               return true;
            }
            if(_arg_1.Grade - _arg_2.Grade < 5)
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.manager.AcademyManager.warningI"));
               return false;
            }
         }
         return false;
      }
      
      public function gotoAcademyState() : void
      {
         var _local_1:* = null;
         if(StateManager.currentStateType == "matchRoom" || StateManager.currentStateType == "missionResult" || StateManager.currentStateType == "dungeonRoom" || StateManager.currentStateType == "freshmanRoom1" || StateManager.currentStateType == "freshmanRoom2")
         {
            _local_1 = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("AlertDialog.Info"),LanguageMgr.GetTranslation("ddt.manager.AcademyManager.warning10"),LanguageMgr.GetTranslation("ok"),"",false,false,false,2);
            _local_1.addEventListener("response",this.__onResponse);
         }
         else
         {
            StateManager.setState("academyRegistration");
         }
      }
      
      private function __onResponse(_arg_1:FrameEvent) : void
      {
         SoundManager.instance.play("008");
         (_arg_1.currentTarget as BaseAlerFrame).removeEventListener("response",this.__onResponse);
         (_arg_1.currentTarget as BaseAlerFrame).dispose();
         switch(_arg_1.responseCode)
         {
            case 2:
            case 3:
               StateManager.setState("academyRegistration");
         }
      }
      
      public function getMasterHonorLevel(_arg_1:int) : int
      {
         var _local_3:int = 0;
         var _local_2:int = 0;
         _local_3 = 0;
         while(_local_3 < GRADUATE_NUM.length)
         {
            if(_arg_1 >= GRADUATE_NUM[_local_3])
            {
               _local_2++;
            }
            _local_3++;
         }
         return _local_2;
      }
      
      public function isFreezes(_arg_1:Boolean) : Boolean
      {
         var _local_2:Date = TimeManager.Instance.serverDate;
         if(PlayerManager.Instance.Self.freezesDate > _local_2)
         {
            if(_arg_1)
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.manager.academyManager.Freezes"));
            }
            else
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.manager.academyManager.FreezesII"));
            }
            return false;
         }
         return true;
      }
      
      public function set messgers(_arg_1:Vector.<SimpleMessger>) : void
      {
         this._messgers = _arg_1;
      }
      
      public function get messgers() : Vector.<SimpleMessger>
      {
         return this._messgers;
      }
      
      public function showAlert() : void
      {
         while(this.messgers.length != 0)
         {
            if(Boolean(this.messgers[0]) && this.messgers[0].type == 1)
            {
               AcademyFrameManager.Instance.showAcademyAnswerApprenticeFrame(this.messgers[0].id,this.messgers[0].name,this.messgers[0].messger);
            }
            else
            {
               AcademyFrameManager.Instance.showAcademyAnswerMasterFrame(this.messgers[0].id,this.messgers[0].name,this.messgers[0].messger);
            }
            this.messgers.shift();
         }
      }
      
      public function isFighting() : Boolean
      {
         if(StateManager.currentStateType != "fightLabGameView" && StateManager.currentStateType != "fighting" && StateManager.currentStateType != "fighting3d" && StateManager.currentStateType != "hotSpringRoom" && StateManager.currentStateType != "churchRoom" && StateManager.currentStateType != "littleGame")
         {
            return false;
         }
         return true;
      }
      
      public function isRecommend() : Boolean
      {
         return AcademyManager.Instance.isShowRecommend && !SharedManager.Instance.isRecommend && PlayerManager.Instance.Self.Grade >= 8 && (PlayerManager.Instance.Self.apprenticeshipState == 0 || PlayerManager.Instance.Self.apprenticeshipState == 2);
      }
      
      public function isOpenSpace(_arg_1:BasePlayer) : Boolean
      {
         return _arg_1.Grade < 41 && _arg_1.Grade > 8;
      }
      
      public function get selfDescribe() : String
      {
         return this._selfDescribe;
      }
      
      public function set selfDescribe(_arg_1:String) : void
      {
         this._selfDescribe = _arg_1;
         dispatchEvent(new Event("selfDescribe"));
      }
   }
}

