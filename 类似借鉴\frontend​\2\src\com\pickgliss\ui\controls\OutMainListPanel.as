package com.pickgliss.ui.controls
{
   import com.pickgliss.events.InteractiveEvent;
   import com.pickgliss.events.ListItemEvent;
   import com.pickgliss.geom.InnerRectangle;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.controls.cell.IListCell;
   import com.pickgliss.ui.controls.cell.IListCellFactory;
   import com.pickgliss.ui.controls.list.ListDataEvent;
   import com.pickgliss.ui.controls.list.ListDataListener;
   import com.pickgliss.ui.controls.list.VectorListModel;
   import com.pickgliss.ui.core.Component;
   import com.pickgliss.utils.ClassUtils;
   import com.pickgliss.utils.DisplayUtils;
   import com.pickgliss.utils.ObjectUtils;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class OutMainListPanel extends Component implements ListDataListener
   {
      
      private var P_vScrollbar:String = "vScrollBar";
      
      private var P_vScrollbarInnerRect:String = "vScrollBarInnerRect";
      
      private var P_cellFactory:String = "cellFactory";
      
      private var _cellsContainer:Sprite;
      
      private var _vScrollbarStyle:String;
      
      private var _vScrollbar:Scrollbar;
      
      private var _vScrollbarInnerRectString:String;
      
      private var _vScrollbarInnerRect:InnerRectangle;
      
      private var _factoryStyle:String;
      
      private var _factory:IListCellFactory;
      
      private var _model:VectorListModel;
      
      private var _cells:Vector.<IListCell>;
      
      private var _presentPos:int;
      
      private var _needNum:int;
      
      public function OutMainListPanel()
      {
         super();
      }
      
      override protected function init() : void
      {
         this.initEvent();
         this._presentPos = 0;
         this._cells = new Vector.<IListCell>();
         this._model = new VectorListModel();
         this._model.addListDataListener(this);
         super.init();
      }
      
      override protected function addChildren() : void
      {
         super.addChildren();
         if(Boolean(this._vScrollbar))
         {
            this._cellsContainer.addChild(this._vScrollbar);
         }
         this._cellsContainer = new Sprite();
         addChild(this._cellsContainer);
      }
      
      public function get vectorListModel() : VectorListModel
      {
         return this._model;
      }
      
      public function contentsChanged(_arg_1:ListDataEvent) : void
      {
         this.changeDate();
      }
      
      public function intervalAdded(_arg_1:ListDataEvent) : void
      {
         this.syncScrollBar();
      }
      
      public function intervalRemoved(_arg_1:ListDataEvent) : void
      {
         this.syncScrollBar();
      }
      
      private function syncScrollBar() : void
      {
         var _local_3:int = 0;
         var _local_4:int = 0;
         var _local_1:int = 0;
         var _local_2:int = int(this._factory.getCellHeight());
         this._needNum = Math.floor(_height / _local_2);
         if(this._vScrollbar != null)
         {
            _local_3 = this._needNum * this._factory.getCellHeight();
            _local_4 = this._presentPos * this._factory.getCellHeight();
            _local_1 = this._factory.getCellHeight() * this._model.elements.length;
            this._vScrollbar.unitIncrement = this._factory.getCellHeight();
            this._vScrollbar.blockIncrement = this._factory.getCellHeight();
            this._vScrollbar.getModel().setRangeProperties(_local_4,_local_3,0,_local_1,false);
         }
         this.changeDate();
      }
      
      private function changeDate() : void
      {
         var _local_1:int = 0;
         _local_1 = 0;
         while(_local_1 < this._needNum)
         {
            this._cells[_local_1].setCellValue(this._model.elements[this._presentPos + _local_1]);
            _local_1++;
         }
      }
      
      private function createCells() : void
      {
         var _local_1:int = 0;
         var _local_7:int = 0;
         var _local_3:int = 0;
         var _local_5:* = undefined;
         var _local_6:int = 0;
         var _local_4:* = null;
         var _local_2:int = int(this._factory.getCellHeight());
         this._needNum = Math.floor(_height / _local_2);
         if(this._cells.length == this._needNum)
         {
            return;
         }
         if(this._cells.length < this._needNum)
         {
            _local_1 = this._needNum - this._cells.length;
            _local_7 = 0;
            while(_local_7 < _local_1)
            {
               _local_4 = this.createNewCell();
               _local_4.y = this._factory.getCellHeight() * _local_7;
               this.addCellToContainer(_local_4);
               _local_7++;
            }
         }
         else
         {
            _local_3 = this._needNum;
            _local_5 = this._cells.splice(_local_3,this._cells.length - _local_3);
            _local_6 = 0;
            while(_local_7 < _local_5.length)
            {
               this.removeCellFromContainer(_local_5[_local_6]);
               _local_6++;
            }
         }
      }
      
      protected function createNewCell() : IListCell
      {
         if(this._factory == null)
         {
            return null;
         }
         return this._factory.createNewCell();
      }
      
      protected function addCellToContainer(_arg_1:IListCell) : void
      {
         _arg_1.addEventListener("click",this.__onItemInteractive);
         _arg_1.addEventListener("mouseDown",this.__onItemInteractive);
         _arg_1.addEventListener("mouseUp",this.__onItemInteractive);
         _arg_1.addEventListener("rollOver",this.__onItemInteractive);
         _arg_1.addEventListener("rollOut",this.__onItemInteractive);
         _arg_1.addEventListener("doubleClick",this.__onItemInteractive);
         this._cells.push(this._cellsContainer.addChild(_arg_1.asDisplayObject()));
      }
      
      protected function __onItemInteractive(_arg_1:MouseEvent) : void
      {
         var _local_4:* = null;
         var _local_3:IListCell = _arg_1.currentTarget as IListCell;
         var _local_2:int = this._model.indexOf(_local_3.getCellValue());
         switch(_arg_1.type)
         {
            case "click":
               _local_4 = "listItemClick";
               break;
            case "doubleClick":
               _local_4 = "listItemDoubleclick";
               break;
            case "mouseDown":
               _local_4 = "listItemMouseDown";
               break;
            case "mouseUp":
               _local_4 = "listItemMouseUp";
               break;
            case "rollOver":
               _local_4 = "listItemRollOver";
               break;
            case "rollOut":
               _local_4 = "listItemRollOut";
         }
         dispatchEvent(new ListItemEvent(_local_3,_local_3.getCellValue(),_local_4,_local_2));
      }
      
      protected function removeAllCell() : void
      {
         var _local_1:int = 0;
         _local_1 = 0;
         while(_local_1 < this._cells.length)
         {
            this.removeCellFromContainer(this._cells[_local_1]);
            _local_1++;
         }
         this._cells = new Vector.<IListCell>();
      }
      
      protected function removeCellFromContainer(_arg_1:IListCell) : void
      {
         _arg_1.removeEventListener("click",this.__onItemInteractive);
         _arg_1.removeEventListener("mouseDown",this.__onItemInteractive);
         _arg_1.removeEventListener("mouseUp",this.__onItemInteractive);
         _arg_1.removeEventListener("rollOver",this.__onItemInteractive);
         _arg_1.removeEventListener("rollOut",this.__onItemInteractive);
         _arg_1.removeEventListener("doubleClick",this.__onItemInteractive);
         ObjectUtils.disposeObject(_arg_1);
      }
      
      protected function initEvent() : void
      {
         addEventListener("mouseWheel",this.onMouseWheel);
      }
      
      public function onMouseWheel(_arg_1:MouseEvent) : void
      {
         var _local_2:int = 0;
         var _local_3:int = 0;
         if(this._needNum > 0)
         {
            _local_2 = int(int(Math.floor(_arg_1.delta / this._needNum)));
            _local_3 = this._presentPos - _local_2;
            if(_local_3 > this._model.elements.length - this._needNum)
            {
               _local_3 = this._model.elements.length - this._needNum;
            }
            else if(_local_3 < 0)
            {
               _local_3 = 0;
            }
            if(this._presentPos == _local_3)
            {
               return;
            }
            this._presentPos = _local_3;
            this.syncScrollBar();
         }
      }
      
      public function set vScrollbarInnerRectString(_arg_1:String) : void
      {
         if(this._vScrollbarInnerRectString == _arg_1)
         {
            return;
         }
         this._vScrollbarInnerRectString = _arg_1;
         this._vScrollbarInnerRect = ClassUtils.CreatInstance("com.pickgliss.geom.InnerRectangle",ComponentFactory.parasArgs(this._vScrollbarInnerRectString));
         onPropertiesChanged(this.P_vScrollbarInnerRect);
      }
      
      public function set vScrollbarStyle(_arg_1:String) : void
      {
         if(this._vScrollbarStyle == _arg_1)
         {
            return;
         }
         this._vScrollbarStyle = _arg_1;
         this.vScrollbar = ComponentFactory.Instance.creat(this._vScrollbarStyle);
      }
      
      public function get vScrollbar() : Scrollbar
      {
         return this._vScrollbar;
      }
      
      public function set vScrollbar(_arg_1:Scrollbar) : void
      {
         if(this._vScrollbar == _arg_1)
         {
            return;
         }
         if(Boolean(this._vScrollbar))
         {
            this._vScrollbar.removeStateListener(this.__onScrollValueChange);
            ObjectUtils.disposeObject(this._vScrollbar);
         }
         this._vScrollbar = _arg_1;
         this._vScrollbar.addStateListener(this.__onScrollValueChange);
         onPropertiesChanged(this.P_vScrollbar);
      }
      
      protected function __onScrollValueChange(_arg_1:InteractiveEvent) : void
      {
         var _local_2:int = int(int(Math.floor(this._vScrollbar.getModel().getValue() / this._factory.getCellHeight())));
         if(_local_2 == this._presentPos)
         {
            return;
         }
         this._presentPos = _local_2;
         this.syncScrollBar();
      }
      
      public function set factoryStyle(_arg_1:String) : void
      {
         if(this._factoryStyle == _arg_1)
         {
            return;
         }
         this._factoryStyle = _arg_1;
         var _local_4:Array = _arg_1.split("|");
         var _local_3:String = _local_4[0];
         var _local_2:Array = ComponentFactory.parasArgs(_local_4[1]);
         this._factory = ClassUtils.CreatInstance(_local_3,_local_2);
         onPropertiesChanged(this.P_cellFactory);
      }
      
      override protected function onProppertiesUpdate() : void
      {
         super.onProppertiesUpdate();
         if(Boolean(_changedPropeties[this.P_cellFactory]))
         {
            this.createCells();
         }
         if(Boolean(_changedPropeties[this.P_vScrollbar]) || Boolean(_changedPropeties[this.P_vScrollbarInnerRect]))
         {
            this.layoutComponent();
         }
      }
      
      protected function layoutComponent() : void
      {
         if(Boolean(this._vScrollbar))
         {
            DisplayUtils.layoutDisplayWithInnerRect(this._vScrollbar,this._vScrollbarInnerRect,_width,_height);
         }
      }
      
      override public function dispose() : void
      {
         removeEventListener("mouseWheel",this.onMouseWheel);
         this.removeAllCell();
         if(Boolean(this._vScrollbar))
         {
            this._vScrollbar.removeStateListener(this.__onScrollValueChange);
            ObjectUtils.disposeObject(this._vScrollbar);
         }
         this._vScrollbar = null;
         if(Boolean(this._cellsContainer))
         {
            ObjectUtils.disposeObject(this._cellsContainer);
         }
         this._cellsContainer = null;
         if(Boolean(this._model))
         {
            this._model.removeListDataListener(this);
         }
         this._model = null;
         super.dispose();
         if(Boolean(this.parent))
         {
            this.parent.removeChild(this);
         }
      }
   }
}

