package bagAndInfo.info
{
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.controls.ComboBox;
   import com.pickgliss.ui.core.Disposeable;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.events.PlayerPropertyEvent;
   import ddt.manager.EffortManager;
   import ddt.manager.LanguageMgr;
   import ddt.manager.MessageTipManager;
   import ddt.manager.PlayerManager;
   import ddt.manager.SocketManager;
   import ddt.manager.SoundManager;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import hall.event.NewHallEvent;
   import newTitle.NewTitleManager;
   import newTitle.event.NewTitleEvent;
   
   public class PlayerInfoEffortHonorView extends Sprite implements Disposeable
   {
      
      private var _nameChoose:ComboBox;
      
      private var _honorArray:Array;
      
      public function PlayerInfoEffortHonorView()
      {
         super();
         this.init();
      }
      
      private function init() : void
      {
         this._nameChoose = ComponentFactory.Instance.creatComponentByStylename("personInfoViewNameChoose");
         addChild(this._nameChoose);
         this._nameChoose.button.addEventListener("click",this.__buttonClick);
         PlayerManager.Instance.Self.addEventListener("propertychange",this.__propertyChange);
         NewTitleManager.instance.addEventListener("selectTitle",this.__onSelectTitle);
         this.setlist(EffortManager.Instance.getHonorArray());
         this.update();
      }
      
      private function __propertyChange(_arg_1:PlayerPropertyEvent) : void
      {
         if(_arg_1.changedProperties["honor"] == true)
         {
            if(PlayerManager.Instance.Self.honor != "")
            {
               this._nameChoose.textField.text = PlayerManager.Instance.Self.honor;
               SocketManager.Instance.dispatchEvent(new NewHallEvent("newhallupdatetitle"));
               NewTitleManager.instance.dispatchEvent(new NewTitleEvent("setSelectTitle"));
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("newTitleView.useTitleSuccessTxt",this._nameChoose.textField.text));
            }
            else
            {
               this._nameChoose.textField.text = LanguageMgr.GetTranslation("bagAndInfo.info.PlayerInfoEffortHonorView.selecting");
            }
         }
      }
      
      private function __buttonClick(_arg_1:MouseEvent) : void
      {
         SoundManager.instance.play("008");
         NewTitleManager.instance.show();
      }
      
      private function update() : void
      {
         if(PlayerManager.Instance.Self.honor != "")
         {
            this._nameChoose.textField.text = PlayerManager.Instance.Self.honor;
         }
         else
         {
            this._nameChoose.textField.text = LanguageMgr.GetTranslation("bagAndInfo.info.PlayerInfoEffortHonorView.selecting");
         }
      }
      
      private function __onSelectTitle(_arg_1:NewTitleEvent) : void
      {
         var _local_2:String = _arg_1.data[0];
         if(Boolean(_local_2))
         {
            SocketManager.Instance.out.sendReworkRank(_local_2);
         }
         else
         {
            SocketManager.Instance.out.sendReworkRank("");
            this._nameChoose.textField.text = LanguageMgr.GetTranslation("bagAndInfo.info.PlayerInfoEffortHonorView.selecting");
         }
      }
      
      public function setlist(_arg_1:Array) : void
      {
         this._honorArray = [];
         this._honorArray = _arg_1;
         if(!this._honorArray)
         {
            return;
         }
      }
      
      public function dispose() : void
      {
         NewTitleManager.instance.removeEventListener("selectTitle",this.__onSelectTitle);
         this._nameChoose.button.removeEventListener("click",this.__buttonClick);
         PlayerManager.Instance.Self.removeEventListener("propertychange",this.__propertyChange);
         if(Boolean(this._nameChoose))
         {
            ObjectUtils.disposeObject(this._nameChoose);
         }
         this._nameChoose = null;
         if(Boolean(this.parent))
         {
            this.parent.removeChild(this);
         }
      }
   }
}

