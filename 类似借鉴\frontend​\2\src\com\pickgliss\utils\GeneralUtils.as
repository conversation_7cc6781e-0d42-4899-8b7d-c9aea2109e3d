package com.pickgliss.utils
{
   import org.as3commons.reflect.Accessor;
   import org.as3commons.reflect.Field;
   import org.as3commons.reflect.Type;
   import org.as3commons.reflect.Variable;
   
   public class GeneralUtils
   {
      
      public function GeneralUtils()
      {
         super();
      }
      
      public static function cloneObject(_arg_1:*) : *
      {
         return doSerializeObject(_arg_1,true);
      }
      
      public static function serializeObject(_arg_1:*) : Object
      {
         return doSerializeObject(_arg_1,false);
      }
      
      public static function deserializeObject(_arg_1:Object) : *
      {
         return doDeserializeObject(_arg_1);
      }
      
      private static function doDeserializeObject(_arg_1:Object) : *
      {
         var _local_5:* = undefined;
         var _local_2:* = undefined;
         var _local_8:String = null;
         var _local_6:Field = null;
         var _local_4:Type = Type.forName(_arg_1["classFullName"]);
         var _local_7:* = _arg_1["warpObject"];
         var _local_3:* = new _local_4.clazz();
         if(_local_3 is Vector.<*> || _local_3 is Array)
         {
            for each(_local_2 in _local_7)
            {
               if(_local_2 is String || _local_2 is int || _local_2 is uint || _local_2 is Number || _local_2 is Boolean)
               {
                  _local_3.push(_local_2);
               }
               else
               {
                  _local_3.push(doDeserializeObject(_local_2));
               }
            }
         }
         else
         {
            _local_5 = null;
            if(_arg_1["classFullName"] == "Object")
            {
               for(_local_8 in _local_7)
               {
                  _local_5 = doDeserializeProperty(_local_7[_local_8],null);
                  _local_3[_local_8] = _local_5;
               }
            }
            else
            {
               for each(_local_6 in _local_4.properties)
               {
                  if(_local_6 is Variable || _local_6 is Accessor && Accessor(_local_6).writeable && _local_6.name != "prototype")
                  {
                     _local_5 = doDeserializeProperty(_local_7[_local_6.name],_local_6);
                     _local_3[_local_6.name] = _local_5;
                  }
               }
            }
         }
         return _local_3;
      }
      
      private static function doDeserializeProperty(_arg_1:*, _arg_2:Field) : *
      {
         if(_arg_1 != null)
         {
            if(_arg_1 is Boolean || _arg_1 is String || _arg_1 is int || _arg_1 is uint || _arg_1 is Number)
            {
               return _arg_1;
            }
            return doDeserializeObject(_arg_1);
         }
         return null;
      }
      
      private static function doSerializeObject(_arg_1:*, _arg_2:Boolean, _arg_3:Type = null) : Object
      {
         var _local_7:* = undefined;
         var _local_4:* = undefined;
         var _local_9:String = null;
         var _local_8:Field = null;
         var _local_6:* = null;
         var _local_5:* = null;
         if(_arg_3 == null)
         {
            _arg_3 = Type.forInstance(_arg_1);
         }
         if(_arg_1 != null)
         {
            if(_arg_1 is Vector.<*> || _arg_1 is Array)
            {
               if(_arg_2)
               {
                  _local_6 = new _arg_3.clazz();
               }
               else
               {
                  _local_6 = [];
               }
               for each(_local_4 in _arg_1)
               {
                  if(_local_4 is String || _local_4 is int || _local_4 is uint || _local_4 is Number || _local_4 is Boolean)
                  {
                     _local_6.push(_local_4);
                  }
                  else
                  {
                     _local_6.push(doSerializeObject(_local_4,_arg_2));
                  }
               }
            }
            else
            {
               if(_arg_2)
               {
                  _local_6 = new _arg_3.clazz();
               }
               else
               {
                  _local_6 = {};
               }
               _local_7 = null;
               if(_arg_3.isDynamic)
               {
                  for(_local_9 in _arg_1)
                  {
                     if(_arg_1[_local_9] != null)
                     {
                        _local_7 = doSerializeProperty(_arg_1[_local_9],_arg_2,null);
                        _local_6[_local_9] = _local_7;
                     }
                  }
               }
               else
               {
                  for each(_local_8 in _arg_3.properties)
                  {
                     if(_local_8 is Variable || _local_8 is Accessor && Accessor(_local_8).writeable && _local_8.name != "prototype")
                     {
                        if(_arg_1[_local_8.name] != null)
                        {
                           _local_7 = doSerializeProperty(_arg_1[_local_8.name],_arg_2,_local_8);
                           _local_6[_local_8.name] = _local_7;
                        }
                     }
                  }
               }
            }
            if(!_arg_2)
            {
               _local_5 = {};
               _local_5["isCETransportObject"] = true;
               _local_5["classFullName"] = _arg_3.fullName;
               _local_5["warpObject"] = _local_6;
               return _local_5;
            }
            return _local_6;
         }
         return null;
      }
      
      private static function doSerializeProperty(_arg_1:*, _arg_2:Boolean, _arg_3:Field) : *
      {
         if(Boolean(_arg_3))
         {
            if(_arg_3 is Variable || _arg_3 is Accessor && Accessor(_arg_3).writeable && _arg_3.name != "prototype")
            {
               if(_arg_3.type.fullName == "Boolean" || _arg_3.type.fullName == "String" || _arg_3.type.fullName == "int" || _arg_3.type.fullName == "uint" || _arg_3.type.fullName == "Number")
               {
                  return _arg_1;
               }
               return doSerializeObject(_arg_1,_arg_2,_arg_3.type);
            }
            return;
         }
         if(_arg_1 is Boolean || _arg_1 is String || _arg_1 is int || _arg_1 is uint || _arg_1 is Number)
         {
            return _arg_1;
         }
         return doSerializeObject(_arg_1,_arg_2,null);
      }
   }
}

