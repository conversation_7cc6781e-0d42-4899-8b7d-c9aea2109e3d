package com.pickgliss.ui.controls
{
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.utils.ObjectUtils;
   import flash.display.DisplayObject;
   import flash.text.TextField;
   
   public class SelectedCheckButton extends SelectedButton
   {
      
      public static const P_fieldX:String = "fieldX";
      
      public static const P_fieldY:String = "fieldY";
      
      public static const P_text:String = "text";
      
      public static const P_textField:String = "textField";
      
      protected var _field:DisplayObject;
      
      protected var _fieldX:Number;
      
      protected var _fieldY:Number;
      
      protected var _text:String = "";
      
      protected var _textStyle:String;
      
      public function SelectedCheckButton()
      {
         super();
      }
      
      override public function dispose() : void
      {
         if(Bo<PERSON>an(this._field))
         {
            ObjectUtils.disposeObject(this._field);
         }
         this._field = null;
         graphics.clear();
         super.dispose();
      }
      
      public function set fieldX(_arg_1:Number) : void
      {
         if(this._fieldX == _arg_1)
         {
            return;
         }
         this._fieldX = _arg_1;
         onPropertiesChanged("fieldX");
      }
      
      public function set fieldY(_arg_1:Number) : void
      {
         if(this._fieldY == _arg_1)
         {
            return;
         }
         this._fieldY = _arg_1;
         onPropertiesChanged("fieldY");
      }
      
      public function set text(_arg_1:String) : void
      {
         if(this._text == _arg_1)
         {
            return;
         }
         this._text = _arg_1;
         onPropertiesChanged("text");
      }
      
      public function set textField(_arg_1:DisplayObject) : void
      {
         if(this._field == _arg_1)
         {
            return;
         }
         ObjectUtils.disposeObject(this._field);
         this._field = _arg_1;
         onPropertiesChanged("textField");
      }
      
      public function set textStyle(_arg_1:String) : void
      {
         if(this._textStyle == _arg_1)
         {
            return;
         }
         this._textStyle = _arg_1;
         this.textField = ComponentFactory.Instance.creat(this._textStyle);
      }
      
      public function get textWidth() : int
      {
         return TextField(this._field).textWidth;
      }
      
      public function get field() : TextField
      {
         return this._field as TextField;
      }
      
      override protected function addChildren() : void
      {
         super.addChildren();
         if(Boolean(this._field))
         {
            addChild(this._field);
         }
      }
      
      protected function drawClickArea() : void
      {
         graphics.beginFill(16711935,0);
         graphics.drawRect(0,0,_width,_height);
         graphics.endFill();
      }
      
      override protected function onProppertiesUpdate() : void
      {
         super.onProppertiesUpdate();
         if(Boolean(_changedPropeties["fieldX"]) || Boolean(_changedPropeties["fieldY"]))
         {
            if(Boolean(this._field))
            {
               this._field.x = this._fieldX;
               this._field.y = this._fieldY;
            }
         }
         if(Boolean(_changedPropeties["text"]))
         {
            if(Boolean(this._field))
            {
               if(this._field is TextField)
               {
                  TextField(this._field).text = this._text;
               }
               _width = this._field.x + this._field.width;
               _height = Math.max(this._field.height,_selectedButton.height);
               this.drawClickArea();
            }
         }
         if(Boolean(_changedPropeties["height"]) || Boolean(_changedPropeties["width"]))
         {
            this.drawClickArea();
         }
      }
   }
}

