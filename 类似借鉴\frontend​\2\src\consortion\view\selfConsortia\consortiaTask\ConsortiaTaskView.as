package consortion.view.selfConsortia.consortiaTask
{
   import baglocked.BaglockedManager;
   import com.pickgliss.events.FrameEvent;
   import com.pickgliss.ui.AlertManager;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.controls.ScrollPanel;
   import com.pickgliss.ui.controls.alert.BaseAlerFrame;
   import com.pickgliss.ui.core.Disposeable;
   import com.pickgliss.ui.text.FilterFrameText;
   import com.pickgliss.utils.ObjectUtils;
   import consortion.ConsortionModelManager;
   import ddt.manager.ChatManager;
   import ddt.manager.LanguageMgr;
   import ddt.manager.LeavePageManager;
   import ddt.manager.MessageTipManager;
   import ddt.manager.PlayerManager;
   import ddt.manager.SocketManager;
   import ddt.manager.SoundManager;
   import ddt.manager.TimeManager;
   import flash.display.Bitmap;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import times.utils.timerManager.TimerJuggler;
   import times.utils.timerManager.TimerManager;
   
   public class ConsortiaTaskView extends Sprite implements Disposeable
   {
      
      public static var RESET_MONEY:int = 500;
      
      public static var SUBMIT_RICHES:int = 5000;
      
      private var _myView:ConsortiaMyTaskView;
      
      private var _timeBG:Bitmap;
      
      private var _panel:ScrollPanel;
      
      private var _lastTimeTxt:FilterFrameText;
      
      private var _noTask:FilterFrameText;
      
      private var _timer:TimerJuggler;
      
      private var diff:Number;
      
      public function ConsortiaTaskView()
      {
         super();
         this.initView();
         this.initEvents();
      }
      
      private function initView() : void
      {
         this._timeBG = ComponentFactory.Instance.creatBitmap("asset.conortionTask.timeBG");
         this._myView = ComponentFactory.Instance.creatCustomObject("ConsortiaMyTaskView");
         this._panel = ComponentFactory.Instance.creatComponentByStylename("consortion.task.scrollpanel");
         this._lastTimeTxt = ComponentFactory.Instance.creatComponentByStylename("consortion.task.lastTimeTxt");
         this._noTask = ComponentFactory.Instance.creatComponentByStylename("conortionTask.notaskTxt");
         this._noTask.text = LanguageMgr.GetTranslation("conortionTask.notaskText");
         addChild(this._timeBG);
         addChild(this._panel);
         addChild(this._lastTimeTxt);
         addChild(this._noTask);
         this._panel.setView(this._myView);
         this._panel.invalidateViewport();
         this._noTask.visible = false;
         this._timeBG.visible = false;
         this._panel.visible = false;
         this._lastTimeTxt.visible = false;
         SocketManager.Instance.out.sendReleaseConsortiaTask(3);
      }
      
      private function initEvents() : void
      {
         ConsortionModelManager.Instance.TaskModel.addEventListener("getConsortiaTaskInfo",this.__getTaskInfo);
         ConsortionModelManager.Instance.TaskModel.addEventListener("Consortia_Delay_Task_Time",this.__updateEndTimeInfo);
      }
      
      private function __resetClick(_arg_1:MouseEvent) : void
      {
         SoundManager.instance.play("008");
         if(PlayerManager.Instance.Self.bagLocked)
         {
            BaglockedManager.Instance.show();
            return;
         }
         if(ConsortionModelManager.Instance.TaskModel.taskInfo == null)
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("consortia.task.stopTable"));
         }
         var _local_2:BaseAlerFrame = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("consortia.task.resetTable"),LanguageMgr.GetTranslation("consortia.task.resetContent"),LanguageMgr.GetTranslation("ok"),LanguageMgr.GetTranslation("cancel"),false,true,true,2);
         _local_2.moveEnable = false;
         _local_2.addEventListener("response",this._responseI);
      }
      
      private function _responseI(_arg_1:FrameEvent) : void
      {
         var _local_2:* = null;
         (_arg_1.currentTarget as BaseAlerFrame).removeEventListener("response",this._responseI);
         if(_arg_1.responseCode == 2 || _arg_1.responseCode == 3)
         {
            if(ConsortionModelManager.Instance.TaskModel.taskInfo == null)
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("consortia.task.stopTable"));
               ChatManager.Instance.sysChatYellow(LanguageMgr.GetTranslation("consortia.task.stopTable"));
            }
            else if(PlayerManager.Instance.Self.Money < RESET_MONEY)
            {
               _local_2 = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("AlertDialog.Info"),LanguageMgr.GetTranslation("tank.consortia.consortiashop.ConsortiaShopItem.Money"),LanguageMgr.GetTranslation("ok"),LanguageMgr.GetTranslation("cancel"));
               _local_2.addEventListener("response",this.__onNoMoneyResponse);
            }
            else
            {
               SocketManager.Instance.out.sendReleaseConsortiaTask(1);
               SocketManager.Instance.out.sendReleaseConsortiaTask(2);
            }
         }
         ObjectUtils.disposeObject(_arg_1.currentTarget as BaseAlerFrame);
      }
      
      private function __onNoMoneyResponse(_arg_1:FrameEvent) : void
      {
         SoundManager.instance.play("008");
         var _local_2:BaseAlerFrame = _arg_1.currentTarget as BaseAlerFrame;
         _local_2.removeEventListener("response",this.__onNoMoneyResponse);
         _local_2.disposeChildren = true;
         _local_2.dispose();
         _local_2 = null;
         if(_arg_1.responseCode == 3)
         {
            LeavePageManager.leaveToFillPath();
         }
      }
      
      private function removeEvents() : void
      {
         if(Boolean(this._timer))
         {
            this._timer.stop();
            this._timer.removeEventListener("timer",this.__timerOne);
         }
         ConsortionModelManager.Instance.TaskModel.removeEventListener("getConsortiaTaskInfo",this.__getTaskInfo);
         ConsortionModelManager.Instance.TaskModel.removeEventListener("Consortia_Delay_Task_Time",this.__updateEndTimeInfo);
      }
      
      private function __updateEndTimeInfo(_arg_1:ConsortiaTaskEvent) : void
      {
         this.diff += _arg_1.value * 60;
      }
      
      private function __getTaskInfo(_arg_1:ConsortiaTaskEvent) : void
      {
         if(_arg_1.value == 3 || _arg_1.value == 2 || _arg_1.value == 4 || _arg_1.value == 5)
         {
            if(ConsortionModelManager.Instance.TaskModel.taskInfo == null)
            {
               this.__noTask();
            }
            else
            {
               this.__showTask();
            }
         }
      }
      
      private function __showTask() : void
      {
         var _local_1:int = PlayerManager.Instance.Self.Right;
         this._noTask.visible = false;
         this._timeBG.visible = true;
         this._panel.visible = true;
         this._lastTimeTxt.visible = true;
         this._myView.taskInfo = ConsortionModelManager.Instance.TaskModel.taskInfo;
         this.__startTimer();
      }
      
      private function __noTask() : void
      {
         this._noTask.visible = true;
         this._timeBG.visible = false;
         this._panel.visible = false;
         this._lastTimeTxt.visible = false;
         if(Boolean(this._timer))
         {
            this._timer.stop();
            this._timer.removeEventListener("timer",this.__timerOne);
            TimerManager.getInstance().removeJugglerByTimer(this._timer);
            this._timer = null;
         }
      }
      
      private function __startTimer() : void
      {
         var _local_1:Date = ConsortionModelManager.Instance.TaskModel.taskInfo.beginTime;
         if(!_local_1)
         {
            return;
         }
         this.diff = ConsortionModelManager.Instance.TaskModel.taskInfo.time * 60 - TimeManager.Instance.TotalSecondToNow(_local_1) + 60;
         if(Boolean(this._timer))
         {
            return;
         }
         this._timer = TimerManager.getInstance().addTimerJuggler(1000);
         this._timer.addEventListener("timer",this.__timerOne);
         this._timer.start();
      }
      
      private function __timerOne(_arg_1:Event) : void
      {
         --this.diff;
         if(this.diff <= 0)
         {
            this._timer.stop();
            this._timer.removeEventListener("timer",this.__timerOne);
            TimerManager.getInstance().removeJugglerByTimer(this._timer);
            this._lastTimeTxt.text = "";
            return;
         }
         this._lastTimeTxt.text = LanguageMgr.GetTranslation("consortia.task.lasttime",int(this.diff / 60),this.diff % 60);
      }
      
      public function dispose() : void
      {
         this.removeEvents();
         if(Boolean(this._myView))
         {
            ObjectUtils.disposeObject(this._myView);
         }
         this._myView = null;
         if(Boolean(this._timeBG))
         {
            ObjectUtils.disposeObject(this._timeBG);
         }
         this._timeBG = null;
         if(Boolean(this._panel))
         {
            ObjectUtils.disposeObject(this._panel);
         }
         this._panel = null;
         if(Boolean(this._lastTimeTxt))
         {
            ObjectUtils.disposeObject(this._lastTimeTxt);
         }
         this._lastTimeTxt = null;
         if(Boolean(this._noTask))
         {
            ObjectUtils.disposeObject(this._noTask);
         }
         this._noTask = null;
         if(Boolean(this._timer))
         {
            this._timer.stop();
            TimerManager.getInstance().removeJugglerByTimer(this._timer);
            this._timer = null;
         }
         ObjectUtils.disposeAllChildren(this);
         if(Boolean(parent))
         {
            parent.removeChild(this);
         }
      }
   }
}

