package consortion.view.selfConsortia.consortiaTask
{
   import baglocked.BaglockedManager;
   import com.pickgliss.events.FrameEvent;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.LayerManager;
   import com.pickgliss.ui.controls.Frame;
   import com.pickgliss.ui.controls.TextButton;
   import com.pickgliss.ui.controls.TextInput;
   import com.pickgliss.ui.text.FilterFrameText;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.manager.LanguageMgr;
   import ddt.manager.MessageTipManager;
   import ddt.manager.PlayerManager;
   import ddt.manager.SocketManager;
   import ddt.manager.SoundManager;
   import flash.events.Event;
   import flash.events.KeyboardEvent;
   import flash.events.MouseEvent;
   
   public class DonateFrame extends Frame
   {
      
      private var _conentText:FilterFrameText;
      
      private var _ownMoney:FilterFrameText;
      
      private var _taxMedal:TextInput;
      
      private var _confirm:TextButton;
      
      private var _cancel:TextButton;
      
      private var _targetValue:int;
      
      public function DonateFrame()
      {
         super();
         this.initView();
         this.initEvents();
      }
      
      private function initView() : void
      {
         escEnable = true;
         enterEnable = true;
         disposeChildren = true;
         titleText = LanguageMgr.GetTranslation("consortia.donateMEDAL");
         this._conentText = ComponentFactory.Instance.creatComponentByStylename("core.MyConsortiaTax.conentTxt");
         this._conentText.text = LanguageMgr.GetTranslation("core.MyConsortiaTax.conentTxt.text");
         this._ownMoney = ComponentFactory.Instance.creatComponentByStylename("core.MyConsortiaTax.totalMEDALTxt");
         this._taxMedal = ComponentFactory.Instance.creatComponentByStylename("core.MyConsortiaMEDAL.input");
         this._confirm = ComponentFactory.Instance.creatComponentByStylename("core.DonateFrame.okBtn");
         this._cancel = ComponentFactory.Instance.creatComponentByStylename("core.DonateFrame.cancelBtn");
         addToContent(this._conentText);
         addToContent(this._ownMoney);
         addToContent(this._taxMedal);
         addToContent(this._confirm);
         addToContent(this._cancel);
         this._taxMedal.textField.restrict = "0-9";
         this._taxMedal.textField.maxChars = 8;
         this._confirm.text = LanguageMgr.GetTranslation("ok");
         this._cancel.text = LanguageMgr.GetTranslation("cancel");
         this._confirm.enable = false;
      }
      
      private function initEvents() : void
      {
         addEventListener("response",this.__response);
         addEventListener("addedToStage",this.__addToStageHandler);
         this._confirm.addEventListener("click",this.__confirmHanlder);
         this._cancel.addEventListener("click",this.__cancelHandler);
         this._taxMedal.addEventListener("change",this.__taxChangeHandler);
         this._taxMedal.addEventListener("keyDown",this.__enterHanlder);
      }
      
      private function removeEvents() : void
      {
         removeEventListener("response",this.__response);
         removeEventListener("addedToStage",this.__addToStageHandler);
         this._confirm.removeEventListener("click",this.__confirmHanlder);
         this._cancel.removeEventListener("click",this.__cancelHandler);
         this._taxMedal.removeEventListener("change",this.__taxChangeHandler);
         this._taxMedal.removeEventListener("keyDown",this.__enterHanlder);
      }
      
      private function __response(_arg_1:FrameEvent) : void
      {
         SoundManager.instance.play("008");
         if(_arg_1.responseCode == 1 || _arg_1.responseCode == 0)
         {
            this.dispose();
         }
      }
      
      private function __addToStageHandler(_arg_1:Event) : void
      {
         this._taxMedal.setFocus();
         this._ownMoney.text = PlayerManager.Instance.Self.DDTMoney.toString();
         this._taxMedal.text = "";
      }
      
      private function __confirmHanlder(_arg_1:MouseEvent) : void
      {
         var _local_2:int = 0;
         SoundManager.instance.play("008");
         if(PlayerManager.Instance.Self.bagLocked)
         {
            BaglockedManager.Instance.show();
            return;
         }
         if(this._taxMedal != null)
         {
            _local_2 = int(this._taxMedal.text);
            SocketManager.Instance.out.sendDonate(-300,_local_2);
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("consortia.task.donateOK"));
            this.dispose();
         }
      }
      
      private function __cancelHandler(_arg_1:MouseEvent) : void
      {
         SoundManager.instance.play("008");
         this.dispose();
      }
      
      private function __taxChangeHandler(_arg_1:Event) : void
      {
         if(this._taxMedal.text == "")
         {
            this._confirm.enable = false;
            return;
         }
         if(this._taxMedal.text == "0")
         {
            this._taxMedal.text = "";
            return;
         }
         this._confirm.enable = true;
         var _local_2:int = int(this._taxMedal.text);
         if(_local_2 >= PlayerManager.Instance.Self.DDTMoney || _local_2 >= this._targetValue)
         {
            this._taxMedal.text = PlayerManager.Instance.Self.DDTMoney <= this._targetValue ? PlayerManager.Instance.Self.DDTMoney.toString() : this._targetValue.toString();
         }
      }
      
      private function __enterHanlder(_arg_1:KeyboardEvent) : void
      {
         _arg_1.stopImmediatePropagation();
         if(_arg_1.keyCode == 13)
         {
            if(this._taxMedal.text == "")
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("consortia.task.plaese"));
            }
            else
            {
               this.__confirmHanlder(null);
            }
         }
         if(_arg_1.keyCode == 27)
         {
            SoundManager.instance.play("008");
            this.dispose();
         }
      }
      
      public function show() : void
      {
         LayerManager.Instance.addToLayer(this,3,true,1);
      }
      
      public function set targetValue(_arg_1:int) : void
      {
         this._targetValue = _arg_1;
      }
      
      override public function dispose() : void
      {
         this.removeEvents();
         if(Boolean(this._conentText))
         {
            ObjectUtils.disposeObject(this._conentText);
         }
         this._conentText = null;
         if(Boolean(this._ownMoney))
         {
            ObjectUtils.disposeObject(this._ownMoney);
         }
         this._ownMoney = null;
         if(Boolean(this._taxMedal))
         {
            ObjectUtils.disposeObject(this._taxMedal);
         }
         this._taxMedal = null;
         if(Boolean(this._confirm))
         {
            ObjectUtils.disposeObject(this._confirm);
         }
         this._confirm = null;
         if(Boolean(this._cancel))
         {
            ObjectUtils.disposeObject(this._cancel);
         }
         this._cancel = null;
         super.dispose();
         ObjectUtils.disposeAllChildren(this);
         if(Boolean(parent))
         {
            parent.removeChild(this);
         }
      }
   }
}

