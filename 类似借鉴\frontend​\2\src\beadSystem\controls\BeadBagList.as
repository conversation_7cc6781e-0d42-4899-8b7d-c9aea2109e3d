package beadSystem.controls
{
   import bagAndInfo.bag.BagListView;
   import bagAndInfo.cell.CellFactory;
   import baglocked.BaglockedManager;
   import beadSystem.beadSystemManager;
   import beadSystem.model.BeadModel;
   import com.pickgliss.events.FrameEvent;
   import com.pickgliss.events.InteractiveEvent;
   import com.pickgliss.ui.AlertManager;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.utils.DoubleClickManager;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.data.BagInfo;
   import ddt.data.goods.InventoryItemInfo;
   import ddt.events.BagEvent;
   import ddt.events.CellEvent;
   import ddt.manager.LanguageMgr;
   import ddt.manager.PlayerManager;
   import ddt.manager.SocketManager;
   import flash.events.Event;
   import flash.utils.Dictionary;
   
   public class BeadBagList extends BagListView
   {
      
      public var _startIndex:int;
      
      public var _stopIndex:int;
      
      private var _toPlace:int;
      
      private var _beadInfo:InventoryItemInfo;
      
      public function BeadBagList(_arg_1:int, _arg_2:int = 32, _arg_3:int = 80, _arg_4:int = 7)
      {
         this._startIndex = _arg_2;
         this._stopIndex = _arg_3;
         super(_arg_1,_arg_4);
      }
      
      override protected function __doubleClickHandler(_arg_1:InteractiveEvent) : void
      {
         var _local_2:* = null;
         if(PlayerManager.Instance.Self.bagLocked)
         {
            BaglockedManager.Instance.show();
            return;
         }
         var _local_3:InventoryItemInfo = (_arg_1.currentTarget as BeadCell).itemInfo;
         this._beadInfo = _local_3;
         if(Boolean(_local_3) && !_local_3.IsBinds)
         {
            _local_2 = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("tips"),LanguageMgr.GetTranslation("ddt.beadSystem.useBindBead"),LanguageMgr.GetTranslation("ok"),LanguageMgr.GetTranslation("cancel"),false,true,false,2);
            _local_2.addEventListener("response",this.__onBindRespones);
         }
         else
         {
            this.doBeadEquip();
         }
      }
      
      private function doBeadEquip() : void
      {
         var _local_1:int = 0;
         if(Boolean(this._beadInfo))
         {
            if(this._beadInfo.Property1 == "31")
            {
               if(this._beadInfo.IsBinds)
               {
               }
               _local_1 = beadSystemManager.Instance.getEquipPlace(this._beadInfo);
               if(Boolean(PlayerManager.Instance.Self.BeadBag.getItemAt(4)) && _local_1 == 4)
               {
                  if(!PlayerManager.Instance.Self.BeadBag.getItemAt(13) && BeadModel._BeadCells[13].isOpend && beadSystemManager.Instance.judgeLevel(this._beadInfo.Hole1,BeadModel._BeadCells[13].HoleLv))
                  {
                     _local_1 = 13;
                  }
                  else if(!PlayerManager.Instance.Self.BeadBag.getItemAt(14) && BeadModel._BeadCells[14].isOpend && beadSystemManager.Instance.judgeLevel(this._beadInfo.Hole1,BeadModel._BeadCells[14].HoleLv))
                  {
                     _local_1 = 14;
                  }
                  else if(!PlayerManager.Instance.Self.BeadBag.getItemAt(15) && BeadModel._BeadCells[15].isOpend && beadSystemManager.Instance.judgeLevel(this._beadInfo.Hole1,BeadModel._BeadCells[15].HoleLv))
                  {
                     _local_1 = 15;
                  }
                  else if(!PlayerManager.Instance.Self.BeadBag.getItemAt(16) && BeadModel._BeadCells[16].isOpend && beadSystemManager.Instance.judgeLevel(this._beadInfo.Hole1,BeadModel._BeadCells[16].HoleLv))
                  {
                     _local_1 = 16;
                  }
                  else if(!PlayerManager.Instance.Self.BeadBag.getItemAt(17) && BeadModel._BeadCells[17].isOpend && beadSystemManager.Instance.judgeLevel(this._beadInfo.Hole1,BeadModel._BeadCells[17].HoleLv))
                  {
                     _local_1 = 17;
                  }
                  else if(!PlayerManager.Instance.Self.BeadBag.getItemAt(18) && BeadModel._BeadCells[18].isOpend && beadSystemManager.Instance.judgeLevel(this._beadInfo.Hole1,BeadModel._BeadCells[18].HoleLv))
                  {
                     _local_1 = 18;
                  }
               }
               SocketManager.Instance.out.sendBeadEquip(this._beadInfo.Place,_local_1);
            }
         }
      }
      
      protected function __onBindRespones(_arg_1:FrameEvent) : void
      {
         switch(_arg_1.responseCode)
         {
            case 0:
            case 1:
            case 4:
               break;
            case 2:
            case 3:
               this.doBeadEquip();
         }
         _arg_1.currentTarget.removeEventListener("response",this.__onBindRespones);
         ObjectUtils.disposeObject(_arg_1.currentTarget);
      }
      
      public function get BeadCells() : Dictionary
      {
         return _cells;
      }
      
      override protected function createCells() : void
      {
         var _local_2:int = 0;
         var _local_1:* = null;
         _cells = new Dictionary();
         _cellMouseOverBg = ComponentFactory.Instance.creatBitmap("bagAndInfo.cell.bagCellOverBgAsset");
         _local_2 = this._startIndex;
         while(_local_2 <= this._stopIndex)
         {
            _local_1 = BeadCell(CellFactory.instance.createBeadCell(_local_2));
            addChild(_local_1);
            _local_1.addEventListener("interactive_click",this.__clickHandler);
            _local_1.addEventListener("interactive_double_click",this.__doubleClickHandler);
            DoubleClickManager.Instance.enableDoubleClick(_local_1);
            _local_1.addEventListener("lockChanged",__cellChanged);
            _cells[_local_1.beadPlace] = _local_1;
            _cellVec.push(_local_1);
            _local_2++;
         }
      }
      
      override public function setData(_arg_1:BagInfo) : void
      {
         var _local_2:String = null;
         if(_bagdata == _arg_1)
         {
            return;
         }
         if(_bagdata != null)
         {
            _bagdata.removeEventListener("update",this.__updateGoods);
         }
         clearDataCells();
         _bagdata = _arg_1;
         for(_local_2 in _bagdata.items)
         {
            if(_cells[_local_2] != null)
            {
               _bagdata.items[_local_2].isMoveSpace = true;
               _cells[_local_2].itemInfo = _bagdata.items[_local_2];
               _cells[_local_2].info = _bagdata.items[_local_2];
            }
         }
         _bagdata.addEventListener("update",this.__updateGoods);
      }
      
      override protected function __updateGoods(_arg_1:BagEvent) : void
      {
         var _local_3:InventoryItemInfo = null;
         var _local_2:* = null;
         var _local_4:Dictionary = _arg_1.changedSlots;
         for each(_local_3 in _local_4)
         {
            _local_2 = _bagdata.getItemAt(_local_3.Place);
            if(_local_2)
            {
               this.setCellInfo(_local_2.Place,_local_2);
            }
            else
            {
               this.setCellInfo(_local_3.Place,null);
            }
            dispatchEvent(new Event("change"));
         }
      }
      
      override protected function __clickHandler(_arg_1:InteractiveEvent) : void
      {
         if((_arg_1.currentTarget as BeadCell).info != null)
         {
            dispatchEvent(new CellEvent("itemclick",_arg_1.currentTarget,false,false,_arg_1.ctrlKey));
         }
      }
      
      override public function setCellInfo(_arg_1:int, _arg_2:InventoryItemInfo) : void
      {
         if(_arg_1 >= this._startIndex && _arg_1 <= this._stopIndex)
         {
            if(_arg_2 == null)
            {
               _cells[String(_arg_1)].info = null;
               _cells[String(_arg_1)].itemInfo = null;
               return;
            }
            if(_arg_2.Count == 0)
            {
               _cells[String(_arg_1)].info = null;
               _cells[String(_arg_1)].itemInfo = null;
            }
            else
            {
               _cells[String(_arg_1)].itemInfo = _arg_2;
               _cells[String(_arg_1)].info = _arg_2;
            }
         }
      }
      
      override public function dispose() : void
      {
         var _local_1:BeadCell = null;
         for each(_local_1 in _cells)
         {
            _local_1.removeEventListener("interactive_click",this.__clickHandler);
            _local_1.removeEventListener("interactive_double_click",this.__doubleClickHandler);
            _local_1.locked = false;
            _local_1.dispose();
         }
         _cells = null;
         super.dispose();
         if(Boolean(this.parent))
         {
            this.parent.removeChild(this);
         }
      }
   }
}

