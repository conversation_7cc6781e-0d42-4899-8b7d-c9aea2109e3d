package com.greensock.plugins
{
   import com.greensock.*;
   import flash.filters.BlurFilter;
   
   public class BlurFilterPlugin extends FilterPlugin
   {
      
      public static const API:Number = 1;
      
      private static var _propNames:Array = ["blurX","blurY","quality"];
      
      public function BlurFilterPlugin()
      {
         super();
         this.propName = "blurFilter";
         this.overwriteProps = ["blurFilter"];
      }
      
      override public function onInitTween(_arg_1:Object, _arg_2:*, _arg_3:TweenLite) : Boolean
      {
         _target = _arg_1;
         _type = BlurFilter;
         initFilter(_arg_2,new BlurFilter(0,0,int(_arg_2.quality) || 2),_propNames);
         return true;
      }
   }
}

