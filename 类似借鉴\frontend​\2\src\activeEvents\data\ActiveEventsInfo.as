package activeEvents.data
{
   import ddt.manager.LanguageMgr;
   import ddt.manager.TimeManager;
   import road7th.utils.DateUtils;
   
   public class ActiveEventsInfo
   {
      
      public static const COMMON:int = 0;
      
      public static const GOODS_EXCHANGE:int = 1;
      
      public static const PICC:int = 2;
      
      public static const SENIOR_PLAYER:int = 5;
      
      public static const SCAN_CODE:int = 6;
      
      public var ActiveID:int;
      
      public var Title:String;
      
      public var isAttend:Boolean = false;
      
      public var Description:String;
      
      private var _StartDate:String;
      
      public var IsShow:Boolean;
      
      public var viewId:int;
      
      private var _start:Date;
      
      private var _EndDate:String;
      
      private var _end:Date;
      
      public var Content:String;
      
      public var AwardContent:String;
      
      public var IsAdvance:Boolean;
      
      public var Type:int;
      
      public var IsOnly:int;
      
      public var HasKey:int;
      
      public var ActiveType:int;
      
      public var IconID:int = 1;
      
      public var GoodsExchangeTypes:String;
      
      public var limitType:String;
      
      public var limitValue:String;
      
      public var GoodsExchangeNum:String;
      
      public var ActionTimeContent:String;
      
      public function ActiveEventsInfo()
      {
         super();
      }
      
      public function get StartDate() : String
      {
         return this._StartDate;
      }
      
      public function set StartDate(_arg_1:String) : void
      {
         this._StartDate = _arg_1;
         this._start = DateUtils.getDateByStr(this._StartDate);
      }
      
      public function get start() : Date
      {
         return this._start;
      }
      
      public function get EndDate() : String
      {
         return this._EndDate;
      }
      
      public function set EndDate(_arg_1:String) : void
      {
         this._EndDate = _arg_1;
         this._end = DateUtils.getDateByStr(this._EndDate);
      }
      
      public function get end() : Date
      {
         return this._end;
      }
      
      public function activeTime() : String
      {
         var _local_1:* = null;
         var _local_3:* = null;
         var _local_2:* = null;
         if(Boolean(this.ActionTimeContent))
         {
            _local_1 = this.ActionTimeContent;
         }
         else if(Boolean(this.EndDate))
         {
            _local_3 = DateUtils.getDateByStr(this.StartDate);
            _local_2 = DateUtils.getDateByStr(this.EndDate);
            _local_1 = this.getActiveString(_local_3) + "-" + this.getActiveString(_local_2);
         }
         else
         {
            _local_1 = LanguageMgr.GetTranslation("tank.data.MovementInfo.begin",this.getActiveString(_local_3));
         }
         return _local_1;
      }
      
      private function getActiveString(_arg_1:Date) : String
      {
         return LanguageMgr.GetTranslation("tank.data.MovementInfo.date",this.addZero(_arg_1.getFullYear()),this.addZero(_arg_1.getMonth() + 1),this.addZero(_arg_1.getDate()));
      }
      
      private function addZero(_arg_1:Number) : String
      {
         var _local_2:* = null;
         if(_arg_1 < 10)
         {
            _local_2 = "0" + _arg_1.toString();
         }
         else
         {
            _local_2 = _arg_1.toString();
         }
         return _local_2;
      }
      
      public function overdue() : Boolean
      {
         var _local_3:* = null;
         var _local_2:Date = TimeManager.Instance.Now();
         var _local_1:Number = _local_2.time;
         var _local_4:Date = DateUtils.getDateByStr(this.StartDate);
         if(_local_1 < _local_4.getTime())
         {
            return true;
         }
         if(Boolean(this.EndDate))
         {
            _local_3 = DateUtils.getDateByStr(this.EndDate);
            if(_local_1 > _local_3.getTime())
            {
               return true;
            }
         }
         return false;
      }
   }
}

