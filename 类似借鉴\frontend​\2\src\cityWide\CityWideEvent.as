package cityWide
{
   import ddt.data.player.PlayerInfo;
   import flash.events.Event;
   
   public class CityWideEvent extends Event
   {
      
      public static const ONS_PLAYERINFO:String = "ons_playerInfo";
      
      private var _playerInfo:PlayerInfo;
      
      public function CityWideEvent(_arg_1:String, _arg_2:PlayerInfo = null, _arg_3:<PERSON><PERSON><PERSON> = false, _arg_4:<PERSON><PERSON><PERSON> = false)
      {
         super(_arg_1,_arg_3,_arg_4);
         this._playerInfo = _arg_2;
      }
      
      public function get playerInfo() : PlayerInfo
      {
         return this._playerInfo;
      }
   }
}

