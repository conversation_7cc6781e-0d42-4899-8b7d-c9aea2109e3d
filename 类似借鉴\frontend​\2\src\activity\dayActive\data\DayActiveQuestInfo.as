package activity.dayActive.data
{
   public class DayActiveQuestInfo
   {
      
      public var ID:int;
      
      public var MinLevel:int;
      
      public var MaxLevel:int;
      
      public var TargetType:int;
      
      public var JumpType:int;
      
      public var Description:String;
      
      public var Count:int;
      
      public var ActivePoint:int;
      
      public var MoneyPoint:int;
      
      public var OverCount:int;
      
      public var RewardID:int;
      
      public var RewardCount:int;
      
      public var RewardIsBind:Boolean;
      
      public var RewardValidTime:int;
      
      public var IsMust:Boolean;
      
      public function DayActiveQuestInfo()
      {
         super();
      }
   }
}

