package bagAndInfo.info
{
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.utils.ObjectUtils;
   import flash.display.DisplayObject;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   
   public class GlowPropButton extends PropButton
   {
      
      private var _overGraphics:DisplayObject;
      
      private var _showOverGraphics:Boolean = true;
      
      public function GlowPropButton()
      {
         super();
         this.addEvent();
         _tipStyle = "core.ChatacterPropTxtTips";
      }
      
      public function get showOverGraphics() : Boolean
      {
         return this._showOverGraphics;
      }
      
      public function set showOverGraphics(_arg_1:<PERSON><PERSON><PERSON>) : void
      {
         this._showOverGraphics = _arg_1;
      }
      
      override protected function addChildren() : void
      {
         if(!_back)
         {
            _back = ComponentFactory.Instance.creatBitmap("bagAndInfo.info.prop_up");
            addChild(_back);
         }
         if(!this._overGraphics)
         {
            this._overGraphics = ComponentFactory.Instance.creatComponentByStylename("bagAndInfo.info.light");
            this._overGraphics.visible = false;
            addChild(this._overGraphics);
         }
      }
      
      public function setOverGraphicsPosition(_arg_1:Point) : void
      {
         if(!_arg_1)
         {
            return;
         }
         this._overGraphics.x = _arg_1.x;
         this._overGraphics.y = _arg_1.y;
      }
      
      private function addEvent() : void
      {
         addEventListener("rollOver",this.__onMouseRollover);
         addEventListener("rollOut",this.__onMouseRollout);
      }
      
      private function __onMouseRollover(_arg_1:MouseEvent) : void
      {
         this._overGraphics.visible = this._showOverGraphics;
      }
      
      private function __onMouseRollout(_arg_1:MouseEvent) : void
      {
         this._overGraphics.visible = false;
      }
      
      private function removeEvent() : void
      {
         removeEventListener("rollOver",this.__onMouseRollover);
         removeEventListener("rollOut",this.__onMouseRollout);
      }
      
      override public function dispose() : void
      {
         this.removeEvent();
         if(Boolean(this._overGraphics))
         {
            ObjectUtils.disposeObject(this._overGraphics);
            this._overGraphics = null;
         }
         super.dispose();
      }
   }
}

