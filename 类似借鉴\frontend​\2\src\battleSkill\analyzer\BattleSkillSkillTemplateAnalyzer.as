package battleSkill.analyzer
{
   import battleSkill.info.BattleSkillSkillInfo;
   import com.pickgliss.loader.DataAnalyzer;
   import com.pickgliss.utils.ObjectUtils;
   
   public class BattleSkillSkillTemplateAnalyzer extends DataAnalyzer
   {
      
      private var _list:Vector.<BattleSkillSkillInfo>;
      
      public function BattleSkillSkillTemplateAnalyzer(_arg_1:Function)
      {
         super(_arg_1);
      }
      
      override public function analyze(_arg_1:*) : void
      {
         var _local_6:int = 0;
         var _local_4:* = null;
         var _local_2:* = null;
         var _local_5:* = null;
         var _local_3:XML = new XML(_arg_1);
         this._list = new Vector.<BattleSkillSkillInfo>();
         if(_local_3.@value == "true")
         {
            _local_4 = _local_3..Item;
            _local_6 = 0;
            while(_local_6 < _local_4.length())
            {
               _local_5 = new BattleSkillSkillInfo();
               ObjectUtils.copyPorpertiesByXML(_local_5,_local_4[_local_6]);
               this._list.push(_local_5);
               _local_6++;
            }
            onAnalyzeComplete();
         }
         else
         {
            message = _local_3.@message;
            onAnalyzeError();
         }
      }
      
      public function get list() : Vector.<BattleSkillSkillInfo>
      {
         return this._list;
      }
   }
}

