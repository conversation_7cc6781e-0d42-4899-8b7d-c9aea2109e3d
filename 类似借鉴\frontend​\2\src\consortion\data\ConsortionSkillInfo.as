package consortion.data
{
   import ddt.manager.LanguageMgr;
   import ddt.manager.TimeManager;
   
   public class ConsortionSkillInfo
   {
      
      public var id:int;
      
      public var type:int;
      
      public var descript:String;
      
      public var value:int;
      
      public var level:int;
      
      public var riches:int;
      
      public var name:String;
      
      public var pic:int;
      
      public var group:int;
      
      public var metal:int;
      
      public var isOpen:Boolean;
      
      public var beginDate:Date;
      
      public var validDate:int;
      
      public function ConsortionSkillInfo()
      {
         super();
      }
      
      public function get validity() : String
      {
         var _local_2:int = TimeManager.Instance.TotalDaysToNow(this.beginDate);
         var _local_1:int = this.validDate - _local_2;
         if(_local_1 <= 1)
         {
            _local_1 = this.validDate * 24 - TimeManager.Instance.TotalHoursToNow(this.beginDate);
            if(_local_1 < 1)
            {
               return this.validDate * 24 * 60 - TimeManager.Instance.TotalMinuteToNow(this.beginDate) + LanguageMgr.GetTranslation("minute");
            }
            return this.validDate * 24 - TimeManager.Instance.TotalHoursToNow(this.beginDate) + LanguageMgr.GetTranslation("hours");
         }
         return _local_1 + LanguageMgr.GetTranslation("shop.ShopIIShoppingCarItem.day");
      }
   }
}

