package bagAndInfo.bag.ring
{
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.core.Disposeable;
   import com.pickgliss.ui.text.FilterFrameText;
   import com.pickgliss.utils.ObjectUtils;
   import flash.display.Bitmap;
   import flash.display.Sprite;
   
   public class RingSystemTip extends Sprite implements Disposeable
   {
      
      private var _addition:Vector.<FilterFrameText>;
      
      private var _ringBitmap:Vector.<Bitmap>;
      
      public function RingSystemTip()
      {
         super();
         this._addition = new Vector.<FilterFrameText>(4);
         this._ringBitmap = new Vector.<Bitmap>(4);
         this.initView();
      }
      
      private function initView() : void
      {
         var _local_1:int = 0;
         _local_1 = 0;
         while(_local_1 < this._addition.length)
         {
            this._ringBitmap[_local_1] = ComponentFactory.Instance.creat("asset.bagAndInfo.bag.RingSystem.tipIcon");
            addChild(this._ringBitmap[_local_1]);
            this._addition[_local_1] = ComponentFactory.Instance.creatComponentByStylename("bagAndInfo.bag.RingSystemView.tipData");
            addChild(this._addition[_local_1]);
            _local_1++;
         }
      }
      
      public function setAdditiontext(_arg_1:Array) : void
      {
         var _local_3:int = 0;
         var _local_2:* = 0;
         _local_3 = 0;
         while(_local_3 < this._addition.length)
         {
            _local_2 = _arg_1[_local_3] * 0.01;
            this._addition[_local_3].text = "+" + _local_2;
            this._addition[_local_3].y = _local_3 * 26 - 3;
            this._ringBitmap[_local_3].y = _local_3 * 26;
            _local_3++;
         }
      }
      
      public function dispose() : void
      {
         var _local_1:int = 0;
         _local_1 = 0;
         while(_local_1 < this._addition.length)
         {
            ObjectUtils.disposeObject(this._addition[_local_1]);
            this._addition[_local_1] = null;
            _local_1++;
         }
      }
   }
}

