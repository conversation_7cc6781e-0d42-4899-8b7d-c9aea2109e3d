package bagAndInfo.info
{
   import beadSystem.beadSystemManager;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.LayerManager;
   import com.pickgliss.ui.controls.Frame;
   import com.pickgliss.ui.controls.SelectedButton;
   import com.pickgliss.ui.controls.SelectedButtonGroup;
   import com.pickgliss.ui.controls.container.HBox;
   import com.pickgliss.ui.image.ScaleBitmapImage;
   import com.pickgliss.ui.image.ScaleFrameImage;
   import com.pickgliss.utils.ClassUtils;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.data.player.PlayerInfo;
   import ddt.events.CEvent;
   import ddt.loader.LoaderCreate;
   import ddt.manager.LanguageMgr;
   import ddt.manager.PlayerManager;
   import ddt.manager.SoundManager;
   import ddt.manager.StateManager;
   import ddt.utils.AssetModuleLoader;
   import ddt.utils.HelperDataModuleLoad;
   import ddt.utils.HelperUIModuleLoad;
   import ddt.utils.PositionUtils;
   import ddt.view.horse.HorseInfoView;
   import elf.ElfManager;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import mark.MarkMgr;
   import petSoulCall.PetSoulCallManager;
   import room.RoomManager;
   import totem.TotemManager;
   import uigeneral.elf.ElfPropertyLookView;
   
   public class PlayerInfoFrame extends Frame
   {
      
      private const PLAYER_VIEW:int = 0;
      
      private const PET_VIEW:int = 1;
      
      private const BEAD_VIEW:int = 2;
      
      private const ELF_VIEW:int = 3;
      
      private const HORSE_VIEW:int = 4;
      
      private const TOTEM_VIEW:int = 5;
      
      private const MARK_VIEW:int = 6;
      
      private var _info:*;
      
      private var _BG:ScaleBitmapImage;
      
      private var _attestBtn:ScaleFrameImage;
      
      private var _hBox:HBox;
      
      private var _btnGroup:SelectedButtonGroup;
      
      private var _infoBtn:SelectedButton;
      
      private var _horseBtn:SelectedButton;
      
      private var _petBtn:SelectedButton;
      
      private var _beadBtn:SelectedButton;
      
      private var _totemBtn:SelectedButton;
      
      private var _markBtn:SelectedButton;
      
      private var _elfBtn:SelectedButton;
      
      private var _view:PlayerInfoView;
      
      private var _beadInfoView:Sprite;
      
      private var _horseView:HorseInfoView;
      
      private var _elfView:ElfPropertyLookView;
      
      private var _petsView:*;
      
      public function PlayerInfoFrame()
      {
         super();
         this.initView();
         this.initEvent();
      }
      
      private function initView() : void
      {
         this.escEnable = true;
         this.enterEnable = true;
         _titleText = LanguageMgr.GetTranslation("game.PlayerThumbnailTipItemText_0");
         this._BG = ComponentFactory.Instance.creatComponentByStylename("PlayerInfoFrame.bg");
         addToContent(this._BG);
         this._infoBtn = ComponentFactory.Instance.creatComponentByStylename("bagAndGiftFrame.playerInfoBtn");
         this._elfBtn = ComponentFactory.Instance.creatComponentByStylename("bagAndGiftFrame.elfInfoBtn");
         this._petBtn = ComponentFactory.Instance.creatComponentByStylename("bagAndGiftFrame.petInfoBtn");
         this._beadBtn = ComponentFactory.Instance.creatComponentByStylename("bagAndGiftFrame.beadInfoBtn");
         this._horseBtn = ComponentFactory.Instance.creatComponentByStylename("bagAndGiftFrame.horseInfoBtn");
         this._totemBtn = ComponentFactory.Instance.creatComponentByStylename("bagAndGiftFrame.totemInfoBtn");
         this._markBtn = ComponentFactory.Instance.creatComponentByStylename("bagAndGiftFrame.markInfoBtn");
         addToContent(this._infoBtn);
         addToContent(this._petBtn);
         addToContent(this._horseBtn);
         addToContent(this._elfBtn);
         addToContent(this._beadBtn);
         addToContent(this._totemBtn);
         addToContent(this._markBtn);
         this._btnGroup = new SelectedButtonGroup();
         this._btnGroup.addSelectItem(this._infoBtn);
         this._btnGroup.addSelectItem(this._petBtn);
         this._btnGroup.addSelectItem(this._beadBtn);
         this._btnGroup.addSelectItem(this._elfBtn);
         this._btnGroup.addSelectItem(this._horseBtn);
         this._btnGroup.addSelectItem(this._totemBtn);
         this._btnGroup.addSelectItem(this._markBtn);
         this._btnGroup.selectIndex = 0;
         if(Boolean(RoomManager.Instance.current) && PlayerInfoViewControl._isBattle)
         {
            this._infoBtn.visible = false;
            this._petBtn.visible = false;
            this._horseBtn.visible = false;
            this._beadBtn.visible = false;
            this._totemBtn.visible = false;
            this._markBtn.visible = false;
         }
         this._attestBtn = ComponentFactory.Instance.creatComponentByStylename("hall.playerInfo.attest");
         addChild(this._attestBtn);
         this._attestBtn.visible = false;
      }
      
      private function initEvent() : void
      {
         this._btnGroup.addEventListener("change",this.__changeHandler);
         this._infoBtn.addEventListener("click",this.__soundPlayer);
         this._horseBtn.addEventListener("click",this.__soundPlayer);
         this._elfBtn.addEventListener("click",this.__soundPlayer);
         this._petBtn.addEventListener("click",this.__soundPlayer);
         this._beadBtn.addEventListener("click",this.__soundPlayer);
         this._totemBtn.addEventListener("click",this.__soundPlayer);
         this._markBtn.addEventListener("click",this.__soundPlayer);
      }
      
      private function __soundPlayer(_arg_1:MouseEvent) : void
      {
         SoundManager.instance.play("008");
      }
      
      private function __changeHandler(_arg_1:Event) : void
      {
         this._attestBtn.visible = false;
         MarkMgr.inst.removeMarkView();
         switch(this._btnGroup.selectIndex)
         {
            case 0:
               this.showInfoFrame();
               return;
            case 1:
               this.loadPetData();
               this.loadPetData();
               return;
            case 2:
               this.showBeadInfoView();
               return;
            case 3:
               AssetModuleLoader.addModelLoader("uigeneral",5);
               AssetModuleLoader.startCodeLoader(this.showElf);
               return;
            case 4:
               this.loadHorseModule();
               return;
            case 5:
               this.showTotem();
               return;
            case 6:
               this.showMark();
            case 7:
         }
      }
      
      private function loadPetData() : void
      {
         AssetModuleLoader.addRequestLoader(LoaderCreate.Instance.createPetsSystemTalentSkillLoader());
         AssetModuleLoader.addRequestLoader(LoaderCreate.Instance.creatPetCharProTemplateInfoLoader());
         AssetModuleLoader.addRequestLoader(LoaderCreate.Instance.creatPetCharProRdTemplateLoader());
         PetSoulCallManager.instance.loadRes();
         AssetModuleLoader.addModelLoader("petsoulcall",4);
         AssetModuleLoader.addModelLoader("playerotherframe",5);
         AssetModuleLoader.addModelLoader("petsSystem",3);
         AssetModuleLoader.startCodeLoader(this.showPetsView);
      }
      
      private function loadPetModule() : void
      {
         new HelperUIModuleLoad().loadUIModule(["petsBag"],this.showPetsView);
      }
      
      private function loadHorseModule() : void
      {
         new HelperUIModuleLoad().loadUIModule(["horse"],this.showHorseView);
      }
      
      private function showInfoFrame() : void
      {
         if(this._view == null)
         {
            this._view = ComponentFactory.Instance.creatCustomObject("bag.PersonalInfoView");
            this._view.showSelfOperation = false;
            addToContent(this._view);
         }
         if(this._info)
         {
            this._view.info = this._info;
         }
         this.setVisible(0);
         this._attestBtn.visible = this._info.isAttest;
      }
      
      private function showPetsView() : void
      {
         PlayerInfoViewControl.isOpenFromBag = false;
         if(this._petsView)
         {
            ObjectUtils.disposeObject(this._petsView);
            this._petsView = null;
         }
         this._petsView = ClassUtils.CreatInstance("playerOther.pet.other.PetssystemOtherPetView",[Boolean(this._info as PlayerInfo) ? (this._info as PlayerInfo).pets : null]);
         PositionUtils.setPos(this._petsView,"petsSystem.otherView.Pos");
         addToContent(this._petsView);
         this.setVisible(1);
      }
      
      private function showBeadInfoView() : void
      {
         if(!this._beadInfoView)
         {
            beadSystemManager.Instance.addEventListener("createComplete",this.__onCreateComplete);
            beadSystemManager.Instance.showFrame("infoview");
         }
         this.setVisible(2);
      }
      
      private function __onCreateComplete(_arg_1:CEvent) : void
      {
         beadSystemManager.Instance.removeEventListener("createComplete",this.__onCreateComplete);
         if(_arg_1.data.type == "infoview")
         {
            this._beadInfoView = _arg_1.data.spr;
            this._beadInfoView["playerInfo"] = this._info;
            addChild(this._beadInfoView);
         }
      }
      
      private function showElf() : void
      {
         if(!this._elfView)
         {
            this._elfView = new ElfPropertyLookView();
            PositionUtils.setPos(this._elfView,"elf.propertylook.viewPos");
            addChild(this._elfView);
         }
         var _local_1:int = ElfManager.instance.getMaxElementDefType(this._info);
         if(Boolean(this._info.isSelf))
         {
            _local_1 = 0;
         }
         this._elfView.updateView(this._info.elfInfo,this._info.elfEquipTempId,_local_1);
         this.setVisible(3);
      }
      
      private function showHorseView() : void
      {
         new HelperDataModuleLoad().loadDataModule([LoaderCreate.Instance.createHorseTemplateDataLoader(),LoaderCreate.Instance.createHorseSkillGetDataLoader()],this.createHorseView);
      }
      
      private function createHorseView() : void
      {
         if(!this._horseView)
         {
            this._horseView = new HorseInfoView();
            PositionUtils.setPos(this._horseView,"PlayerInfoFrame.horseViewPos");
            addToContent(this._horseView);
         }
         this._horseView.info = this._info;
         this.setVisible(4);
      }
      
      private function showTotem() : void
      {
         TotemManager.instance.showView("infoview",{
            "parent":_container,
            "info":this._info
         });
         this.setVisible(5);
      }
      
      private function showMark() : void
      {
         MarkMgr.inst.showMarkView(_container,this._info);
         this.setVisible(6);
      }
      
      private function setVisible(_arg_1:int) : void
      {
         if(Boolean(this._view))
         {
            this._view.visible = _arg_1 == 0;
            if(this._view.visible)
            {
               this._view.switchShowII(_arg_1 == 3);
            }
         }
         if(this._petsView)
         {
            this._petsView.visible = _arg_1 == 1;
         }
         if(Boolean(this._beadInfoView))
         {
            this._beadInfoView.visible = _arg_1 == 2;
         }
         if(Boolean(this._horseView))
         {
            this._horseView.visible = _arg_1 == 4;
         }
         if(Boolean(this._elfView))
         {
            this._elfView.visible = _arg_1 == 3;
         }
         TotemManager.instance.setVisible("infoview",_arg_1 == 5);
      }
      
      private function removeEvent() : void
      {
         this._btnGroup.removeEventListener("change",this.__changeHandler);
         this._infoBtn.removeEventListener("click",this.__soundPlayer);
         this._horseBtn.removeEventListener("click",this.__soundPlayer);
         this._elfBtn.removeEventListener("click",this.__soundPlayer);
         this._petBtn.removeEventListener("click",this.__soundPlayer);
         this._beadBtn.removeEventListener("click",this.__soundPlayer);
         this._totemBtn.removeEventListener("click",this.__soundPlayer);
         this._markBtn.removeEventListener("click",this.__soundPlayer);
      }
      
      public function show() : void
      {
         LayerManager.Instance.addToLayer(this,3,true,1);
         this._btnGroup.selectIndex = 0;
         this.__changeHandler(null);
      }
      
      public function set info(_arg_1:*) : void
      {
         this._info = _arg_1;
         if(Boolean(this._view))
         {
            this._view.info = this._info;
         }
         if(PlayerInfoViewControl._isBattle)
         {
            return;
         }
         if(this._petsView)
         {
            this._petsView.infoPlayer = this._info;
         }
         if(Boolean(this._horseView))
         {
            this._horseView.info = this._info;
         }
         if(this._info.Grade < 19 || StateManager.currentStateType == "fighting" && this._info.ZoneID != 0 && this._info.ZoneID != PlayerManager.Instance.Self.ZoneID)
         {
            this._petBtn.enable = false;
         }
         else
         {
            this._petBtn.enable = true;
         }
         if(this._info.Grade < 12 || StateManager.currentStateType == "fighting" && this._info.ZoneID != 0 && this._info.ZoneID != PlayerManager.Instance.Self.ZoneID)
         {
            this._horseBtn.enable = false;
         }
         else
         {
            this._horseBtn.enable = true;
         }
         if(this._info.Grade < 16 || StateManager.currentStateType == "fighting" && this._info.ZoneID != 0 && this._info.ZoneID != PlayerManager.Instance.Self.ZoneID)
         {
            this._beadBtn.enable = false;
         }
         else
         {
            this._beadBtn.enable = true;
         }
         if(this._info.Grade < 22 || StateManager.currentStateType == "fighting" && this._info.ZoneID != 0 && this._info.ZoneID != PlayerManager.Instance.Self.ZoneID)
         {
            this._totemBtn.enable = false;
         }
         else
         {
            this._totemBtn.enable = true;
         }
         if(this._info.Grade < 30 || (StateManager.currentStateType == "fighting" || StateManager.currentStateType == "fighting3d") && this._info.ZoneID != 0 && this._info.ZoneID != PlayerManager.Instance.Self.ZoneID)
         {
            this._elfBtn.enable = false;
         }
         else
         {
            this._elfBtn.enable = true;
         }
         this._markBtn.enable = MarkMgr.inst.checkMarkOpen(this._info);
      }
      
      public function setAchivEnable(_arg_1:Boolean) : void
      {
         this._view.setAchvEnable(_arg_1);
      }
      
      override public function dispose() : void
      {
         this.removeEvent();
         this._info = null;
         PlayerInfoViewControl.currentPlayer = null;
         ObjectUtils.disposeObject(this._BG);
         this._BG = null;
         ObjectUtils.disposeObject(this._infoBtn);
         this._infoBtn = null;
         ObjectUtils.disposeObject(this._horseBtn);
         this._horseBtn = null;
         ObjectUtils.disposeObject(this._petBtn);
         this._petBtn = null;
         ObjectUtils.disposeObject(this._totemBtn);
         this._totemBtn = null;
         ObjectUtils.disposeObject(this._markBtn);
         this._markBtn = null;
         ObjectUtils.disposeObject(this._btnGroup);
         this._btnGroup = null;
         ObjectUtils.disposeObject(this._view);
         this._view = null;
         ObjectUtils.disposeObject(this._horseView);
         this._horseView = null;
         ObjectUtils.disposeObject(this._petsView);
         this._petsView = null;
         ObjectUtils.disposeObject(this._elfBtn);
         this._elfBtn = null;
         ObjectUtils.disposeObject(this._elfView);
         this._elfView = null;
         TotemManager.instance.closeView("infoview");
         PlayerInfoViewControl.clearView();
         super.dispose();
         if(Boolean(this.parent))
         {
            this.parent.removeChild(this);
         }
      }
   }
}

