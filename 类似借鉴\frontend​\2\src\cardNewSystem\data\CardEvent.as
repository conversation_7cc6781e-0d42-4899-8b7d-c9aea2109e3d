package cardNewSystem.data
{
   import flash.events.Event;
   
   public class Card<PERSON><PERSON> extends Event
   {
      
      public static const UPDATE_CARDINFO:String = "update_cardInfo";
      
      public static const CARD_PRORESET:String = "card_proReset";
      
      public static const CARD_XIDIAN:String = "card_xidian";
      
      private var _data:Object;
      
      public function CardEvent(_arg_1:String, _arg_2:Object = null, _arg_3:<PERSON><PERSON><PERSON> = false, _arg_4:<PERSON><PERSON><PERSON> = false)
      {
         this._data = _arg_2;
         super(_arg_1,_arg_3,_arg_4);
      }
      
      public function get data() : Object
      {
         return this._data;
      }
   }
}

