package com.demonsters.debugger
{
   import flash.display.DisplayObject;
   
   public class MonsterDebugger
   {
      
      public static var logger:Function;
      
      internal static const VERSION:Number = 3.02;
      
      private static var _enabled:Boolean = true;
      
      private static var _initialized:Boolean = false;
      
      public function MonsterDebugger()
      {
         super();
      }
      
      public static function initialize(_arg_1:Object, _arg_2:String = "127.0.0.1") : void
      {
         if(!_initialized)
         {
            _initialized = true;
            MonsterDebuggerCore.base = _arg_1;
            MonsterDebuggerCore.initialize();
            MonsterDebuggerConnection.initialize();
            MonsterDebuggerConnection.address = _arg_2;
            MonsterDebuggerConnection.connect();
         }
      }
      
      public static function get enabled() : <PERSON><PERSON><PERSON>
      {
         return _enabled;
      }
      
      public static function set enabled(_arg_1:Boolean) : void
      {
         _enabled = _arg_1;
      }
      
      public static function trace(_arg_1:*, _arg_2:*, _arg_3:String = "", _arg_4:String = "", _arg_5:uint = 0, _arg_6:int = 5) : void
      {
         if(_initialized && _enabled)
         {
            MonsterDebuggerCore.trace(_arg_1,_arg_2,_arg_3,_arg_4,_arg_5,_arg_6);
         }
      }
      
      public static function log(... _args) : void
      {
         var _local_5:* = undefined;
         var _local_7:* = undefined;
         var _local_6:* = undefined;
         var _local_4:* = undefined;
         var _local_3:int = 0;
         var _local_2:int = 0;
         _local_5 = undefined;
         _local_7 = undefined;
         _local_6 = undefined;
         _local_4 = undefined;
         _local_5 = null;
         _local_7 = null;
         _local_6 = null;
         _local_4 = null;
         if(_initialized && _enabled)
         {
            if(_args.length == 0)
            {
               return;
            }
            _local_5 = "Log";
            try
            {
               throw new Error();
            }
            catch(e:Error)
            {
               _local_7 = e.getStackTrace();
               if(_local_7 != null && _local_7 != "")
               {
                  _local_7 = _local_7.split("\t").join("");
                  _local_6 = _local_7.split("\n");
                  if(_local_6.length > 2)
                  {
                     _local_6.shift();
                     _local_6.shift();
                     _local_4 = _local_6[0];
                     _local_4 = _local_4.substring(3,_local_4.length);
                     _local_3 = int(_local_4.indexOf("["));
                     _local_2 = int(_local_4.indexOf("/"));
                     if(_local_3 == -1)
                     {
                        _local_3 = int(_local_4.length);
                     }
                     if(_local_2 == -1)
                     {
                        _local_2 = _local_3;
                     }
                     _local_5 = MonsterDebuggerUtils.parseType(_local_4.substring(0,_local_2));
                     if(_local_5 == "<anonymous>")
                     {
                        _local_5 = "";
                     }
                     if(_local_5 == "")
                     {
                        _local_5 = "Log";
                     }
                  }
               }
            }
            if(_args.length == 1)
            {
               MonsterDebuggerCore.trace(_local_5,_args[0],"","",0,5);
            }
            else
            {
               MonsterDebuggerCore.trace(_local_5,_args,"","",0,5);
            }
         }
      }
      
      public static function snapshot(_arg_1:*, _arg_2:DisplayObject, _arg_3:String = "", _arg_4:String = "") : void
      {
         if(_initialized && _enabled)
         {
            MonsterDebuggerCore.snapshot(_arg_1,_arg_2,_arg_3,_arg_4);
         }
      }
      
      public static function breakpoint(_arg_1:*, _arg_2:String = "breakpoint") : void
      {
         if(_initialized && _enabled)
         {
            MonsterDebuggerCore.breakpoint(_arg_1,_arg_2);
         }
      }
      
      public static function inspect(_arg_1:*) : void
      {
         if(_initialized && _enabled)
         {
            MonsterDebuggerCore.inspect(_arg_1);
         }
      }
      
      public static function clear() : void
      {
         if(_initialized && _enabled)
         {
            MonsterDebuggerCore.clear();
         }
      }
   }
}

