package cloudBuyLottery.view
{
   import bagAndInfo.cell.BaseCell;
   import cloudBuyLottery.CloudBuyLotteryManager;
   import com.greensock.TweenMax;
   import com.greensock.easing.Elastic;
   import com.pickgliss.events.FrameEvent;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.controls.BaseButton;
   import com.pickgliss.ui.controls.Frame;
   import com.pickgliss.ui.image.ScaleBitmapImage;
   import com.pickgliss.ui.text.FilterFrameText;
   import com.pickgliss.utils.ClassUtils;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.data.goods.InventoryItemInfo;
   import ddt.data.goods.ItemTemplateInfo;
   import ddt.manager.ItemManager;
   import ddt.manager.LanguageMgr;
   import ddt.manager.MessageTipManager;
   import ddt.manager.SocketManager;
   import ddt.manager.SoundManager;
   import ddt.utils.PositionUtils;
   import flash.display.Bitmap;
   import flash.display.MovieClip;
   import flash.display.Shape;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import flash.utils.setTimeout;
   
   public class IndividualLottery extends Frame
   {
      
      private var _bg:ScaleBitmapImage;
      
      private var _bg2:Bitmap;
      
      private var _jubaoMC:MovieClip;
      
      private var _helpTxt:FilterFrameText;
      
      private var _juBaoBtn:BaseButton;
      
      private var _numTxt:FilterFrameText;
      
      private var _num:FilterFrameText;
      
      private var _selectSprite:Sprite;
      
      private var _selectedCell:BaseCell;
      
      private var itemInfo:ItemTemplateInfo;
      
      private var tInfo:InventoryItemInfo;
      
      private var flag:Boolean = false;
      
      private var gotoFlag:Boolean = true;
      
      private var _look:BaseButton;
      
      private var _lookGoods:LookGoodsFrame;
      
      private var _paopao:Bitmap;
      
      public function IndividualLottery()
      {
         super();
         this.initView();
         this.initEvent();
      }
      
      private function initView() : void
      {
         this._bg2 = ComponentFactory.Instance.creatBitmap("asset.IndividualLottery.BG");
         this._bg = ComponentFactory.Instance.creatComponentByStylename("IndividualLottery.BG");
         this._jubaoMC = ClassUtils.CreatInstance("asset.IndividualLottery.jubaoMC") as MovieClip;
         PositionUtils.setPos(this._jubaoMC,"IndividualLottery.jubaoMC");
         this._helpTxt = ComponentFactory.Instance.creatComponentByStylename("IndividualLottery.helpTxt");
         this._helpTxt.htmlText = LanguageMgr.GetTranslation("IndividualLottery.helpHtmlTxt",500);
         this._juBaoBtn = ComponentFactory.Instance.creat("IndividualLottery.jubaoBtn");
         this._num = ComponentFactory.Instance.creatComponentByStylename("IndividualLottery.num");
         this._num.text = CloudBuyLotteryManager.Instance.model.remainTimes.toString();
         this._look = ComponentFactory.Instance.creat("IndividualLottery.lookGoodsBtn");
         this._lookGoods = ComponentFactory.Instance.creatCustomObject("IndividualLottery.LookGoods");
         this._paopao = ComponentFactory.Instance.creatBitmap("asset.cloudbuy.paopao");
         PositionUtils.setPos(this._paopao,"asset.cloudbuy.paopao");
         addToContent(this._bg2);
         addToContent(this._bg);
         addToContent(this._jubaoMC);
         addToContent(this._helpTxt);
         addToContent(this._juBaoBtn);
         addToContent(this._paopao);
         addToContent(this._num);
         addToContent(this._look);
         var _local_2:Point = ComponentFactory.Instance.creatCustomObject("IndividualLottery.selectCellSize");
         var _local_1:Shape = new Shape();
         _local_1.graphics.beginFill(16777215,0);
         _local_1.graphics.drawRect(0,0,_local_2.x,_local_2.y);
         _local_1.graphics.endFill();
         this._selectSprite = ComponentFactory.Instance.creatCustomObject("IndividualLottery.selectSprite");
         this._selectedCell = new BaseCell(_local_1);
         this._selectedCell.x = this._selectedCell.width / -2;
         this._selectedCell.y = this._selectedCell.height / -2;
         this._selectedCell.visible = false;
         this._selectSprite.addChild(this._selectedCell);
         addToContent(this._selectSprite);
      }
      
      private function initEvent() : void
      {
         addEventListener("response",this.__responseHandler);
         addEventListener("enterFrame",this.__onEnterFrame);
         this._juBaoBtn.addEventListener("click",this.__onClick);
         this._look.addEventListener("click",this.__lookGoods);
         CloudBuyLotteryManager.Instance.addEventListener("Individual",this.__updateInfo);
      }
      
      private function removeEvent() : void
      {
         removeEventListener("response",this.__responseHandler);
         removeEventListener("enterFrame",this.__onEnterFrame);
         CloudBuyLotteryManager.Instance.removeEventListener("Individual",this.__updateInfo);
      }
      
      private function __updateInfo(_arg_1:Event) : void
      {
         if(CloudBuyLotteryManager.Instance.model.isGetReward)
         {
            this._num.text = CloudBuyLotteryManager.Instance.model.remainTimes.toString();
            this.updateData(CloudBuyLotteryManager.Instance.model.luckDrawId);
         }
         else
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("IndividualLottery.getRewardTip"));
            addEventListener("response",this.__responseHandler);
            this.btnIsClick(true);
         }
      }
      
      private function updateData(_arg_1:int) : void
      {
         this.itemInfo = ItemManager.Instance.getTemplateById(_arg_1) as ItemTemplateInfo;
         this.tInfo = new InventoryItemInfo();
         this.tInfo.IsBinds = true;
         ObjectUtils.copyProperties(this.tInfo,this.itemInfo);
         this.flag = true;
      }
      
      private function __lookGoods(_arg_1:MouseEvent) : void
      {
         var _local_6:int = 0;
         var _local_4:* = null;
         var _local_2:* = null;
         var _local_5:Vector.<InventoryItemInfo> = new Vector.<InventoryItemInfo>();
         var _local_3:Array = CloudBuyLotteryManager.Instance.itemInfoList;
         if(_local_3.length < 0)
         {
            return;
         }
         _local_6 = 0;
         while(_local_6 < _local_3.length)
         {
            _local_4 = ItemManager.Instance.getTemplateById(_local_3[_local_6].TemplateID) as ItemTemplateInfo;
            _local_2 = new InventoryItemInfo();
            ObjectUtils.copyProperties(_local_2,_local_4);
            _local_2.ValidDate = _local_3[_local_6].ValidDate;
            _local_2.StrengthenLevel = _local_3[_local_6].StrengthLevel;
            _local_2.AttackCompose = _local_3[_local_6].AttackCompose;
            _local_2.DefendCompose = _local_3[_local_6].DefendCompose;
            _local_2.LuckCompose = _local_3[_local_6].LuckCompose;
            _local_2.AgilityCompose = _local_3[_local_6].AgilityCompose;
            _local_2.IsBinds = _local_3[_local_6].IsBind;
            _local_2.Count = _local_3[_local_6].Count;
            _local_5.push(_local_2);
            _local_6++;
         }
         this._lookGoods.show(_local_5);
      }
      
      private function __onClick(_arg_1:MouseEvent) : void
      {
         if(int(this._num.text) > 0)
         {
            this.btnIsClick(false);
            removeEventListener("response",this.__responseHandler);
            SocketManager.Instance.out.sendLuckDrawGo();
         }
         else
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("IndividualLottery.NumNOTip"));
         }
      }
      
      private function __onEnterFrame(_arg_1:Event) : void
      {
         if(this.flag)
         {
            if(this.gotoFlag)
            {
               this.gotoFlag = false;
               this._jubaoMC.gotoAndPlay(41);
               this._selectedCell.info = this.tInfo;
            }
            if(this._jubaoMC.currentFrame >= 65)
            {
               this.flag = false;
               this._selectedCell.visible = true;
               this.creatTweenMagnify();
            }
         }
      }
      
      private function btnIsClick(_arg_1:Boolean) : void
      {
         if(_arg_1)
         {
            this._juBaoBtn.enable = true;
            this._juBaoBtn.mouseChildren = true;
            this._juBaoBtn.mouseEnabled = true;
         }
         else
         {
            this._juBaoBtn.enable = false;
            this._juBaoBtn.mouseChildren = false;
            this._juBaoBtn.mouseEnabled = false;
         }
      }
      
      private function creatTweenMagnify(_arg_1:Number = 1, _arg_2:Number = 1.5, _arg_3:int = -1, _arg_4:Boolean = true, _arg_5:int = 1100) : void
      {
         TweenMax.to(this._selectSprite,_arg_1,{
            "scaleX":_arg_2,
            "scaleY":_arg_2,
            "repeat":_arg_3,
            "yoyo":_arg_4,
            "ease":Elastic.easeOut
         });
         setTimeout(this._timeOut,_arg_5);
      }
      
      private function _timeOut() : void
      {
         this._clear();
      }
      
      private function _clear() : void
      {
         var _local_1:* = undefined;
         this.gotoFlag = true;
         MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("IndividualLottery.GoodsName",this.tInfo.Name));
         TweenMax.killTweensOf(this._selectSprite);
         if(Boolean(this._selectedCell))
         {
            this._selectedCell.visible = false;
         }
         if(Boolean(this._selectSprite))
         {
            _local_1 = 1;
            this._selectSprite.scaleY = _local_1;
            this._selectSprite.scaleX = _local_1;
         }
         setTimeout(this.showJuBaoBtn,1500);
      }
      
      private function showJuBaoBtn() : void
      {
         addEventListener("response",this.__responseHandler);
         this.btnIsClick(true);
      }
      
      private function __responseHandler(_arg_1:FrameEvent) : void
      {
         if(_arg_1.responseCode == 0 || _arg_1.responseCode == 1)
         {
            SoundManager.instance.play("008");
            this.dispose();
         }
      }
      
      override public function dispose() : void
      {
         this.removeEvent();
         ObjectUtils.disposeAllChildren(this);
         if(Boolean(this._lookGoods))
         {
            ObjectUtils.disposeObject(this._lookGoods);
         }
         this._lookGoods = null;
         super.dispose();
      }
   }
}

