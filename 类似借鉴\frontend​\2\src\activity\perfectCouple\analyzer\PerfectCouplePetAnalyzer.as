package activity.perfectCouple.analyzer
{
   import activity.perfectCouple.data.PerfectCouplePetTemp;
   import com.pickgliss.loader.DataAnalyzer;
   import com.pickgliss.utils.ObjectUtils;
   import road7th.data.DictionaryData;
   
   public class PerfectCouplePetAnalyzer extends DataAnalyzer
   {
      
      private var _data:DictionaryData;
      
      public function PerfectCouplePetAnalyzer(_arg_1:Function)
      {
         super(_arg_1);
      }
      
      override public function analyze(_arg_1:*) : void
      {
         var _local_5:int = 0;
         var _local_3:* = null;
         var _local_4:* = null;
         this._data = new DictionaryData();
         var _local_2:XML = new XML(_arg_1);
         if(_local_2.@value == "true")
         {
            _local_3 = _local_2..Item;
            _local_5 = 0;
            while(_local_5 < _local_3.length())
            {
               _local_4 = new PerfectCouplePetTemp();
               ObjectUtils.copyPorpertiesByXML(_local_4,_local_3[_local_5]);
               this._data.add(_local_4.Level,_local_4);
               _local_5++;
            }
         }
         else
         {
            message = _local_2.@message;
            onAnalyzeError();
         }
         onAnalyzeComplete();
         this._data = null;
      }
      
      public function get data() : DictionaryData
      {
         return this._data;
      }
   }
}

