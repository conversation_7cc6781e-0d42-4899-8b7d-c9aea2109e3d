package consortion.analyze
{
   import com.pickgliss.loader.DataAnalyzer;
   import consortion.ConsortionModelManager;
   import ddt.data.player.ConsortiaPlayerInfo;
   import ddt.data.player.PlayerState;
   import ddt.manager.PlayerManager;
   import road7th.data.DictionaryData;
   
   public class ConsortionMemberAnalyer extends DataAnalyzer
   {
      
      public var consortionMember:DictionaryData;
      
      public function ConsortionMemberAnalyer(_arg_1:Function)
      {
         super(_arg_1);
      }
      
      override public function analyze(_arg_1:*) : void
      {
         var _local_6:int = 0;
         var _local_4:* = null;
         var _local_5:* = null;
         var _local_2:* = null;
         var _local_3:XML = new XML(_arg_1);
         this.consortionMember = new DictionaryData();
         if(_local_3.@value == "true")
         {
            ConsortionModelManager.Instance.model.systemDate = XML(_local_3).@currentDate;
            _local_4 = _local_3..Item;
            _local_6 = 0;
            while(_local_6 < _local_4.length())
            {
               _local_5 = new ConsortiaPlayerInfo();
               _local_5.beginChanges();
               _local_5.IsVote = this.converBoolean(_local_4[_local_6].@IsVote);
               _local_5.privateID = _local_4[_local_6].@ID;
               _local_5.ConsortiaID = PlayerManager.Instance.Self.ConsortiaID;
               _local_5.ConsortiaName = PlayerManager.Instance.Self.ConsortiaName;
               _local_5.DutyID = _local_4[_local_6].@DutyID;
               _local_5.DutyName = _local_4[_local_6].@DutyName;
               _local_5.GP = _local_4[_local_6].@GP;
               _local_5.Grade = _local_4[_local_6].@Grade;
               _local_5.FightPower = _local_4[_local_6].@FightPower;
               _local_5.FightPower_Show = _local_4[_local_6].@ExFightPower;
               _local_5.AchievementPoint = _local_4[_local_6].@AchievementPoint;
               _local_5.honor = _local_4[_local_6].@Rank;
               _local_5.IsChat = this.converBoolean(_local_4[_local_6].@IsChat);
               _local_5.IsDiplomatism = this.converBoolean(_local_4[_local_6].@IsDiplomatism);
               _local_5.IsDownGrade = this.converBoolean(_local_4[_local_6].@IsDownGrade);
               _local_5.IsEditorDescription = this.converBoolean(_local_4[_local_6].@IsEditorDescription);
               _local_5.IsEditorPlacard = this.converBoolean(_local_4[_local_6].@IsEditorPlacard);
               _local_5.IsEditorUser = this.converBoolean(_local_4[_local_6].@IsEditorUser);
               _local_5.IsExpel = this.converBoolean(_local_4[_local_6].@IsExpel);
               _local_5.IsInvite = this.converBoolean(_local_4[_local_6].@IsInvite);
               _local_5.IsManageDuty = this.converBoolean(_local_4[_local_6].@IsManageDuty);
               _local_5.IsRatify = this.converBoolean(_local_4[_local_6].@IsRatify);
               _local_5.IsUpGrade = this.converBoolean(_local_4[_local_6].@IsUpGrade);
               _local_5.IsBandChat = this.converBoolean(_local_4[_local_6].@IsBanChat);
               _local_5.Offer = int(_local_4[_local_6].@Offer);
               _local_5.RatifierID = _local_4[_local_6].@RatifierID;
               _local_5.RatifierName = _local_4[_local_6].@RatifierName;
               _local_5.Remark = _local_4[_local_6].@Remark;
               _local_5.Repute = _local_4[_local_6].@Repute;
               _local_2 = new PlayerState(int(_local_4[_local_6].@State));
               _local_5.playerState = _local_2;
               _local_5.LastDate = _local_4[_local_6].@LastDate;
               _local_5.ID = _local_4[_local_6].@UserID;
               _local_5.NickName = _local_4[_local_6].@UserName;
               _local_5.typeVIP = _local_4[_local_6].@typeVIP;
               _local_5.VIPLevel = _local_4[_local_6].@VIPLevel;
               _local_5.LoginName = _local_4[_local_6].@LoginName;
               _local_5.Sex = this.converBoolean(_local_4[_local_6].@Sex);
               _local_5.isAttest = this.converBoolean(_local_4[_local_6].@IsBeauty);
               _local_5.EscapeCount = _local_4[_local_6].@EscapeCount;
               _local_5.Right = _local_4[_local_6].@Right;
               _local_5.WinCount = _local_4[_local_6].@WinCount;
               _local_5.TotalCount = _local_4[_local_6].@TotalCount;
               _local_5.RichesOffer = _local_4[_local_6].@RichesOffer;
               _local_5.RichesRob = _local_4[_local_6].@RichesRob;
               _local_5.UseOffer = _local_4[_local_6].@TotalRichesOffer;
               _local_5.DutyLevel = _local_4[_local_6].@DutyLevel;
               _local_5.LastWeekRichesOffer = parseInt(_local_4[_local_6].@LastWeekRichesOffer);
               _local_5.isOld = int(_local_4[_local_6].@OldPlayer) == 1;
               _local_5.commitChanges();
               this.consortionMember.add(_local_5.ID,_local_5);
               if(_local_5.ID == PlayerManager.Instance.Self.ID)
               {
                  PlayerManager.Instance.Self.ConsortiaID = _local_5.ConsortiaID;
                  PlayerManager.Instance.Self.DutyLevel = _local_5.DutyLevel;
                  PlayerManager.Instance.Self.DutyName = _local_5.DutyName;
                  PlayerManager.Instance.Self.Right = _local_5.Right;
               }
               _local_6++;
            }
            onAnalyzeComplete();
         }
         else
         {
            message = _local_3.@message;
            onAnalyzeError();
            onAnalyzeComplete();
         }
      }
      
      private function converBoolean(_arg_1:String) : Boolean
      {
         return _arg_1 == "true" ? true : false;
      }
   }
}

