package consortion.view.selfConsortia.consortiaTask
{
   import baglocked.BaglockedManager;
   import com.pickgliss.events.FrameEvent;
   import com.pickgliss.ui.AlertManager;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.LayerManager;
   import com.pickgliss.ui.controls.TextButton;
   import com.pickgliss.ui.controls.alert.BaseAlerFrame;
   import com.pickgliss.ui.controls.container.VBox;
   import com.pickgliss.ui.core.Disposeable;
   import com.pickgliss.ui.image.MutipleImage;
   import com.pickgliss.ui.text.FilterFrameText;
   import com.pickgliss.utils.ObjectUtils;
   import consortion.ConsortionModelManager;
   import consortion.data.ConsortionSkillInfo;
   import ddt.events.PlayerPropertyEvent;
   import ddt.manager.ChatManager;
   import ddt.manager.ConsortiaDutyManager;
   import ddt.manager.LanguageMgr;
   import ddt.manager.LeavePageManager;
   import ddt.manager.MessageTipManager;
   import ddt.manager.PlayerManager;
   import ddt.manager.ServerConfigManager;
   import ddt.manager.SocketManager;
   import ddt.manager.SoundManager;
   import ddt.utils.CheckMoneyUtils;
   import ddt.utils.PositionUtils;
   import flash.display.Bitmap;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class ConsortiaMyTaskView extends Sprite implements Disposeable
   {
      
      private var _taskInfo:ConsortiaTaskInfo;
      
      private var _vbox:VBox;
      
      private var _finishItemList:Vector.<ConsortiaMyTaskFinishItem>;
      
      private var _myFinishTxt:FilterFrameText;
      
      private var _expTxt:FilterFrameText;
      
      private var _offerTxt:FilterFrameText;
      
      private var _richesTxt:FilterFrameText;
      
      private var _skillNameTxt:FilterFrameText;
      
      private var _contentTxt1:FilterFrameText;
      
      private var _contentTxt2:FilterFrameText;
      
      private var _contentTxt3:FilterFrameText;
      
      private var _expText:FilterFrameText;
      
      private var _moneyText:FilterFrameText;
      
      private var _caiText:FilterFrameText;
      
      private var _skillText:FilterFrameText;
      
      private var _contributionLbl:FilterFrameText;
      
      private var _contributionTxt:FilterFrameText;
      
      private var _badgeLbl:FilterFrameText;
      
      private var _badgeText:FilterFrameText;
      
      private var _myReseBtn:TextButton;
      
      private var _contributionRankBtn:TextButton;
      
      private var _delayTimeBtn:TextButton;
      
      private var _reSetTaskMoney:int;
      
      public function ConsortiaMyTaskView()
      {
         super();
         this.initView();
         this.initEvents();
      }
      
      private function initView() : void
      {
         var _local_5:int = 0;
         var _local_4:int = 0;
         var _local_3:* = null;
         this._finishItemList = new Vector.<ConsortiaMyTaskFinishItem>();
         var _local_2:MutipleImage = ComponentFactory.Instance.creatComponentByStylename("consortion.task.bgI");
         var _local_6:MutipleImage = ComponentFactory.Instance.creatComponentByStylename("consortion.task.bgII");
         this._vbox = ComponentFactory.Instance.creatComponentByStylename("consortion.task.vboxI");
         this._myFinishTxt = ComponentFactory.Instance.creatComponentByStylename("consortion.task.MyfinishTxt");
         this._expText = ComponentFactory.Instance.creatComponentByStylename("consortion.task.expTxt");
         this._expText.text = LanguageMgr.GetTranslation("consortion.task.exp");
         this._moneyText = ComponentFactory.Instance.creatComponentByStylename("consortion.task.FontIMoneyTxt");
         this._moneyText.text = LanguageMgr.GetTranslation("consortion.task.offer");
         this._caiText = ComponentFactory.Instance.creatComponentByStylename("consortion.task.FontCaiTxt");
         this._caiText.text = LanguageMgr.GetTranslation("consortion.task.Money");
         this._skillText = ComponentFactory.Instance.creatComponentByStylename("consortion.task.FontSkillTxt");
         this._skillText.text = LanguageMgr.GetTranslation("consortion.task.skillName");
         this._expTxt = ComponentFactory.Instance.creatComponentByStylename("consortion.task.EXPTxt");
         this._offerTxt = ComponentFactory.Instance.creatComponentByStylename("consortion.task.MoneyTxt");
         this._richesTxt = ComponentFactory.Instance.creatComponentByStylename("consortion.task.caiTxt");
         this._skillNameTxt = ComponentFactory.Instance.creatComponentByStylename("consortion.task.SkillTxt");
         this._contributionLbl = ComponentFactory.Instance.creatComponentByStylename("consortion.task.contributionLbl");
         this._contributionLbl.text = LanguageMgr.GetTranslation("consortion.task.contribution");
         this._contributionTxt = ComponentFactory.Instance.creatComponentByStylename("consortion.task.contributionTxt");
         this._badgeLbl = ComponentFactory.Instance.creatComponentByStylename("consortion.task.badgeLbl");
         this._badgeLbl.text = LanguageMgr.GetTranslation("consortion.task.badge");
         this._badgeText = ComponentFactory.Instance.creatComponentByStylename("consortion.task.badgeTxt");
         var _local_1:Bitmap = ComponentFactory.Instance.creatBitmap("asset.conortionTask.FontContent");
         this._contentTxt1 = ComponentFactory.Instance.creatComponentByStylename("consortion.task.contentTxt1");
         this._contentTxt2 = ComponentFactory.Instance.creatComponentByStylename("consortion.task.contentTxt2");
         this._contentTxt3 = ComponentFactory.Instance.creatComponentByStylename("consortion.task.contentTxt3");
         _local_5 = 0;
         while(_local_5 < 3)
         {
            _local_3 = ComponentFactory.Instance.creatCustomObject("ConsortiaMyTaskFinishItem");
            this._finishItemList.push(_local_3);
            this._vbox.addChild(_local_3);
            _local_5++;
         }
         addChild(_local_2);
         addChild(_local_6);
         addChild(this._vbox);
         addChild(this._myFinishTxt);
         addChild(this._expText);
         addChild(this._moneyText);
         addChild(this._caiText);
         addChild(this._skillText);
         addChild(this._contributionLbl);
         addChild(this._badgeLbl);
         addChild(this._badgeText);
         addChild(this._expTxt);
         addChild(this._offerTxt);
         addChild(this._richesTxt);
         addChild(this._skillNameTxt);
         addChild(this._contributionTxt);
         this._myReseBtn = ComponentFactory.Instance.creatComponentByStylename("consortion.submitTask.reset1");
         this._myReseBtn.text = LanguageMgr.GetTranslation("consortia.task.resetTable");
         addChild(this._myReseBtn);
         this._contributionRankBtn = ComponentFactory.Instance.creatComponentByStylename("consortion.submitTask.btn");
         PositionUtils.setPos(this._contributionRankBtn,"taskRank.taskRankBtn.posNotFisished");
         this._contributionRankBtn.text = LanguageMgr.GetTranslation("consortia.task.cRank");
         addChild(this._contributionRankBtn);
         this._delayTimeBtn = ComponentFactory.Instance.creatComponentByStylename("consortion.task.delayTimeBtn");
         this._delayTimeBtn.text = LanguageMgr.GetTranslation("consortia.task.delayTime");
         addChild(this._delayTimeBtn);
         _local_4 = PlayerManager.Instance.Self.Right;
         this._myReseBtn.visible = ConsortiaDutyManager.GetRight(_local_4,512);
         this._delayTimeBtn.visible = ConsortiaDutyManager.GetRight(_local_4,512);
         ConsortionModelManager.Instance.TaskModel.lockNum = 0;
      }
      
      private function initEvents() : void
      {
         this._myReseBtn.addEventListener("click",this.__resetClick);
         this._contributionRankBtn.addEventListener("click",this.__taskRankClick);
         this._delayTimeBtn.addEventListener("click",this.__delayTimeClick);
         PlayerManager.Instance.Self.addEventListener("propertychange",this.__propChange);
      }
      
      protected function __taskRankClick(_arg_1:MouseEvent) : void
      {
         SoundManager.instance.play("008");
         var _local_2:ConsortionTaskRank = ComponentFactory.Instance.creatComponentByStylename("consortion.taskRank.frame");
         LayerManager.Instance.addToLayer(_local_2,3,true,1);
      }
      
      private function __delayTimeClick(_arg_1:MouseEvent) : void
      {
         SoundManager.instance.play("008");
         if(PlayerManager.Instance.Self.bagLocked)
         {
            BaglockedManager.Instance.show();
            return;
         }
         var _local_2:int = ConsortionModelManager.Instance.TaskModel.taskInfo.level - 1;
         var _local_3:int = int(ServerConfigManager.instance.consortiaTaskDelayInfo[_local_2][0]);
         var _local_5:int = int(ServerConfigManager.instance.consortiaTaskDelayInfo[_local_2][1]);
         var _local_4:BaseAlerFrame = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("consortia.task.delayTime"),LanguageMgr.GetTranslation("consortia.task.delayTimeContent",_local_5,_local_3),LanguageMgr.GetTranslation("ok"),LanguageMgr.GetTranslation("cancel"),true,true,true,1);
         _local_4.moveEnable = false;
         _local_4.addEventListener("response",this._responseII);
      }
      
      private function _responseII(_arg_1:FrameEvent) : void
      {
         SoundManager.instance.play("008");
         (_arg_1.currentTarget as BaseAlerFrame).removeEventListener("response",this._responseII);
         if(_arg_1.responseCode == 2 || _arg_1.responseCode == 3)
         {
            SocketManager.Instance.out.sendReleaseConsortiaTask(6);
         }
      }
      
      private function getLockIdArr() : Array
      {
         var _local_2:int = 0;
         var _local_1:Array = [];
         _local_2 = 0;
         while(_local_2 < this._finishItemList.length)
         {
            if(this._finishItemList[_local_2].isLock)
            {
               _local_1.push(this._finishItemList[_local_2].lockId);
            }
            _local_2++;
         }
         return _local_1;
      }
      
      private function __resetClick(_arg_1:MouseEvent) : void
      {
         var _local_3:* = null;
         SoundManager.instance.play("008");
         if(PlayerManager.Instance.Self.bagLocked)
         {
            BaglockedManager.Instance.show();
            return;
         }
         if(ConsortionModelManager.Instance.TaskModel.taskInfo == null)
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("consortia.task.stopTable"));
         }
         var _local_2:Array = this.getLockIdArr();
         var _local_4:int = int(_local_2.length);
         if(Boolean(_local_4))
         {
            if(_local_4 == 1)
            {
               this._reSetTaskMoney = ConsortiaTaskView.RESET_MONEY + ServerConfigManager.instance.consortiaTaskPriceArr[0];
            }
            else if(_local_4 == 2)
            {
               this._reSetTaskMoney = ConsortiaTaskView.RESET_MONEY + ServerConfigManager.instance.consortiaTaskPriceArr[0] + ServerConfigManager.instance.consortiaTaskPriceArr[1];
            }
            _local_3 = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("consortia.task.resetTable"),LanguageMgr.GetTranslation("consortia.task.resetLuckContent",_local_2.length,this._reSetTaskMoney),LanguageMgr.GetTranslation("ok"),LanguageMgr.GetTranslation("cancel"),false,true,true,2,null,"SimpleAlert",30,true,1);
            _local_3.moveEnable = false;
            _local_3.addEventListener("response",this._responseI);
         }
         else
         {
            this._reSetTaskMoney = ConsortiaTaskView.RESET_MONEY;
            _local_3 = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("consortia.task.resetTable"),LanguageMgr.GetTranslation("consortia.task.resetContent",this._reSetTaskMoney),LanguageMgr.GetTranslation("ok"),LanguageMgr.GetTranslation("cancel"),false,true,true,2,null,"SimpleAlert",30,true,1);
            _local_3.moveEnable = false;
            _local_3.addEventListener("response",this._responseI);
         }
      }
      
      private function _responseI(_arg_1:FrameEvent) : void
      {
         (_arg_1.currentTarget as BaseAlerFrame).removeEventListener("response",this._responseI);
         if(_arg_1.responseCode == 2 || _arg_1.responseCode == 3)
         {
            if(ConsortionModelManager.Instance.TaskModel.taskInfo == null)
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("consortia.task.stopTable"));
               ChatManager.Instance.sysChatYellow(LanguageMgr.GetTranslation("consortia.task.stopTable"));
            }
            else
            {
               CheckMoneyUtils.instance.checkMoney(_arg_1.currentTarget.isBand,this._reSetTaskMoney,this.onCheckComplete);
            }
         }
         ObjectUtils.disposeObject(_arg_1.currentTarget as BaseAlerFrame);
      }
      
      protected function onCheckComplete() : void
      {
         var _local_1:Array = this.getLockIdArr();
         var _local_3:int = Boolean(_local_1[0]) ? int(_local_1[0]) : 0;
         var _local_2:int = Boolean(_local_1[1]) ? int(_local_1[1]) : 0;
         SocketManager.Instance.out.sendReleaseConsortiaTask(1,CheckMoneyUtils.instance.isBind,1,1,_local_3,_local_2);
         SocketManager.Instance.out.sendReleaseConsortiaTask(2);
      }
      
      private function __onNoMoneyResponse(_arg_1:FrameEvent) : void
      {
         SoundManager.instance.play("008");
         var _local_2:BaseAlerFrame = _arg_1.currentTarget as BaseAlerFrame;
         _local_2.removeEventListener("response",this.__onNoMoneyResponse);
         _local_2.disposeChildren = true;
         _local_2.dispose();
         _local_2 = null;
         if(_arg_1.responseCode == 3)
         {
            LeavePageManager.leaveToFillPath();
         }
      }
      
      private function removeEvents() : void
      {
         if(Boolean(this._myReseBtn))
         {
            this._myReseBtn.removeEventListener("click",this.__resetClick);
         }
         if(Boolean(this._delayTimeBtn))
         {
            this._delayTimeBtn.removeEventListener("click",this.__delayTimeClick);
         }
         if(Boolean(this._contributionRankBtn))
         {
            this._contributionRankBtn.removeEventListener("click",this.__taskRankClick);
         }
         PlayerManager.Instance.Self.removeEventListener("propertychange",this.__propChange);
      }
      
      private function __propChange(_arg_1:PlayerPropertyEvent) : void
      {
         var _local_2:int = 0;
         if(Boolean(_arg_1.changedProperties["Right"]))
         {
            _local_2 = PlayerManager.Instance.Self.Right;
            this._myReseBtn.visible = ConsortiaDutyManager.GetRight(_local_2,512);
            this._delayTimeBtn.visible = ConsortiaDutyManager.GetRight(_local_2,512);
         }
      }
      
      private function update() : void
      {
         var _local_11:int = 0;
         var _local_5:int = 0;
         var _local_10:int = 0;
         var _local_7:int = 0;
         var _local_8:int = PlayerManager.Instance.Self.Right;
         var _local_4:Boolean = true;
         var _local_6:int = int(this._taskInfo.itemList.length);
         _local_11 = 0;
         while(_local_11 < _local_6)
         {
            if(this._taskInfo.itemList[_local_11].currenValue - this._taskInfo.itemList[_local_11].targetValue < 0)
            {
               _local_4 = false;
               break;
            }
            _local_11++;
         }
         PositionUtils.setPos(this._contributionRankBtn,_local_4 ? "taskRank.taskRankBtn.posFisished" : "taskRank.taskRankBtn.posNotFisished");
         this._myReseBtn.visible = ConsortiaDutyManager.GetRight(_local_8,512) && !_local_4;
         this._delayTimeBtn.visible = ConsortiaDutyManager.GetRight(_local_8,512);
         var _local_3:Array = this.getLockIdArr();
         for(_local_5 = 0; _local_5 < this._finishItemList.length; )
         {
            if(Boolean(_local_3.length))
            {
               if(this._finishItemList[_local_5].isLock)
               {
                  _local_10 = 0;
                  while(_local_10 < this._taskInfo.itemList.length)
                  {
                     if(this._finishItemList[_local_5].taskId == this._taskInfo.itemList[_local_10]["id"])
                     {
                        this._finishItemList[_local_5].updateFinishTxt(this._taskInfo.itemList[_local_10]["currenValue"]);
                     }
                     _local_10++;
                  }
               }
               else
               {
                  while(_local_7 < this._taskInfo.itemList.length)
                  {
                     if(_local_3.indexOf(this._taskInfo.itemList[_local_7]["id"]) == -1)
                     {
                        this._finishItemList[_local_5].update(this._taskInfo.itemList[_local_7]["taskType"],this._taskInfo.itemList[_local_7]["content"],this._taskInfo.itemList[_local_7]["currenValue"],this._taskInfo.itemList[_local_7]["targetValue"],this._taskInfo.itemList[_local_7]["id"]);
                        _local_7++;
                        break;
                     }
                     _local_7++;
                  }
               }
            }
            else
            {
               this._finishItemList[_local_5].update(this._taskInfo.itemList[_local_5]["taskType"],this._taskInfo.itemList[_local_5]["content"],this._taskInfo.itemList[_local_5]["currenValue"],this._taskInfo.itemList[_local_5]["targetValue"],this._taskInfo.itemList[_local_5]["id"]);
            }
            _local_5++;
         }
         this._expTxt.text = this._taskInfo.exp.toString();
         this._offerTxt.text = this._taskInfo.offer.toString();
         this._richesTxt.text = this._taskInfo.riches.toString();
         this._contributionTxt.text = this._taskInfo.contribution.toString();
         var _local_9:ConsortionSkillInfo = ConsortionModelManager.Instance.model.getSkillInfoByID(this._taskInfo.buffID);
         if(_local_9 != null)
         {
            this._skillNameTxt.text = _local_9.name + "*1天";
         }
         this._contentTxt1.text = "1. " + this._taskInfo.itemList[0]["content"];
         this._contentTxt2.text = "2. " + this._taskInfo.itemList[1]["content"];
         this._contentTxt3.text = "3. " + this._taskInfo.itemList[2]["content"];
         var _local_2:Number = (this._taskInfo.itemList[0]["finishValue"] / this._taskInfo.itemList[0]["targetValue"] + this._taskInfo.itemList[1]["finishValue"] / this._taskInfo.itemList[1]["targetValue"] + this._taskInfo.itemList[2]["finishValue"] / this._taskInfo.itemList[2]["targetValue"]) / 3;
         var _local_1:int = _local_2 * 100;
         this._myFinishTxt.text = _local_1 + "%";
         this._badgeText.text = String(50 * (this._taskInfo.level + 1));
      }
      
      public function set taskInfo(_arg_1:ConsortiaTaskInfo) : void
      {
         this._taskInfo = _arg_1;
         this.update();
      }
      
      public function dispose() : void
      {
         var _local_1:int = 0;
         this.removeEvents();
         this._taskInfo = null;
         if(Boolean(this._myReseBtn))
         {
            ObjectUtils.disposeObject(this._myReseBtn);
         }
         this._myReseBtn = null;
         if(Boolean(this._contributionRankBtn))
         {
            ObjectUtils.disposeObject(this._contributionRankBtn);
         }
         this._contributionRankBtn = null;
         if(Boolean(this._delayTimeBtn))
         {
            ObjectUtils.disposeObject(this._delayTimeBtn);
         }
         this._delayTimeBtn = null;
         _local_1 = 0;
         while(this._finishItemList != null && _local_1 < this._finishItemList.length)
         {
            ObjectUtils.disposeObject(this._finishItemList[_local_1]);
            _local_1++;
         }
         this._finishItemList = null;
         if(Boolean(this._vbox))
         {
            ObjectUtils.disposeObject(this._vbox);
         }
         this._vbox = null;
         if(Boolean(this._myFinishTxt))
         {
            ObjectUtils.disposeObject(this._myFinishTxt);
         }
         this._myFinishTxt = null;
         if(Boolean(this._expText))
         {
            ObjectUtils.disposeObject(this._expText);
         }
         this._expText = null;
         if(Boolean(this._moneyText))
         {
            ObjectUtils.disposeObject(this._moneyText);
         }
         this._moneyText = null;
         if(Boolean(this._caiText))
         {
            ObjectUtils.disposeObject(this._caiText);
         }
         this._caiText = null;
         if(Boolean(this._skillText))
         {
            ObjectUtils.disposeObject(this._skillText);
         }
         this._skillText = null;
         if(Boolean(this._expTxt))
         {
            ObjectUtils.disposeObject(this._expTxt);
         }
         this._expTxt = null;
         if(Boolean(this._offerTxt))
         {
            ObjectUtils.disposeObject(this._offerTxt);
         }
         this._offerTxt = null;
         if(Boolean(this._richesTxt))
         {
            ObjectUtils.disposeObject(this._richesTxt);
         }
         this._richesTxt = null;
         if(Boolean(this._skillNameTxt))
         {
            ObjectUtils.disposeObject(this._skillNameTxt);
         }
         this._skillNameTxt = null;
         if(Boolean(this._contentTxt1))
         {
            ObjectUtils.disposeObject(this._contentTxt1);
         }
         this._contentTxt1 = null;
         if(Boolean(this._contentTxt2))
         {
            ObjectUtils.disposeObject(this._contentTxt2);
         }
         this._contentTxt2 = null;
         if(Boolean(this._contentTxt3))
         {
            ObjectUtils.disposeObject(this._contentTxt3);
         }
         this._contentTxt3 = null;
         ObjectUtils.disposeAllChildren(this);
         this._badgeLbl = null;
         this._badgeText = null;
         if(Boolean(parent))
         {
            parent.removeChild(this);
         }
      }
   }
}

