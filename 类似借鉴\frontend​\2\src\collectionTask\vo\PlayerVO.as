package collectionTask.vo
{
   import ddt.data.player.PlayerInfo;
   import ddt.view.sceneCharacter.SceneCharacterDirection;
   import flash.events.EventDispatcher;
   import flash.events.IEventDispatcher;
   import flash.geom.Point;
   
   public class PlayerVO extends EventDispatcher
   {
      
      private var _playerInfo:PlayerInfo;
      
      private var _playerPos:Point;
      
      private var _playerMoveSpeed:Number = 0.15;
      
      private var _sceneCharacterDirection:SceneCharacterDirection;
      
      private var _playerDirection:int = 3;
      
      private var _walkPath:Array = [];
      
      public var currentWalkStartPoint:Point;
      
      private var _isRobert:Boolean;
      
      public function PlayerVO(_arg_1:IEventDispatcher = null)
      {
         super(_arg_1);
         this._sceneCharacterDirection = SceneCharacterDirection.RB;
      }
      
      public function get scenePlayerDirection() : SceneCharacterDirection
      {
         if(!this._sceneCharacterDirection)
         {
            this._sceneCharacterDirection = SceneCharacterDirection.RB;
         }
         return this._sceneCharacterDirection;
      }
      
      public function set scenePlayerDirection(_arg_1:SceneCharacterDirection) : void
      {
         this._sceneCharacterDirection = _arg_1;
         switch(this._sceneCharacterDirection)
         {
            case SceneCharacterDirection.RT:
               this._playerDirection = 1;
               return;
            case SceneCharacterDirection.LT:
               this._playerDirection = 2;
               return;
            case SceneCharacterDirection.RB:
               this._playerDirection = 3;
               return;
            case SceneCharacterDirection.LB:
               this._playerDirection = 4;
               return;
            default:
               return;
         }
      }
      
      public function get playerPos() : Point
      {
         return this._playerPos;
      }
      
      public function set playerPos(_arg_1:Point) : void
      {
         this._playerPos = _arg_1;
      }
      
      public function get playerMoveSpeed() : Number
      {
         return this._playerMoveSpeed;
      }
      
      public function set playerMoveSpeed(_arg_1:Number) : void
      {
         this._playerMoveSpeed = _arg_1;
      }
      
      public function get playerInfo() : PlayerInfo
      {
         return this._playerInfo;
      }
      
      public function set playerInfo(_arg_1:PlayerInfo) : void
      {
         this._playerInfo = _arg_1;
      }
      
      public function get walkPath() : Array
      {
         return this._walkPath;
      }
      
      public function set walkPath(_arg_1:Array) : void
      {
         this._walkPath = _arg_1;
      }
      
      public function dispose() : void
      {
         while(Boolean(this._walkPath) && this._walkPath.length > 0)
         {
            this._walkPath.shift();
         }
         this._walkPath = null;
         this._playerPos = null;
         this._sceneCharacterDirection = null;
      }
      
      public function get isRobert() : Boolean
      {
         return this._isRobert;
      }
      
      public function set isRobert(_arg_1:Boolean) : void
      {
         this._isRobert = _arg_1;
      }
   }
}

