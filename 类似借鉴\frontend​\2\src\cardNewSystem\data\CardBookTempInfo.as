package cardNewSystem.data
{
   import ddt.manager.LanguageMgr;
   
   public class CardBookTempInfo
   {
      
      public var TemplateId:int;
      
      public var TemplateName:String;
      
      public var Profile:int;
      
      public var Hp:int;
      
      public var Attack:int;
      
      public var Defence:int;
      
      public var Agility:int;
      
      public var Lucky:int;
      
      public var MagicAttack:int;
      
      public var MagicDefence:int;
      
      public var Crit:int;
      
      public var SunderArmor:int;
      
      public var ViolenceInjury:int;
      
      public var Speed:int;
      
      public var TricKill:int;
      
      public var Damage:int;
      
      public var Armor:int;
      
      public var Desc:String;
      
      public function CardBookTempInfo()
      {
         super();
      }
      
      public function getProArr() : Array
      {
         var _local_1:Array = [];
         var _local_2:Array = LanguageMgr.GetTranslation("tank.newCard.ProName").split(",");
         if(this.Hp > 0)
         {
            _local_1.push({
               "proName":_local_2[0],
               "count":this.Hp
            });
         }
         if(this.Attack > 0)
         {
            _local_1.push({
               "proName":_local_2[1],
               "count":this.Attack
            });
         }
         if(this.Defence > 0)
         {
            _local_1.push({
               "proName":_local_2[2],
               "count":this.Defence
            });
         }
         if(this.Agility > 0)
         {
            _local_1.push({
               "proName":_local_2[3],
               "count":this.Agility
            });
         }
         if(this.Lucky > 0)
         {
            _local_1.push({
               "proName":_local_2[4],
               "count":this.Lucky
            });
         }
         if(this.MagicAttack > 0)
         {
            _local_1.push({
               "proName":_local_2[5],
               "count":this.MagicAttack
            });
         }
         if(this.MagicDefence > 0)
         {
            _local_1.push({
               "proName":_local_2[6],
               "count":this.MagicDefence
            });
         }
         if(this.Crit > 0)
         {
            _local_1.push({
               "proName":_local_2[7],
               "count":this.Crit
            });
         }
         if(this.SunderArmor > 0)
         {
            _local_1.push({
               "proName":_local_2[8],
               "count":this.SunderArmor
            });
         }
         if(this.ViolenceInjury > 0)
         {
            _local_1.push({
               "proName":_local_2[9],
               "count":this.ViolenceInjury
            });
         }
         if(this.Speed > 0)
         {
            _local_1.push({
               "proName":_local_2[10],
               "count":this.Speed
            });
         }
         if(this.TricKill > 0)
         {
            _local_1.push({
               "proName":_local_2[11],
               "count":this.TricKill
            });
         }
         if(this.Damage > 0)
         {
            _local_1.push({
               "proName":_local_2[12],
               "count":this.Damage
            });
         }
         if(this.Armor > 0)
         {
            _local_1.push({
               "proName":_local_2[13],
               "count":this.Armor
            });
         }
         return _local_1;
      }
   }
}

