package bagAndInfo.bag.ring.data
{
   import com.pickgliss.loader.DataAnalyzer;
   import com.pickgliss.utils.ObjectUtils;
   import flash.utils.Dictionary;
   
   public class RingDataAnalyzer extends DataAnalyzer
   {
      
      private var _data:Dictionary;
      
      public function RingDataAnalyzer(_arg_1:Function)
      {
         super(_arg_1);
      }
      
      override public function analyze(_arg_1:*) : void
      {
         var _local_5:int = 0;
         var _local_3:* = null;
         var _local_4:* = null;
         var _local_2:XML = new XML(_arg_1);
         this._data = new Dictionary();
         if(_local_2.@value == "true")
         {
            _local_3 = _local_2..Item;
            RingSystemData.TotalLevel = _local_3.length();
            _local_5 = 0;
            while(_local_5 < RingSystemData.TotalLevel)
            {
               _local_4 = new RingSystemData();
               ObjectUtils.copyPorpertiesByXML(_local_4,_local_3[_local_5]);
               this._data[_local_4.Level] = _local_4;
               _local_5++;
            }
            onAnalyzeComplete();
         }
         else
         {
            message = _local_2.@message;
            onAnalyzeError();
         }
      }
      
      public function get data() : Dictionary
      {
         return this._data;
      }
   }
}

