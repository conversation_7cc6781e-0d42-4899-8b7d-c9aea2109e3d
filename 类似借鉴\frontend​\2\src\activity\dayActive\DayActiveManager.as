package activity.dayActive
{
   import activity.dayActive.analyzer.DayActiveQuestAnalyzer;
   import activity.dayActive.data.DayActiveInfo;
   import activity.dayActive.data.DayActiveModel;
   import activity.dayActive.data.DayActiveShopInfo;
   import activity.dayActive.event.DayActiveEvent;
   import bagAndInfo.BagAndInfoManager;
   import com.pickgliss.ui.ComponentSetting;
   import com.pickgliss.ui.LayerManager;
   import com.pickgliss.utils.ClassUtils;
   import cryptBoss.CryptBossManager;
   import ddt.bagStore.BagStore;
   import ddt.events.PkgEvent;
   import ddt.manager.LanguageMgr;
   import ddt.manager.LeavePageManager;
   import ddt.manager.MessageTipManager;
   import ddt.manager.PlayerManager;
   import ddt.manager.ServerConfigManager;
   import ddt.manager.SocketManager;
   import ddt.manager.StateManager;
   import ddt.utils.AssetModuleLoader;
   import ddtBuried.BuriedManager;
   import dreamlandChallenge.DreamlandChallengeManager;
   import elf.ElfManager;
   import explorerManual.ExplorerManualManager;
   import flash.display.Sprite;
   import flash.events.EventDispatcher;
   import gypsyShop.model.GypsyNPCModel;
   import hall.HallStateGotoManager;
   import hallIcon.HallIconManager;
   import horse.HorseManager;
   import limitedTimeGift.LimitedTimeGiftManager;
   import road7th.comm.PackageIn;
   import worldboss.WorldBossManager;
   
   public class DayActiveManager extends EventDispatcher
   {
      
      private static var _instance:DayActiveManager;
      
      public static const REWARD_CONDITION:Array = [30,60,90,120,150];
      
      private var _model:DayActiveModel;
      
      public var isWorship:Boolean = false;
      
      public var rewardData:String;
      
      public function DayActiveManager()
      {
         super();
         this._model = new DayActiveModel();
      }
      
      public static function get Instance() : DayActiveManager
      {
         if(_instance == null)
         {
            _instance = new DayActiveManager();
         }
         return _instance;
      }
      
      public function setup() : void
      {
         SocketManager.Instance.addEventListener(PkgEvent.format(631,1),this.initActiveList);
         SocketManager.Instance.addEventListener(PkgEvent.format(631,4),this.__onResetQuest);
         SocketManager.Instance.addEventListener(PkgEvent.format(631,6),this.__shopInfo);
         SocketManager.Instance.addEventListener(PkgEvent.format(631,8),this.__scoreInfo);
      }
      
      public function analyzerQuestInfo(_arg_1:DayActiveQuestAnalyzer) : void
      {
         this._model.activeQuestData = _arg_1.data;
         this.analyzerActiveReward();
      }
      
      private function analyzerActiveReward() : void
      {
         var _local_2:int = 0;
         var _local_1:Array = ServerConfigManager.instance.dayActiveRewardTemplate.split("|");
         _local_2 = 0;
         while(_local_2 < _local_1.length)
         {
            this._model.addRewardData(_local_1[_local_2]);
            _local_2++;
         }
      }
      
      private function __scoreInfo(_arg_1:PkgEvent) : void
      {
         var _local_2:PackageIn = _arg_1.pkg;
         this._model.shopScore = _local_2.readInt();
         dispatchEvent(new DayActiveEvent("updatescore"));
      }
      
      private function __shopInfo(_arg_1:PkgEvent) : void
      {
         var _local_5:int = 0;
         var _local_4:* = null;
         var _local_3:Array = [];
         var _local_2:PackageIn = _arg_1.pkg;
         var _local_6:int = _local_2.readInt();
         _local_5 = 0;
         while(_local_5 < _local_6)
         {
            _local_4 = new DayActiveShopInfo();
            _local_4.shopId = _local_2.readInt();
            _local_4.price = _local_2.readInt();
            _local_4.tempId = _local_2.readInt();
            _local_4.itemCount = _local_2.readInt();
            _local_4.validityDay = _local_2.readInt();
            _local_4.isBind = _local_2.readBoolean();
            _local_4.canBuyCount = _local_2.readInt();
            _local_3.push(_local_4);
            _local_5++;
         }
         this._model.lastRefreshDate = _arg_1.pkg.readDate();
         this._model.shopList = _local_3;
         this._model.shopList.sortOn("shopId",16);
         dispatchEvent(new DayActiveEvent("updateshop"));
      }
      
      private function initActiveList(_arg_1:PkgEvent) : void
      {
         var _local_6:int = 0;
         var _local_5:* = null;
         var _local_3:* = null;
         var _local_2:Array = [];
         this._model.activeOldValue = _arg_1.pkg.readInt();
         this._model.activeValue = _arg_1.pkg.readInt();
         this._model.resetQuestCount = _arg_1.pkg.readInt();
         this._model.clearList();
         var _local_4:int = _arg_1.pkg.readInt();
         _local_6 = 0;
         while(_local_6 < _local_4)
         {
            _local_5 = new DayActiveInfo();
            _local_5.id = _arg_1.pkg.readInt();
            _local_5.count = _arg_1.pkg.readInt();
            _local_5.isGetReward = _arg_1.pkg.readBoolean();
            _local_3 = this._model.getActiveQuestInfoByID(_local_5.id);
            _local_5.isComplete = _local_3.Count <= _local_5.count;
            _local_5.IsMust = _local_3.IsMust;
            _local_5.sort = _local_6;
            this._model.addInfo(_local_5);
            _local_6++;
         }
         this._model.sortList();
         this.rewardData = _arg_1.pkg.readUTF();
         this.isWorship = _arg_1.pkg.readBoolean();
         this._model.updateRewardData(this.rewardData);
         this._model.fastComlateTaskCount = _arg_1.pkg.readInt();
         dispatchEvent(new DayActiveEvent("updatelist"));
      }
      
      private function __onResetQuest(_arg_1:PkgEvent) : void
      {
         var _local_2:int = _arg_1.pkg.readInt();
         var _local_3:DayActiveInfo = this._model.getInfoByID(_local_2);
         _local_3.id = _arg_1.pkg.readInt();
         _local_3.count = _arg_1.pkg.readInt();
         _local_3.isGetReward = _arg_1.pkg.readBoolean();
         this._model.resetQuestCount = _arg_1.pkg.readInt();
         dispatchEvent(new DayActiveEvent("updateitemo",_local_3.sort));
      }
      
      public function show() : void
      {
         AssetModuleLoader.addModelLoader("dayactive",5);
         AssetModuleLoader.startCodeLoader(this.onLoadComplete);
      }
      
      private function onLoadComplete() : void
      {
         SocketManager.Instance.out.sendDayActiveInfo();
         var _local_1:Sprite = ClassUtils.CreatInstance("activity.dayActive.view.DayActiveMainView");
         LayerManager.Instance.addToLayer(_local_1,3,true,1);
         SocketManager.Instance.out.sendDayActiveShopInfo();
         SocketManager.Instance.out.sendDayActiveScoreInfo();
      }
      
      public function checkIsSameWeek(_arg_1:Date) : Boolean
      {
         var _local_3:Date = new Date();
         var _local_5:Number = _local_3.getDay();
         var _local_4:Date = new Date(_local_3.getTime() + (7 - (_local_5 || 7)) * 24 * 60 * 60 * 1000);
         _local_4.setHours(23,59,59,999);
         var _local_6:Date = new Date(_local_3.getTime() - ((_local_5 || 7) - 1) * 24 * 60 * 60 * 1000);
         _local_6.setHours(0,0,0,0);
         var _local_2:Number = _arg_1.getTime();
         return _local_2 <= _local_4.getTime() && _local_2 >= _local_6.getTime();
      }
      
      public function showIcon() : void
      {
         HallIconManager.instance.updateSwitchHandler("DayActive",true);
      }
      
      public function jump(_arg_1:int) : void
      {
         var _local_2:int = 0;
         switch(_arg_1)
         {
            case 0:
               return;
            case 1:
               if(PlayerManager.Instance.Self.Grade < 19)
               {
                  MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.functionLimitTip",19));
                  return;
               }
               break;
            case 2:
               if(PlayerManager.Instance.Self.Grade < 25)
               {
                  MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.functionLimitTip",25));
                  return;
               }
               BuriedManager.Instance.enter();
               break;
            case 3:
               if(PlayerManager.Instance.Self.Grade < 30)
               {
                  MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.functionLimitTip",30));
                  return;
               }
               HallStateGotoManager.instance.gotoLabyrinthView();
               break;
            case 4:
               if(!WorldBossManager.Instance.isOpen)
               {
                  MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.specialGift.lotteryTip"));
                  return;
               }
               HallStateGotoManager.instance.gotoWorldBossView();
               break;
            case 5:
               if(!GypsyNPCModel.getInstance().isStart())
               {
                  MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.hall.scancode.avtivityNotOpen"));
                  return;
               }
               HallStateGotoManager.instance.gotoGypsyNPCView();
               break;
            case 6:
               HallStateGotoManager.instance.addPKRoom();
               break;
            case 7:
               if(PlayerManager.Instance.Self.Grade < 12)
               {
                  MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.functionLimitTip",12));
                  return;
               }
               HorseManager.instance.show();
               break;
            case 8:
               if(PlayerManager.Instance.Self.Grade < 6)
               {
                  MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.functionLimitTip",6));
                  return;
               }
               StateManager.setState("shop");
               ComponentSetting.SEND_USELOG_ID(1);
               break;
            case 9:
               if(PlayerManager.Instance.Self.Grade < 16)
               {
                  MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.functionLimitTip",16));
                  return;
               }
               BagAndInfoManager.Instance.showBagAndInfo(4);
               if(!PlayerManager.Instance.Self.IsWeakGuildFinish(38))
               {
                  SocketManager.Instance.out.syncWeakStep(38);
                  SocketManager.Instance.out.syncWeakStep(8);
               }
               break;
            case 10:
               if(PlayerManager.Instance.Self.Grade < 17)
               {
                  MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.functionLimitTip",17));
                  return;
               }
               HallStateGotoManager.instance.gotoConsortia();
               break;
            case 11:
               HallStateGotoManager.instance.gotoDungeonView();
               break;
            case 12:
               CryptBossManager.instance.show();
               break;
            case 13:
               BagAndInfoManager.Instance.showBagAndInfo(8);
               break;
            case 14:
               HallStateGotoManager.instance.gotoFashionView();
               break;
            case 15:
               if(PlayerManager.Instance.Self.Grade < 19)
               {
                  MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.functionLimitTip",19));
                  return;
               }
               break;
            case 16:
               if(PlayerManager.Instance.Self.Grade < 13)
               {
                  MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.functionLimitTip",13));
                  return;
               }
               BagAndInfoManager.Instance.showBagAndInfo(1);
               break;
            case 17:
               if(PlayerManager.Instance.Self.Grade < 23)
               {
                  MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.functionLimitTip",23));
                  return;
               }
               if(!DreamlandChallengeManager.instance.isOpen)
               {
                  MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.hall.scancode.avtivityNotOpen"));
                  return;
               }
               HallStateGotoManager.instance.gotoDreamLandView();
               break;
            case 18:
            case 19:
               break;
            case 20:
               BagAndInfoManager.Instance.showBagAndInfo();
               break;
            case 21:
               this.gotoStore();
               break;
            case 22:
               HallStateGotoManager.instance.gotoTaskView();
               break;
            case 23:
            case 24:
               break;
            case 25:
               this.gotoStore("bag_store",1);
               break;
            case 26:
               this.gotoStore("forge_store",3);
               break;
            case 27:
               this.gotoStore("forge_store",2);
               break;
            case 28:
               this.gotoStore("fine_store");
               break;
            case 29:
               this.gotoStore("fine_store",1);
               break;
            case 30:
               BagAndInfoManager.Instance.showBagAndInfo(7);
               break;
            case 31:
               HorseManager.instance.show();
               break;
            case 32:
               ExplorerManualManager.instance.show();
               break;
            case 33:
               HallStateGotoManager.instance.gotoFriendCenterView();
               break;
            case 34:
               HallStateGotoManager.instance.gotoAuctionView();
               break;
            case 35:
               LeavePageManager.leaveToFillPath();
               break;
            case 36:
               HallStateGotoManager.instance.gotoPetFameView();
               break;
            case 37:
               ElfManager.instance.show(2);
               break;
            case 38:
               if(LimitedTimeGiftManager.instance.activeState)
               {
                  LimitedTimeGiftManager.instance.show();
               }
               break;
            case 39:
               HallStateGotoManager.instance.gotoDayActivity();
               break;
            case 41:
         }
         if(_arg_1 != 39)
         {
            dispatchEvent(new DayActiveEvent("close"));
         }
      }
      
      private function gotoStore(_arg_1:String = "bag_store", _arg_2:int = 0) : void
      {
         if(PlayerManager.Instance.Self.Grade < 5)
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.functionLimitTip",5));
            return;
         }
         BagStore.instance.openStore(_arg_1,_arg_2);
      }
      
      public function get model() : DayActiveModel
      {
         return this._model;
      }
   }
}

