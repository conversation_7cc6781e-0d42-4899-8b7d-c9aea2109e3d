package caveloot.data
{
   public class CaveLootCnfInfo
   {
      
      public var Num:int;
      
      public var OpenSwitch:int;
      
      public var AreaRankServerType:int;
      
      public var StartDate:String;
      
      public var EndDate:String;
      
      public var ShowDate:String;
      
      public var Params1:String;
      
      public var Params2:String;
      
      public var Params3:String;
      
      public var Params4:String;
      
      public var Params5:String;
      
      public var PrivateParams:String;
      
      public var Name:String;
      
      public var RankAreaAward:String;
      
      public var RankGsAward:String;
      
      public function CaveLootCnfInfo()
      {
         super();
      }
   }
}

