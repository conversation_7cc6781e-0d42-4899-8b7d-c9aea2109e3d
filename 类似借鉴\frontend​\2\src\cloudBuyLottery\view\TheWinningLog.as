package cloudBuyLottery.view
{
   import cloudBuyLottery.CloudBuyLotteryManager;
   import com.pickgliss.events.FrameEvent;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.controls.Frame;
   import com.pickgliss.ui.controls.ScrollPanel;
   import com.pickgliss.ui.controls.container.VBox;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.manager.LanguageMgr;
   import ddt.manager.SoundManager;
   import flash.display.Bitmap;
   
   public class TheWinningLog extends Frame
   {
      
      private var SHOP_ITEM_NUM:int;
      
      private var itemList:Array;
      
      private var _list:VBox;
      
      private var _panel:ScrollPanel;
      
      private var _logArr:Array;
      
      private var _bg:Bitmap;
      
      public function TheWinningLog()
      {
         super();
         this.initView();
         this.loadList();
         this.initEvent();
      }
      
      private function initView() : void
      {
         var _local_2:int = 0;
         var _local_1:* = null;
         this._bg = ComponentFactory.Instance.creatBitmap("asset.IndividualLottery.dituBG");
         addToContent(this._bg);
         _titleText = LanguageMgr.GetTranslation("TheWinningLog.titleText");
         this._list = ComponentFactory.Instance.creatComponentByStylename("TheWinningLog.goodsListBox");
         this._list.spacing = 0;
         this._panel = ComponentFactory.Instance.creatComponentByStylename("TheWinningLog.right.scrollpanel");
         this._panel.x = 0;
         this._panel.y = -4;
         this._panel.width = 210;
         this._panel.height = 465;
         this.itemList = [];
         this._logArr = CloudBuyLotteryManager.Instance.logArr;
         if(this._logArr != null)
         {
            this.SHOP_ITEM_NUM = this._logArr.length;
            if(this.SHOP_ITEM_NUM <= 0)
            {
               return;
            }
            _local_2 = 0;
            while(_local_2 < this.SHOP_ITEM_NUM)
            {
               _local_1 = ComponentFactory.Instance.creatCustomObject("TheWinningLog.WinningLogListItem");
               this.itemList.push(_local_1);
               this.itemList[_local_2].initView(this._logArr[_local_2].nickName,_local_2);
               this.itemList[_local_2].y = (this.itemList[_local_2].height + 1) * _local_2;
               this._list.addChild(this.itemList[_local_2]);
               _local_2++;
            }
         }
         this._panel.setView(this._list);
         addToContent(this._panel);
         this._panel.invalidateViewport();
      }
      
      private function initEvent() : void
      {
         addEventListener("response",this.__responseHandler);
      }
      
      private function removeEvent() : void
      {
         removeEventListener("response",this.__responseHandler);
      }
      
      private function loadList() : void
      {
         this.setList(CloudBuyLotteryManager.Instance.model.myGiftData);
      }
      
      private function setList(_arg_1:Vector.<WinningLogItemInfo>) : void
      {
         var _local_2:int = 0;
         if(_arg_1 == null)
         {
            return;
         }
         this.clearitems();
         _local_2 = 0;
         while(_local_2 < this.SHOP_ITEM_NUM)
         {
            if(!_arg_1)
            {
               break;
            }
            if(_local_2 < _arg_1.length && Boolean(_arg_1[_local_2]))
            {
               this.itemList[_local_2].shopItemInfo = _arg_1[_local_2];
               this.itemList[_local_2].itemID = _arg_1[_local_2].TemplateID;
            }
            _local_2++;
         }
      }
      
      private function clearitems() : void
      {
         var _local_1:int = 0;
         if(this.itemList[_local_1] == null)
         {
            return;
         }
         _local_1 = 0;
         while(_local_1 < this.SHOP_ITEM_NUM)
         {
            this.itemList[_local_1].shopItemInfo = null;
            _local_1++;
         }
      }
      
      private function disposeItems() : void
      {
         var _local_1:int = 0;
         if(Boolean(this.itemList))
         {
            _local_1 = 0;
            while(_local_1 < this.itemList.length)
            {
               ObjectUtils.disposeObject(this.itemList[_local_1]);
               this.itemList[_local_1] = null;
               _local_1++;
            }
            this.itemList = null;
         }
      }
      
      private function __responseHandler(_arg_1:FrameEvent) : void
      {
         if(_arg_1.responseCode == 0 || _arg_1.responseCode == 1)
         {
            SoundManager.instance.play("008");
            this.dispose();
         }
      }
      
      override public function dispose() : void
      {
         this.removeEvent();
         this.disposeItems();
         CloudBuyLotteryFrame.logFrameFlag = false;
         ObjectUtils.disposeAllChildren(this);
         super.dispose();
      }
   }
}

