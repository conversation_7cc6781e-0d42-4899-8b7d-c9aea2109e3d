package com.pickgliss.loader
{
   import com.pickgliss.ui.ComponentSetting;
   import com.pickgliss.utils.ClassUtils;
   import flash.utils.Dictionary;
   
   public class CodeLoader
   {
      
      private static var _loadedDic:Dictionary = new Dictionary();
      
      private const DDT_CLASS_PATH:String = "DDT_Core";
      
      private var _onLoaded:Function;
      
      private var _url:String;
      
      private var _onProgress:Function;
      
      private var _coreLoader:BaseLoader;
      
      public function CodeLoader()
      {
         super();
      }
      
      public static function loaded(_arg_1:String) : Boolean
      {
         return _loadedDic[_arg_1] != null;
      }
      
      public static function removeURL(_arg_1:String) : void
      {
         delete _loadedDic[_arg_1];
      }
      
      public static function addLoadURL(_arg_1:String) : void
      {
         _loadedDic[_arg_1] = 1;
      }
      
      public function loadPNG(_arg_1:String, _arg_2:Function, _arg_3:Function) : void
      {
         this._url = _arg_1;
         this._onLoaded = _arg_2;
         this._onProgress = _arg_3;
         this.startLoad();
      }
      
      public function stop() : void
      {
         this._coreLoader.removeEventListener("complete",this.__onloadCoreComplete);
         this._coreLoader.removeEventListener("progress",this.__onLoadCoreProgress);
      }
      
      private function startLoad() : void
      {
         var _local_1:String = ComponentSetting.FLASHSITE + this._url;
         this._coreLoader = LoadResourceManager.Instance.createLoader(_local_1,4);
         this._coreLoader.addEventListener("complete",this.__onloadCoreComplete);
         this._coreLoader.addEventListener("progress",this.__onLoadCoreProgress);
         LoadResourceManager.Instance.startLoad(this._coreLoader);
      }
      
      protected function __onLoadCoreProgress(_arg_1:LoaderEvent) : void
      {
         this._onProgress(_arg_1.loader.progress);
      }
      
      private function __onloadCoreComplete(_arg_1:LoaderEvent) : void
      {
         var _local_3:* = undefined;
         _arg_1.loader.removeEventListener("complete",this.__onloadCoreComplete);
         _arg_1.loader.removeEventListener("progress",this.__onLoadCoreProgress);
         var _local_2:* = ClassUtils.CreatInstance("DDT_Core");
         if(_local_2 != null)
         {
            LoaderSavingManager.saveFilesToLocal();
            _local_3 = _local_2;
            _local_3["setup"]();
            _loadedDic[this._url] = 1;
            if(this._onLoaded != null)
            {
               this._onLoaded();
            }
            this._coreLoader = null;
            this._onLoaded = null;
            this._onProgress = null;
            return;
         }
         throw "断网了，请刷新页面重试。";
      }
   }
}

