package com.crypto
{
   import flash.utils.ByteArray;
   
   public class New<PERSON>rypto
   {
      
      private static const _FLAG:String = "^_^";
      
      private static const _SKIP_INDEX:int = 16;
      
      public function NewCrypto()
      {
         super();
      }
      
      public static function decry(_arg_1:ByteArray) : ByteArray
      {
         var _local_6:ByteArray = null;
         if(!isEncryed(_arg_1))
         {
            return _arg_1;
         }
         var _local_5:int = int(_arg_1.position);
         _arg_1.position = 0;
         _arg_1.readUTF();
         var _local_3:ByteArray = new ByteArray();
         _arg_1.readBytes(_local_3,0,16);
         var _local_4:int = _arg_1.readByte();
         _local_4 = ~_local_4;
         _local_6 = new ByteArray();
         _arg_1.readBytes(_local_6,0,_local_6.bytesAvailable);
         var _local_2:ByteArray = new ByteArray();
         _local_2.writeBytes(_local_3);
         _local_2.writeByte(_local_4);
         _local_2.writeBytes(_local_6);
         _arg_1.position = _local_5;
         _local_3.clear();
         _local_6.clear();
         return _local_2;
      }
      
      public static function isEncryed(_arg_1:ByteArray) : Boolean
      {
         var _local_3:Boolean = false;
         var _local_2:* = null;
         var _local_4:int = int(_arg_1.position);
         _arg_1.position = 0;
         try
         {
            _local_2 = _arg_1.readUTF();
         }
         catch(e:Error)
         {
         }
         if(_local_2 == "^_^")
         {
            _local_3 = true;
         }
         _arg_1.position = _local_4;
         return _local_3;
      }
   }
}

