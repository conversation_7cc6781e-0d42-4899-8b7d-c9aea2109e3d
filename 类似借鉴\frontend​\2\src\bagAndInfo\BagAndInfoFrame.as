package bagAndInfo
{
   import bagAndInfo.bag.BagView;
   import bagAndInfo.info.PlayerInfoView;
   import beadSystem.beadSystemManager;
   import com.pickgliss.events.UIModuleEvent;
   import com.pickgliss.loader.UIModuleLoader;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.LayerManager;
   import com.pickgliss.ui.core.Disposeable;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.data.goods.ItemTemplateInfo;
   import ddt.data.player.SelfInfo;
   import ddt.events.CEvent;
   import ddt.events.CellEvent;
   import ddt.manager.DraftManager;
   import ddt.manager.PlayerManager;
   import ddt.view.UIModuleSmallLoading;
   import flash.display.Sprite;
   import flash.events.Event;
   import playerDress.PlayerDressManager;
   import playerDress.event.PlayerDressEvent;
   import texpSystem.controller.TexpManager;
   
   public class BagAndInfoFrame extends Sprite implements Disposeable
   {
      
      private var _info:SelfInfo;
      
      public var _infoView:PlayerInfoView;
      
      public var bagView:BagView;
      
      private var _beadInfoView:Sprite;
      
      private var _playerDressView:Sprite;
      
      private var _currentType:int;
      
      private var _visible:Boolean = false;
      
      private var _isFirstOpenBead:Boolean = true;
      
      private var _isLoadBeadComplete:Boolean = false;
      
      private var _isLoadStoreComplete:Boolean = false;
      
      public function BagAndInfoFrame()
      {
         super();
         this.initView();
         this.initEvents();
      }
      
      private function initView() : void
      {
         this.bagView = ComponentFactory.Instance.creatCustomObject("bagFrameBagView");
         addChild(this.bagView);
         this._infoView = ComponentFactory.Instance.creatCustomObject("bagAndInfoPersonalInfoView");
         this._infoView.showSelfOperation = true;
         addChild(this._infoView);
      }
      
      private function initEvents() : void
      {
         this.bagView.addEventListener("dragStart",this.__startShine);
         this.bagView.addEventListener("dragStop",this.__stopShine);
         this.bagView.addEventListener("tabChange",this.__changeHandler);
      }
      
      private function removeEvents() : void
      {
         this.bagView.removeEventListener("dragStart",this.__startShine);
         this.bagView.removeEventListener("dragStop",this.__stopShine);
         this.bagView.removeEventListener("tabChange",this.__changeHandler);
         PlayerDressManager.instance.removeEventListener("dressViewComplete",this.showPlayerDressView);
      }
      
      public function set isScreenFood(_arg_1:Boolean) : void
      {
         this.bagView.isScreenFood = _arg_1;
      }
      
      public function switchShow(_arg_1:int, _arg_2:int = 0) : void
      {
         this.info = PlayerManager.Instance.Self;
         this._currentType = _arg_1;
         this.bagView.enableOrdisableSB(true);
         this.bagView.showOrHideSB(true);
         if(_arg_1 == 0)
         {
            if(TexpManager.Instance.isShow("texpView"))
            {
               TexpManager.Instance.changeVisible("texpView",false);
            }
            this._infoView.visible = true;
            if(DraftManager.instance.showDraft)
            {
               this.bagType = 8;
               this.bagView.showDressSelectedBtnOnly(true);
            }
            else
            {
               this.bagType = 0;
            }
            this.bagType = _arg_2;
            this.bagView.isNeedCard(true);
            this.bagView.tableEnable = true;
            this.bagView.cardbtnVible = false;
            this.bagView.sortBagEnable = true;
            this.bagView.breakBtnEnable = true;
            this.bagView.sortBagFilter = ComponentFactory.Instance.creatFilters("lightFilter");
            this.bagView.breakBtnFilter = ComponentFactory.Instance.creatFilters("lightFilter");
            this._infoView.visible = _arg_2 == 8 ? false : true;
         }
         else if(_arg_1 == 3)
         {
            this._infoView.visible = false;
            this.bagView.tableEnable = false;
            this.bagView.isNeedCard(false);
            this.bagView.cardbtnVible = true;
            this.bagView.cardbtnFilter = ComponentFactory.Instance.creatFilters("grayFilter");
            this.bagView.sortBagEnable = false;
            this.bagView.breakBtnEnable = false;
            this.bagView.sortBagFilter = ComponentFactory.Instance.creatFilters("grayFilter");
            this.bagView.breakBtnFilter = ComponentFactory.Instance.creatFilters("grayFilter");
            this.bagView.enableDressSelectedBtn(false);
            this.showTexpView();
         }
         else if(_arg_1 == 5)
         {
            this._infoView.visible = false;
            this.bagView.tableEnable = false;
            this.bagView.isNeedCard(false);
            this.bagView.cardbtnVible = true;
            this.bagView.sortBagEnable = false;
            this.bagView.breakBtnEnable = false;
            this.bagView.sortBagFilter = ComponentFactory.Instance.creatFilters("grayFilter");
            this.bagView.cardbtnFilter = ComponentFactory.Instance.creatFilters("grayFilter");
            this.bagView.breakBtnFilter = ComponentFactory.Instance.creatFilters("grayFilter");
            this.bagView.enableDressSelectedBtn(false);
         }
         else if(_arg_1 == 21)
         {
            this._infoView.visible = false;
            this.bagView.isNeedCard(true);
            this.bagView.tableEnable = true;
            this.bagView.cardbtnVible = false;
            this.bagView.sortBagEnable = true;
            this.bagView.breakBtnEnable = true;
            this.bagView.sortBagFilter = ComponentFactory.Instance.creatFilters("lightFilter");
            this.bagView.breakBtnFilter = ComponentFactory.Instance.creatFilters("lightFilter");
            this.bagType = 21;
            this._currentType = 21;
            this.showBeadInfoView();
            this.bagView.enableOrdisableSB(false);
         }
      }
      
      public function clearTexpInfo() : void
      {
         if(TexpManager.Instance.isShow("texpView"))
         {
            TexpManager.Instance.cleanInfo();
         }
      }
      
      private function showTexpView() : void
      {
         if(!TexpManager.Instance.isShow("texpView"))
         {
            TexpManager.Instance.showTexpView("texpView",this);
         }
         this.bagType = 1;
         TexpManager.Instance.changeVisible("texpView",true);
      }
      
      private function __onPetsSmallLoadingClose(_arg_1:Event) : void
      {
         UIModuleSmallLoading.Instance.hide();
         UIModuleSmallLoading.Instance.removeEventListener("close",this.__onPetsSmallLoadingClose);
         UIModuleLoader.Instance.removeEventListener("uiMoudleProgress",this.__onPetsUIProgress);
      }
      
      private function __onPetsUIProgress(_arg_1:UIModuleEvent) : void
      {
         if(_arg_1.module == "petsBag")
         {
            UIModuleSmallLoading.Instance.progress = _arg_1.loader.progress * 100;
         }
      }
      
      private function __changeHandler(_arg_1:Event) : void
      {
         if(this.bagView.bagType == 21)
         {
            this._currentType = 21;
            this.showBeadInfoView();
            this.bagView.switchButtomVisible(false);
            if(Boolean(this._playerDressView))
            {
               this._playerDressView.visible = false;
            }
            return;
         }
         this.bagView.switchButtomVisible(true);
         if(Boolean(this._beadInfoView))
         {
            this._beadInfoView.visible = false;
         }
         if(this.bagView.bagType == 8)
         {
            this._currentType = 8;
            PlayerDressManager.instance.addEventListener("dressViewComplete",this.showPlayerDressView);
            PlayerDressManager.instance.showView(0);
            return;
         }
         if(Boolean(this._playerDressView))
         {
            this._playerDressView.visible = false;
         }
         if(this._currentType != 2 && this._currentType != 5 && this._currentType != 3)
         {
            this._infoView.switchShow(false);
            this._infoView.visible = true;
         }
      }
      
      private function showPlayerDressView(_arg_1:PlayerDressEvent) : void
      {
         var _local_2:* = undefined;
         if(!this._playerDressView)
         {
            this._playerDressView = _arg_1.info;
            addChild(this._playerDressView);
         }
         else
         {
            this._playerDressView.visible = true;
            _local_2 = this._playerDressView;
            _local_2["updateModel"]();
         }
         if(TexpManager.Instance.isShow("texpView"))
         {
            TexpManager.Instance.changeVisible("texpView",false);
         }
         if(Boolean(this._infoView))
         {
            this._infoView.visible = false;
         }
      }
      
      private function showBeadInfoView() : void
      {
         if(!this._beadInfoView)
         {
            beadSystemManager.Instance.addEventListener("createComplete",this.__onCreateComplete);
            beadSystemManager.Instance.showFrame("mainView");
         }
         else
         {
            this._beadInfoView.visible = true;
         }
         if(TexpManager.Instance.isShow("texpView"))
         {
            TexpManager.Instance.changeVisible("texpView",false);
         }
         if(Boolean(this._infoView))
         {
            this._infoView.visible = false;
         }
      }
      
      private function __onCreateComplete(_arg_1:CEvent) : void
      {
         beadSystemManager.Instance.removeEventListener("createComplete",this.__onCreateComplete);
         if(_arg_1.data.type == "mainView")
         {
            this._beadInfoView = _arg_1.data.spr;
            addChild(this._beadInfoView);
            this.bagView.initBeadButton();
         }
      }
      
      private function __stopShine(_arg_1:CellEvent) : void
      {
         var _local_2:* = undefined;
         this._infoView.stopShine();
         if(Boolean(this._beadInfoView))
         {
            _local_2 = this._beadInfoView;
            _local_2["stopShine"]();
         }
         if(TexpManager.Instance.isShow("texpView"))
         {
            TexpManager.Instance.shine(false);
         }
      }
      
      private function __startShine(_arg_1:CellEvent) : void
      {
         var _local_2:Number = NaN;
         var _local_3:* = undefined;
         if(_arg_1.data is ItemTemplateInfo)
         {
            if(_local_2 == 20 || _local_2 == 53 || _local_2 == 78)
            {
               if(TexpManager.Instance.isShow("texpView"))
               {
                  TexpManager.Instance.shine(true);
               }
            }
            else if((_arg_1.data as ItemTemplateInfo).Property1 != "31")
            {
               this._infoView.startShine(_arg_1.data as ItemTemplateInfo);
            }
            else
            {
               _local_3 = this._beadInfoView;
               _local_3["startShine"](_arg_1.data as ItemTemplateInfo);
            }
         }
      }
      
      public function dispose() : void
      {
         this.removeEvents();
         if(TexpManager.Instance.isShow("texpView"))
         {
            TexpManager.Instance.closeTexpView("texpView");
         }
         ObjectUtils.disposeObject(this._beadInfoView);
         this._beadInfoView = null;
         this.bagView.dispose();
         this.bagView = null;
         this._infoView.dispose();
         this._infoView = null;
         this._info = null;
         ObjectUtils.disposeObject(this._playerDressView);
         this._playerDressView = null;
         PlayerDressManager.instance.disposeView(0);
         if(Boolean(parent))
         {
            parent.removeChild(this);
         }
      }
      
      public function get info() : SelfInfo
      {
         return this._info;
      }
      
      public function set info(_arg_1:SelfInfo) : void
      {
         this._info = _arg_1;
         this._infoView.info = _arg_1;
         this.bagView.info = _arg_1;
         this._infoView.allowLvIconClick();
      }
      
      public function set bagType(_arg_1:int) : void
      {
         this.bagView.setBagType(_arg_1);
      }
      
      public function show() : void
      {
         LayerManager.Instance.addToLayer(this,3,true,1);
      }
      
      public function get currentType() : int
      {
         return this._currentType;
      }
      
      public function checkGuide() : void
      {
         if(Boolean(this._infoView))
         {
            this._infoView.checkGuide();
         }
      }
   }
}

