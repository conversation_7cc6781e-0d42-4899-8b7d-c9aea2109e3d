package com.pickgliss.action
{
   import com.pickgliss.ui.ComponentSetting;
   import com.pickgliss.ui.LayerManager;
   import flash.display.DisplayObject;
   
   public class ShowTipAction extends BaseAction
   {
      
      private var _tip:DisplayObject;
      
      private var _sound:String;
      
      public function ShowTipAction(_arg_1:DisplayObject, _arg_2:String = null)
      {
         super();
         this._tip = _arg_1;
         this._sound = _arg_2;
      }
      
      override public function act() : void
      {
         if(<PERSON><PERSON><PERSON>(this._sound) && ComponentSetting.PLAY_SOUND_FUNC is Function)
         {
            ComponentSetting.PLAY_SOUND_FUNC(this._sound);
         }
         LayerManager.Instance.addToLayer(this._tip,2,false,0,false);
      }
   }
}

