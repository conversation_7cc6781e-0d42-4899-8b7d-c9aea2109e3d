package bagAndInfo
{
   import AvatarCollection.AvatarCollectionManager;
   import beadSystem.beadSystemManager;
   import beadSystem.data.BeadEvent;
   import com.pickgliss.effect.EffectManager;
   import com.pickgliss.effect.IEffect;
   import com.pickgliss.events.FrameEvent;
   import com.pickgliss.events.UIModuleEvent;
   import com.pickgliss.loader.UIModuleLoader;
   import com.pickgliss.toplevel.StageReferance;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.LayerManager;
   import com.pickgliss.ui.controls.BaseButton;
   import com.pickgliss.ui.controls.Frame;
   import com.pickgliss.ui.controls.SelectedButton;
   import com.pickgliss.ui.controls.SelectedButtonGroup;
   import com.pickgliss.ui.controls.alert.BaseAlerFrame;
   import com.pickgliss.ui.controls.container.HBox;
   import com.pickgliss.ui.image.ScaleBitmapImage;
   import com.pickgliss.utils.ClassUtils;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.events.BagEvent;
   import ddt.events.PkgEvent;
   import ddt.events.PlayerPropertyEvent;
   import ddt.loader.LoaderCreate;
   import ddt.manager.DraftManager;
   import ddt.manager.LanguageMgr;
   import ddt.manager.MessageTipManager;
   import ddt.manager.PlayerManager;
   import ddt.manager.ServerConfigManager;
   import ddt.manager.SharedManager;
   import ddt.manager.SocketManager;
   import ddt.manager.SoundManager;
   import ddt.manager.StateManager;
   import ddt.utils.AssetModuleLoader;
   import ddt.utils.HelpBtnEnable;
   import ddt.utils.HelpFrameUtils;
   import ddt.utils.PositionUtils;
   import ddt.view.MainToolBar;
   import ddt.view.UIModuleSmallLoading;
   import ddt.view.tips.OneLineTip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import flash.utils.setTimeout;
   import giftSystem.GiftManager;
   import hall.tasktrack.HallTaskGuideManager;
   import magicStone.MagicStoneManager;
   import magicStone.event.MagicStoneEvent;
   import mark.MarkMgr;
   import petsBag.PetsBagManager;
   import powerUp.PowerUpMovieManager;
   import pvePowerBuff.PvePowerBuffEvent;
   import pvePowerBuff.PvePowerBuffManager;
   import quest.TaskManager;
   import road7th.comm.PackageIn;
   import totem.TotemManager;
   import trainer.view.NewHandContainer;
   import uigeneral.petsSystem.PetGetView;
   
   public class BagAndGiftFrame extends Frame
   {
      
      public static const BAGANDINFO:int = 0;
      
      public static const GIFTVIEW:int = 1;
      
      public static const CARDVIEW:int = 2;
      
      public static const TEXPVIEW:int = 3;
      
      public static const EFFORT:int = 4;
      
      public static const PETVIEW:int = 5;
      
      public static const BEADVIEW:int = 21;
      
      public static const TOTEMVIEW:int = 6;
      
      public static const AVATARCOLLECTIONVIEW:int = 7;
      
      public static const DRESSVIEW:int = 8;
      
      public static const MAGICSTONEVIEW:int = 9;
      
      public static const HOME_TEMPLE:int = 10;
      
      public static const PVEPOWERBUFF_VIEW:int = 11;
      
      public static const MARK:int = 12;
      
      public static const BEAD_CHANGE:String = "beadChanged";
      
      private static const TEXP_OPEN_LEVEL:int = 13;
      
      private static const GIFT_OPEN_LEVEL:int = 16;
      
      private static const CARD_OPEN_LEVEL:int = 15;
      
      private static const PET_OPEN_LEVEL:int = 25;
      
      private static const TOTEM_OPEN_LEVEL:int = 22;
      
      private static const BEAD_OPEN_LEVEL:int = 16;
      
      private static const AVATAR_COLLECTION_OPEN_LEVEL:int = 10;
      
      private static const MAGIC_STONE_LEVEL:int = 40;
      
      private static const GOD_TEMPLE_LEVEL:int = 35;
      
      private static var _firstOpenCard:Boolean = true;
      
      private static var _isFirstEfforOpen:Boolean = true;
      
      private static var _firstOpenGift:Boolean = true;
      
      private static var _isFirstOpenBead:Boolean = true;
      
      private var _infoFrame:BagAndInfoFrame;
      
      private var _cardView:Sprite;
      
      private var _magicStoneMainView:Sprite;
      
      private var _BG:ScaleBitmapImage;
      
      private var _btnGroup:SelectedButtonGroup;
      
      private var _infoBtn:SelectedButton;
      
      private var _texpBtn:SelectedButton;
      
      private var _texpBtnTip:OneLineTip;
      
      private var _texpBtnSprite:Sprite;
      
      private var _texpBtnShine:IEffect;
      
      private var _giftBtn:SelectedButton;
      
      private var _giftBtnShine:IEffect;
      
      private var _giftBtnTip:OneLineTip;
      
      private var _giftBtnSprite:Sprite;
      
      private var _petBtn:SelectedButton;
      
      private var _petBtnSprite:Sprite;
      
      private var _petBtnShine:IEffect;
      
      private var _petBtnTip:OneLineTip;
      
      private var _totemBtn:SelectedButton;
      
      private var _totemBtnSprite:Sprite;
      
      private var _totemBtnShine:IEffect;
      
      private var _totemBtnTip:OneLineTip;
      
      private var _beadBtn:SelectedButton;
      
      private var _beadBtnSprite:Sprite;
      
      private var _beadBtnShine:IEffect;
      
      private var _beadBtnTip:OneLineTip;
      
      private var _cardBtn:SelectedButton;
      
      private var _cardBtnSprite:Sprite;
      
      private var _cardBtnTip:OneLineTip;
      
      private var _avatarCollBtn:SelectedButton;
      
      private var _avatarCollBtnSprite:Sprite;
      
      private var _avatarCollBtnShine:IEffect;
      
      private var _avatarCollBtnTip:OneLineTip;
      
      private var _magicStoneBtn:SelectedButton;
      
      private var _magicStoneBtnSprite:Sprite;
      
      private var _magicStoneBtnShine:IEffect;
      
      private var _magicStoneBtnTip:OneLineTip;
      
      private var _godTempleBtn:SelectedButton;
      
      private var _godTempleBtnSprite:Sprite;
      
      private var _godTempleBtnShine:IEffect;
      
      private var _godTempleBtnTip:OneLineTip;
      
      private var _markBtnTip:OneLineTip;
      
      private var _markBtnSprite:Sprite;
      
      private var _pvePowerBuffBtn:SelectedButton;
      
      private var _pvePowerBuffBtnSprite:Sprite;
      
      private var _pvePowerBuffBtnShine:IEffect;
      
      private var _pvePowerBuffBtnTip:OneLineTip;
      
      private var _pvePowerBuffMainView:Sprite;
      
      private var _selectedBtnsHBox:HBox;
      
      private var _markBtn:SelectedButton;
      
      private var _markHelpBtn:BaseButton;
      
      private var _bagType:int = 0;
      
      private var _frame:BaseAlerFrame;
      
      private var _fightPower:Number;
      
      private var _markOpen:Boolean = false;
      
      private var _homeTempleView:Sprite;
      
      public function BagAndGiftFrame()
      {
         BagAndInfoManager.Instance.isInBagAndInfoView = true;
         super();
         escEnable = true;
         this.initView();
         this.initEvent();
      }
      
      private function initView() : void
      {
         titleText = LanguageMgr.GetTranslation("tank.view.common.BellowStripViewII.bag");
         this._BG = ComponentFactory.Instance.creatComponentByStylename("bagAndInfoFrame.bg1");
         this._infoBtn = ComponentFactory.Instance.creatComponentByStylename("bagAndGiftFrame.infoBtn1");
         this._texpBtn = ComponentFactory.Instance.creatComponentByStylename("bagAndGiftFrame.texpBtn1");
         this._cardBtn = ComponentFactory.Instance.creatComponentByStylename("bagAndGiftFrame.cardBtn");
         this._totemBtn = ComponentFactory.Instance.creatComponentByStylename("bagAndGiftFrame.totemBtn1");
         this._beadBtn = ComponentFactory.Instance.creatComponentByStylename("bagAndGiftFrame.BeadBtn");
         this._avatarCollBtn = ComponentFactory.Instance.creatComponentByStylename("bagAndGiftFrame.avatarCollBtn1");
         this._magicStoneBtn = ComponentFactory.Instance.creatComponentByStylename("bagAndGiftFrame.magicStoneBtn");
         HelpBtnEnable.getInstance().addMouseOverTips(this._magicStoneBtn,1,"tips.open");
         this._godTempleBtn = ComponentFactory.Instance.creatComponentByStylename("bagAndGiftFrame.godTempleBtn");
         this._pvePowerBuffBtn = ComponentFactory.Instance.creatComponentByStylename("bagAndGiftFrame.pvepowerbuffBtn");
         this._markBtn = ComponentFactory.Instance.creatComponentByStylename("bagAndGiftFrame.markBtn");
         this._markHelpBtn = HelpFrameUtils.Instance.simpleHelpButton(this,"mark.btnHelp",null,LanguageMgr.GetTranslation("store.view.HelpButtonText"),"asset.markHelpInfo",463,550);
         this._giftBtn = ComponentFactory.Instance.creatComponentByStylename("bagAndGiftFrame.giftBtn1");
         this._petBtn = ComponentFactory.Instance.creatComponentByStylename("bagAndGiftFrame.PetBtn");
         addToContent(this._BG);
         addToContent(this._infoBtn);
         addToContent(this._texpBtn);
         addToContent(this._cardBtn);
         addToContent(this._totemBtn);
         addToContent(this._beadBtn);
         addToContent(this._avatarCollBtn);
         addToContent(this._magicStoneBtn);
         addToContent(this._godTempleBtn);
         addToContent(this._pvePowerBuffBtn);
         addToContent(this._markBtn);
         this._btnGroup = new SelectedButtonGroup();
         this._btnGroup.addSelectItem(this._infoBtn);
         this._btnGroup.addSelectItem(this._giftBtn);
         this._btnGroup.addSelectItem(this._texpBtn);
         this._btnGroup.addSelectItem(this._petBtn);
         this._btnGroup.addSelectItem(this._cardBtn);
         this._btnGroup.addSelectItem(this._totemBtn);
         this._btnGroup.addSelectItem(this._beadBtn);
         this._btnGroup.addSelectItem(this._avatarCollBtn);
         this._btnGroup.addSelectItem(this._magicStoneBtn);
         this._btnGroup.addSelectItem(this._godTempleBtn);
         this._btnGroup.addSelectItem(this._pvePowerBuffBtn);
         this._btnGroup.addSelectItem(this._markBtn);
         this.texpBtnEnable();
         this.cardBtnEnable();
         this.totemBtnEnable();
         this.beadBtnEnable();
         this.avatarCollBtnEnable();
         this.magicStoneBtnEnable();
         this.godTempleBtnEnable();
         this.pvePowerBuffEnable();
         this.markBtnEnable();
         if(PetsBagManager.instance().haveTaskOrderByID(368))
         {
            PetsBagManager.instance().clearCurrentPetFarmGuildeArrow(94);
            PetsBagManager.instance().showPetFarmGuildArrow(95,-150,"farmTrainer.openPetLabelArrowPos","asset.farmTrainer.clickHere","farmTrainer.openPetLabelTipPos");
         }
         this.texpGuide();
         this.beadGuide();
         this.totemGuide();
         this.templeGuide();
         this.shGuide();
      }
      
      private function shGuide() : void
      {
         if(!PlayerManager.Instance.Self.isNewOnceFinish(143))
         {
            if(PlayerManager.Instance.Self.Grade >= 10 && TaskManager.instance.isAvailable(TaskManager.instance.getQuestByID(327)))
            {
               NewHandContainer.Instance.showArrow(201,180,new Point(597,235),"asset.trainer.txtWeaponTip","asset.trainer.shoushi.txtPos",LayerManager.Instance.getLayerByType(2));
            }
         }
      }
      
      private function texpGuide() : void
      {
         if(!PlayerManager.Instance.Self.isNewOnceFinish(124))
         {
            if(PlayerManager.Instance.Self.Grade == 13 && TaskManager.instance.isAchieved(TaskManager.instance.getQuestByID(7)))
            {
               NewHandContainer.Instance.showArrow(140,180,new Point(202,157),"","",LayerManager.Instance.getLayerByType(2));
            }
         }
      }
      
      private function beadGuide() : void
      {
         if(!PlayerManager.Instance.Self.isNewOnceFinish(126))
         {
            if(PlayerManager.Instance.Self.Grade == 16 && TaskManager.instance.isAchieved(TaskManager.instance.getQuestByID(29)))
            {
               NewHandContainer.Instance.showArrow(142,180,new Point(453,157),"","",LayerManager.Instance.getLayerByType(2));
            }
         }
      }
      
      private function totemGuide() : void
      {
         if(!PlayerManager.Instance.Self.IsWeakGuildFinish(101))
         {
            if(PlayerManager.Instance.Self.Grade >= 1)
            {
               NewHandContainer.Instance.showArrow(145,180,new Point(372,157),"","",LayerManager.Instance.getLayerByType(2));
            }
         }
      }
      
      private function templeGuide() : void
      {
         if(!PlayerManager.Instance.Self.IsWeakGuildFinish(139))
         {
            if(PlayerManager.Instance.Self.Grade >= 1)
            {
               NewHandContainer.Instance.showArrow(149,180,new Point(714,157),"","",LayerManager.Instance.getLayerByType(2));
            }
         }
      }
      
      public function get btnGroup() : SelectedButtonGroup
      {
         return this._btnGroup;
      }
      
      private function markBtnEnable() : void
      {
         if(MarkMgr.inst.checkMarkOpen())
         {
            this._markBtn.enable = true;
            ObjectUtils.disposeObject(this._markBtnSprite);
            this._markBtnSprite = null;
         }
         else
         {
            this._markBtn.enable = false;
            if(this._markBtnSprite == null)
            {
               this._markBtnSprite = new Sprite();
               this._markBtnSprite.graphics.beginFill(0,0);
               this._markBtnSprite.graphics.drawRect(0,0,this._markBtn.displayWidth - 12,this._markBtn.displayHeight);
               this._markBtnSprite.graphics.endFill();
               this._markBtnSprite.x = this._markBtn.x + 17;
               this._markBtnSprite.y = this._markBtn.y + 3;
               addToContent(this._markBtnSprite);
               this._markBtnTip = new OneLineTip();
               this._markBtnTip.tipData = LanguageMgr.GetTranslation("mark.openTips",ServerConfigManager.instance.markOpenLevel,ServerConfigManager.instance.markOpenLevel);
               this._markBtnTip.visible = false;
               this._markBtnSprite.addEventListener("rollOver",this.__markOverHandler);
               this._markBtnSprite.addEventListener("rollOut",this.__markOutHandler);
            }
         }
      }
      
      private function __markOverHandler(_arg_1:MouseEvent) : void
      {
         this._markBtnTip.visible = true;
         LayerManager.Instance.addToLayer(this._markBtnTip,2);
         var _local_2:Point = this._markBtn.localToGlobal(new Point(0,0));
         this._markBtnTip.x = _local_2.x - this._markBtnTip.width * 0.5;
         this._markBtnTip.y = _local_2.y + this._markBtn.height;
      }
      
      private function __markOutHandler(_arg_1:MouseEvent) : void
      {
         if(Boolean(this._markBtnTip))
         {
            this._markBtnTip.visible = false;
         }
      }
      
      private function pvePowerBuffEnable() : void
      {
         if(PlayerManager.Instance.Self.Grade >= ServerConfigManager.instance.pvePowerBuffLevelLimit)
         {
            this._pvePowerBuffBtn.enable = true;
            ObjectUtils.disposeObject(this._pvePowerBuffBtnSprite);
            this._pvePowerBuffBtnSprite = null;
         }
         else
         {
            this._pvePowerBuffBtn.enable = false;
            if(this._pvePowerBuffBtnSprite == null)
            {
               this._pvePowerBuffBtnSprite = new Sprite();
               this._pvePowerBuffBtnSprite.graphics.beginFill(0,0);
               this._pvePowerBuffBtnSprite.graphics.drawRect(0,0,this._pvePowerBuffBtn.displayWidth - 12,this._pvePowerBuffBtn.displayHeight);
               this._pvePowerBuffBtnSprite.graphics.endFill();
               this._pvePowerBuffBtnSprite.x = this._pvePowerBuffBtn.x + 17;
               this._pvePowerBuffBtnSprite.y = this._pvePowerBuffBtn.y + 3;
               addToContent(this._pvePowerBuffBtnSprite);
               this._pvePowerBuffBtnTip = new OneLineTip();
               this._pvePowerBuffBtnTip.tipData = LanguageMgr.GetTranslation("ddt.pvePowerBuff.openPvePowerBuffBtn.text",ServerConfigManager.instance.pvePowerBuffLevelLimit);
               this._pvePowerBuffBtnTip.visible = false;
               this._pvePowerBuffBtnSprite.addEventListener("rollOver",this.__pvePowerBuffOverHandler);
               this._pvePowerBuffBtnSprite.addEventListener("rollOut",this.__pvePowerBuffOutHandler);
            }
         }
      }
      
      private function __pvePowerBuffOverHandler(_arg_1:MouseEvent) : void
      {
         this._pvePowerBuffBtnTip.visible = true;
         LayerManager.Instance.addToLayer(this._pvePowerBuffBtnTip,2);
         var _local_2:Point = this._pvePowerBuffBtn.localToGlobal(new Point(0,0));
         this._pvePowerBuffBtnTip.x = _local_2.x;
         this._pvePowerBuffBtnTip.y = _local_2.y + 45;
      }
      
      private function __pvePowerBuffOutHandler(_arg_1:MouseEvent) : void
      {
         if(Boolean(this._pvePowerBuffBtnTip))
         {
            this._pvePowerBuffBtnTip.visible = false;
         }
      }
      
      private function godTempleBtnEnable() : void
      {
         if(PlayerManager.Instance.Self.Grade >= 1)
         {
            this._godTempleBtn.enable = true;
            ObjectUtils.disposeObject(this._godTempleBtnSprite);
            this._godTempleBtnSprite = null;
         }
         else
         {
            this._godTempleBtn.enable = false;
            if(this._godTempleBtnSprite == null)
            {
               this._godTempleBtnSprite = new Sprite();
               this._godTempleBtnSprite.graphics.beginFill(0,0);
               this._godTempleBtnSprite.graphics.drawRect(0,0,this._godTempleBtn.displayWidth - 12,this._godTempleBtn.displayHeight);
               this._godTempleBtnSprite.graphics.endFill();
               this._godTempleBtnSprite.x = this._godTempleBtn.x + 17;
               this._godTempleBtnSprite.y = this._godTempleBtn.y + 3;
               addToContent(this._godTempleBtnSprite);
               this._godTempleBtnTip = new OneLineTip();
               this._godTempleBtnTip.tipData = LanguageMgr.GetTranslation("ddt.godTemple.openGodTempleBtn.text",35);
               this._godTempleBtnTip.visible = false;
               this._godTempleBtnSprite.addEventListener("rollOver",this.__godTempleOverHandler);
               this._godTempleBtnSprite.addEventListener("rollOut",this.__godTempleOutHandler);
            }
         }
      }
      
      private function __godTempleOverHandler(_arg_1:MouseEvent) : void
      {
         this._godTempleBtnTip.visible = true;
         LayerManager.Instance.addToLayer(this._godTempleBtnTip,2);
         var _local_2:Point = this._godTempleBtn.localToGlobal(new Point(0,0));
         this._godTempleBtnTip.x = _local_2.x;
         this._godTempleBtnTip.y = _local_2.y + this._godTempleBtn.height;
      }
      
      private function __godTempleOutHandler(_arg_1:MouseEvent) : void
      {
         if(Boolean(this._godTempleBtnTip))
         {
            this._godTempleBtnTip.visible = false;
         }
      }
      
      private function magicStoneBtnEnable() : void
      {
         if(PlayerManager.Instance.Self.Grade >= 1)
         {
            this._magicStoneBtn.enable = true;
            ObjectUtils.disposeObject(this._magicStoneBtnSprite);
            this._magicStoneBtnSprite = null;
         }
         else
         {
            this._magicStoneBtn.enable = false;
            if(this._magicStoneBtnSprite == null)
            {
               this._magicStoneBtnSprite = new Sprite();
               this._magicStoneBtnSprite.graphics.beginFill(0,0);
               this._magicStoneBtnSprite.graphics.drawRect(0,0,this._magicStoneBtn.displayWidth - 12,this._magicStoneBtn.displayHeight);
               this._magicStoneBtnSprite.graphics.endFill();
               this._magicStoneBtnSprite.x = this._magicStoneBtn.x + 17;
               this._magicStoneBtnSprite.y = this._magicStoneBtn.y + 3;
               addToContent(this._magicStoneBtnSprite);
               this._magicStoneBtnTip = new OneLineTip();
               this._magicStoneBtnTip.tipData = LanguageMgr.GetTranslation("ddt.magicStoneSystem.openMagicStoneBtn.text",1);
               this._magicStoneBtnTip.visible = false;
               this._magicStoneBtnSprite.addEventListener("mouseOver",this.__magicStoneOverHandler);
               this._magicStoneBtnSprite.addEventListener("mouseOut",this.__magicStoneOutHandler);
            }
         }
      }
      
      private function __magicStoneOverHandler(_arg_1:MouseEvent) : void
      {
         this._magicStoneBtnTip.visible = true;
         LayerManager.Instance.addToLayer(this._magicStoneBtnTip,2);
         var _local_2:Point = this._magicStoneBtn.localToGlobal(new Point(0,0));
         this._magicStoneBtnTip.x = _local_2.x;
         this._magicStoneBtnTip.y = _local_2.y + this._magicStoneBtn.height;
      }
      
      private function __magicStoneOutHandler(_arg_1:MouseEvent) : void
      {
         if(Boolean(this._magicStoneBtnTip))
         {
            this._magicStoneBtnTip.visible = false;
         }
      }
      
      private function avatarCollBtnEnable() : void
      {
         var _local_1:* = null;
         if(PlayerManager.Instance.Self.Grade >= 10)
         {
            this._avatarCollBtn.enable = true;
            if(Boolean(this._avatarCollBtnSprite))
            {
               ObjectUtils.disposeObject(this._avatarCollBtnSprite);
               this._avatarCollBtnSprite = null;
            }
            if(!PlayerManager.Instance.Self.isNewOnceFinish(106) && !GiftManager.Instance.inChurch)
            {
               _local_1 = {};
               _local_1["color"] = "gold";
               this._avatarCollBtnShine = EffectManager.Instance.creatEffect(3,this._avatarCollBtn,_local_1);
               this._avatarCollBtnShine.play();
            }
         }
         else
         {
            this._avatarCollBtn.enable = false;
            if(!this._avatarCollBtnSprite)
            {
               this._avatarCollBtnSprite = new Sprite();
               this._avatarCollBtnSprite.addEventListener("mouseOver",this.__avatarCollBtnOverHandler);
               this._avatarCollBtnSprite.addEventListener("mouseOut",this.__avatarCollBtnOutHandler);
               this._avatarCollBtnSprite.graphics.beginFill(0,0);
               this._avatarCollBtnSprite.graphics.drawRect(0,0,this._avatarCollBtn.displayWidth - 12,this._avatarCollBtn.displayHeight);
               this._avatarCollBtnSprite.graphics.endFill();
               this._avatarCollBtnSprite.x = this._avatarCollBtn.x + 17;
               this._avatarCollBtnSprite.y = this._avatarCollBtn.y + 3;
               addToContent(this._avatarCollBtnSprite);
               this._avatarCollBtnTip = new OneLineTip();
               this._avatarCollBtnTip.tipData = LanguageMgr.GetTranslation("ddt.avatarCollSystem.openAvatarCollBtn.text",10);
               this._avatarCollBtnTip.visible = false;
            }
         }
      }
      
      private function __avatarCollBtnOverHandler(_arg_1:MouseEvent) : void
      {
         this._avatarCollBtnTip.visible = true;
         LayerManager.Instance.addToLayer(this._avatarCollBtnTip,2);
         var _local_2:Point = this._avatarCollBtn.localToGlobal(new Point(0,0));
         this._avatarCollBtnTip.x = _local_2.x - 12;
         this._avatarCollBtnTip.y = _local_2.y + this._avatarCollBtn.height;
      }
      
      private function __avatarCollBtnOutHandler(_arg_1:MouseEvent) : void
      {
         this._avatarCollBtnTip.visible = false;
      }
      
      private function GiftbtnEnable() : void
      {
         var _local_1:* = null;
         if(PlayerManager.Instance.Self.Grade >= 16 || GiftManager.Instance.inChurch == true)
         {
            this._giftBtn.enable = true;
            if(Boolean(this._giftBtnSprite))
            {
               ObjectUtils.disposeObject(this._giftBtnSprite);
            }
            this._giftBtnSprite = null;
            if(SharedManager.Instance.giftFirstShow)
            {
               _local_1 = {};
               _local_1["color"] = "gold";
               this._giftBtnShine = EffectManager.Instance.creatEffect(3,this._giftBtn,_local_1);
               this._giftBtnShine.play();
            }
         }
         else
         {
            this._giftBtn.enable = false;
            if(this._giftBtnSprite == null)
            {
               this._giftBtnSprite = new Sprite();
               this._giftBtnSprite.graphics.beginFill(0,0);
               this._giftBtnSprite.graphics.drawRect(0,0,this._giftBtn.displayWidth,this._giftBtn.displayHeight);
               this._giftBtnSprite.graphics.endFill();
               this._giftBtnSprite.x = this._giftBtn.x + 49;
               this._giftBtnSprite.y = this._giftBtn.y + 3;
               addToContent(this._giftBtnSprite);
               this._giftBtnTip = new OneLineTip();
               this._giftBtnTip.tipData = LanguageMgr.GetTranslation("ddt.giftSystem.openGiftBtn.text");
               this._giftBtnTip.visible = false;
               this._giftBtnSprite.addEventListener("mouseOver",this.__overHandler);
               this._giftBtnSprite.addEventListener("mouseOut",this.__outHandler);
            }
         }
      }
      
      private function texpBtnEnable() : void
      {
         var _local_1:* = null;
         if(PlayerManager.Instance.Self.Grade >= 13)
         {
            this._texpBtn.enable = true;
            if(Boolean(this._texpBtnSprite))
            {
               ObjectUtils.disposeObject(this._texpBtnSprite);
               this._texpBtnSprite = null;
            }
            if(SharedManager.Instance.texpSystemShow && !GiftManager.Instance.inChurch)
            {
               _local_1 = {};
               _local_1["color"] = "gold";
               this._texpBtnShine = EffectManager.Instance.creatEffect(3,this._texpBtn,_local_1);
               this._texpBtnShine.play();
            }
         }
         else
         {
            this._texpBtn.enable = false;
            if(!this._texpBtnSprite)
            {
               this._texpBtnSprite = new Sprite();
               this._texpBtnSprite.addEventListener("mouseOver",this.__texpBtnOverHandler);
               this._texpBtnSprite.addEventListener("mouseOut",this.__texpBtnOutHandler);
               this._texpBtnSprite.graphics.beginFill(0,0);
               this._texpBtnSprite.graphics.drawRect(0,0,this._texpBtn.displayWidth - 5,this._texpBtn.displayHeight);
               this._texpBtnSprite.graphics.endFill();
               this._texpBtnSprite.x = this._texpBtn.x + 33;
               this._texpBtnSprite.y = this._texpBtn.y + 3;
               addToContent(this._texpBtnSprite);
               this._texpBtnTip = new OneLineTip();
               this._texpBtnTip.tipData = LanguageMgr.GetTranslation("ddt.texpSystem.openTexpBtn.text",13);
               this._texpBtnTip.visible = false;
            }
         }
      }
      
      private function cardBtnEnable() : void
      {
         if(PlayerManager.Instance.Self.Grade >= 15)
         {
            this._cardBtn.enable = true;
            if(Boolean(this._cardBtnSprite))
            {
               ObjectUtils.disposeObject(this._cardBtnSprite);
               this._cardBtnSprite = null;
            }
         }
         else
         {
            this._cardBtn.enable = false;
            if(!this._cardBtnSprite)
            {
               this._cardBtnSprite = new Sprite();
               this._cardBtnSprite.addEventListener("mouseOver",this.__cardBtnOverHandler);
               this._cardBtnSprite.addEventListener("mouseOut",this.__cardBtnOutHandler);
               this._cardBtnSprite.graphics.beginFill(0,0);
               this._cardBtnSprite.graphics.drawRect(0,0,this._cardBtn.displayWidth - 7,this._cardBtn.displayHeight);
               this._cardBtnSprite.graphics.endFill();
               this._cardBtnSprite.x = this._cardBtn.x + 70;
               this._cardBtnSprite.y = this._cardBtn.y + 6;
               addToContent(this._cardBtnSprite);
               this._cardBtnTip = new OneLineTip();
               this._cardBtnTip.tipData = LanguageMgr.GetTranslation("ddt.giftSystem.openCardBtn.text",15);
               this._cardBtnTip.visible = false;
            }
         }
      }
      
      private function __cardBtnOverHandler(_arg_1:MouseEvent) : void
      {
         this._cardBtnTip.visible = true;
         LayerManager.Instance.addToLayer(this._cardBtnTip,2);
         var _local_2:Point = this._cardBtn.localToGlobal(new Point(0,0));
         this._cardBtnTip.x = _local_2.x;
         this._cardBtnTip.y = _local_2.y + this._cardBtn.height;
      }
      
      private function __cardBtnOutHandler(_arg_1:MouseEvent) : void
      {
         this._cardBtnTip.visible = false;
      }
      
      private function totemBtnEnable() : void
      {
         var _local_1:* = null;
         if(PlayerManager.Instance.Self.Grade >= 1)
         {
            this._totemBtn.enable = true;
            if(Boolean(this._totemBtnSprite))
            {
               ObjectUtils.disposeObject(this._totemBtnSprite);
               this._totemBtnSprite = null;
            }
            if(!PlayerManager.Instance.Self.isNewOnceFinish(102) && !GiftManager.Instance.inChurch)
            {
               _local_1 = {};
               _local_1["color"] = "gold";
               this._totemBtnShine = EffectManager.Instance.creatEffect(3,this._totemBtn,_local_1);
               this._totemBtnShine.play();
            }
         }
         else
         {
            this._totemBtn.enable = false;
            if(!this._totemBtnSprite)
            {
               this._totemBtnSprite = new Sprite();
               this._totemBtnSprite.addEventListener("mouseOver",this.__totemBtnOverHandler);
               this._totemBtnSprite.addEventListener("mouseOut",this.__totemBtnOutHandler);
               this._totemBtnSprite.graphics.beginFill(0,0);
               this._totemBtnSprite.graphics.drawRect(0,0,this._totemBtn.displayWidth - 12,this._totemBtn.displayHeight);
               this._totemBtnSprite.graphics.endFill();
               this._totemBtnSprite.x = this._totemBtn.x + 17;
               this._totemBtnSprite.y = this._totemBtn.y + 3;
               addToContent(this._totemBtnSprite);
               this._totemBtnTip = new OneLineTip();
               this._totemBtnTip.tipData = LanguageMgr.GetTranslation("ddt.totemSystem.openTotemBtn.text",22);
               this._totemBtnTip.visible = false;
            }
         }
      }
      
      private function beadBtnEnable() : void
      {
         var _local_1:* = null;
         if(PlayerManager.Instance.Self.Grade >= 16)
         {
            this._beadBtn.enable = true;
            if(Boolean(this._beadBtnSprite))
            {
               ObjectUtils.disposeObject(this._beadBtnSprite);
               this._beadBtnSprite = null;
            }
            if(!PlayerManager.Instance.Self.isNewOnceFinish(105) && !GiftManager.Instance.inChurch)
            {
               _local_1 = {};
               _local_1["color"] = "gold";
               this._beadBtnShine = EffectManager.Instance.creatEffect(3,this._beadBtn,_local_1);
               this._beadBtnShine.play();
            }
         }
         else
         {
            this._beadBtn.enable = false;
            if(!this._beadBtnSprite)
            {
               this._beadBtnSprite = new Sprite();
               this._beadBtnSprite.addEventListener("mouseOver",this.__beadBtnOverHandler);
               this._beadBtnSprite.addEventListener("mouseOut",this.__beadBtnOutHandler);
               this._beadBtnSprite.graphics.beginFill(0,0);
               this._beadBtnSprite.graphics.drawRect(0,0,this._beadBtn.displayWidth - 12,this._beadBtn.displayHeight);
               this._beadBtnSprite.graphics.endFill();
               this._beadBtnSprite.x = this._beadBtn.x + 17;
               this._beadBtnSprite.y = this._beadBtn.y + 3;
               addToContent(this._beadBtnSprite);
               this._beadBtnTip = new OneLineTip();
               this._beadBtnTip.tipData = LanguageMgr.GetTranslation("ddt.giftSystem.openBeadBtn.text",16);
               this._beadBtnTip.visible = false;
            }
         }
      }
      
      private function initEvent() : void
      {
         this._btnGroup.addEventListener("change",this.__changeHandler);
         this._infoBtn.addEventListener("click",this.__soundPlay);
         this._texpBtn.addEventListener("click",this.__soundPlay);
         this._giftBtn.addEventListener("click",this.__soundPlay);
         this._petBtn.addEventListener("click",this.__soundPlay);
         this._cardBtn.addEventListener("click",this.__soundPlay);
         this._totemBtn.addEventListener("click",this.__soundPlay);
         this._beadBtn.addEventListener("click",this.__soundPlay);
         this._avatarCollBtn.addEventListener("click",this.__soundPlay);
         this._magicStoneBtn.addEventListener("click",this.__soundPlay);
         this._godTempleBtn.addEventListener("click",this.__soundPlay);
         this._pvePowerBuffBtn.addEventListener("click",this.__soundPlay);
         addEventListener("response",this.__responseHandler);
         addEventListener("addedToStage",this.__getFocus);
         PlayerManager.Instance.Self.addEventListener("propertychange",this.__propertyChange);
         SocketManager.Instance.addEventListener(PkgEvent.format(68,2),this.__addPet);
         PlayerManager.Instance.addEventListener("quickBugCards",this.__quickBuyCards);
      }
      
      private function __frameClose(_arg_1:FrameEvent) : void
      {
         switch(_arg_1.responseCode)
         {
            case 2:
            case 3:
               this._frame.removeEventListener("response",this.__frameClose);
               SoundManager.instance.play("008");
               (_arg_1.currentTarget as BaseAlerFrame).removeEventListener("response",this.__frameClose);
               (_arg_1.currentTarget as BaseAlerFrame).dispose();
               SocketManager.Instance.out.sendClearStoreBag();
         }
      }
      
      private function __quickBuyCards(_arg_1:BagEvent) : void
      {
         this._btnGroup.selectIndex = 0;
      }
      
      public function __addPet(_arg_1:PkgEvent) : void
      {
         var _local_6:* = null;
         var _local_5:* = null;
         var _local_4:PackageIn = _arg_1.pkg;
         var _local_2:int = _local_4.readInt();
         var _local_3:Boolean = _local_4.readBoolean();
         if(_local_3)
         {
            _local_6 = PlayerManager.Instance.Self.getPetInfoById(_local_2);
            if(_local_6)
            {
               _local_5 = new PetGetView();
               _local_5.info = _local_6;
               LayerManager.Instance.addToLayer(_local_5,3,false,1);
               PositionUtils.setPos(_local_5,"petsSystem.petShow.mainViewPos");
            }
            this._infoFrame.clearTexpInfo();
         }
      }
      
      private function removeEvent() : void
      {
         if(Boolean(this._giftBtnSprite))
         {
            this._giftBtnSprite.removeEventListener("mouseOver",this.__overHandler);
            this._giftBtnSprite.removeEventListener("mouseOut",this.__outHandler);
         }
         if(Boolean(this._texpBtnSprite))
         {
            this._texpBtnSprite.removeEventListener("mouseOver",this.__texpBtnOverHandler);
            this._texpBtnSprite.removeEventListener("mouseOut",this.__texpBtnOutHandler);
         }
         if(Boolean(this._cardBtnSprite))
         {
         }
         if(Boolean(this._totemBtnSprite))
         {
            this._totemBtnSprite.removeEventListener("mouseOver",this.__totemBtnOverHandler);
            this._totemBtnSprite.removeEventListener("mouseOut",this.__totemBtnOutHandler);
         }
         if(Boolean(this._beadBtnSprite))
         {
            this._beadBtnSprite.removeEventListener("mouseOver",this.__beadBtnOverHandler);
            this._beadBtnSprite.removeEventListener("mouseOut",this.__beadBtnOutHandler);
         }
         this._btnGroup.removeEventListener("change",this.__changeHandler);
         this._infoBtn.removeEventListener("click",this.__soundPlay);
         this._texpBtn.removeEventListener("click",this.__soundPlay);
         this._giftBtn.removeEventListener("click",this.__soundPlay);
         this._petBtn.removeEventListener("click",this.__soundPlay);
         this._cardBtn.removeEventListener("click",this.__soundPlay);
         this._totemBtn.removeEventListener("click",this.__soundPlay);
         this._beadBtn.removeEventListener("click",this.__soundPlay);
         this._avatarCollBtn.removeEventListener("click",this.__soundPlay);
         this._magicStoneBtn.removeEventListener("click",this.__soundPlay);
         removeEventListener("response",this.__responseHandler);
         removeEventListener("addedToStage",this.__getFocus);
         PlayerManager.Instance.Self.removeEventListener("propertychange",this.__propertyChange);
         SocketManager.Instance.removeEventListener(PkgEvent.format(68,2),this.__addPet);
         PlayerManager.Instance.removeEventListener("quickBugCards",this.__quickBuyCards);
         MagicStoneManager.instance.removeEventListener("magicStoneLoadComplete",this.doShowMagicStone);
      }
      
      protected function __propertyChange(_arg_1:PlayerPropertyEvent) : void
      {
         if(Boolean(_arg_1.changedProperties["Grade"]))
         {
            if(PlayerManager.Instance.Self.Grade == 13)
            {
               this.texpBtnEnable();
            }
            if(PlayerManager.Instance.Self.Grade == 15)
            {
               this.cardBtnEnable();
            }
            if(PlayerManager.Instance.Self.Grade == 22)
            {
               this.totemBtnEnable();
            }
            if(PlayerManager.Instance.Self.Grade == 40)
            {
               this.magicStoneBtnEnable();
            }
         }
      }
      
      private function __soundPlay(_arg_1:MouseEvent) : void
      {
         SoundManager.instance.play("008");
      }
      
      private function __overHandler(_arg_1:MouseEvent) : void
      {
         this._giftBtnTip.visible = true;
         LayerManager.Instance.addToLayer(this._giftBtnTip,2);
         var _local_2:Point = this._giftBtn.localToGlobal(new Point(0,0));
         this._giftBtnTip.x = _local_2.x;
         this._giftBtnTip.y = _local_2.y + this._giftBtn.height;
      }
      
      private function __outHandler(_arg_1:MouseEvent) : void
      {
         this._giftBtnTip.visible = false;
      }
      
      private function __texpBtnOverHandler(_arg_1:MouseEvent) : void
      {
         this._texpBtnTip.visible = true;
         LayerManager.Instance.addToLayer(this._texpBtnTip,2);
         var _local_2:Point = this._texpBtn.localToGlobal(new Point(0,0));
         this._texpBtnTip.x = _local_2.x;
         this._texpBtnTip.y = _local_2.y + this._texpBtn.height;
      }
      
      private function __texpBtnOutHandler(_arg_1:MouseEvent) : void
      {
         this._texpBtnTip.visible = false;
      }
      
      private function __petBtnOverHandler(_arg_1:MouseEvent) : void
      {
         this._petBtnTip.visible = true;
         LayerManager.Instance.addToLayer(this._petBtnTip,2);
         var _local_2:Point = this._petBtn.localToGlobal(new Point(0,0));
         this._petBtnTip.x = _local_2.x;
         this._petBtnTip.y = _local_2.y + this._petBtn.height + 5;
      }
      
      private function __petBtnOutHandler(_arg_1:MouseEvent) : void
      {
         this._petBtnTip.visible = false;
      }
      
      private function __totemBtnOverHandler(_arg_1:MouseEvent) : void
      {
         this._totemBtnTip.visible = true;
         LayerManager.Instance.addToLayer(this._totemBtnTip,2);
         var _local_2:Point = this._totemBtn.localToGlobal(new Point(0,0));
         this._totemBtnTip.x = _local_2.x - 12;
         this._totemBtnTip.y = _local_2.y + this._totemBtn.height;
      }
      
      private function __totemBtnOutHandler(_arg_1:MouseEvent) : void
      {
         this._totemBtnTip.visible = false;
      }
      
      private function __beadBtnOverHandler(_arg_1:MouseEvent) : void
      {
         this._beadBtnTip.visible = true;
         LayerManager.Instance.addToLayer(this._beadBtnTip,2);
         var _local_2:Point = this._beadBtn.localToGlobal(new Point(0,0));
         this._beadBtnTip.x = _local_2.x + 10;
         this._beadBtnTip.y = _local_2.y + this._beadBtn.height;
      }
      
      private function __beadBtnOutHandler(_arg_1:MouseEvent) : void
      {
         this._beadBtnTip.visible = false;
      }
      
      public function selectTab(_arg_1:int) : void
      {
         this._btnGroup.selectIndex = _arg_1;
         this.__changeHandler(null);
      }
      
      private function __changeHandler(_arg_1:Event) : void
      {
         var event:* = _arg_1;
         if(this._markOpen && MarkMgr.inst.needSave)
         {
            if(this._btnGroup.selectIndex == 12 - 1)
            {
               return;
            }
            MarkMgr.inst.promptSchemeSave((function():*
            {
               var callBackHandler:* = function(_arg_1:Boolean):void
               {
                  if(_arg_1)
                  {
                     switchHandler();
                  }
                  else
                  {
                     _btnGroup.selectIndex = 12 - 1;
                  }
               };
               return callBackHandler;
            })());
         }
         else
         {
            this.switchHandler();
         }
      }
      
      private function switchHandler() : void
      {
         if(Boolean(this._infoFrame))
         {
            this._infoFrame.clearTexpInfo();
         }
         MarkMgr.inst.removeMarkView();
         this._markOpen = false;
         switch(this._btnGroup.selectIndex)
         {
            case 0:
               this._fightPower = PlayerManager.Instance.Self.FightPower;
               this.showInfoFrame(0,this._bagType);
               if(Boolean(this._infoFrame))
               {
                  this._infoFrame.checkGuide();
               }
               break;
            case 1:
               this.showGiftFrame();
               break;
            case 2:
               this.showInfoFrame(3);
               break;
            case 3:
               this.showInfoFrame(5);
               break;
            case 4:
               this.loadCardRes();
               break;
            case 5:
               this.showTotem();
               break;
            case 6:
               this._fightPower = PlayerManager.Instance.Self.FightPower;
               this.showBeadView();
               break;
            case 7:
               this.showAvatarCollection();
               break;
            case 8:
               this._fightPower = PlayerManager.Instance.Self.FightPower;
               this.showMagicStone();
               break;
            case 9:
               this._fightPower = PlayerManager.Instance.Self.FightPower;
               this.showGodTemple();
               break;
            case 10:
               this.showPvePowerBuff();
               break;
            case 11:
               this.setVisible(12);
               MarkMgr.inst.showMarkView(_container);
               this._markOpen = true;
         }
         this._markHelpBtn.visible = this._btnGroup.selectIndex == 12 - 1;
         beadSystemManager.Instance.dispatchEvent(new BeadEvent("autoOpenBead",3));
      }
      
      private function showPvePowerBuff() : void
      {
         if(!this._pvePowerBuffMainView)
         {
            PvePowerBuffManager.instance.addEventListener("pvePowerBuffLoadComplete",this.__doShowPvePowerBuff);
            PvePowerBuffManager.instance.show();
         }
         else
         {
            this.setVisible(11);
         }
      }
      
      private function __doShowPvePowerBuff(_arg_1:PvePowerBuffEvent) : void
      {
         PvePowerBuffManager.instance.removeEventListener("pvePowerBuffLoadComplete",this.__doShowPvePowerBuff);
         this._pvePowerBuffMainView = _arg_1.info;
         addToContent(this._pvePowerBuffMainView);
         this.setVisible(11);
      }
      
      private function showGodTemple() : void
      {
         if(!PlayerManager.Instance.Self.IsWeakGuildFinish(139))
         {
            if(PlayerManager.Instance.Self.Grade >= 1)
            {
               SocketManager.Instance.out.syncWeakStep(139);
               NewHandContainer.Instance.clearArrowByID(149);
            }
         }
         if(Boolean(this._homeTempleView))
         {
            this.setVisible(10);
         }
         else
         {
            AssetModuleLoader.addRequestLoader(LoaderCreate.Instance.creatHomeTempAdvanceTemplate());
            AssetModuleLoader.addModelLoader("homeTemple",7);
            AssetModuleLoader.startCodeLoader(this.loadGodTempleComplete);
         }
      }
      
      private function loadGodTempleComplete() : void
      {
         SocketManager.Instance.out.getHomeTempleLevel();
         this._homeTempleView = ComponentFactory.Instance.creatCustomObject("homeTemple.view");
         addToContent(this._homeTempleView);
         this.setVisible(10);
      }
      
      private function showMagicStone() : void
      {
         if(Boolean(this._magicStoneBtnShine))
         {
            this._magicStoneBtnShine.stop();
         }
         if(!this._magicStoneMainView)
         {
            MagicStoneManager.instance.addEventListener("magicStoneLoadComplete",this.doShowMagicStone);
            MagicStoneManager.instance.show();
         }
         else
         {
            this.setVisible(9);
         }
      }
      
      private function doShowMagicStone(_arg_1:MagicStoneEvent) : void
      {
         MagicStoneManager.instance.removeEventListener("magicStoneLoadComplete",this.doShowMagicStone);
         this._magicStoneMainView = _arg_1.info;
         addToContent(this._magicStoneMainView);
         this.setVisible(9);
      }
      
      private function loadCardRes() : void
      {
         AssetModuleLoader.addRequestLoader(LoaderCreate.Instance.creatCardMainTempInfosLoader());
         AssetModuleLoader.addRequestLoader(LoaderCreate.Instance.creatCardSuitInfosLoader());
         AssetModuleLoader.addRequestLoader(LoaderCreate.Instance.creatCardBookInfosLoader());
         AssetModuleLoader.addModelLoader("cardNewSmallSystem",4);
         AssetModuleLoader.addModelLoader("cardNewSystem",5);
         AssetModuleLoader.startCodeLoader(this.showCard);
      }
      
      private function showCard() : void
      {
         if(!this._cardView)
         {
            this._cardView = ClassUtils.CreatInstance("cardNewSystem.view.CardMainView") as Sprite;
            _container.addChild(this._cardView);
            this._cardView.x = 4;
            this._cardView.y = 48;
         }
         this.setVisible(2);
         SocketManager.Instance.out.sendRequestCardInfo();
      }
      
      private function showBeadView() : void
      {
         if(Boolean(this._beadBtnShine))
         {
            this._beadBtnShine.stop();
            SocketManager.Instance.out.syncWeakStep(105);
         }
         this.showInfoFrame(21);
      }
      
      private function setVisible(_arg_1:int) : void
      {
         var _local_2:Boolean = true;
         if(Boolean(this._cardView))
         {
            this._cardView.visible = _arg_1 == 2;
         }
         if(Boolean(this._homeTempleView))
         {
            this._homeTempleView.visible = _arg_1 == 10;
         }
         if(Boolean(this._infoFrame))
         {
            if(_arg_1 == 0)
            {
               _local_2 = true;
            }
            else
            {
               _local_2 = false;
            }
            this._infoFrame.visible = _local_2;
            if(_arg_1 == 0)
            {
               this._infoFrame._infoView.switchShow(false);
            }
            this._infoFrame._infoView.x = -9;
            this._infoFrame._infoView.y = 2;
            this._infoFrame.bagView.x = 442;
            this._infoFrame.bagView.y = -16;
            this._infoFrame.bagView.bagLockBtn.visible = _arg_1 == 0;
         }
         TotemManager.instance.setVisible("totemview",_arg_1 == 6);
         AvatarCollectionManager.instance.visible(_arg_1 == 7);
         if(Boolean(this._magicStoneMainView))
         {
            this._magicStoneMainView.visible = _arg_1 == 9;
         }
         if(Boolean(this._pvePowerBuffMainView))
         {
            this._pvePowerBuffMainView.visible = _arg_1 == 11;
         }
      }
      
      private function showTotem() : void
      {
         if(Boolean(this._totemBtnShine))
         {
            this._totemBtnShine.stop();
            SocketManager.Instance.out.syncWeakStep(102);
         }
         TotemManager.instance.showView("totemview",{"parent":_container});
         this.setVisible(6);
      }
      
      private function showAvatarCollection() : void
      {
         if(Boolean(this._avatarCollBtnShine))
         {
            this._avatarCollBtnShine.stop();
            SocketManager.Instance.out.syncWeakStep(106);
         }
         AvatarCollectionManager.instance.showFrame(_container);
         this.setVisible(7);
      }
      
      private function showGiftFrame() : void
      {
         if(Boolean(this._giftBtnShine))
         {
            this._giftBtnShine.stop();
            SharedManager.Instance.giftFirstShow = false;
            SharedManager.Instance.save();
         }
         if(_firstOpenGift)
         {
            UIModuleSmallLoading.Instance.progress = 0;
            UIModuleSmallLoading.Instance.show();
            UIModuleSmallLoading.Instance.addEventListener("close",this.__onGiftSmallLoadingClose);
            UIModuleLoader.Instance.addEventListener("uiModuleComplete",this.__createGift);
            UIModuleLoader.Instance.addEventListener("uiMoudleProgress",this.__onGiftUIProgress);
            UIModuleLoader.Instance.addUIModuleImp("ddtgiftsystem");
         }
         else
         {
            GiftManager.Instance.canActive = true;
            SocketManager.Instance.out.sendUpdateGoodsCount();
            this.setVisible(1);
         }
      }
      
      private function __createGift(_arg_1:UIModuleEvent) : void
      {
         if(_arg_1.module == "ddtgiftsystem")
         {
            UIModuleSmallLoading.Instance.hide();
            UIModuleSmallLoading.Instance.removeEventListener("close",this.__onGiftSmallLoadingClose);
            UIModuleLoader.Instance.removeEventListener("uiModuleComplete",this.__createGift);
            UIModuleLoader.Instance.removeEventListener("uiMoudleProgress",this.__onGiftUIProgress);
            _firstOpenGift = false;
            this.showGiftFrame();
         }
      }
      
      protected function __onGiftSmallLoadingClose(_arg_1:Event) : void
      {
         UIModuleSmallLoading.Instance.hide();
         UIModuleSmallLoading.Instance.removeEventListener("close",this.__onGiftSmallLoadingClose);
         UIModuleLoader.Instance.removeEventListener("uiModuleComplete",this.__createGift);
         UIModuleLoader.Instance.removeEventListener("uiMoudleProgress",this.__onGiftUIProgress);
      }
      
      protected function __onGiftUIProgress(_arg_1:UIModuleEvent) : void
      {
         if(_arg_1.module == "ddtgiftsystem")
         {
            UIModuleSmallLoading.Instance.progress = _arg_1.loader.progress * 100;
         }
      }
      
      private function showInfoFrame(_arg_1:int, _arg_2:int = 0) : void
      {
         if(_arg_1 == 3 && Boolean(this._texpBtnShine))
         {
            this._texpBtnShine.stop();
            SharedManager.Instance.texpSystemShow = false;
            SharedManager.Instance.save();
         }
         if(this._infoFrame == null)
         {
            this._infoFrame = ComponentFactory.Instance.creatCustomObject("bagAndInfoFrame");
            addToContent(this._infoFrame);
         }
         if(_arg_1 == 5)
         {
            this._infoFrame.isScreenFood = true;
         }
         else
         {
            this._infoFrame.isScreenFood = false;
         }
         this._infoFrame.switchShow(_arg_1,_arg_2);
         this.setVisible(0);
      }
      
      private function __getFocus(_arg_1:Event) : void
      {
         removeEventListener("addedToStage",this.__getFocus);
         StageReferance.stage.focus = this;
      }
      
      private function __responseHandler(_arg_1:FrameEvent) : void
      {
         var evt:* = _arg_1;
         if(evt.responseCode == 0 || evt.responseCode == 1)
         {
            SoundManager.instance.play("008");
            if(this._btnGroup.selectIndex == 12 - 1 && MarkMgr.inst.needSave)
            {
               MarkMgr.inst.promptSchemeSave((function():*
               {
                  var callBackHandler:* = function(_arg_1:Boolean):void
                  {
                     if(_arg_1)
                     {
                        closeBagAndGiftFrame();
                     }
                  };
                  return callBackHandler;
               })());
            }
            else
            {
               this.closeBagAndGiftFrame();
            }
         }
      }
      
      private function closeBagAndGiftFrame() : void
      {
         if(PvePowerBuffManager.instance.isInGetBuff == true)
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("feedback.view.SystemBusy"));
            return;
         }
         this.dispose();
         if(this._btnGroup.selectIndex == 0 || this._btnGroup.selectIndex == 6 || this._btnGroup.selectIndex == 8)
         {
            if(this._fightPower < PlayerManager.Instance.Self.FightPower)
            {
               PowerUpMovieManager.powerNum = this._fightPower;
               PowerUpMovieManager.addedPowerNum = PlayerManager.Instance.Self.FightPower - this._fightPower;
               PowerUpMovieManager.Instance.dispatchEvent(new Event("powerUp"));
            }
         }
         if(PlayerManager.Instance.Self.Grade == 3)
         {
            MainToolBar.Instance.tipTask();
         }
      }
      
      public function show(_arg_1:int, _arg_2:String = "", _arg_3:int = 0) : void
      {
         var _local_4:* = undefined;
         LayerManager.Instance.addToLayer(this,3,true,1);
         this._bagType = _arg_3;
         if(_arg_1 == 21)
         {
            this._btnGroup.selectIndex = 6;
         }
         else
         {
            this._btnGroup.selectIndex = _arg_1;
         }
         if(DraftManager.instance.showDraft)
         {
            _local_4 = false;
            this._pvePowerBuffBtn.enable = _local_4;
            this._godTempleBtn.enable = _local_4;
            this._beadBtn.enable = _local_4;
            this._avatarCollBtn.enable = _local_4;
            this._totemBtn.enable = _local_4;
            this._cardBtn.enable = _local_4;
            this._magicStoneBtn.enable = _local_4;
            this._texpBtn.enable = _local_4;
            this._infoBtn.enable = _local_4;
            if(Boolean(this._texpBtnShine))
            {
               this._texpBtnShine.stop();
            }
            if(Boolean(this._magicStoneBtnShine))
            {
               this._magicStoneBtnShine.stop();
            }
            if(Boolean(this._totemBtnShine))
            {
               this._totemBtnShine.stop();
            }
            if(Boolean(this._avatarCollBtnShine))
            {
               this._avatarCollBtnShine.stop();
            }
            if(Boolean(this._beadBtnShine))
            {
               this._beadBtnShine.stop();
            }
         }
         if(GiftManager.Instance.inChurch == true)
         {
            this._infoBtn.enable = false;
            this._texpBtn.enable = false;
            this._petBtn.enable = false;
            this._cardBtn.enable = false;
            this._totemBtn.enable = false;
            this._beadBtn.enable = false;
            this._avatarCollBtn.enable = false;
            this._godTempleBtn.enable = false;
            this._pvePowerBuffBtn.enable = false;
         }
         if(_arg_1 == 1 && _arg_2 != "")
         {
            setTimeout(GiftManager.Instance.RebackClick,300,_arg_2);
         }
         if(StateManager.isInGame(StateManager.currentStateType))
         {
            this._infoBtn.enable = false;
            this._texpBtn.enable = false;
            this._giftBtn.enable = false;
            this._petBtn.enable = false;
         }
      }
      
      override public function dispose() : void
      {
         if(DraftManager.instance.showDraft)
         {
            DraftManager.instance.show();
         }
         NewHandContainer.Instance.hideGuideCover();
         NewHandContainer.Instance.clearArrowByID(138);
         NewHandContainer.Instance.clearArrowByID(150);
         NewHandContainer.Instance.clearArrowByID(145);
         NewHandContainer.Instance.clearArrowByID(149);
         NewHandContainer.Instance.clearArrowByID(201);
         NewHandContainer.Instance.clearArrowByID(151);
         BagAndInfoManager.Instance.isInBagAndInfoView = false;
         if(Boolean(this._giftBtnShine))
         {
            EffectManager.Instance.removeEffect(this._giftBtnShine);
         }
         this._giftBtnShine = null;
         if(Boolean(this._texpBtnShine))
         {
            EffectManager.Instance.removeEffect(this._texpBtnShine);
         }
         this._texpBtnShine = null;
         if(Boolean(this._totemBtnShine))
         {
            EffectManager.Instance.removeEffect(this._totemBtnShine);
         }
         this._totemBtnShine = null;
         if(Boolean(this._beadBtnShine))
         {
            EffectManager.Instance.removeEffect(this._beadBtnShine);
         }
         this._beadBtnShine = null;
         BagAndInfoManager.Instance.clearReference();
         this.removeEvent();
         if(Boolean(this._petBtn))
         {
            ObjectUtils.disposeObject(this._petBtn);
            this._petBtn = null;
         }
         if(Boolean(this._frame))
         {
            this._frame.removeEventListener("response",this.__frameClose);
            this._frame.dispose();
            this._frame = null;
         }
         if(Boolean(this._infoBtn))
         {
            ObjectUtils.disposeObject(this._infoBtn);
         }
         this._infoBtn = null;
         if(Boolean(this._texpBtn))
         {
            ObjectUtils.disposeObject(this._texpBtn);
         }
         this._texpBtn = null;
         if(Boolean(this._texpBtnTip))
         {
            ObjectUtils.disposeObject(this._texpBtnTip);
         }
         this._texpBtnTip = null;
         if(Boolean(this._texpBtnSprite))
         {
            ObjectUtils.disposeObject(this._texpBtnSprite);
         }
         this._texpBtnSprite = null;
         if(Boolean(this._giftBtn))
         {
            ObjectUtils.disposeObject(this._giftBtn);
         }
         this._giftBtn = null;
         if(Boolean(this._BG))
         {
            ObjectUtils.disposeObject(this._BG);
         }
         this._BG = null;
         if(Boolean(this._giftBtnSprite))
         {
            ObjectUtils.disposeObject(this._giftBtnSprite);
         }
         this._giftBtnSprite = null;
         if(Boolean(this._totemBtnSprite))
         {
            ObjectUtils.disposeObject(this._totemBtnSprite);
         }
         this._totemBtnSprite = null;
         if(Boolean(this._markBtnSprite))
         {
            ObjectUtils.disposeObject(this._markBtnSprite);
         }
         this._markBtnSprite = null;
         if(Boolean(this._beadBtnSprite))
         {
            ObjectUtils.disposeObject(this._beadBtnSprite);
         }
         this._beadBtnSprite = null;
         if(Boolean(this._giftBtnTip))
         {
            ObjectUtils.disposeObject(this._giftBtnTip);
         }
         this._giftBtnTip = null;
         if(Boolean(this._petBtnTip))
         {
            ObjectUtils.disposeObject(this._petBtnTip);
         }
         this._petBtnTip = null;
         if(Boolean(this._cardBtnTip))
         {
            ObjectUtils.disposeObject(this._cardBtnTip);
         }
         this._cardBtnTip = null;
         if(Boolean(this._totemBtnTip))
         {
            ObjectUtils.disposeObject(this._totemBtnTip);
         }
         this._totemBtnTip = null;
         if(Boolean(this._markBtnTip))
         {
            ObjectUtils.disposeObject(this._markBtnTip);
         }
         this._markBtnTip = null;
         if(Boolean(this._beadBtnTip))
         {
            ObjectUtils.disposeObject(this._beadBtnTip);
         }
         this._beadBtnTip = null;
         if(Boolean(this._infoFrame))
         {
            this._infoFrame.dispose();
         }
         this._infoFrame = null;
         if(Boolean(this._totemBtn))
         {
            ObjectUtils.disposeObject(this._totemBtn);
         }
         this._totemBtn = null;
         if(Boolean(this._avatarCollBtn))
         {
            ObjectUtils.disposeObject(this._avatarCollBtn);
         }
         this._avatarCollBtn = null;
         if(Boolean(this._cardView))
         {
            ObjectUtils.disposeObject(this._cardView);
         }
         this._cardView = null;
         ObjectUtils.disposeObject(this._godTempleBtn);
         this._godTempleBtn = null;
         ObjectUtils.disposeObject(this._pvePowerBuffBtn);
         this._pvePowerBuffBtn = null;
         ObjectUtils.disposeObject(this._magicStoneBtn);
         this._magicStoneBtn = null;
         ObjectUtils.disposeObject(this._magicStoneBtnShine);
         this._magicStoneBtnShine = null;
         ObjectUtils.disposeObject(this._magicStoneBtnSprite);
         this._magicStoneBtnSprite = null;
         ObjectUtils.disposeObject(this._magicStoneBtnTip);
         this._magicStoneBtnTip = null;
         TotemManager.instance.closeView("totemview");
         AvatarCollectionManager.instance.closeFrame();
         ObjectUtils.disposeObject(this._magicStoneMainView);
         this._magicStoneMainView = null;
         MagicStoneManager.instance.disposeView();
         ObjectUtils.disposeObject(this._pvePowerBuffMainView);
         this._pvePowerBuffMainView = null;
         PvePowerBuffManager.instance.disposeView();
         PetsBagManager.instance().clearCurrentPetFarmGuildeArrow(95);
         if(Boolean(this.parent))
         {
            this.parent.removeChild(this);
         }
         super.dispose();
         HallTaskGuideManager.instance.clearTask1Arrow();
         HallTaskGuideManager.instance.showTask1ClickBagArrow();
         NewHandContainer.Instance.clearArrowByID(140);
         NewHandContainer.Instance.clearArrowByID(141);
         NewHandContainer.Instance.clearArrowByID(142);
         MarkMgr.inst.removeMarkView();
      }
      
      public function get infoFrame() : BagAndInfoFrame
      {
         return this._infoFrame;
      }
   }
}

