package activity.perfectCouple.model
{
   public class PerfectCoupleModel
   {
      
      public var petLevel:int = 0;
      
      public var petExp:int;
      
      public var petRewardState:String = "";
      
      public var giveWifeGiftNum:int;
      
      public var getWifeGiftNum:int;
      
      public var giveFriendGiftNum:int;
      
      public var getFriendGiftNum:int;
      
      public var achvScore:int;
      
      public var rank:int;
      
      public var wifeName:String = "";
      
      public var wifePetLevel:int;
      
      public var wifePetExp:int;
      
      public var wifeAchvScore:int;
      
      public var wifeRank:int;
      
      private var _achvList:Array;
      
      public function PerfectCoupleModel()
      {
         super();
      }
      
      public function set achvList(_arg_1:Array) : void
      {
         var _local_2:* = null;
         if(_arg_1.length > 0)
         {
            _arg_1.sortOn(["isComplete","sortDate"],[16,16]);
            _local_2 = _arg_1.pop();
            if(!_local_2.isComplete)
            {
               _arg_1.push(_local_2);
               _local_2 = null;
            }
            _arg_1.sortOn(["isComplete","sortDate","id"],[16,0x10 | 2,16]);
            if(_local_2)
            {
               _arg_1.unshift(_local_2);
            }
         }
         this._achvList = _arg_1;
      }
      
      public function get achvList() : Array
      {
         return this._achvList;
      }
      
      public function get hasPet() : Boolean
      {
         return this.petLevel > 0;
      }
      
      public function getHasGetReward(_arg_1:int) : Boolean
      {
         if(this.petRewardState.length == 0 || this.petRewardState.length < _arg_1)
         {
            return false;
         }
         var _local_3:int = _arg_1 - 1;
         var _local_2:String = this.petRewardState.charAt(_local_3);
         return _local_2 == "1";
      }
   }
}

