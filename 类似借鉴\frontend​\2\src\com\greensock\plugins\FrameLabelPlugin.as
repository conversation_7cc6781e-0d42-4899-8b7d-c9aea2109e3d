package com.greensock.plugins
{
   import com.greensock.*;
   import flash.display.*;
   
   public class FrameLabelPlugin extends FramePlugin
   {
      
      public static const API:Number = 1;
      
      public function FrameLabelPlugin()
      {
         super();
         this.propName = "frameLabel";
      }
      
      override public function onInitTween(_arg_1:Object, _arg_2:*, _arg_3:TweenLite) : <PERSON><PERSON>an
      {
         if(!_arg_3.target is MovieClip)
         {
            return false;
         }
         _target = _arg_1 as MovieClip;
         this.frame = _target.currentFrame;
         var _local_5:Array = _target.currentLabels;
         var _local_6:String = _arg_2;
         var _local_4:int = _target.currentFrame;
         var _local_7:int = int(_local_5.length);
         while(<PERSON><PERSON>an(_local_7--))
         {
            if(_local_5[_local_7].name == _local_6)
            {
               _local_4 = int(_local_5[_local_7].frame);
               break;
            }
         }
         if(this.frame != _local_4)
         {
            addTween(this,"frame",this.frame,_local_4,"frame");
         }
         return true;
      }
   }
}

