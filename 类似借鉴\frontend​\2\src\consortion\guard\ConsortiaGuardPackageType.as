package consortion.guard
{
   public class ConsortiaGuardPackageType
   {
      
      public static const OPEN_ACTIVITY:int = 0;
      
      public static const PLAYER:int = 1;
      
      public static const MOVE:int = 2;
      
      public static const EXIT_SCENE:int = 3;
      
      public static const UPDATE_BOSS_STATE:int = 4;
      
      public static const FIGHT:int = 5;
      
      public static const INIT_SCENE:int = 6;
      
      public static const UPDATE_PLAYER_STATE:int = 8;
      
      public static const REVIVE:int = 10;
      
      public static const REMOVE_PLAYER:int = 12;
      
      public static const GAME_STATE:int = 9;
      
      public static const PLAYER_RANK:int = 16;
      
      public static const BUY_BUFF:int = 17;
      
      public static const PLAYER_BOSS_RANK:int = 19;
      
      public function ConsortiaGuardPackageType()
      {
         super();
      }
   }
}

