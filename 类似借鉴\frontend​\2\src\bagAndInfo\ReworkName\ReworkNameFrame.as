package bagAndInfo.ReworkName
{
   import com.pickgliss.events.FrameEvent;
   import com.pickgliss.loader.BaseLoader;
   import com.pickgliss.loader.LoadResourceManager;
   import com.pickgliss.loader.LoaderEvent;
   import com.pickgliss.toplevel.StageReferance;
   import com.pickgliss.ui.AlertManager;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.controls.BaseButton;
   import com.pickgliss.ui.controls.Frame;
   import com.pickgliss.ui.controls.TextButton;
   import com.pickgliss.ui.controls.alert.BaseAlerFrame;
   import com.pickgliss.ui.text.FilterFrameText;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.data.analyze.ReworkNameAnalyzer;
   import ddt.manager.LanguageMgr;
   import ddt.manager.PathManager;
   import ddt.manager.PlayerManager;
   import ddt.manager.SocketManager;
   import ddt.manager.SoundManager;
   import ddt.utils.FilterWordManager;
   import ddt.utils.RequestVairableCreater;
   import flash.display.Bitmap;
   import flash.events.ErrorEvent;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import flash.net.URLVariables;
   import flash.text.TextFormat;
   import flash.utils.setTimeout;
   
   public class ReworkNameFrame extends Frame
   {
      
      public static const Close:String = "close";
      
      public static const ReworkDone:String = "ReworkDone";
      
      public static const Aviable:String = "aviable";
      
      public static const Unavialbe:String = "unaviable";
      
      public static const Input:String = "input";
      
      protected var _tittleField:FilterFrameText;
      
      protected var _nicknameInput:FilterFrameText;
      
      protected var _inputBackground:Bitmap;
      
      protected var _resultField:FilterFrameText;
      
      protected var _checkButton:BaseButton;
      
      protected var _submitButton:TextButton;
      
      protected var _available:Boolean = true;
      
      protected var _nicknameDetail:String = LanguageMgr.GetTranslation("choosecharacter.ChooseCharacterView.check_txt");
      
      private var _resultDefaultFormat:TextFormat;
      
      private var _avialableFormat:TextFormat;
      
      private var _unAviableFormat:TextFormat;
      
      private var _disEnabledFilters:Array = [ComponentFactory.Instance.model.getSet("bagAndInfo.reworkname.ButtonDisenable")];
      
      private var _complete:Boolean = true;
      
      protected var _path:String = "NickNameCheck.ashx";
      
      protected var _bagType:int;
      
      protected var _place:int;
      
      protected var _maxChars:int;
      
      protected var _state:String;
      
      protected var _isCanRework:Boolean;
      
      private var checkCallBack:Function;
      
      private var nickNameShield:String;
      
      public function ReworkNameFrame()
      {
         super();
         escEnable = true;
         enterEnable = true;
         this.configUi();
         this.addEvent();
      }
      
      protected function configUi() : void
      {
         titleText = LanguageMgr.GetTranslation("tank.view.ReworkNameView.reworkName");
         this._resultDefaultFormat = ComponentFactory.Instance.model.getSet("bagAndInfo.reworkname.ResultDefaultTF");
         this._avialableFormat = ComponentFactory.Instance.model.getSet("bagAndInfo.reworkname.ResultAvailableTF");
         this._unAviableFormat = ComponentFactory.Instance.model.getSet("bagAndInfo.reworkname.ResultUnAvailableTF");
         this._inputBackground = ComponentFactory.Instance.creatBitmap("bagAndInfo.reworkname.backgound_input");
         addToContent(this._inputBackground);
         this._tittleField = ComponentFactory.Instance.creatComponentByStylename("bagAndInfo.reworkname.ReworkNameTittle");
         this._tittleField.text = LanguageMgr.GetTranslation("tank.view.ReworkNameView.inputName");
         addToContent(this._tittleField);
         this._resultField = ComponentFactory.Instance.creatComponentByStylename("bagAndInfo.reworkname.ReworkNameCheckResult");
         this._resultField.defaultTextFormat = this._resultDefaultFormat;
         this._resultField.text = this._nicknameDetail;
         addToContent(this._resultField);
         this._nicknameInput = ComponentFactory.Instance.creatComponentByStylename("bagAndInfo.reworkname.NicknameInput");
         addToContent(this._nicknameInput);
         this._checkButton = ComponentFactory.Instance.creatComponentByStylename("bagAndInfo.reworkname.CheckButton");
         addToContent(this._checkButton);
         this._submitButton = ComponentFactory.Instance.creatComponentByStylename("bagAndInfo.reworkname.SubmitButton");
         this._submitButton.text = LanguageMgr.GetTranslation("tank.view.ReworkNameView.okLabel");
         addToContent(this._submitButton);
         this._submitButton.enable = false;
         this._submitButton.filters = this._disEnabledFilters;
      }
      
      private function addEvent() : void
      {
         this._nicknameInput.addEventListener("change",this.__onInputChange);
         this._checkButton.addEventListener("click",this.__onCheckClick);
         this._submitButton.addEventListener("click",this.__onSubmitClick);
         addEventListener("response",this.__onResponse);
         addEventListener("addedToStage",this.__onToStage);
      }
      
      private function removeEvent() : void
      {
         this._nicknameInput.removeEventListener("change",this.__onInputChange);
         this._checkButton.removeEventListener("click",this.__onCheckClick);
         this._submitButton.removeEventListener("click",this.__onSubmitClick);
         removeEventListener("response",this.__onResponse);
         removeEventListener("addedToStage",this.__onToStage);
      }
      
      private function __onToStage(_arg_1:Event) : void
      {
         removeEventListener("addedToStage",this.__onToStage);
         StageReferance.stage.focus = this._nicknameInput;
         setTimeout(this._nicknameInput.setFocus,100);
      }
      
      private function __onResponse(_arg_1:FrameEvent) : void
      {
         SoundManager.instance.play("008");
         switch(_arg_1.responseCode)
         {
            case 0:
            case 1:
            case 4:
               this.close();
               return;
            case 2:
            case 3:
               if(this._submitButton.enable)
               {
                  this.__onSubmitClick(null);
                  break;
               }
         }
      }
      
      protected function __onInputChange(_arg_1:Event) : void
      {
         this.state = "input";
         if(this.state != "input")
         {
            this.state = "input";
         }
         if(!this._nicknameInput.text || this._nicknameInput.text == "")
         {
            this._submitButton.enable = false;
            this._submitButton.filters = this._disEnabledFilters;
         }
         else
         {
            this._submitButton.enable = true;
            this._submitButton.filters = null;
         }
      }
      
      protected function __onCheckClick(_arg_1:MouseEvent) : void
      {
         if(this.complete)
         {
            SoundManager.instance.play("008");
            this._isCanRework = false;
            if(this.nameInputCheck())
            {
               this.createCheckLoader(this.checkNameCallBack);
            }
            else
            {
               this.visibleCheckText();
            }
         }
      }
      
      protected function visibleCheckText() : void
      {
         this.state = "input";
         this._resultField.text = this._nicknameDetail;
      }
      
      private function __onSubmitClick(_arg_1:MouseEvent) : void
      {
         var _local_2:int = 0;
         if(this.complete)
         {
            SoundManager.instance.play("008");
            this._isCanRework = false;
            if(this._nicknameInput.text == "")
            {
               this.setCheckTxt(LanguageMgr.GetTranslation("tank.view.ReworkNameView.inputName"));
            }
            if(!this.nameInputCheck())
            {
               this.visibleCheckText();
               return;
            }
            this.checkCallBack = this.submitCheckCallBack;
            _local_2 = this.checkShieldNickName();
            if(_local_2 == 1)
            {
               this.createCheckLoader(this.submitCheckCallBack);
            }
         }
      }
      
      protected function setCheckTxt(_arg_1:String) : void
      {
         if(_arg_1 == LanguageMgr.GetTranslation("choosecharacter.ChooseCharacterView.setCheckTxt"))
         {
            this.state = "aviable";
            this._isCanRework = true;
         }
         else
         {
            this.state = "unaviable";
         }
         this._resultField.text = _arg_1;
      }
      
      private function __onLoadError(_arg_1:LoaderEvent) : void
      {
         this.complete = true;
         this.state = "unaviable";
         _arg_1.loader.removeEventListener("loadError",this.__onLoadError);
      }
      
      protected function createCheckLoader(_arg_1:Function) : BaseLoader
      {
         var _local_3:URLVariables = RequestVairableCreater.creatWidthKey(true);
         _local_3["id"] = PlayerManager.Instance.Self.ID;
         _local_3["bagType"] = this._bagType;
         _local_3["place"] = this._place;
         _local_3["NickName"] = this._nicknameInput.text;
         var _local_2:BaseLoader = LoadResourceManager.Instance.createLoader(PathManager.solveRequestPath(this._path),6,_local_3);
         _local_2.loadErrorMessage = LanguageMgr.GetTranslation("choosecharacter.LoadCheckName.m");
         _local_2.analyzer = new ReworkNameAnalyzer(_arg_1);
         _local_2.addEventListener("loadError",this.__onLoadError);
         LoadResourceManager.Instance.startLoad(_local_2);
         this.complete = false;
         return _local_2;
      }
      
      protected function checkNameCallBack(_arg_1:ReworkNameAnalyzer) : void
      {
         this.complete = true;
         var _local_2:XML = _arg_1.result;
         this.setCheckTxt(_local_2.@message);
      }
      
      protected function reworkNameComplete() : void
      {
         this.complete = true;
         SoundManager.instance.play("047");
         var _local_1:BaseAlerFrame = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("AlertDialog.Info"),LanguageMgr.GetTranslation("tank.view.ReworkNameView.reworkNameComplete"),LanguageMgr.GetTranslation("ok"));
         _local_1.addEventListener("response",this.__onAlertResponse);
         this.close();
      }
      
      protected function __onAlertResponse(_arg_1:FrameEvent) : void
      {
         SoundManager.instance.play("008");
         var _local_2:BaseAlerFrame = _arg_1.currentTarget as BaseAlerFrame;
         _local_2.removeEventListener("response",this.__onAlertResponse);
         switch(_arg_1.responseCode)
         {
            case 0:
            case 1:
            case 2:
            case 3:
            case 4:
               _local_2.dispose();
         }
         StageReferance.stage.focus = this._nicknameInput;
      }
      
      protected function submitCheckCallBack(_arg_1:ReworkNameAnalyzer) : void
      {
         var _local_3:* = null;
         this.complete = true;
         var _local_2:XML = _arg_1.result;
         this.setCheckTxt(_local_2.@message);
         if(this.nameInputCheck() && this._isCanRework)
         {
            _local_3 = this._nicknameInput.text;
            SocketManager.Instance.out.sendUseReworkName(this._bagType,this._place,_local_3);
            this.reworkNameComplete();
         }
      }
      
      protected function __onFrameResponse(_arg_1:FrameEvent) : void
      {
         var _local_2:BaseAlerFrame = _arg_1.currentTarget as BaseAlerFrame;
         _local_2.removeEventListener("response",this.__onFrameResponse);
         _local_2.dispose();
         this.state = "input";
      }
      
      protected function nameInputCheck() : Boolean
      {
         var _local_1:* = null;
         if(this._nicknameInput.text != "")
         {
            if(FilterWordManager.isGotForbiddenWords(this._nicknameInput.text,"name"))
            {
               _local_1 = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("AlertDialog.Info"),LanguageMgr.GetTranslation("choosecharacter.ChooseCharacterView.name"),LanguageMgr.GetTranslation("ok"),"",false,false,false,2);
               _local_1.addEventListener("response",this.__onAlertResponse);
               return false;
            }
            if(FilterWordManager.IsNullorEmpty(this._nicknameInput.text))
            {
               _local_1 = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("AlertDialog.Info"),LanguageMgr.GetTranslation("choosecharacter.ChooseCharacterView.space"),LanguageMgr.GetTranslation("ok"),"",false,false,false,2);
               _local_1.addEventListener("response",this.__onAlertResponse);
               return false;
            }
            if(FilterWordManager.containUnableChar(this._nicknameInput.text))
            {
               _local_1 = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("AlertDialog.Info"),LanguageMgr.GetTranslation("choosecharacter.ChooseCharacterView.string"),LanguageMgr.GetTranslation("ok"),"",false,false,false,2);
               _local_1.addEventListener("response",this.__onAlertResponse);
               return false;
            }
            return true;
         }
         _local_1 = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("AlertDialog.Info"),LanguageMgr.GetTranslation("choosecharacter.ChooseCharacterView.input"),LanguageMgr.GetTranslation("ok"),"",false,false,false,2);
         _local_1.addEventListener("response",this.__onAlertResponse);
         return false;
      }
      
      public function initialize(_arg_1:int, _arg_2:int) : void
      {
         this._bagType = _arg_1;
         this._place = _arg_2;
      }
      
      public function get state() : String
      {
         return this._state;
      }
      
      public function set state(_arg_1:String) : void
      {
         if(this._state != _arg_1)
         {
            this._state = _arg_1;
            if(this._state == "aviable")
            {
               this._resultField.defaultTextFormat = this._avialableFormat;
               this._resultField.setTextFormat(this._avialableFormat,0,this._resultField.length);
            }
            else if(this._state == "unaviable")
            {
               this._resultField.defaultTextFormat = this._unAviableFormat;
               this._resultField.setTextFormat(this._unAviableFormat,0,this._resultField.length);
            }
            else
            {
               this._resultField.defaultTextFormat = this._resultDefaultFormat;
               this._resultField.setTextFormat(this._resultDefaultFormat,0,this._resultField.length);
               this._resultField.text = this._nicknameDetail;
               this._isCanRework = true;
            }
         }
      }
      
      public function get complete() : Boolean
      {
         return this._complete;
      }
      
      public function set complete(_arg_1:Boolean) : void
      {
         if(this._complete != _arg_1)
         {
            this._complete = _arg_1;
            if(this._complete)
            {
               if(!this._nicknameInput.text || this._nicknameInput.text == "")
               {
                  this._submitButton.enable = false;
                  this._submitButton.filters = this._disEnabledFilters;
               }
               else
               {
                  this._submitButton.enable = true;
                  this._submitButton.filters = null;
               }
            }
            else
            {
               this._submitButton.enable = false;
               this._submitButton.filters = this._disEnabledFilters;
            }
         }
      }
      
      private function checkShieldNickName() : int
      {
         var _local_4:int = 0;
         var _local_5:int = 0;
         var _local_1:int = 0;
         var _local_2:* = null;
         var _local_3:* = null;
         if(Boolean(this.nickNameShield))
         {
            _local_2 = this._nicknameInput.text;
            _local_4 = 0;
            while(_local_5 < _local_2.length)
            {
               _local_1 = int(this.nickNameShield.indexOf(_local_2.charAt(_local_5)));
               if(_local_1 != -1)
               {
                  _local_4++;
               }
               _local_5++;
            }
            if(_local_4 >= 7)
            {
               _local_3 = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("AlertDialog.Info"),LanguageMgr.GetTranslation("choosecharacter.ChooseCharacterView.string"),LanguageMgr.GetTranslation("ok"),"",false,false,false,2);
               _local_3.addEventListener("response",this.__onAlertResponse);
               return 0;
            }
            return 1;
         }
         this.loadNickNameShieldTxt();
         return 2;
      }
      
      private function loadNickNameShieldTxt() : void
      {
         var _local_2:URLRequest = new URLRequest(PathManager.FLASHSITE + "registerNickNameShield.txt");
         var _local_1:URLLoader = new URLLoader();
         _local_1.addEventListener("complete",this.__onLoadNickNameShieldTxtComplete);
         _local_1.addEventListener("ioError",this.__onLoadNickNameShieldTxtError);
         _local_1.load(_local_2);
      }
      
      private function __onLoadNickNameShieldTxtComplete(_arg_1:Event) : void
      {
         _arg_1.target.removeEventListener("complete",this.__onLoadNickNameShieldTxtComplete);
         _arg_1.target.removeEventListener("ioError",this.__onLoadNickNameShieldTxtError);
         this.nickNameShield = _arg_1.target.data;
         var _local_2:int = this.checkShieldNickName();
         if(_local_2 == 1)
         {
            this.createCheckLoader(this.checkCallBack);
         }
      }
      
      private function __onLoadNickNameShieldTxtError(_arg_1:ErrorEvent) : void
      {
         _arg_1.target.removeEventListener("complete",this.__onLoadNickNameShieldTxtComplete);
         _arg_1.target.removeEventListener("ioError",this.__onLoadNickNameShieldTxtError);
         trace("onLoadNickNameShieldTxtError:" + _arg_1.text);
         this.createCheckLoader(this.checkCallBack);
      }
      
      public function close() : void
      {
         dispatchEvent(new Event("complete"));
      }
      
      override public function dispose() : void
      {
         this.removeEvent();
         if(Boolean(this._tittleField))
         {
            ObjectUtils.disposeObject(this._tittleField);
            this._tittleField = null;
         }
         if(Boolean(this._resultField))
         {
            ObjectUtils.disposeObject(this._resultField);
            this._resultField = null;
         }
         if(Boolean(this._nicknameInput))
         {
            ObjectUtils.disposeObject(this._nicknameInput);
            this._nicknameInput = null;
         }
         if(Boolean(this._checkButton))
         {
            ObjectUtils.disposeObject(this._checkButton);
            this._checkButton = null;
         }
         if(Boolean(this._submitButton))
         {
            ObjectUtils.disposeObject(this._submitButton);
            this._submitButton = null;
         }
         if(Boolean(this._inputBackground))
         {
            ObjectUtils.disposeObject(this._inputBackground);
            this._inputBackground = null;
         }
         this._resultDefaultFormat = null;
         this._avialableFormat = null;
         this._disEnabledFilters = null;
         super.dispose();
      }
   }
}

