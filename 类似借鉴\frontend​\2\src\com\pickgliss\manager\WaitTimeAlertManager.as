package com.pickgliss.manager
{
   import com.pickgliss.ui.AlertManager;
   import com.pickgliss.ui.controls.alert.BaseAlerFrame;
   import flash.events.TimerEvent;
   import flash.utils.Timer;
   
   public class WaitTimeAlertManager
   {
      
      private static var _instance:WaitTimeAlertManager;
      
      private var _waitTimer:Timer;
      
      private var _waitFrame:BaseAlerFrame;
      
      private var _waitPalyerCount:int;
      
      private var _waitTime:int;
      
      private var _callBack:Function;
      
      private var _msg:String;
      
      private var _progressArr:Array = ["...","....",".....","......"];
      
      private var _currentProgress:uint = 0;
      
      public function WaitTimeAlertManager()
      {
         super();
      }
      
      public static function get Instance() : WaitTimeAlertManager
      {
         if(_instance == null)
         {
            _instance = new WaitTimeAlertManager();
         }
         return _instance;
      }
      
      public function createWaitFrame(_arg_1:String, _arg_2:String, _arg_3:int, _arg_4:int, _arg_5:Function = null) : BaseAlerFrame
      {
         this._msg = _arg_2;
         this._waitPalyerCount = _arg_3;
         this._waitTime = _arg_4;
         this._callBack = _arg_5;
         this.clearWaitFrame();
         this._waitFrame = AlertManager.Instance.simpleAlert(_arg_1,"","","",false,false,false,2,null,"waitTimeFrameAlert");
         this.showWaitFrameMsg();
         this.createWaitTimer();
         return this._waitFrame;
      }
      
      private function showWaitFrameMsg() : void
      {
         var _local_1:String = this._msg.replace(/r/g,this._waitPalyerCount);
         var _local_2:String = this._msg.replace(/r/g,this._waitTime);
         if(Boolean(this._waitFrame))
         {
            this._waitFrame.info.data = _local_2 + this.getProgressStr();
         }
      }
      
      private function getProgressStr() : String
      {
         var _local_1:String = this._progressArr[this._currentProgress];
         ++this._currentProgress;
         if(this._currentProgress > this._progressArr.length - 1)
         {
            this._currentProgress = 0;
         }
         return "\n" + _local_1;
      }
      
      public function updateWaitFrameMsg(_arg_1:String) : void
      {
         if(Boolean(this._waitFrame))
         {
            this._waitFrame.info.data = _arg_1;
         }
      }
      
      private function createWaitTimer() : void
      {
         if(Boolean(this._waitTimer))
         {
            this._waitTimer.removeEventListener("timer",this.__waitTimerHandler);
            this._waitTimer.stop();
            this._waitTimer = null;
         }
         this._waitTimer = new Timer(1000);
         this._waitTimer.addEventListener("timer",this.__waitTimerHandler);
         this._waitTimer.start();
      }
      
      private function __waitTimerHandler(_arg_1:TimerEvent) : void
      {
         if(this._waitTime > 0)
         {
            --this._waitTime;
            this.showWaitFrameMsg();
         }
         else
         {
            this.clearWaitTimer();
            if(this._callBack != null)
            {
               this._callBack();
            }
         }
      }
      
      private function clearWaitFrame() : void
      {
         if(Boolean(this._waitFrame))
         {
            this._waitFrame.dispose();
            this._waitFrame = null;
         }
      }
      
      private function clearWaitTimer() : void
      {
         if(Boolean(this._waitTimer))
         {
            this._waitTimer.removeEventListener("timer",this.__waitTimerHandler);
            this._waitTimer.stop();
            this._waitTimer = null;
         }
      }
      
      public function dispose() : void
      {
         this.clearWaitTimer();
         this.clearWaitFrame();
         this._waitTime = 0;
      }
   }
}

