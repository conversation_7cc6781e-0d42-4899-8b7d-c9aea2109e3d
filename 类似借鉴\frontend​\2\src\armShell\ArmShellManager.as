package armShell
{
   import ddt.CoreManager;
   import ddt.events.CEvent;
   import flash.events.IEventDispatcher;
   
   public class ArmShellManager extends CoreManager
   {
      
      private static var _instance:ArmShellManager;
      
      public static const SHOWARMSHELLFRAME:String = "showArmShellFrame";
      
      public function ArmShellManager(_arg_1:IEventDispatcher = null)
      {
         super(_arg_1);
      }
      
      public static function get instance() : ArmShellManager
      {
         if(_instance == null)
         {
            _instance = new ArmShellManager();
         }
         return _instance;
      }
      
      public function showArmShellFrame() : void
      {
         show();
      }
      
      override protected function start() : void
      {
         dispatchEvent(new CEvent("showArmShellFrame"));
      }
   }
}

