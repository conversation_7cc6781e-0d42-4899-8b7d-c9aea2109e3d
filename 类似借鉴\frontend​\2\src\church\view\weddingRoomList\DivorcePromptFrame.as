package church.view.weddingRoomList
{
   import com.pickgliss.events.FrameEvent;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.ComponentSetting;
   import com.pickgliss.ui.LayerManager;
   import com.pickgliss.ui.controls.alert.BaseAlerFrame;
   import com.pickgliss.ui.text.FilterFrameText;
   import com.pickgliss.ui.vo.AlertInfo;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.events.PkgEvent;
   import ddt.manager.LanguageMgr;
   import ddt.manager.PlayerManager;
   import ddt.manager.ServerConfigManager;
   import ddt.manager.SharedManager;
   import ddt.manager.SocketManager;
   import ddt.manager.SoundManager;
   import ddt.manager.StateManager;
   import ddt.manager.TimeManager;
   import quest.QuestDescTextAnalyz;
   
   public class DivorcePromptFrame extends BaseAlerFrame
   {
      
      private static var _instance:DivorcePromptFrame;
      
      private var _alertInfo:AlertInfo;
      
      private var _infoText:FilterFrameText;
      
      public var isOpenDivorce:Boolean = false;
      
      public function DivorcePromptFrame()
      {
         super();
         this.initialize();
      }
      
      public static function get Instance() : DivorcePromptFrame
      {
         if(_instance == null)
         {
            _instance = ComponentFactory.Instance.creatComponentByStylename("DivorcePromptFrame");
         }
         return _instance;
      }
      
      protected function initialize() : void
      {
         this.setView();
         this.setEvent();
      }
      
      private function setView() : void
      {
         cancelButtonStyle = "core.simplebt";
         submitButtonStyle = "core.simplebt";
         this._alertInfo = new AlertInfo(LanguageMgr.GetTranslation("tank.view.task.TaskCatalogContentView.tip"),LanguageMgr.GetTranslation("church.view.weddingRoomList.DivorcePromptFrame.yes"),LanguageMgr.GetTranslation("church.view.weddingRoomList.DivorcePromptFrame.no"));
         this._alertInfo.moveEnable = false;
         info = this._alertInfo;
         this.escEnable = true;
         this._infoText = ComponentFactory.Instance.creatComponentByStylename("DivorcePromptFrameText");
         addToContent(this._infoText);
      }
      
      public function show() : void
      {
         if(PlayerManager.Instance.Self.SpouseName != null && PlayerManager.Instance.Self.SpouseName != "")
         {
            SocketManager.Instance.out.sendMateTime(PlayerManager.Instance.Self.SpouseID);
            SocketManager.Instance.addEventListener(PkgEvent.format(85),this.__mateTimeA);
            SharedManager.Instance.divorceBoolean = false;
            SharedManager.Instance.save();
         }
      }
      
      private function __mateTimeA(_arg_1:PkgEvent) : void
      {
         var _local_3:Date = _arg_1.pkg.readDate();
         SocketManager.Instance.removeEventListener(PkgEvent.format(85),this.__mateTimeA);
         var _local_2:Date = TimeManager.Instance.Now();
         var _local_4:int = int(int((_local_2.valueOf() - _local_3.valueOf()) / 3600000));
         if(_local_4 > 720)
         {
            LayerManager.Instance.addToLayer(this,3,true,1);
            this.setTxt(_local_4);
         }
         else
         {
            this.dispose();
         }
      }
      
      private function setTxt(_arg_1:int) : void
      {
         var _local_3:int = 1;
         var _local_2:* = 999;
         if(_arg_1 >= 2160)
         {
            _local_3 = 3;
            _local_2 = 0;
         }
         else if(PlayerManager.Instance.Self.isFirstDivorce == 0 || _arg_1 >= 720)
         {
            _local_2 = ServerConfigManager.instance.firstDivorcedMoney;
         }
         else
         {
            ServerConfigManager.instance.firstDivorcedMoney;
         }
         var _local_4:String = LanguageMgr.GetTranslation("church.weddingRoom.frame.AddWeddingRoomFrame.frameInfo",_local_3,_local_2);
         _local_4 = _local_4.replace(/XXXX/g,"<font COLOR=\'#FF0000\'>" + PlayerManager.Instance.Self.SpouseName + "</font>");
         _local_4 = QuestDescTextAnalyz.start(_local_4);
         this._infoText.htmlText = _local_4;
      }
      
      private function removeView() : void
      {
      }
      
      private function setEvent() : void
      {
         addEventListener("response",this.onFrameResponse);
      }
      
      private function removeEvent() : void
      {
         removeEventListener("response",this.onFrameResponse);
      }
      
      private function onFrameResponse(_arg_1:FrameEvent) : void
      {
         SoundManager.instance.play("008");
         switch(_arg_1.responseCode)
         {
            case 0:
            case 1:
            case 4:
               this.dispose();
               return;
            case 2:
            case 3:
               this.isOpenDivorce = true;
               this.dispose();
               StateManager.setState("ddtchurchroomlist");
               ComponentSetting.SEND_USELOG_ID(6);
         }
      }
      
      override public function dispose() : void
      {
         super.dispose();
         if(Boolean(this._infoText))
         {
            ObjectUtils.disposeObject(this._infoText);
         }
         this._infoText = null;
         this.removeEvent();
         this.removeView();
      }
   }
}

