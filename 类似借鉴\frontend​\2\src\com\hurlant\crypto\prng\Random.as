package com.hurlant.crypto.prng
{
   import com.hurlant.util.Memory;
   import flash.system.Capabilities;
   import flash.system.System;
   import flash.text.Font;
   import flash.utils.ByteArray;
   import flash.utils.getTimer;
   
   public class Random
   {
      
      private var psize:int;
      
      private var ready:<PERSON>olean = false;
      
      private var seeded:Boolean = false;
      
      private var state:IPRNG;
      
      private var pool:ByteArray;
      
      private var pptr:int;
      
      public function Random(_arg_1:Class = null)
      {
         var _local_2:uint = 0;
         var _local_3:* = undefined;
         var _local_4:* = undefined;
         super();
         if(_arg_1 == null)
         {
            _arg_1 = ARC4;
         }
         this.state = new _arg_1() as IPRNG;
         this.psize = this.state.getPoolSize();
         this.pool = new ByteArray();
         this.pptr = 0;
         while(this.pptr < this.psize)
         {
            _local_2 = uint(65536 * Math.random());
            _local_3 = this.pptr++;
            this.pool[_local_3] = _local_2 >>> 8;
            _local_4 = this.pptr++;
            this.pool[_local_4] = _local_2 & 0xFF;
         }
         this.pptr = 0;
         this.seed();
      }
      
      public function seed(_arg_1:int = 0) : void
      {
         if(_arg_1 == 0)
         {
            _arg_1 = new Date().getTime();
         }
         var _local_2:* = this.pptr++;
         this.pool[_local_2] ^= _arg_1 & 0xFF;
         var _local_3:* = this.pptr++;
         this.pool[_local_3] ^= _arg_1 >> 8 & 0xFF;
         var _local_4:* = this.pptr++;
         this.pool[_local_4] ^= _arg_1 >> 16 & 0xFF;
         var _local_5:* = this.pptr++;
         this.pool[_local_5] ^= _arg_1 >> 24 & 0xFF;
         this.pptr %= this.psize;
         this.seeded = true;
      }
      
      public function toString() : String
      {
         return "random-" + this.state.toString();
      }
      
      public function dispose() : void
      {
         var _local_1:uint = 0;
         _local_1 = 0;
         while(_local_1 < this.pool.length)
         {
            this.pool[_local_1] = Math.random() * 256;
            _local_1++;
         }
         this.pool.length = 0;
         this.pool = null;
         this.state.dispose();
         this.state = null;
         this.psize = 0;
         this.pptr = 0;
         Memory.gc();
      }
      
      public function autoSeed() : void
      {
         var _local_1:ByteArray = null;
         var _local_2:Array = null;
         var _local_3:Font = null;
         _local_1 = new ByteArray();
         _local_1.writeUnsignedInt(System.totalMemory);
         _local_1.writeUTF(Capabilities.serverString);
         _local_1.writeUnsignedInt(getTimer());
         _local_1.writeUnsignedInt(new Date().getTime());
         _local_2 = Font.enumerateFonts(true);
         for each(_local_3 in _local_2)
         {
            _local_1.writeUTF(_local_3.fontName);
            _local_1.writeUTF(_local_3.fontStyle);
            _local_1.writeUTF(_local_3.fontType);
         }
         _local_1.position = 0;
         while(_local_1.bytesAvailable >= 4)
         {
            this.seed(_local_1.readUnsignedInt());
         }
      }
      
      public function nextByte() : int
      {
         if(!this.ready)
         {
            if(!this.seeded)
            {
               this.autoSeed();
            }
            this.state.init(this.pool);
            this.pool.length = 0;
            this.pptr = 0;
            this.ready = true;
         }
         return this.state.next();
      }
      
      public function nextBytes(_arg_1:ByteArray, _arg_2:int) : void
      {
         while(Boolean(_arg_2--))
         {
            _arg_1.writeByte(this.nextByte());
         }
      }
   }
}

