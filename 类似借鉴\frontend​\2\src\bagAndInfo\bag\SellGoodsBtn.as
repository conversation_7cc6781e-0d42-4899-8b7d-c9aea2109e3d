package bagAndInfo.bag
{
   import bagAndInfo.cell.BagCell;
   import bagAndInfo.cell.DragEffect;
   import baglocked.BaglockedManager;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.LayerManager;
   import com.pickgliss.ui.controls.TextButton;
   import ddt.data.EquipType;
   import ddt.interfaces.ICell;
   import ddt.interfaces.IDragable;
   import ddt.manager.DragManager;
   import ddt.manager.LanguageMgr;
   import ddt.manager.MessageTipManager;
   import ddt.manager.PlayerManager;
   import ddt.manager.SoundManager;
   import flash.display.Bitmap;
   import flash.events.Event;
   import flash.filters.ColorMatrixFilter;
   
   public class SellGoodsBtn extends TextButton implements IDragable
   {
      
      public static const StopSell:String = "stopsell";
      
      public var isActive:Boolean = false;
      
      private var sellFrame:SellGoodsFrame;
      
      private var lightingFilter:ColorMatrixFilter;
      
      private var _dragTarget:BagCell;
      
      public function SellGoodsBtn()
      {
         super();
         this.init();
      }
      
      override protected function init() : void
      {
         buttonMode = true;
         super.init();
      }
      
      public function dragStart(_arg_1:Number, _arg_2:Number) : void
      {
         this.isActive = true;
         var _local_3:Bitmap = ComponentFactory.Instance.creatBitmap("bagAndInfo.bag.sellIconAsset");
         DragManager.startDrag(this,this,_local_3,_arg_1,_arg_2,"move",false);
      }
      
      public function dragStop(_arg_1:DragEffect) : void
      {
         var _local_2:* = null;
         this.isActive = false;
         if(PlayerManager.Instance.Self.bagLocked && _arg_1.target is ICell)
         {
            BaglockedManager.Instance.show();
            this.isActive = true;
            if(_arg_1.target is BagCell)
            {
               (_arg_1.target as BagCell).locked = false;
               (_arg_1.target as BagCell).allowDrag = true;
               dispatchEvent(new Event("stopsell"));
               this.isActive = false;
            }
            return;
         }
         if(_arg_1.action == "move" && _arg_1.target is ICell)
         {
            _local_2 = _arg_1.target as BagCell;
            if(_local_2 && Boolean(_local_2.info))
            {
               if(EquipType.isValuableEquip(_local_2.info))
               {
                  MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.view.bagII.SellGoodsBtn.CantSellEquip"));
                  _local_2.locked = false;
                  dispatchEvent(new Event("stopsell"));
               }
               else if(EquipType.isPetSpeciallFood(_local_2.info))
               {
                  MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.bagAndInfo.sell.CanNotSell"));
                  _local_2.locked = false;
                  dispatchEvent(new Event("stopsell"));
               }
               else if(_local_2.info.CategoryID == 34)
               {
                  MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.bagAndInfo.sell.CanNotSell"));
                  _local_2.locked = false;
                  dispatchEvent(new Event("stopsell"));
               }
               else
               {
                  this._dragTarget = _local_2;
                  this.showSellFrame();
               }
            }
            else
            {
               dispatchEvent(new Event("stopsell"));
            }
         }
         else
         {
            dispatchEvent(new Event("stopsell"));
         }
      }
      
      private function showSellFrame() : void
      {
         SoundManager.instance.play("008");
         if(this.sellFrame == null)
         {
            this.sellFrame = ComponentFactory.Instance.creatComponentByStylename("sellGoodsFrame");
            this.sellFrame.itemInfo = this._dragTarget.itemInfo;
            this.sellFrame.addEventListener("cancel",this.cancelBack);
            this.sellFrame.addEventListener("ok",this.confirmBack);
         }
         LayerManager.Instance.addToLayer(this.sellFrame,2,true,1);
      }
      
      public function getSource() : IDragable
      {
         return this;
      }
      
      public function getDragData() : Object
      {
         return this;
      }
      
      private function confirmBack(_arg_1:Event) : void
      {
         if(Boolean(stage))
         {
            this.dragStart(stage.mouseX,stage.mouseY);
         }
         this.__disposeSellFrame();
      }
      
      private function setUpLintingFilter() : void
      {
         var _local_1:Array = [];
         _local_1 = _local_1.concat([1,0,0,0,25]);
         _local_1 = _local_1.concat([0,1,0,0,25]);
         _local_1 = _local_1.concat([0,0,1,0,25]);
         _local_1 = _local_1.concat([0,0,0,1,0]);
         this.lightingFilter = new ColorMatrixFilter(_local_1);
      }
      
      override public function dispose() : void
      {
         if(Boolean(this._dragTarget))
         {
            this._dragTarget.locked = false;
         }
         PlayerManager.Instance.Self.Bag.unLockAll();
         this.__disposeSellFrame();
         super.dispose();
      }
      
      private function __disposeSellFrame() : void
      {
         if(Boolean(this.sellFrame))
         {
            this.sellFrame.removeEventListener("cancel",this.cancelBack);
            this.sellFrame.removeEventListener("ok",this.confirmBack);
            this.sellFrame.dispose();
         }
         this.sellFrame = null;
      }
      
      private function cancelBack(_arg_1:Event) : void
      {
         if(Boolean(this._dragTarget))
         {
            this._dragTarget.locked = false;
         }
         if(Boolean(stage))
         {
            this.dragStart(stage.mouseX,stage.mouseY);
         }
         this.__disposeSellFrame();
      }
   }
}

