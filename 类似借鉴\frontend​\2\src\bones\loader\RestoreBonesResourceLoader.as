package bones.loader
{
   import bones.model.BoneVo;
   import com.pickgliss.loader.BaseLoader;
   import com.pickgliss.loader.LoadResourceManager;
   import com.pickgliss.loader.LoaderEvent;
   import com.pickgliss.ui.core.Disposeable;
   import deng.fzip.FZip;
   import flash.display.Bitmap;
   import flash.display.Loader;
   import flash.events.Event;
   import flash.events.EventDispatcher;
   import flash.utils.ByteArray;
   
   public class RestoreBonesResourceLoader extends EventDispatcher implements Disposeable
   {
      
      protected var _vo:BoneVo;
      
      private var _skeletonList:Array;
      
      private var _loader:Loader;
      
      private var _image:Bitmap = null;
      
      private var _atlas:XML;
      
      private var _fzip:FZip;
      
      public function RestoreBonesResourceLoader(_arg_1:BoneVo)
      {
         super();
         this._vo = _arg_1;
         this._skeletonList = [];
      }
      
      public function load() : void
      {
         var _local_1:BaseLoader = LoadResourceManager.Instance.createLoader(this.getLoaderPath(this._vo),3);
         _local_1.addEventListener("complete",this.__onLoadComplete);
         LoadResourceManager.Instance.startLoad(_local_1);
      }
      
      private function __onLoadComplete(_arg_1:LoaderEvent) : void
      {
         _arg_1.loader.removeEventListener("complete",this.__onLoadComplete);
         var _local_2:ByteArray = _arg_1.loader.content;
         var _local_3:FZip = new FZip();
         _local_3.addEventListener("complete",this.__onZipParaComplete);
         _local_3.loadBytes(_local_2);
      }
      
      private function __onZipParaComplete(_arg_1:Event) : void
      {
         this._fzip = _arg_1.currentTarget as FZip;
         this._fzip.removeEventListener("complete",this.__onZipParaComplete);
         this._loader = new Loader();
         this._loader.contentLoaderInfo.addEventListener("complete",this.onLoadBitmapComplete);
         var _local_2:ByteArray = this._fzip.getFileByName(this._vo.atlasName + ".png").content;
         this._loader.loadBytes(_local_2);
      }
      
      private function onLoadBitmapComplete(_arg_1:Event) : void
      {
         this._loader.contentLoaderInfo.removeEventListener("complete",this.onLoadBitmapComplete);
         this._image = this._loader.content as Bitmap;
         this._fzip.close();
         this._loader.unload();
         this.loaderComplete();
      }
      
      public function loaderComplete() : void
      {
         dispatchEvent(new Event("complete"));
      }
      
      private function getLoaderPath(_arg_1:BoneVo) : String
      {
         var _local_2:* = null;
         if(this._vo.loadType == 1)
         {
            _local_2 = BonesLoaderManager.RESOURCE_SITE;
         }
         else
         {
            _local_2 = BonesLoaderManager.SITE_MAIN;
         }
         return _local_2 + _arg_1.path + _arg_1.atlasName + _arg_1.ext;
      }
      
      public function get image() : Bitmap
      {
         return this._image;
      }
      
      public function get atlas() : XML
      {
         return this._atlas;
      }
      
      public function get skeletonList() : Array
      {
         return this._skeletonList;
      }
      
      public function get vo() : BoneVo
      {
         return this._vo;
      }
      
      public function dispose() : void
      {
         while(Boolean(this._skeletonList.length))
         {
            this._skeletonList.pop();
         }
         if(Boolean(this._image) && Boolean(this._image.bitmapData))
         {
            this._image.bitmapData.dispose();
         }
         this._vo = null;
         this._atlas = null;
         this._image = null;
         this._skeletonList = null;
         this._fzip = null;
         this._loader = null;
      }
   }
}

