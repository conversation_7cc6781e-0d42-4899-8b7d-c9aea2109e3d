package com.hurlant.math
{
   use namespace bi_internal;
   
   internal class ClassicReduction implements IReduction
   {
      
      private var m:BigInteger;
      
      public function ClassicReduction(_arg_1:BigInteger)
      {
         super();
         this.m = _arg_1;
      }
      
      public function revert(_arg_1:BigInteger) : BigInteger
      {
         return _arg_1;
      }
      
      public function reduce(_arg_1:BigInteger) : void
      {
         _arg_1.bi_internal::divRemTo(this.m,null,_arg_1);
      }
      
      public function convert(_arg_1:BigInteger) : BigInteger
      {
         if(_arg_1.bi_internal::s < 0 || _arg_1.compareTo(this.m) >= 0)
         {
            return _arg_1.mod(this.m);
         }
         return _arg_1;
      }
      
      public function sqrTo(_arg_1:BigInteger, _arg_2:BigInteger) : void
      {
         _arg_1.bi_internal::squareTo(_arg_2);
         this.reduce(_arg_2);
      }
      
      public function mulTo(_arg_1:BigInte<PERSON>, _arg_2:<PERSON>Inte<PERSON>, _arg_3:BigInteger) : void
      {
         _arg_1.bi_internal::multiplyTo(_arg_2,_arg_3);
         this.reduce(_arg_3);
      }
   }
}

