package caveloot
{
   import flash.events.Event;
   
   public class CaveLootEvent extends Event
   {
      
      public static const UPDATE_ACTIVITYSTATE:String = "updateActivityState";
      
      public static const UPDATE_MINE_VIEW:String = "updateMineView";
      
      public static const UPDATE_MINE_BAG:String = "updateMineBag";
      
      public static const UPDATE_PROGRESS_VIEW:String = "updateProgressView";
      
      public static const UPDATE_RANK_VIEW:String = "updateRankView";
      
      public static const GET_MINERESLUT:String = "getMineReslut";
      
      public static const UPDATE_SHOP_VIEW:String = "updateShopView";
      
      public var data:Object;
      
      public function CaveLootEvent(_arg_1:String, _arg_2:Object = null)
      {
         super(_arg_1);
         this.data = _arg_2;
      }
   }
}

