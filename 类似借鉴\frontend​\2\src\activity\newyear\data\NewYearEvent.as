package activity.newyear.data
{
   import flash.events.Event;
   
   public class NewYear<PERSON>vent extends Event
   {
      
      public static const ACTIVITE_STATE_CHANGE:String = "activitestatechange";
      
      public static const UPDATE_INFO:String = "updateinfo";
      
      public static const UPDATE_RANK:String = "updaterank";
      
      public static const UPDATE_CONTENT:String = "updateContent";
      
      public function NewYearEvent(_arg_1:String, _arg_2:Bo<PERSON>an = false, _arg_3:<PERSON><PERSON><PERSON> = false)
      {
         super(_arg_1,_arg_2,_arg_3);
      }
   }
}

