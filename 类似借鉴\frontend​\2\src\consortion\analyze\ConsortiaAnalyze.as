package consortion.analyze
{
   import com.pickgliss.loader.DataAnalyzer;
   import com.pickgliss.utils.ObjectUtils;
   import consortion.rank.RankData;
   
   public class ConsortiaAnalyze extends DataAnalyzer
   {
      
      private var _dataList:Array;
      
      public function ConsortiaAnalyze(_arg_1:Function)
      {
         super(_arg_1);
      }
      
      public function get dataList() : Array
      {
         return this._dataList;
      }
      
      override public function analyze(_arg_1:*) : void
      {
         var _local_6:int = 0;
         var _local_3:* = undefined;
         var _local_2:* = null;
         this._dataList = [];
         var _local_4:XML = new XML(_arg_1);
         var _local_5:XMLList = _local_4.Item;
         _local_6 = 0;
         while(_local_6 < _local_5.length())
         {
            _local_2 = new RankData();
            ObjectUtils.copyPorpertiesByXML(_local_2,_local_5[_local_6]);
            this._dataList.push(_local_2);
            _local_6++;
         }
         this._dataList.sortOn("Rank",16);
         for each(_local_3 in this._dataList)
         {
            trace("======>" + _local_3.Rank);
         }
         onAnalyzeComplete();
      }
   }
}

