package consortiaDomain
{
   public class ConsortiaDomainPackageType
   {
      
      public static const RemovePlayer:int = 1;
      
      public static const Enter:int = 2;
      
      public static const OtherInfo:int = 3;
      
      public static const MOVE:int = 4;
      
      public static const Fight:int = 5;
      
      public static const Active:int = 7;
      
      public static const Repair:int = 8;
      
      public static const MonsterInfoInFight:int = 9;
      
      public static const BuildInfoInFight:int = 10;
      
      public static const GetConsortiaInfo:int = 12;
      
      public static const KillInfo:int = 13;
      
      public static const FightState:int = 14;
      
      public static const ActiveSate:int = 15;
      
      public static const MonsterInfoSingle:int = 16;
      
      public static const buildRepairInfo:int = 17;
      
      public static const playerRepairChange:int = 18;
      
      public static const ReduceBloodState:int = 19;
      
      public function ConsortiaDomainPackageType()
      {
         super();
      }
   }
}

