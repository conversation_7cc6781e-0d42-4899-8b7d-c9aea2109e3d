package consortiaDomain
{
   import com.pickgliss.events.FrameEvent;
   import com.pickgliss.toplevel.StageReferance;
   import com.pickgliss.ui.AlertManager;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.LayerManager;
   import com.pickgliss.ui.UICreatShortcut;
   import com.pickgliss.ui.controls.TextButton;
   import com.pickgliss.ui.controls.alert.BaseAlerFrame;
   import com.pickgliss.utils.ObjectUtils;
   import consortion.ConsortionModelManager;
   import ddt.CoreManager;
   import ddt.data.ServerConfigInfo;
   import ddt.events.CEvent;
   import ddt.events.PkgEvent;
   import ddt.manager.ChatManager;
   import ddt.manager.GameInSocketOut;
   import ddt.manager.LanguageMgr;
   import ddt.manager.MessageTipManager;
   import ddt.manager.PlayerManager;
   import ddt.manager.ServerConfigManager;
   import ddt.manager.SocketManager;
   import ddt.manager.SoundManager;
   import ddt.manager.StateManager;
   import ddt.manager.TimeManager;
   import ddt.view.ConsortiaDomainIcon;
   import ddt.view.chat.ChatData;
   import email.MailManager;
   import flash.display.BitmapData;
   import flash.events.Event;
   import flash.events.IEventDispatcher;
   import flash.events.KeyboardEvent;
   import flash.events.TimerEvent;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import flash.utils.Timer;
   import hall.IHallStateView;
   import hall.aStar.AStarPathFinder;
   import org.aswing.KeyboardManager;
   import quest.TaskManager;
   import road7th.comm.PackageIn;
   import room.RoomManager;
   import starling.scene.hall.SceneMapGridData;
   import starling.textures.Texture;
   
   public class ConsortiaDomainManager extends CoreManager
   {
      
      private static var _instance:ConsortiaDomainManager;
      
      public static const EVENT_BUILD_IN_FIGHT_STATE_CHANGE:String = "event_build_in_fight_state_change";
      
      public static const EVENT_MONSTER_STATE_CHANGE:String = "event_monster_state_change";
      
      public static const EVENT_KILL_RANK_UPDATE:String = "event_kill_rank_update";
      
      public static const EVENT_GET_CONSORTIA_INFO_RES:String = "event_get_consortia_info_res";
      
      public static const EVENT_ACTIVE_RES:String = "event_active_res";
      
      public static const EVENT_ACTIVE_STATE_CHANGE:String = "event_active_state_change";
      
      public static const EVENT_REMOVE_OTHER_PLAYER:String = "event_remove_other_player";
      
      public static const EVENT_MONSTER_INFO_SINGLE:String = "event_monster_info_single";
      
      public static const EVENT_REMOVE_CHILD_MONSTER:String = "event_remove_child_monster";
      
      public static const EVENT_REPAIR_PLAYER_NUM_CHANGE:String = "event_repair_player_num_change";
      
      public static const EVENT_MONSTER_BORN:String = "event_monster_born";
      
      public static const ACTIVE_STATE_MIN10_BEFORE:int = -10;
      
      public static const ACTIVE_STATE_MIN5_BEFORE:int = -5;
      
      public static const ACTIVE_STATE_MIN4_BEFORE:int = -4;
      
      public static const ACTIVE_STATE_MIN3_BEFORE:int = -3;
      
      public static const ACTIVE_STATE_MIN2_BEFORE:int = -2;
      
      public static const ACTIVE_STATE_MIN1_BEFORE:int = -1;
      
      public static const ACTIVE_STATE_INIT:int = 0;
      
      public static const ACTIVE_STATE_OPEN:int = 1;
      
      public static const ACTIVE_STATE_END:int = 100;
      
      public static const CONSORTIA_HOME_BANK_ID:int = 1;
      
      public static const CONSORTIA_SKILL_ID:int = 2;
      
      public static const CONSORTIA_SHOP_ID:int = 3;
      
      public static const CONSORTIA_BAG_STORE_ID:int = 4;
      
      public static const CONSORTIA_CITY_ID:int = 5;
      
      public static const BUILD_CENTER_POS_ARR:Array = [null,new Point(856,902),new Point(2221,851),new Point(1140,495),new Point(1982,442),new Point(1597,880)];
      
      public static const BUILD_RADIUS_ARR:Array = [null,123,180,197,169,320];
      
      public static const BUILD_RES_NAME_ARR:Array = [null,"consortiaDomainHomeBank","consortiaDomainConsortiaSkill","consortiaDomainConsortiaShop","consortiaDomainBagStore","consortiaDomainConsortiaCity"];
      
      public static const MONSTER_ATK_TYPE_NORMAL:int = 0;
      
      public static const MONSTER_ATK_TYPE_BUILD:int = 1;
      
      public static const MONSTER_BORN_BUILD_STATE_CLOSE:int = 1;
      
      public static const MONSTER_BORN_BUILD_STATE_OPEN:int = 2;
      
      public static const MONSTER_STATE_BORN:int = 1;
      
      public static const MONSTER_STATE_WALK:int = 3;
      
      public static const MONSTER_STATE_BEAT_BUILD:int = 4;
      
      public static const MONSTER_STATE_BEAT_PLAYER:int = 5;
      
      public static const MONSTER_STATE_DIE:int = 6;
      
      public static const MONSTER_STATE_NOT_BORN:int = 100;
      
      public static const PLAYER_STATE_STAND:int = 1;
      
      public static const PLAYER_STATE_WALK:int = 2;
      
      public static const PLAYER_STATE_BEAT_MONSTER:int = 3;
      
      public static const PLAYER_STATE_DIE:int = 4;
      
      public static const BUILD_STATE_NORMAL:int = 1;
      
      public static const BUILD_STATE_WAIT_GRADE:int = 2;
      
      public static const BUILD_STATE_REPAIR:int = 3;
      
      public static const BUILD_STATE_WAIT_REPAIR:int = 4;
      
      public static const BUILD_STATE_FIGHT:int = 5;
      
      public static const BUILD_STATE_BE_BEAT:int = 6;
      
      public static const MONSTER_ACTION_STAND:String = "stand";
      
      public static const MONSTER_ACTION_WALK:String = "walk";
      
      public static const MONSTER_ACTION_DIE:String = "die";
      
      public static const BUILD_LOW_HP_WARN_ARR:Array = [0.75,0.5,0.25,0.1,0.05,0.01];
      
      public static const AUTO_REPAIR_TIME:int = 172800000;
      
      public static var CAN_USE_K:Boolean = true;
      
      public static var CAN_USE_R:Boolean = true;
      
      public static var CAN_USE_Q:Boolean = true;
      
      public var model:ConsortiaDomainModel;
      
      public var sceneMapGridData:SceneMapGridData;
      
      public var bgLayerViewRect:Rectangle;
      
      private var _hall:IHallStateView;
      
      private var _icon:ConsortiaDomainIcon;
      
      private var _secTickTimer:Timer;
      
      public var aStarPathFinder:AStarPathFinder;
      
      private var _hasAskActiveSate:Boolean = false;
      
      public var activeState:int = 0;
      
      private var _buildViewUpGradeBtnTexture:Texture;
      
      private var _buildViewOpenBtnTexture:Texture;
      
      private var _bulidViewBtn:TextButton;
      
      public var consortiaLandBuildBlood:int;
      
      public var consortiaLandRepairBlood:int;
      
      public var consortiaLandMonsterSpeed:int;
      
      public var consortiaLandRepairCount:int;
      
      public var buildNameArr:Array;
      
      public var isShowFightMonster:Boolean = true;
      
      public function ConsortiaDomainManager(_arg_1:IEventDispatcher = null)
      {
         super();
         _instance = this;
         this.model = new ConsortiaDomainModel();
         this.model.monsterInfo = {};
         this.model.allBuildInfo = {};
         SocketManager.Instance.addEventListener(PkgEvent.format(371,12),this.onGetConsortiaInfo);
         SocketManager.Instance.addEventListener(PkgEvent.format(371,7),this.onActive);
         SocketManager.Instance.addEventListener(PkgEvent.format(371,15),this.onActiveSate);
         RoomManager.Instance.addEventListener("SINGLE_ROOM_BEGIN",this.onSingleRoomBeginRes);
      }
      
      public static function get instance() : ConsortiaDomainManager
      {
         if(_instance == null)
         {
            _instance = new ConsortiaDomainManager();
         }
         return _instance;
      }
      
      public function setup() : void
      {
      }
      
      public function askActiveSate() : void
      {
         if(this._hasAskActiveSate)
         {
            return;
         }
         SocketManager.Instance.out.getConsortiaDomainConsortiaInfo();
         SocketManager.Instance.out.getConsortiaDomainActiveState();
      }
      
      private function onActiveSate(_arg_1:PkgEvent) : void
      {
         var _local_3:* = null;
         var _local_5:* = null;
         var _local_4:PackageIn = _arg_1.pkg;
         var _local_2:int = _local_4.readInt();
         if(this.activeState != _local_2)
         {
            this.activeState = _local_2;
            this.checkIcon();
         }
         this._hasAskActiveSate = true;
         var _local_6:* = StateManager.currentStateType == "consortia_domain";
         if(this.activeState == 1)
         {
            _local_5 = new ChatData();
            _local_5.channel = 3;
            _local_5.type = 113;
            _local_5.msg = LanguageMgr.GetTranslation("consortiadomain.clickChatLinkEnterScene");
            ChatManager.Instance.chat(_local_5);
            if(StateManager.currentStateType != "fighting" && StateManager.currentStateType != "gameLoading" && !_local_6)
            {
               _local_3 = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("AlertDialog.Info"),LanguageMgr.GetTranslation("consortiadomain.activeOpenAleart"),LanguageMgr.GetTranslation("ok"),LanguageMgr.GetTranslation("cancel"),false,false,false,2);
               _local_3.addEventListener("response",this.onActiveOpenAleartResponse);
            }
         }
         if(_local_6)
         {
            this.playMusic();
         }
         dispatchEvent(new Event("event_active_state_change"));
      }
      
      private function onActiveOpenAleartResponse(_arg_1:FrameEvent) : void
      {
         SoundManager.instance.play("008");
         var _local_2:BaseAlerFrame = _arg_1.currentTarget as BaseAlerFrame;
         _local_2.removeEventListener("response",this.onActiveOpenAleartResponse);
         switch(_arg_1.responseCode)
         {
            case 2:
            case 3:
               this.enterScene(false);
         }
         _local_2.dispose();
      }
      
      private function onActive(_arg_1:PkgEvent) : void
      {
         this.model.isActive = true;
         dispatchEvent(new Event("event_active_res"));
      }
      
      private function onGetKillInfo(_arg_1:PkgEvent) : void
      {
         var _local_5:int = 0;
         var _local_4:* = null;
         var _local_3:PackageIn = _arg_1.pkg;
         var _local_2:int = _local_3.readInt();
         this.model.killRankArr = [];
         _local_5 = 0;
         while(_local_5 < _local_2)
         {
            _local_4 = {};
            _local_4["UserID"] = _local_3.readInt();
            _local_4["NickName"] = _local_3.readUTF();
            _local_4["KillCount"] = _local_3.readInt();
            this.model.killRankArr.push(_local_4);
            _local_5++;
         }
         this.model.killRankArr.sortOn("KillCount",2 | 0x10);
         _local_5 = 0;
         while(_local_5 < _local_2)
         {
            _local_4 = this.model.killRankArr[_local_5];
            _local_4["Rank"] = _local_5 + 1;
            if(_local_4["UserID"] == PlayerManager.Instance.Self.ID)
            {
               this.model.myRank = _local_4["Rank"];
               this.model.myKillNum = _local_4["KillCount"];
            }
            _local_5++;
         }
         dispatchEvent(new Event("event_kill_rank_update"));
      }
      
      protected function onSecTickTimer(_arg_1:TimerEvent) : void
      {
         var _local_4:int = 0;
         var _local_6:int = 0;
         var _local_5:int = 0;
         var _local_2:* = null;
         if(this._secTickTimer.currentCount % 5 == 0)
         {
            if(this.activeState == 0 || this.activeState == 100 || this.activeState == -10)
            {
               if(!this.isAllBuildFullHp())
               {
                  SocketManager.Instance.out.getConsortiaDomainConsortiaInfo();
               }
            }
            else if(this.activeState == 1)
            {
               SocketManager.Instance.out.getConsortiaDomainBuildInfoInFight();
               SocketManager.Instance.out.getConsortiaDomainKillInfo();
            }
         }
         var _local_3:int = -1;
         if(ConsortiaDomainManager.instance.activeState == 1)
         {
            if(Boolean(ConsortiaDomainManager.instance.model.monsterBornArr) && Boolean(ConsortiaDomainManager.instance.model.BeginTime))
            {
               _local_4 = int(int((TimeManager.Instance.NowTime() - ConsortiaDomainManager.instance.model.BeginTime.time) / 1000));
               _local_2 = ConsortiaDomainManager.instance.model.monsterBornArr;
               _local_6 = this.model.monsterWaveIndex + 1;
               while(_local_6 < _local_2.length)
               {
                  _local_5 = int(_local_2[_local_6]);
                  if(_local_5 > _local_4)
                  {
                     _local_3 = _local_6;
                  }
                  _local_6++;
               }
            }
         }
         if(_local_3 > this.model.monsterWaveIndex)
         {
            this.model.monsterWaveIndex = _local_3;
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("consortiadomain.monsterIsComing"));
            dispatchEvent(new Event("event_monster_born"));
         }
      }
      
      public function isAllBuildFullHp() : Boolean
      {
         var _local_1:EachBuildInfo = null;
         for each(_local_1 in this.model.allBuildInfo)
         {
            if(_local_1.Repair != 0)
            {
               return false;
            }
         }
         return true;
      }
      
      private function onStageResize(_arg_1:Event) : void
      {
         this.bgLayerViewRect.width = StageReferance.stageWidth;
         this.bgLayerViewRect.height = StageReferance.stageHeight;
         dispatchEvent(new Event("resize"));
      }
      
      private function initFightBuildInfo(_arg_1:int, _arg_2:PackageIn) : void
      {
         var _local_6:int = 0;
         var _local_5:* = null;
         var _local_3:EachBuildInfo = this.model.allBuildInfo[_arg_1];
         if(!_local_3)
         {
            _local_3 = new EachBuildInfo();
            _local_3.Id = _arg_1;
            this.model.allBuildInfo[_arg_1] = _local_3;
         }
         _local_3.Id = _arg_1;
         _local_3.Blood = _arg_2.readInt();
         _local_3.State = _arg_2.readInt();
         if(_local_3.State == 1)
         {
            _local_3.State = 6;
         }
         else
         {
            _local_3.State = 5;
         }
         var _local_4:* = -1;
         _local_6 = 0;
         while(_local_6 < BUILD_LOW_HP_WARN_ARR.length)
         {
            if(_local_3.Blood < BUILD_LOW_HP_WARN_ARR[_local_6] * this.consortiaLandBuildBlood && !_local_3.lowHpWarnArr[_local_6])
            {
               _local_3.lowHpWarnArr[_local_6] = true;
               _local_4 = BUILD_LOW_HP_WARN_ARR[_local_6];
            }
            _local_6++;
         }
         if(_local_4 != -1)
         {
            _local_5 = LanguageMgr.GetTranslation("consortiadomain.build.lowHp",this.buildNameArr[_arg_1],_local_4 * 100);
            ChatManager.Instance.sysChatConsortia(_local_5);
            MessageTipManager.getInstance().show(_local_5);
         }
      }
      
      private function initUnFightBuildInfo(_arg_1:int, _arg_2:PackageIn) : void
      {
         var _local_3:EachBuildInfo = this.model.allBuildInfo[_arg_1];
         if(!_local_3)
         {
            _local_3 = new EachBuildInfo();
            _local_3.Id = _arg_1;
            this.model.allBuildInfo[_arg_1] = _local_3;
         }
         if(_arg_1 == 5)
         {
            _local_3.Repair = 0;
            _local_3.State = 0;
         }
         else if(_arg_2 != null)
         {
            _local_3.Repair = _arg_2.readInt();
            _local_3.State = _arg_2.readInt();
            if(_local_3.Repair == 0)
            {
               _local_3.State = 0;
            }
         }
         _local_3.State = this.getFixUnFightBuildState(_arg_1,_local_3.State,_local_3.Repair);
      }
      
      public function isCanGradeBuild(_arg_1:int) : Boolean
      {
         if(_arg_1 == 5)
         {
            return ConsortionModelManager.Instance.model.checkConsortiaRichesForUpGrade(0) && this.checkGoldOrLevel(0);
         }
         if(_arg_1 == 4)
         {
            return ConsortionModelManager.Instance.model.checkConsortiaRichesForUpGrade(1) && this.checkGoldOrLevel(1);
         }
         if(_arg_1 == 3)
         {
            return ConsortionModelManager.Instance.model.checkConsortiaRichesForUpGrade(2) && this.checkGoldOrLevel(2);
         }
         if(_arg_1 == 1)
         {
            return ConsortionModelManager.Instance.model.checkConsortiaRichesForUpGrade(3) && this.checkGoldOrLevel(3);
         }
         if(_arg_1 == 2)
         {
            return ConsortionModelManager.Instance.model.checkConsortiaRichesForUpGrade(4) && this.checkGoldOrLevel(4);
         }
         return false;
      }
      
      public function getBuildLv(_arg_1:int) : int
      {
         if(_arg_1 == 5)
         {
            return PlayerManager.Instance.Self.consortiaInfo.Level;
         }
         if(_arg_1 == 3)
         {
            return PlayerManager.Instance.Self.consortiaInfo.ShopLevel;
         }
         if(_arg_1 == 4)
         {
            return PlayerManager.Instance.Self.consortiaInfo.SmithLevel;
         }
         if(_arg_1 == 1)
         {
            return PlayerManager.Instance.Self.consortiaInfo.StoreLevel;
         }
         if(_arg_1 == 2)
         {
            return PlayerManager.Instance.Self.consortiaInfo.BufferLevel;
         }
         return 0;
      }
      
      private function checkGoldOrLevel(_arg_1:int) : Boolean
      {
         switch(_arg_1)
         {
            case 0:
               if(PlayerManager.Instance.Self.consortiaInfo.Level >= 10)
               {
                  return false;
               }
               break;
            case 2:
               if(PlayerManager.Instance.Self.consortiaInfo.ShopLevel >= 5)
               {
                  return false;
               }
               if((PlayerManager.Instance.Self.consortiaInfo.ShopLevel + 1) * 2 > PlayerManager.Instance.Self.consortiaInfo.Level && PlayerManager.Instance.Self.consortiaInfo.Level != 10)
               {
                  return false;
               }
               break;
            case 3:
               if(PlayerManager.Instance.Self.consortiaInfo.StoreLevel >= 10)
               {
                  return false;
               }
               if(PlayerManager.Instance.Self.consortiaInfo.StoreLevel >= PlayerManager.Instance.Self.consortiaInfo.Level && PlayerManager.Instance.Self.consortiaInfo.Level != 10)
               {
                  return false;
               }
               break;
            case 1:
               if(PlayerManager.Instance.Self.consortiaInfo.SmithLevel >= 10)
               {
                  return false;
               }
               if(PlayerManager.Instance.Self.consortiaInfo.SmithLevel >= PlayerManager.Instance.Self.consortiaInfo.Level && PlayerManager.Instance.Self.consortiaInfo.Level != 10)
               {
                  return false;
               }
               break;
            case 4:
               if(PlayerManager.Instance.Self.consortiaInfo.BufferLevel >= 10)
               {
                  return false;
               }
               if(PlayerManager.Instance.Self.consortiaInfo.BufferLevel >= PlayerManager.Instance.Self.consortiaInfo.Level && PlayerManager.Instance.Self.consortiaInfo.Level != 10)
               {
                  return false;
               }
         }
         if(_arg_1 == 0 && PlayerManager.Instance.Self.Gold < ConsortionModelManager.Instance.model.getLevelData(PlayerManager.Instance.Self.consortiaInfo.Level + 1).NeedGold)
         {
            return false;
         }
         return true;
      }
      
      private function onGetMonsterInfo(_arg_1:PkgEvent) : void
      {
         var _local_9:int = 0;
         var _local_3:int = 0;
         var _local_8:int = 0;
         var _local_2:int = 0;
         var _local_6:int = 0;
         var _local_5:* = null;
         var _local_4:PackageIn = _arg_1.pkg;
         var _local_7:int = _local_4.readInt();
         this.model.monsterBornArr = [];
         _local_9 = 0;
         while(_local_9 < _local_7)
         {
            _local_3 = _local_4.readInt();
            _local_8 = this.monsterStateSeverToClient(_local_3);
            _local_2 = _local_4.readInt();
            _local_6 = 0;
            while(_local_6 < _local_2)
            {
               _local_5 = this.readMonsterInfoSingle(_local_4);
               _local_5.state = _local_8;
               _local_5.serverMonsterState = _local_3;
               if(this.model.monsterBornArr.indexOf(_local_5.BirthSecond) == -1)
               {
                  this.model.monsterBornArr.push(_local_5.BirthSecond);
               }
               _local_6++;
            }
            _local_9++;
         }
         this.model.monsterBornArr.sort(16);
         dispatchEvent(new Event("event_monster_state_change"));
      }
      
      private function monsterStateSeverToClient(_arg_1:int) : int
      {
         var _local_2:* = 2147483647;
         if(_arg_1 == -6)
         {
            _local_2 = 1;
         }
         else if(_arg_1 == -1)
         {
            _local_2 = 100;
         }
         else if(_arg_1 == -2 || _arg_1 == -3)
         {
            _local_2 = 3;
         }
         else if(_arg_1 == -4)
         {
            _local_2 = 5;
         }
         else if(_arg_1 == -5)
         {
            _local_2 = 6;
         }
         else if(_arg_1 > 0)
         {
            _local_2 = 4;
         }
         return _local_2;
      }
      
      private function readMonsterInfoSingle(_arg_1:PackageIn) : EachMonsterInfo
      {
         var _local_3:int = _arg_1.readInt();
         var _local_2:EachMonsterInfo = this.model.monsterInfo[_local_3];
         if(!_local_2)
         {
            _local_2 = new EachMonsterInfo();
            _local_2.LivingID = _local_3;
            this.model.monsterInfo[_local_3] = _local_2;
         }
         _local_2.LastTargetID = _local_2.TargetID;
         _local_2.TargetID = _arg_1.readInt();
         _local_2.Type = _arg_1.readInt();
         _local_2.BeginSecond = _arg_1.readInt();
         _local_2.BirthSecond = _arg_1.readInt();
         _local_2.FightID = _arg_1.readInt();
         _local_2.posX = _arg_1.readInt();
         _local_2.posY = _arg_1.readInt();
         return _local_2;
      }
      
      private function onMonsterInfoSingle(_arg_1:PkgEvent) : void
      {
         var _local_3:PackageIn = _arg_1.pkg;
         var _local_2:int = _local_3.readInt();
         var _local_5:int = this.monsterStateSeverToClient(_local_2);
         var _local_4:EachMonsterInfo = this.readMonsterInfoSingle(_local_3);
         _local_4.state = _local_5;
         _local_4.serverMonsterState = _local_2;
         dispatchEvent(new CEvent("event_monster_info_single",_local_4));
      }
      
      private function onGetBuildInfoInFight(_arg_1:PkgEvent) : void
      {
         var _local_2:PackageIn = _arg_1.pkg;
         _local_2.readBoolean();
         _local_2.readDate();
         this.initFightBuildInfo(1,_local_2);
         this.initFightBuildInfo(2,_local_2);
         this.initFightBuildInfo(3,_local_2);
         this.initFightBuildInfo(4,_local_2);
         this.initFightBuildInfo(5,null);
         dispatchEvent(new Event("event_build_in_fight_state_change"));
      }
      
      private function onGetConsortiaInfo(_arg_1:PkgEvent) : void
      {
         var _local_2:* = null;
         var _local_4:PackageIn = _arg_1.pkg;
         var _local_3:Boolean = _local_4.readBoolean();
         if(_local_3)
         {
            this.model.isActive = _local_4.readInt() == 0 ? false : true;
            this.model.BeginTime = _local_4.readDate();
            this.initUnFightBuildInfo(1,_local_4);
            this.initUnFightBuildInfo(2,_local_4);
            this.initUnFightBuildInfo(3,_local_4);
            this.initUnFightBuildInfo(4,_local_4);
            this.initUnFightBuildInfo(5,_local_4);
            this.model.EndTime = _local_4.readDate();
            _local_2 = new Date();
            _local_2.time = this.model.EndTime.time + 172800000;
            this.model.autoRepairCompleteTime = _local_2;
         }
         else
         {
            this.model.isActive = false;
         }
         dispatchEvent(new Event("event_get_consortia_info_res"));
      }
      
      private function getFixUnFightBuildState(_arg_1:int, _arg_2:int, _arg_3:int) : int
      {
         if(_arg_2 == 0)
         {
            if(_arg_3 == 0)
            {
               return this.isCanGradeBuild(_arg_1) ? 2 : 1;
            }
            return 4;
         }
         if(_arg_2 == 1)
         {
            return 3;
         }
         return 0;
      }
      
      private function onBuildRepairInfo(_arg_1:PkgEvent) : void
      {
         var _local_6:int = 0;
         var _local_4:int = 0;
         var _local_2:* = null;
         var _local_5:PackageIn = _arg_1.pkg;
         var _local_3:int = _local_5.readInt();
         _local_6 = 0;
         while(_local_6 < _local_3)
         {
            _local_4 = _local_5.readInt();
            _local_2 = this.model.allBuildInfo[_local_4];
            if(!_local_2)
            {
               _local_2 = new EachBuildInfo();
               _local_2.Id = _local_4;
               this.model.allBuildInfo[_local_4] = _local_2;
            }
            _local_2.repairPlayerNum = _local_5.readInt();
            _local_6++;
         }
         dispatchEvent(new Event("event_repair_player_num_change"));
      }
      
      private function onReduceBloodState(_arg_1:PkgEvent) : void
      {
         var _local_5:* = null;
         var _local_3:PackageIn = _arg_1.pkg;
         var _local_4:int = _local_3.readInt();
         var _local_2:int = _local_3.readInt();
         if(_local_4 == 0)
         {
            _local_5 = LanguageMgr.GetTranslation("consortiadomain.buildFirstBeBeat",this.buildNameArr[_local_2]);
         }
         else if(_local_4 == 1)
         {
            _local_5 = LanguageMgr.GetTranslation("consortiadomain.build1MinBeBeat",this.buildNameArr[_local_2]);
         }
         ChatManager.Instance.sysChatConsortia(_local_5);
         MessageTipManager.getInstance().show(_local_5);
      }
      
      private function onConsortiaBuildLevelUp(_arg_1:Event) : void
      {
         var _local_4:int = 0;
         var _local_2:* = null;
         var _local_3:Array = [1,2,3,4,5];
         for each(_local_4 in _local_3)
         {
            _local_2 = this.model.allBuildInfo[_local_4];
            _local_2.State = this.isCanGradeBuild(_local_4) ? 2 : 1;
         }
         dispatchEvent(new Event("event_get_consortia_info_res"));
      }
      
      public function enterScene(_arg_1:Boolean) : void
      {
         var _local_2:* = null;
         var _local_3:* = null;
         if(this.model.isActive)
         {
            if(PlayerManager.Instance.Self.ConsortiaID > 0)
            {
               if(_arg_1)
               {
                  _local_2 = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("AlertDialog.Info"),LanguageMgr.GetTranslation("consortiadomain.enterSceneAlert"),LanguageMgr.GetTranslation("ok"),LanguageMgr.GetTranslation("cancel"),false,false,false,2);
                  _local_2.addEventListener("response",this.onEnterSceneResponse);
               }
               else
               {
                  GameInSocketOut.sendSingleRoomBegin(22);
               }
            }
            else
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("demonChiYou.cannotEnter.noConsortia"));
            }
         }
         else
         {
            _local_3 = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("AlertDialog.Info"),LanguageMgr.GetTranslation("consortiadomain.activityActiveAlert"),LanguageMgr.GetTranslation("ok"),"",false,false,false,2);
            _local_3.addEventListener("response",this.onActivityActiveAlertResponse);
         }
      }
      
      private function onActivityActiveAlertResponse(_arg_1:FrameEvent) : void
      {
         SoundManager.instance.play("008");
         var _local_2:BaseAlerFrame = _arg_1.currentTarget as BaseAlerFrame;
         _local_2.removeEventListener("response",this.onActivityActiveAlertResponse);
         _local_2.dispose();
      }
      
      private function onEnterSceneResponse(_arg_1:FrameEvent) : void
      {
         SoundManager.instance.play("008");
         var _local_2:BaseAlerFrame = _arg_1.currentTarget as BaseAlerFrame;
         _local_2.removeEventListener("response",this.onEnterSceneResponse);
         switch(_arg_1.responseCode)
         {
            case 2:
            case 3:
               GameInSocketOut.sendSingleRoomBegin(22);
         }
         _local_2.dispose();
      }
      
      private function onSingleRoomBeginRes(_arg_1:Event) : void
      {
         if(RoomManager.Instance.current.type == 150)
         {
            StateManager.setState("consortia_domain");
         }
      }
      
      public function leaveScene() : void
      {
         var _local_1:BaseAlerFrame = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("AlertDialog.Info"),LanguageMgr.GetTranslation("consortiadomain.leaveSceneAlert"),LanguageMgr.GetTranslation("ok"),LanguageMgr.GetTranslation("cancel"),false,false,false,2);
         _local_1.addEventListener("response",this.onLeaveSceneResponse);
      }
      
      private function onLeaveSceneResponse(_arg_1:FrameEvent) : void
      {
         SoundManager.instance.play("008");
         var _local_2:BaseAlerFrame = _arg_1.currentTarget as BaseAlerFrame;
         _local_2.removeEventListener("response",this.onLeaveSceneResponse);
         switch(_arg_1.responseCode)
         {
            case 2:
            case 3:
               SocketManager.Instance.out.leaveConsortiaDomainScene();
         }
         _local_2.dispose();
      }
      
      public function onEnterScene() : void
      {
         KeyboardManager.getInstance().addEventListener("keyDown",this.__onKeyDown);
         SocketManager.Instance.addEventListener(PkgEvent.format(371,9),this.onGetMonsterInfo);
         SocketManager.Instance.addEventListener(PkgEvent.format(371,10),this.onGetBuildInfoInFight);
         SocketManager.Instance.addEventListener(PkgEvent.format(371,13),this.onGetKillInfo);
         SocketManager.Instance.addEventListener(PkgEvent.format(371,1),this.onRemovePlayer);
         SocketManager.Instance.addEventListener(PkgEvent.format(371,16),this.onMonsterInfoSingle);
         SocketManager.Instance.addEventListener(PkgEvent.format(371,17),this.onBuildRepairInfo);
         SocketManager.Instance.addEventListener(PkgEvent.format(371,19),this.onReduceBloodState);
         ConsortionModelManager.Instance.addEventListener("event_consortia_level_up",this.onConsortiaBuildLevelUp);
         var _local_2:ServerConfigInfo = ServerConfigManager.instance.serverConfigInfo["ConsortiaLandBuildBlood"];
         if(_local_2 != null)
         {
            this.consortiaLandBuildBlood = int(_local_2.Value);
         }
         else
         {
            this.consortiaLandBuildBlood = 1000;
         }
         var _local_1:ServerConfigInfo = ServerConfigManager.instance.serverConfigInfo["ConsortiaLandRepairBlood"];
         if(_local_1 != null)
         {
            this.consortiaLandRepairBlood = int(_local_1.Value);
         }
         else
         {
            this.consortiaLandRepairBlood = 36000;
         }
         var _local_4:ServerConfigInfo = ServerConfigManager.instance.serverConfigInfo["ConsortiaLandMonsterSpeedInfo"];
         if(_local_4 != null)
         {
            this.consortiaLandMonsterSpeed = int(_local_4.Value);
         }
         else
         {
            this.consortiaLandMonsterSpeed = 10;
         }
         var _local_3:ServerConfigInfo = ServerConfigManager.instance.serverConfigInfo["ConsortiaLandRepairCountInfo"];
         if(_local_3 != null)
         {
            this.consortiaLandRepairCount = int(_local_3.Value);
         }
         else
         {
            this.consortiaLandRepairCount = 50;
         }
         this.buildNameArr = LanguageMgr.GetTranslation("consortiadomain.buildNameArr").split(",");
         this.sceneMapGridData = ComponentFactory.Instance.creatCustomObject("consortiadomain.map.SceneMapGridData");
         this.aStarPathFinder = new AStarPathFinder();
         this.aStarPathFinder.init(this.sceneMapGridData);
         this._secTickTimer = new Timer(1000,2147483647);
         this._secTickTimer.addEventListener("timer",this.onSecTickTimer);
         this._secTickTimer.start();
         StageReferance.stage.addEventListener("resize",this.onStageResize);
         this.bgLayerViewRect = new Rectangle();
         this.bgLayerViewRect.width = StageReferance.stageWidth;
         this.bgLayerViewRect.height = StageReferance.stageHeight;
         this.playMusic();
      }
      
      private function __onKeyDown(_arg_1:KeyboardEvent) : void
      {
         var _local_2:* = null;
         if(_arg_1.keyCode == 75 && ConsortiaDomainManager.CAN_USE_K)
         {
            _local_2 = ComponentFactory.Instance.creatComponentByStylename("consortionBankFrame");
            LayerManager.Instance.addToLayer(_local_2,3,true,1);
         }
         else if(_arg_1.keyCode == 82 && ConsortiaDomainManager.CAN_USE_R)
         {
            MailManager.Instance.switchVisible();
         }
         else if(_arg_1.keyCode == 81 && ConsortiaDomainManager.CAN_USE_Q)
         {
            TaskManager.instance.switchVisible();
         }
      }
      
      public function onLeaveScene() : void
      {
         KeyboardManager.getInstance().removeEventListener("keyDown",this.__onKeyDown);
         SocketManager.Instance.removeEventListener(PkgEvent.format(371,9),this.onGetMonsterInfo);
         SocketManager.Instance.removeEventListener(PkgEvent.format(371,10),this.onGetBuildInfoInFight);
         SocketManager.Instance.removeEventListener(PkgEvent.format(371,13),this.onGetKillInfo);
         SocketManager.Instance.removeEventListener(PkgEvent.format(371,1),this.onRemovePlayer);
         SocketManager.Instance.removeEventListener(PkgEvent.format(371,16),this.onMonsterInfoSingle);
         SocketManager.Instance.removeEventListener(PkgEvent.format(371,17),this.onBuildRepairInfo);
         SocketManager.Instance.addEventListener(PkgEvent.format(371,19),this.onReduceBloodState);
         ConsortionModelManager.Instance.removeEventListener("event_consortia_level_up",this.onConsortiaBuildLevelUp);
         StageReferance.stage.removeEventListener("resize",this.onStageResize);
         this._secTickTimer.removeEventListener("timer",this.onSecTickTimer);
         this._secTickTimer.stop();
         this.bgLayerViewRect = null;
      }
      
      public function getInfoOnEnterScene() : void
      {
         if(ConsortiaDomainManager.instance.activeState == 0 || ConsortiaDomainManager.instance.activeState == 100)
         {
            SocketManager.Instance.out.getConsortiaDomainConsortiaInfo();
            SocketManager.Instance.out.getConsortiaDomainBuildRepairInfo();
         }
         else if(ConsortiaDomainManager.instance.activeState == 1)
         {
            SocketManager.Instance.out.getConsortiaDomainBuildInfoInFight();
            SocketManager.Instance.out.getConsortiaDomainMonsterInfoInFight();
         }
      }
      
      private function playMusic() : void
      {
         if(this.activeState == 1)
         {
            SoundManager.instance.playMusic("12018");
         }
         else
         {
            SoundManager.instance.playMusic("062");
         }
      }
      
      private function onRemovePlayer(_arg_1:PkgEvent) : void
      {
         var _local_3:PackageIn = _arg_1.pkg;
         var _local_2:int = _local_3.readInt();
         if(PlayerManager.Instance.Self.ID == _local_2)
         {
            if(StateManager.currentStateType == "consortia_domain")
            {
               StateManager.setState("main");
            }
         }
         else
         {
            dispatchEvent(new CEvent("event_remove_other_player",_local_2));
         }
      }
      
      public function getIntersectionPoint(_arg_1:int, _arg_2:int, _arg_3:int, _arg_4:*, _arg_5:*) : Point
      {
         var _local_7:int = (_arg_1 - _arg_4) * (_arg_1 - _arg_4) + (_arg_2 - _arg_5) * (_arg_2 - _arg_5);
         if(_local_7 <= _arg_3 * _arg_3)
         {
            return null;
         }
         if(_arg_4 == _arg_1)
         {
            if(_arg_5 > _arg_2)
            {
               return new Point(_arg_1,_arg_2 + _arg_3);
            }
            return new Point(_arg_1,_arg_2 - _arg_3);
         }
         var _local_9:Number = Math.atan2(_arg_5 - _arg_2,_arg_4 - _arg_1);
         var _local_8:Number = Math.cos(_local_9);
         var _local_6:Number = Math.sin(_local_9);
         return new Point(_arg_1 + _local_8 * _arg_3,_arg_2 + _local_6 * _arg_3);
      }
      
      public function getBuildViewUpGradeBtnTexture() : Texture
      {
         var _local_1:* = null;
         if(!this._buildViewUpGradeBtnTexture)
         {
            if(!this._bulidViewBtn)
            {
               this._bulidViewBtn = UICreatShortcut.creatAndAdd("consortiadomain.buildView.openBtn");
            }
            this._bulidViewBtn.text = LanguageMgr.GetTranslation("tank.consortia.myconsortia.frame.MyConsortiaUpgrade.okLabel");
            _local_1 = new BitmapData(this._bulidViewBtn.width,this._bulidViewBtn.height,true,0);
            _local_1.draw(this._bulidViewBtn);
            this._buildViewUpGradeBtnTexture = Texture.fromBitmapData(_local_1,true);
            _local_1.dispose();
         }
         return this._buildViewUpGradeBtnTexture;
      }
      
      public function getBuildViewOpenBtnTexture() : Texture
      {
         var _local_1:* = null;
         if(!this._buildViewOpenBtnTexture)
         {
            if(!this._bulidViewBtn)
            {
               this._bulidViewBtn = UICreatShortcut.creatAndAdd("consortiadomain.buildView.openBtn");
            }
            this._bulidViewBtn.text = LanguageMgr.GetTranslation("tank.consortia.myconsortia.frame.MyConsortiaUpgrade.openLabel");
            _local_1 = new BitmapData(this._bulidViewBtn.width,this._bulidViewBtn.height,true,0);
            _local_1.draw(this._bulidViewBtn);
            this._buildViewOpenBtnTexture = Texture.fromBitmapData(_local_1,true);
            _local_1.dispose();
         }
         return this._buildViewOpenBtnTexture;
      }
      
      public function disposeBuildViewBtn() : void
      {
         ObjectUtils.disposeObject(this._bulidViewBtn);
         this._bulidViewBtn = null;
         this._buildViewOpenBtnTexture && this._buildViewOpenBtnTexture.dispose();
         this._buildViewOpenBtnTexture = null;
         this._buildViewUpGradeBtnTexture && this._buildViewUpGradeBtnTexture.dispose();
         this._buildViewUpGradeBtnTexture = null;
      }
      
      public function initHall(_arg_1:IHallStateView) : void
      {
         this._hall = _arg_1;
         this.checkIcon();
      }
      
      public function checkIcon() : void
      {
      }
      
      public function deleteIcon() : void
      {
      }
   }
}

