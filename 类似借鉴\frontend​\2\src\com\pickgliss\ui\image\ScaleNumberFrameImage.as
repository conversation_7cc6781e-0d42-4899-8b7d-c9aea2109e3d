package com.pickgliss.ui.image
{
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.utils.ObjectUtils;
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   
   public class ScaleNumberFrameImage extends ScaleFrameImage
   {
      
      private var _cutWidth:int;
      
      private var _image:Bitmap;
      
      public function ScaleNumberFrameImage()
      {
         super();
      }
      
      public function set cutWidth(_arg_1:int) : void
      {
         if(this._cutWidth == _arg_1)
         {
            return;
         }
         this._cutWidth = _arg_1;
      }
      
      override protected function addChildren() : void
      {
         if(Boolean(this._image))
         {
            addChild(this._image);
         }
      }
      
      override protected function resetDisplay() : void
      {
         _display = ComponentFactory.Instance.creat(_resourceLink);
         _display.blendMode = "layer";
         if(this._cutWidth == 0)
         {
            this.cutWidth = _display.width / 10;
         }
      }
      
      override public function setFrame(_arg_1:int) : void
      {
         if(_arg_1 > 10)
         {
            return;
         }
         _currentFrame = _arg_1;
         if(_arg_1 == 10)
         {
            _arg_1 = 0;
         }
         ObjectUtils.disposeObject(this._image);
         var _local_3:BitmapData = (_display as Bitmap).bitmapData;
         var _local_4:Rectangle = new Rectangle(this._cutWidth * _arg_1,0,this._cutWidth,_display.height);
         var _local_2:BitmapData = new BitmapData(this._cutWidth,_display.height);
         _local_2.copyPixels(_local_3,_local_4,new Point(0,0));
         this._image = new Bitmap(_local_2);
         if(_width != Math.round(this._image.width))
         {
            _width = Math.round(this._image.width);
            _changedPropeties["width"] = true;
         }
         this.addChildren();
         fillRect();
      }
      
      override public function dispose() : void
      {
         ObjectUtils.disposeObject(this._image);
         super.dispose();
      }
   }
}

