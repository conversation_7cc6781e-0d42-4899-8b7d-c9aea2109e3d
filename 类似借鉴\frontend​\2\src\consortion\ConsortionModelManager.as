package consortion
{
   import com.pickgliss.events.FrameEvent;
   import com.pickgliss.loader.BaseLoader;
   import com.pickgliss.loader.LoadResourceManager;
   import com.pickgliss.toplevel.StageReferance;
   import com.pickgliss.ui.AlertManager;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.ComponentSetting;
   import com.pickgliss.ui.controls.SimpleBitmapButton;
   import com.pickgliss.ui.controls.alert.BaseAlerFrame;
   import com.pickgliss.utils.ObjectUtils;
   import consortion.analyze.ConsortiaAnalyze;
   import consortion.analyze.ConsortiaBossDataAnalyzer;
   import consortion.analyze.ConsortiaRichRankAnalyze;
   import consortion.analyze.ConsortiaTaskRankAnalyzer;
   import consortion.analyze.ConsortiaWeekRewardAnalyze;
   import consortion.analyze.ConsortionApplyListAnalyzer;
   import consortion.analyze.ConsortionBuildingUseConditionAnalyer;
   import consortion.analyze.ConsortionDutyListAnalyzer;
   import consortion.analyze.ConsortionEventListAnalyzer;
   import consortion.analyze.ConsortionInventListAnalyzer;
   import consortion.analyze.ConsortionLevelUpAnalyzer;
   import consortion.analyze.ConsortionListAnalyzer;
   import consortion.analyze.ConsortionMemberAnalyer;
   import consortion.analyze.ConsortionPollListAnalyzer;
   import consortion.analyze.ConsortionSkillInfoAnalyzer;
   import consortion.analyze.PersonalRankAnalyze;
   import consortion.data.CallBackModel;
   import consortion.data.ConsortiaAssetLevelOffer;
   import consortion.data.ConsortionActiveTargetData;
   import consortion.event.ConsortionEvent;
   import consortion.view.selfConsortia.consortiaTask.ConsortiaTaskEvent;
   import consortion.view.selfConsortia.consortiaTask.ConsortiaTaskInfo;
   import consortion.view.selfConsortia.consortiaTask.ConsortiaTaskModel;
   import ddt.data.ConsortiaInfo;
   import ddt.data.goods.InventoryItemInfo;
   import ddt.data.player.ConsortiaPlayerInfo;
   import ddt.data.player.PlayerState;
   import ddt.events.CEvent;
   import ddt.events.PkgEvent;
   import ddt.loader.LoaderCreate;
   import ddt.manager.BadgeInfoManager;
   import ddt.manager.ChatManager;
   import ddt.manager.ExternalInterfaceManager;
   import ddt.manager.ItemManager;
   import ddt.manager.LanguageMgr;
   import ddt.manager.MessageTipManager;
   import ddt.manager.PathManager;
   import ddt.manager.PlayerManager;
   import ddt.manager.ServerConfigManager;
   import ddt.manager.ServerManager;
   import ddt.manager.SharedManager;
   import ddt.manager.SocketManager;
   import ddt.manager.SoundManager;
   import ddt.manager.StateManager;
   import ddt.manager.TimeManager;
   import ddt.utils.HelperDataModuleLoad;
   import ddt.utils.RequestVairableCreater;
   import email.MailManager;
   import flash.events.Event;
   import flash.events.EventDispatcher;
   import flash.events.MouseEvent;
   import flash.net.URLVariables;
   import flash.text.TextField;
   import hall.IHallStateView;
   import quest.TaskManager;
   import redPackage.RedPackageManager;
   import road7th.comm.PackageIn;
   import road7th.utils.DateUtils;
   import road7th.utils.StringHelper;
   import times.utils.timerManager.TimerJuggler;
   import times.utils.timerManager.TimerManager;
   import trainer.controller.SystemOpenPromptManager;
   import trainer.controller.WeakGuildManager;
   
   public class ConsortionModelManager extends EventDispatcher
   {
      
      private static var _instance:ConsortionModelManager;
      
      public static const ALERT_RED_PACKAGE:String = "cmctrl_alert_redpackage";
      
      public static const ALERT_TAX:String = "cmctrl_alert_tax";
      
      public static const ALERT_MANAGER:String = "cmctrl_alert_manager";
      
      public static const RANK:String = "cmctrl_rank";
      
      public static const ALERT_SHOP:String = "cmctrl_alert_shop";
      
      public static const ALERT_BANK:String = "cmctrl_alert_bank";
      
      public static const HIDE_BANK:String = "cmctrl_hide_bank";
      
      public static const ALERT_TAKEIN:String = "cmctrl_alert_takein";
      
      public static const ALERT_QUIT:String = "cmctrl_alert_quit";
      
      public static const OPEN_BOSS:String = "cmctrl_open_boss";
      
      public static const CLEAR_REFERENCE:String = "cmctrl_clear_reference";
      
      public static const EVENT_CONSORTIA_BACK_INFO:String = "event_consortia_back_info";
      
      public static const EVENT_CONSORTIA_BACK_AWARD:String = "event_consortia_back_award";
      
      public static const EVENT_CONSORTIA_LEVEL_UP:String = "event_consortia_level_up";
      
      public static const LEAVE_CALL_BACK_VIEW:String = "leave_call_back_view";
      
      private var _model:ConsortionModel;
      
      private var _taskModel:ConsortiaTaskModel;
      
      private var _timer:TimerJuggler;
      
      private var _alertFlagList:Array = [30000,60000,120000,300000,600000,1200000,1800000,3600000];
      
      private var _alertStatusList:Array = [false,false,false,false,false,false,false,false];
      
      private var _alertMsgList:Array = ["consortia.task.remainSeconds","consortia.task.remainMinutes"];
      
      private var _firstShow:Boolean = true;
      
      private var str:String;
      
      private var _invateID:int;
      
      private var _enterConfirm:BaseAlerFrame;
      
      private var _bossConfigDataList:Array;
      
      public var isShowBossOpenTip:Boolean = false;
      
      public var isBossOpen:Boolean = false;
      
      private var _enterBtn:SimpleBitmapButton;
      
      public var isClickConsortionBuyGiftTask:Boolean;
      
      public function ConsortionModelManager()
      {
         super();
         this._model = new ConsortionModel();
         this._taskModel = new ConsortiaTaskModel();
         this.addEvent();
      }
      
      public static function get Instance() : ConsortionModelManager
      {
         if(_instance == null)
         {
            _instance = new ConsortionModelManager();
         }
         return _instance;
      }
      
      public function get model() : ConsortionModel
      {
         return this._model;
      }
      
      public function get TaskModel() : ConsortiaTaskModel
      {
         return this._taskModel;
      }
      
      public function setup() : void
      {
         ConsortionModelManager.Instance.initConsortionActiveTarget();
         this.requestSortionActiveTargetSchedule();
      }
      
      private function addEvent() : void
      {
         SocketManager.Instance.addEventListener(PkgEvent.format(129,0),this.__consortiaTryIn);
         SocketManager.Instance.addEventListener(PkgEvent.format(129,5),this.__tryInDel);
         SocketManager.Instance.addEventListener(PkgEvent.format(129,4),this.__consortiaTryInPass);
         SocketManager.Instance.addEventListener(PkgEvent.format(129,2),this.__consortiaDisband);
         SocketManager.Instance.addEventListener(PkgEvent.format(129,11),this.__consortiaInvate);
         SocketManager.Instance.addEventListener(PkgEvent.format(129,12),this.__consortiaInvitePass);
         SocketManager.Instance.addEventListener(PkgEvent.format(129,1),this.__consortiaCreate);
         SocketManager.Instance.addEventListener(PkgEvent.format(129,15),this.__consortiaPlacardUpdate);
         SocketManager.Instance.addEventListener(PkgEvent.format(129,24),this.__onConsortiaEquipControl);
         SocketManager.Instance.addEventListener(PkgEvent.format(129,6),this.__givceOffer);
         SocketManager.Instance.addEventListener(PkgEvent.format(128),this.__consortiaResponse);
         SocketManager.Instance.addEventListener(PkgEvent.format(129,3),this.__renegadeUser);
         SocketManager.Instance.addEventListener(PkgEvent.format(129,21),this.__onConsortiaLevelUp);
         SocketManager.Instance.addEventListener(PkgEvent.format(129,19),this.__oncharmanChange);
         SocketManager.Instance.addEventListener(PkgEvent.format(129,18),this.__consortiaUserUpGrade);
         SocketManager.Instance.addEventListener(PkgEvent.format(129,14),this.__consortiaDescriptionUpdate);
         SocketManager.Instance.addEventListener(PkgEvent.format(129,26),this.__skillChangehandler);
         SocketManager.Instance.addEventListener(PkgEvent.format(43),this.__consortiaMailMessage);
         SocketManager.Instance.addEventListener(PkgEvent.format(129,28),this.__buyBadgeHandler);
         SocketManager.Instance.addEventListener(PkgEvent.format(129,31),this.bossOpenCloseHandler);
         SocketManager.Instance.addEventListener(PkgEvent.format(343,5),this.onConSortiaBackAward);
         SocketManager.Instance.addEventListener(PkgEvent.format(343,6),this.onConSortiaBackInfo);
         SocketManager.Instance.addEventListener(PkgEvent.format(412,1),this.onConSortionActiveTargetSchedule);
         SocketManager.Instance.addEventListener(PkgEvent.format(412,2),this.onConSortionActiveTargetStatus);
         this._taskModel.addEventListener("getConsortiaTaskInfo",this.onTaskInfoChange);
      }
      
      protected function onTaskInfoChange(_arg_1:ConsortiaTaskEvent) : void
      {
         var _local_2:ConsortiaTaskInfo = this._taskModel.taskInfo;
         if(_local_2 != null && _local_2.beginTime != null)
         {
            if(this._timer == null)
            {
               this._timer = TimerManager.getInstance().addTimerJuggler(2000);
               this._timer.addEventListener("timer",this.onTaskTimerTimer);
               this._timer.start();
            }
         }
         else if(Boolean(this._timer))
         {
            this._timer.stop();
            this._timer.removeEventListener("timer",this.onTaskTimerTimer);
            TimerManager.getInstance().removeJugglerByTimer(this._timer);
            this._timer = null;
            this._alertStatusList = [false,false,false,false,false,false,false,false];
         }
      }
      
      protected function onTaskTimerTimer(_arg_1:Event) : void
      {
         var _local_6:int = 0;
         var _local_8:int = 0;
         var _local_4:Boolean = false;
         var _local_3:Number = NaN;
         var _local_2:int = 0;
         var _local_5:* = null;
         var _local_7:* = null;
         if(Boolean(this._taskModel.taskInfo) && Boolean(this._taskModel.taskInfo.beginTime))
         {
            _local_4 = true;
            _local_6 = int(this._taskModel.taskInfo.itemList.length);
            _local_8 = 0;
            while(_local_8 < _local_6)
            {
               if(this._taskModel.taskInfo.itemList[_local_8].currenValue - this._taskModel.taskInfo.itemList[_local_8].targetValue < 0)
               {
                  _local_4 = false;
                  break;
               }
               _local_8++;
            }
            if(_local_4)
            {
               return;
            }
            _local_5 = ConsortionModelManager.Instance.TaskModel.taskInfo.beginTime;
            _local_3 = ConsortionModelManager.Instance.TaskModel.taskInfo.time * 60 - TimeManager.Instance.TotalSecondToNow(_local_5) + 60;
            if(_local_3 <= 0)
            {
               return;
            }
            _local_3 *= 1000;
            _local_6 = int(this._alertFlagList.length);
            _local_8 = 0;
            while(_local_8 < _local_6)
            {
               if(_local_3 <= this._alertFlagList[_local_8])
               {
                  if(this._alertStatusList[_local_8] == false)
                  {
                     this._alertStatusList[_local_8] = true;
                     if(this._firstShow)
                     {
                        this._firstShow = false;
                        _local_7 = _local_8 > 1 ? this._alertMsgList[1] : this._alertMsgList[0];
                        _local_2 = _local_8 > 1 ? int(Math.round(_local_3 / 60000)) : int(int(_local_3 / 1000));
                        ChatManager.Instance.sysChatConsortia(LanguageMgr.GetTranslation(_local_7,_local_2));
                     }
                     else
                     {
                        _local_7 = _local_8 > 0 ? this._alertMsgList[1] : this._alertMsgList[0];
                        _local_2 = _local_8 > 0 ? int(Math.round(this._alertFlagList[_local_8] / 60000)) : 30;
                        ChatManager.Instance.sysChatConsortia(LanguageMgr.GetTranslation(_local_7,_local_2));
                     }
                  }
                  return;
               }
               _local_8++;
            }
         }
      }
      
      private function __consortiaResponse(_arg_1:PkgEvent) : void
      {
         var _local_16:int = 0;
         var _local_36:Boolean = false;
         var _local_24:int = 0;
         var _local_11:int = 0;
         var _local_35:int = 0;
         var _local_25:Boolean = false;
         var _local_22:int = 0;
         var _local_21:int = 0;
         var _local_7:int = 0;
         var _local_8:int = 0;
         var _local_23:int = 0;
         var _local_42:int = 0;
         var _local_15:int = 0;
         var _local_41:int = 0;
         var _local_29:int = 0;
         var _local_10:int = 0;
         var _local_31:int = 0;
         var _local_17:int = 0;
         var _local_44:int = 0;
         var _local_38:int = 0;
         var _local_39:int = 0;
         var _local_19:* = null;
         var _local_6:* = null;
         var _local_28:* = null;
         var _local_2:* = null;
         var _local_5:* = null;
         var _local_3:* = null;
         var _local_20:* = null;
         var _local_33:* = null;
         var _local_30:* = null;
         var _local_43:* = null;
         var _local_40:* = null;
         var _local_37:* = null;
         var _local_27:* = null;
         var _local_12:* = null;
         var _local_34:* = null;
         var _local_4:* = null;
         var _local_13:* = null;
         var _local_9:* = null;
         var _local_18:* = null;
         var _local_26:* = null;
         var _local_32:PackageIn = _arg_1.pkg;
         var _local_14:int = _local_32.readByte();
         switch(_local_14)
         {
            case 1:
               _local_6 = new ConsortiaPlayerInfo();
               _local_6.privateID = _local_32.readInt();
               _local_36 = _local_32.readBoolean();
               _local_6.ConsortiaID = _local_32.readInt();
               _local_6.ConsortiaName = _local_32.readUTF();
               _local_6.ID = _local_32.readInt();
               _local_6.NickName = _local_32.readUTF();
               _local_24 = _local_32.readInt();
               _local_28 = _local_32.readUTF();
               _local_6.DutyID = _local_32.readInt();
               _local_6.DutyName = _local_32.readUTF();
               _local_6.Offer = _local_32.readInt();
               _local_6.RichesOffer = _local_32.readInt();
               _local_6.RichesRob = _local_32.readInt();
               _local_6.LastDate = _local_32.readDateString();
               _local_6.Grade = _local_32.readInt();
               _local_6.DutyLevel = _local_32.readInt();
               _local_11 = _local_32.readInt();
               _local_6.playerState = new PlayerState(_local_11);
               _local_6.Sex = _local_32.readBoolean();
               _local_6.Right = _local_32.readInt();
               _local_6.WinCount = _local_32.readInt();
               _local_6.TotalCount = _local_32.readInt();
               _local_6.EscapeCount = _local_32.readInt();
               _local_6.Repute = _local_32.readInt();
               _local_6.LoginName = _local_32.readUTF();
               _local_6.FightPower = _local_32.readLong;
               _local_6.AchievementPoint = _local_32.readInt();
               _local_6.honor = _local_32.readUTF();
               _local_6.UseOffer = _local_32.readInt();
               if(!(_local_36 && _local_6.ID == PlayerManager.Instance.Self.ID))
               {
                  if(_local_6.ID == PlayerManager.Instance.Self.ID)
                  {
                     MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.manager.PlayerManager.one",_local_6.ConsortiaName));
                  }
               }
               _local_2 = "";
               if(_local_6.ID == PlayerManager.Instance.Self.ID)
               {
                  this.setPlayerConsortia(_local_6.ConsortiaID,_local_6.ConsortiaName);
                  this.getConsortionMember(this.memberListComplete);
                  this.getConsortionList(this.selfConsortionComplete,1,6,_local_6.ConsortiaName,-1,-1,-1,_local_6.ConsortiaID);
                  if(_local_36)
                  {
                     _local_2 = LanguageMgr.GetTranslation("tank.manager.PlayerManager.isInvent.msg",_local_6.ConsortiaName);
                  }
                  else
                  {
                     _local_2 = LanguageMgr.GetTranslation("tank.manager.PlayerManager.pass",_local_6.ConsortiaName);
                  }
                  if(StateManager.currentStateType == "consortia")
                  {
                     dispatchEvent(new ConsortionEvent("consortionStateChange"));
                  }
                  TaskManager.instance.requestClubTask();
                  if(PathManager.solveExternalInterfaceEnabel())
                  {
                     ExternalInterfaceManager.sendToAgent(5,PlayerManager.Instance.Self.ID,PlayerManager.Instance.Self.NickName,ServerManager.Instance.zoneName,-1,_local_6.ConsortiaName);
                  }
               }
               else
               {
                  this._model.addMember(_local_6);
                  _local_2 = LanguageMgr.GetTranslation("tank.manager.PlayerManager.player",_local_6.NickName);
               }
               _local_2 = StringHelper.rePlaceHtmlTextField(_local_2);
               ChatManager.Instance.sysChatYellow(_local_2);
               if(_local_6.ConsortiaID == 0)
               {
                  ConsortionModelManager.Instance.TaskModel.taskInfo = null;
               }
               return;
            case 2:
               _local_16 = _local_32.readInt();
               PlayerManager.Instance.Self.consortiaInfo.Level = 0;
               this.setPlayerConsortia();
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.manager.PlayerManager.your"));
               this.getConsortionMember();
               ChatManager.Instance.sysChatYellow(LanguageMgr.GetTranslation("tank.manager.PlayerManager.disband"));
               if(StateManager.currentStateType == "consortia")
               {
                  StateManager.back();
               }
               ConsortionModelManager.Instance.TaskModel.taskInfo = null;
               return;
            case 3:
               _local_16 = _local_32.readInt();
               _local_35 = _local_32.readInt();
               _local_25 = _local_32.readBoolean();
               _local_19 = _local_32.readUTF();
               _local_5 = _local_32.readUTF();
               if(PlayerManager.Instance.Self.ID == _local_16)
               {
                  this.setPlayerConsortia();
                  this.getConsortionMember();
                  TaskManager.instance.onGuildUpdate();
                  _local_3 = "";
                  if(_local_25)
                  {
                     this.dispatchEvent(new ConsortionEvent("kick_consortion"));
                     _local_3 = LanguageMgr.GetTranslation("tank.manager.PlayerManager.delect",_local_5);
                     MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.manager.PlayerManager.hit"));
                  }
                  else
                  {
                     _local_3 = LanguageMgr.GetTranslation("tank.manager.PlayerManager.leave");
                  }
                  if(StateManager.currentStateType == "consortia")
                  {
                     StateManager.back();
                  }
                  else if(StateManager.currentStateType == "consortiaGuard")
                  {
                     StateManager.setState("main");
                  }
                  PlayerManager.Instance.Self.consortiaInfo.StoreLevel = 0;
                  _local_3 = StringHelper.rePlaceHtmlTextField(_local_3);
                  ChatManager.Instance.sysChatRed(_local_3);
               }
               else
               {
                  this.removeConsortiaMember(_local_16,_local_25,_local_5);
               }
               if(_local_19 == PlayerManager.Instance.Self.NickName)
               {
                  ConsortionModelManager.Instance.TaskModel.taskInfo = null;
               }
               return;
            case 4:
               this._invateID = _local_32.readInt();
               _local_22 = _local_32.readInt();
               _local_20 = _local_32.readUTF();
               _local_21 = _local_32.readInt();
               _local_33 = _local_32.readUTF();
               _local_7 = _local_32.readInt();
               _local_30 = _local_32.readUTF();
               if(SharedManager.Instance.showCI)
               {
                  if(this.str != _local_33)
                  {
                     SoundManager.instance.play("018");
                     _local_43 = _local_33 + LanguageMgr.GetTranslation("tank.manager.PlayerManager.come",_local_30);
                     _local_43 = StringHelper.rePlaceHtmlTextField(_local_43);
                     _local_40 = StageReferance.stage.focus;
                     if(Boolean(this._enterConfirm))
                     {
                        this._enterConfirm.removeEventListener("response",this.__enterConsortiaConfirm);
                        ObjectUtils.disposeObject(this._enterConfirm);
                        this._enterConfirm = null;
                     }
                     this._enterConfirm = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("tank.manager.PlayerManager.request"),_local_43,LanguageMgr.GetTranslation("tank.manager.PlayerManager.sure"),LanguageMgr.GetTranslation("tank.manager.PlayerManager.refuse"),false,true,true,2,"alertInFight");
                     this._enterConfirm.addEventListener("response",this.__enterConsortiaConfirm);
                     this.str = _local_33;
                     if(_local_40 is TextField)
                     {
                        if(TextField(_local_40).type == "input")
                        {
                           StageReferance.stage.focus = _local_40;
                        }
                     }
                  }
               }
               return;
            case 5:
               return;
            case 6:
               _local_8 = _local_32.readInt();
               _local_37 = _local_32.readUTF();
               _local_23 = _local_32.readInt();
               if(PlayerManager.Instance.Self.ConsortiaID == _local_8)
               {
                  PlayerManager.Instance.Self.consortiaInfo.Level = _local_23;
                  ChatManager.Instance.sysChatYellow(LanguageMgr.GetTranslation("tank.manager.PlayerManager.upgrade",_local_23,this._model.getLevelData(_local_23).Count));
                  TaskManager.instance.requestClubTask();
                  SoundManager.instance.play("1001");
                  this.getConsortionList(this.selfConsortionComplete,1,6,PlayerManager.Instance.Self.ConsortiaName,-1,-1,-1,PlayerManager.Instance.Self.ConsortiaID);
                  TaskManager.instance.onGuildUpdate();
               }
               return;
            case 7:
               return;
            case 8:
               _local_42 = _local_32.readByte();
               _local_15 = _local_32.readInt();
               _local_41 = _local_32.readInt();
               _local_27 = _local_32.readUTF();
               _local_29 = _local_32.readInt();
               _local_12 = _local_32.readUTF();
               _local_10 = _local_32.readInt();
               _local_31 = _local_32.readInt();
               _local_34 = _local_32.readUTF();
               if(_local_42 != 1)
               {
                  if(_local_42 == 2)
                  {
                     this.updateDutyInfo(_local_29,_local_12,_local_10);
                  }
                  else if(_local_42 == 3)
                  {
                     this.upDateSelfDutyInfo(_local_29,_local_12,_local_10);
                  }
                  else if(_local_42 == 4)
                  {
                     this.upDateSelfDutyInfo(_local_29,_local_12,_local_10);
                  }
                  else if(_local_42 == 5)
                  {
                     this.upDateSelfDutyInfo(_local_29,_local_12,_local_10);
                  }
                  else if(_local_42 == 6)
                  {
                     this.updateConsortiaMemberDuty(_local_41,_local_29,_local_12,_local_10);
                     _local_4 = "";
                     if(_local_41 == PlayerManager.Instance.Self.ID)
                     {
                        _local_4 = LanguageMgr.GetTranslation("tank.manager.PlayerManager.youUpgrade",_local_34,_local_12);
                     }
                     else if(_local_41 == _local_31)
                     {
                        _local_4 = LanguageMgr.GetTranslation("tank.manager.PlayerManager.upgradeSelf",_local_27,_local_12);
                     }
                     else
                     {
                        _local_4 = LanguageMgr.GetTranslation("tank.manager.PlayerManager.upgradeOther",_local_34,_local_27,_local_12);
                     }
                     _local_4 = StringHelper.rePlaceHtmlTextField(_local_4);
                     ChatManager.Instance.sysChatYellow(_local_4);
                  }
                  else if(_local_42 == 7)
                  {
                     this.updateConsortiaMemberDuty(_local_41,_local_29,_local_12,_local_10);
                     _local_13 = "";
                     if(_local_41 == PlayerManager.Instance.Self.ID)
                     {
                        _local_13 = LanguageMgr.GetTranslation("tank.manager.PlayerManager.youDemotion",_local_34,_local_12);
                     }
                     else if(_local_41 == _local_31)
                     {
                        _local_13 = LanguageMgr.GetTranslation("tank.manager.PlayerManager.demotionSelf",_local_27,_local_12);
                     }
                     else
                     {
                        _local_13 = LanguageMgr.GetTranslation("tank.manager.PlayerManager.demotionOther",_local_34,_local_27,_local_12);
                     }
                     _local_13 = StringHelper.rePlaceHtmlTextField(_local_13);
                     ChatManager.Instance.sysChatYellow(_local_13);
                  }
                  else if(_local_42 == 8)
                  {
                     this.updateConsortiaMemberDuty(_local_41,_local_29,_local_12,_local_10);
                     SoundManager.instance.play("1001");
                  }
                  else if(_local_42 == 9)
                  {
                     this.updateConsortiaMemberDuty(_local_41,_local_29,_local_12,_local_10);
                     PlayerManager.Instance.Self.consortiaInfo.ChairmanName = _local_27;
                     _local_9 = "<" + _local_27 + ">" + LanguageMgr.GetTranslation("tank.manager.PlayerManager.up") + _local_12;
                     _local_9 = StringHelper.rePlaceHtmlTextField(_local_9);
                     ChatManager.Instance.sysChatYellow(_local_9);
                     SoundManager.instance.play("1001");
                  }
               }
               return;
            case 9:
               _local_17 = _local_32.readInt();
               _local_44 = _local_32.readInt();
               _local_18 = _local_32.readUTF();
               _local_38 = _local_32.readInt();
               if(_local_17 != PlayerManager.Instance.Self.ConsortiaID)
               {
                  return;
               }
               _local_26 = "";
               if(PlayerManager.Instance.Self.ID == _local_44)
               {
                  _local_26 = LanguageMgr.GetTranslation("tank.manager.PlayerManager.contributionSelf",_local_38);
               }
               else
               {
                  _local_26 = LanguageMgr.GetTranslation("tank.manager.PlayerManager.contributionOther",_local_18,_local_38);
               }
               ChatManager.Instance.sysChatYellow(_local_26);
               return;
               break;
            case 10:
               this.consortiaUpLevel(10,_local_32.readInt(),_local_32.readUTF(),_local_32.readInt());
               return;
            case 11:
               this.consortiaUpLevel(11,_local_32.readInt(),_local_32.readUTF(),_local_32.readInt());
               return;
            case 12:
               this.consortiaUpLevel(12,_local_32.readInt(),_local_32.readUTF(),_local_32.readInt());
               return;
            case 13:
               this.consortiaUpLevel(13,_local_32.readInt(),_local_32.readUTF(),_local_32.readInt());
               return;
            case 14:
               _local_39 = _local_32.readInt();
               switch(_local_39)
               {
                  case 1:
                     PlayerManager.Instance.Self.consortiaInfo.IsVoting = true;
                     break;
                  case 2:
                     PlayerManager.Instance.Self.consortiaInfo.IsVoting = false;
                     break;
                  case 3:
                     PlayerManager.Instance.Self.consortiaInfo.IsVoting = false;
               }
               return;
            case 15:
               _local_32.readInt();
               ChatManager.Instance.sysChatYellow(_local_32.readUTF());
               return;
            case 16:
               this.getConsortionList(this.selfConsortionComplete,1,6,PlayerManager.Instance.Self.ConsortiaName,-1,-1,-1,PlayerManager.Instance.Self.ConsortiaID);
         }
      }
      
      private function consortiaUpLevel(_arg_1:int, _arg_2:int, _arg_3:String, _arg_4:int) : void
      {
         if(_arg_2 != PlayerManager.Instance.Self.ConsortiaID)
         {
            return;
         }
         SoundManager.instance.play("1001");
         var _local_5:String = "";
         if(_arg_1 == 10)
         {
            if(PlayerManager.Instance.Self.DutyLevel == 1)
            {
               _local_5 = LanguageMgr.GetTranslation("tank.manager.PlayerManager.consortiaShop",_arg_4);
            }
            else
            {
               _local_5 = LanguageMgr.GetTranslation("tank.manager.PlayerManager.consortiaShop2",_arg_4);
            }
            PlayerManager.Instance.Self.consortiaInfo.ShopLevel = _arg_4;
         }
         else if(_arg_1 == 11)
         {
            if(PlayerManager.Instance.Self.DutyLevel == 1)
            {
               _local_5 = LanguageMgr.GetTranslation("tank.manager.PlayerManager.consortiaStore",_arg_4);
            }
            else
            {
               _local_5 = LanguageMgr.GetTranslation("tank.manager.PlayerManager.consortiaStore2",_arg_4);
            }
            PlayerManager.Instance.Self.consortiaInfo.SmithLevel = _arg_4;
         }
         else if(_arg_1 == 12)
         {
            if(PlayerManager.Instance.Self.DutyLevel == 1)
            {
               _local_5 = LanguageMgr.GetTranslation("tank.manager.PlayerManager.consortiaSmith",_arg_4);
            }
            else
            {
               _local_5 = LanguageMgr.GetTranslation("tank.manager.PlayerManager.consortiaSmith2",_arg_4);
            }
            PlayerManager.Instance.Self.consortiaInfo.StoreLevel = _arg_4;
         }
         else if(_arg_1 == 13)
         {
            if(PlayerManager.Instance.Self.DutyLevel == 1)
            {
               _local_5 = LanguageMgr.GetTranslation("tank.manager.PlayerManager.consortiaSkill",_arg_4);
            }
            else
            {
               _local_5 = LanguageMgr.GetTranslation("tank.manager.PlayerManager.consortiaSkill2",_arg_4);
            }
            PlayerManager.Instance.Self.consortiaInfo.BufferLevel = _arg_4;
         }
         ChatManager.Instance.sysChatYellow(_local_5);
         this.getConsortionList(this.selfConsortionComplete,1,6,PlayerManager.Instance.Self.ConsortiaName,-1,-1,-1,PlayerManager.Instance.Self.ConsortiaID);
         TaskManager.instance.onGuildUpdate();
      }
      
      private function updateDutyInfo(_arg_1:int, _arg_2:String, _arg_3:int) : void
      {
         var _local_4:ConsortiaPlayerInfo = null;
         for each(_local_4 in this._model.memberList)
         {
            if(_local_4.DutyLevel == _arg_1)
            {
               _local_4.DutyLevel == _arg_1;
               _local_4.DutyName = _arg_2;
               _local_4.Right = _arg_3;
               this._model.updataMember(_local_4);
            }
         }
      }
      
      private function upDateSelfDutyInfo(_arg_1:int, _arg_2:String, _arg_3:int) : void
      {
         var _local_4:ConsortiaPlayerInfo = null;
         var _local_5:* = undefined;
         for each(_local_4 in this._model.memberList)
         {
            if(_local_4.ID == PlayerManager.Instance.Self.ID)
            {
               PlayerManager.Instance.Self.beginChanges();
               _local_5 = _arg_1;
               PlayerManager.Instance.Self.DutyLevel = _local_5;
               _local_4.DutyLevel = _local_5;
               _local_5 = _arg_2;
               PlayerManager.Instance.Self.DutyName = _local_5;
               _local_4.DutyName = _local_5;
               _local_5 = _arg_3;
               PlayerManager.Instance.Self.Right = _local_5;
               _local_4.Right = _local_5;
               PlayerManager.Instance.Self.commitChanges();
               this._model.updataMember(_local_4);
            }
         }
      }
      
      private function updateConsortiaMemberDuty(_arg_1:int, _arg_2:int, _arg_3:String, _arg_4:int) : void
      {
         var _local_5:ConsortiaPlayerInfo = null;
         for each(_local_5 in this._model.memberList)
         {
            if(_local_5.ID == _arg_1)
            {
               _local_5.beginChanges();
               _local_5.DutyLevel = _arg_2;
               _local_5.DutyName = _arg_3;
               _local_5.Right = _arg_4;
               if(_local_5.ID == PlayerManager.Instance.Self.ID)
               {
                  PlayerManager.Instance.Self.beginChanges();
                  PlayerManager.Instance.Self.DutyLevel = _arg_2;
                  PlayerManager.Instance.Self.DutyName = _arg_3;
                  PlayerManager.Instance.Self.Right = _arg_4;
                  PlayerManager.Instance.Self.consortiaInfo.Level = PlayerManager.Instance.Self.consortiaInfo.Level == 0 ? 1 : PlayerManager.Instance.Self.consortiaInfo.Level;
                  PlayerManager.Instance.Self.commitChanges();
                  this.getConsortionList(this.selfConsortionComplete,1,6,PlayerManager.Instance.Self.consortiaInfo.ConsortiaName,-1,-1,-1,PlayerManager.Instance.Self.consortiaInfo.ConsortiaID);
               }
               _local_5.commitChanges();
               this._model.updataMember(_local_5);
            }
         }
      }
      
      private function removeConsortiaMember(_arg_1:int, _arg_2:Boolean, _arg_3:String) : void
      {
         var _local_5:ConsortiaPlayerInfo = null;
         var _local_4:* = null;
         for each(_local_5 in this._model.memberList)
         {
            if(_local_5.ID == _arg_1)
            {
               _local_4 = "";
               if(_arg_2)
               {
                  _local_4 = LanguageMgr.GetTranslation("tank.manager.PlayerManager.consortia",_arg_3,_local_5.NickName);
               }
               else
               {
                  _local_4 = LanguageMgr.GetTranslation("tank.manager.PlayerManager.leaveconsortia",_local_5.NickName);
               }
               _local_4 = StringHelper.rePlaceHtmlTextField(_local_4);
               ChatManager.Instance.sysChatYellow(_local_4);
               this._model.removeMember(_local_5);
            }
         }
      }
      
      private function __enterConsortiaConfirm(_arg_1:FrameEvent) : void
      {
         SoundManager.instance.play("008");
         var _local_2:BaseAlerFrame = _arg_1.currentTarget as BaseAlerFrame;
         _local_2.removeEventListener("response",this.__enterConsortiaConfirm);
         if(Boolean(_local_2))
         {
            ObjectUtils.disposeObject(_local_2);
            _local_2 = null;
         }
         if(_arg_1.responseCode == 3 || _arg_1.responseCode == 2)
         {
            this.accpetConsortiaInvent();
         }
         if(_arg_1.responseCode == 0 || _arg_1.responseCode == 4)
         {
            this.rejectConsortiaInvent();
         }
      }
      
      private function accpetConsortiaInvent() : void
      {
         SocketManager.Instance.out.sendConsortiaInvatePass(this._invateID);
         this.str = "";
      }
      
      private function rejectConsortiaInvent() : void
      {
         SocketManager.Instance.out.sendConsortiaInvateDelete(this._invateID);
         this.str = "";
      }
      
      private function __givceOffer(_arg_1:PkgEvent) : void
      {
         var _local_2:int = _arg_1.pkg.readInt();
         var _local_3:Boolean = _arg_1.pkg.readBoolean();
         var _local_4:String = _arg_1.pkg.readUTF();
         MessageTipManager.getInstance().show(_local_4);
         if(_local_3)
         {
            PlayerManager.Instance.Self.consortiaInfo.Riches += Math.floor(_local_2 / 2);
            this._model.getConsortiaMemberInfo(PlayerManager.Instance.Self.ID).RichesOffer = this._model.getConsortiaMemberInfo(PlayerManager.Instance.Self.ID).RichesOffer + Math.floor(_local_2 / 2);
            TaskManager.instance.onGuildUpdate();
         }
      }
      
      private function __onConsortiaEquipControl(_arg_1:PkgEvent) : void
      {
         var _local_2:* = undefined;
         var _local_4:int = 0;
         var _local_3:Boolean = _arg_1.pkg.readBoolean();
         if(_local_3)
         {
            _local_2 = new Vector.<ConsortiaAssetLevelOffer>();
            _local_4 = 0;
            while(_local_4 < 7)
            {
               _local_2[_local_4] = new ConsortiaAssetLevelOffer();
               if(_local_4 < 5)
               {
                  _local_2[_local_4].Type = 1;
                  _local_2[_local_4].Level = _local_4 + 1;
               }
               else if(_local_4 == 5)
               {
                  _local_2[_local_4].Type = 2;
               }
               else
               {
                  _local_2[_local_4].Type = 3;
               }
               _local_4++;
            }
            _local_2[0].Riches = _arg_1.pkg.readInt();
            _local_2[1].Riches = _arg_1.pkg.readInt();
            _local_2[2].Riches = _arg_1.pkg.readInt();
            _local_2[3].Riches = _arg_1.pkg.readInt();
            _local_2[4].Riches = _arg_1.pkg.readInt();
            _local_2[5].Riches = _arg_1.pkg.readInt();
            _local_2[6].Riches = _arg_1.pkg.readInt();
            if(PlayerManager.Instance.Self.ID == PlayerManager.Instance.Self.consortiaInfo.ChairmanID)
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.consortia.onConsortiaEquipControl.executeSuccess"));
            }
            this._model.useConditionList = _local_2;
         }
         else if(PlayerManager.Instance.Self.ID == PlayerManager.Instance.Self.consortiaInfo.ChairmanID)
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.consortia.onConsortiaEquipControl.executeFail"));
         }
      }
      
      private function __consortiaTryIn(_arg_1:PkgEvent) : void
      {
         var _local_2:int = _arg_1.pkg.readInt();
         var _local_3:Boolean = _arg_1.pkg.readBoolean();
         var _local_4:String = _arg_1.pkg.readUTF();
         MessageTipManager.getInstance().show(_local_4);
         if(_local_3)
         {
            this.getApplyRecordList(this.applyListComplete,PlayerManager.Instance.Self.ID);
         }
      }
      
      private function __tryInDel(_arg_1:PkgEvent) : void
      {
         var _local_2:int = _arg_1.pkg.readInt();
         var _local_4:Boolean = _arg_1.pkg.readBoolean();
         var _local_3:String = _arg_1.pkg.readUTF();
         MessageTipManager.getInstance().show(_local_3);
         if(_local_4)
         {
            this._model.deleteOneApplyRecord(_local_2);
         }
      }
      
      private function __consortiaTryInPass(_arg_1:PkgEvent) : void
      {
         var _local_2:int = _arg_1.pkg.readInt();
         var _local_3:Boolean = _arg_1.pkg.readBoolean();
         var _local_4:String = _arg_1.pkg.readUTF();
         MessageTipManager.getInstance().show(_local_4);
         if(_local_3)
         {
            this._model.deleteOneApplyRecord(_local_2);
         }
      }
      
      private function __consortiaInvate(_arg_1:PkgEvent) : void
      {
         var _local_2:String = _arg_1.pkg.readUTF();
         var _local_3:Boolean = _arg_1.pkg.readBoolean();
         var _local_4:String = _arg_1.pkg.readUTF();
         MessageTipManager.getInstance().show(_local_4);
      }
      
      private function __consortiaInvitePass(_arg_1:PkgEvent) : void
      {
         var _local_5:int = _arg_1.pkg.readInt();
         var _local_4:Boolean = _arg_1.pkg.readBoolean();
         var _local_2:int = _arg_1.pkg.readInt();
         var _local_3:String = _arg_1.pkg.readUTF();
         MessageTipManager.getInstance().show(_arg_1.pkg.readUTF());
         if(_local_4)
         {
            this.setPlayerConsortia(_local_2,_local_3);
            this.getConsortionMember(this.memberListComplete);
            this.getConsortionList(this.selfConsortionComplete,1,6,_local_3,-1,-1,-1,_local_2);
         }
      }
      
      private function __consortiaCreate(_arg_1:PkgEvent) : void
      {
         var _local_5:String = _arg_1.pkg.readUTF();
         var _local_4:Boolean = _arg_1.pkg.readBoolean();
         var _local_2:int = _arg_1.pkg.readInt();
         var _local_7:String = _arg_1.pkg.readUTF();
         var _local_9:String = _arg_1.pkg.readUTF();
         MessageTipManager.getInstance().show(_local_9);
         var _local_3:int = _arg_1.pkg.readInt();
         var _local_6:String = _arg_1.pkg.readUTF();
         var _local_8:int = _arg_1.pkg.readInt();
         if(_local_4)
         {
            this.setPlayerConsortia(_local_2,_local_5);
            this.getConsortionMember(this.memberListComplete);
            this.getConsortionList(this.selfConsortionComplete,1,6,_local_5,-1,-1,-1,_local_2);
            dispatchEvent(new ConsortionEvent("consortionStateChange"));
            TaskManager.instance.requestClubTask();
            if(PathManager.solveExternalInterfaceEnabel())
            {
               ExternalInterfaceManager.sendToAgent(4,PlayerManager.Instance.Self.ID,PlayerManager.Instance.Self.NickName,ServerManager.Instance.zoneName,-1,_local_7);
            }
         }
      }
      
      private function __consortiaDisband(_arg_1:PkgEvent) : void
      {
         var _local_2:int = 0;
         var _local_3:* = null;
         if(_arg_1.pkg.readBoolean())
         {
            if(_arg_1.pkg.readInt() == PlayerManager.Instance.Self.ID)
            {
               this.setPlayerConsortia();
               if(StateManager.currentStateType == "consortia")
               {
                  StateManager.back();
               }
               ChatManager.Instance.sysChatRed(LanguageMgr.GetTranslation("tank.manager.PlayerManager.msg"));
               MessageTipManager.getInstance().show(_arg_1.pkg.readUTF());
            }
         }
         else
         {
            _local_2 = _arg_1.pkg.readInt();
            _local_3 = _arg_1.pkg.readUTF();
            MessageTipManager.getInstance().show(_local_3);
         }
      }
      
      private function __consortiaPlacardUpdate(_arg_1:PkgEvent) : void
      {
         PlayerManager.Instance.Self.consortiaInfo.Placard = _arg_1.pkg.readUTF();
         var _local_2:Boolean = _arg_1.pkg.readBoolean();
         var _local_3:String = _arg_1.pkg.readUTF();
         MessageTipManager.getInstance().show(_local_3);
      }
      
      private function __renegadeUser(_arg_1:PkgEvent) : void
      {
         var _local_2:int = _arg_1.pkg.readInt();
         var _local_3:Boolean = _arg_1.pkg.readBoolean();
         var _local_4:String = _arg_1.pkg.readUTF();
         MessageTipManager.getInstance().show(_local_4);
         var _local_5:int = PlayerManager.Instance.Self.ID;
         if(_local_5 == _local_2)
         {
            PlayerManager.Instance.Self.consortiaInfo.StoreLevel = 0;
         }
      }
      
      private function __onConsortiaLevelUp(_arg_1:PkgEvent) : void
      {
         var _local_4:int = _arg_1.pkg.readByte();
         var _local_2:int = _arg_1.pkg.readByte();
         var _local_3:Boolean = _arg_1.pkg.readBoolean();
         var _local_5:String = _arg_1.pkg.readUTF();
         MessageTipManager.getInstance().show(_local_5);
         if(_local_3)
         {
            switch(_local_4)
            {
               case 1:
                  PlayerManager.Instance.Self.consortiaInfo.Level = _local_2;
                  break;
               case 2:
                  PlayerManager.Instance.Self.consortiaInfo.StoreLevel = _local_2;
                  break;
               case 3:
                  PlayerManager.Instance.Self.consortiaInfo.ShopLevel = _local_2;
                  break;
               case 4:
                  PlayerManager.Instance.Self.consortiaInfo.SmithLevel = _local_2;
                  break;
               case 5:
                  PlayerManager.Instance.Self.consortiaInfo.BufferLevel = _local_2;
            }
            dispatchEvent(new Event("event_consortia_level_up"));
         }
      }
      
      private function __oncharmanChange(_arg_1:PkgEvent) : void
      {
         var _local_2:String = _arg_1.pkg.readUTF();
         var _local_3:Boolean = _arg_1.pkg.readBoolean();
         var _local_4:String = _arg_1.pkg.readUTF();
         MessageTipManager.getInstance().show(_local_4);
      }
      
      private function __consortiaUserUpGrade(_arg_1:PkgEvent) : void
      {
         var _local_2:int = _arg_1.pkg.readInt();
         var _local_3:Boolean = _arg_1.pkg.readBoolean();
         var _local_4:Boolean = _arg_1.pkg.readBoolean();
         var _local_5:String = _arg_1.pkg.readUTF();
         if(_local_3)
         {
            if(_local_4)
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.manager.PlayerManager.upsuccess"));
            }
            else
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.manager.PlayerManager.upfalse"));
            }
         }
         else if(_local_4)
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.manager.PlayerManager.downsuccess"));
         }
         else
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.manager.PlayerManager.downfalse"));
         }
      }
      
      private function __consortiaDescriptionUpdate(_arg_1:PkgEvent) : void
      {
         var _local_2:String = _arg_1.pkg.readUTF();
         var _local_3:Boolean = _arg_1.pkg.readBoolean();
         var _local_4:String = _arg_1.pkg.readUTF();
         MessageTipManager.getInstance().show(_local_4);
         if(_local_3)
         {
            PlayerManager.Instance.Self.consortiaInfo.Description = _local_2;
         }
      }
      
      private function __skillChangehandler(_arg_1:PkgEvent) : void
      {
         var _local_7:int = 0;
         var _local_2:int = 0;
         var _local_3:Boolean = false;
         var _local_4:int = 0;
         var _local_6:* = null;
         var _local_5:int = _arg_1.pkg.readInt();
         _local_7 = 0;
         while(_local_7 < _local_5)
         {
            _local_2 = _arg_1.pkg.readInt();
            _local_3 = _arg_1.pkg.readBoolean();
            _local_6 = _arg_1.pkg.readDate();
            _local_4 = _arg_1.pkg.readInt();
            this._model.updateSkillInfo(_local_2,_local_3,_local_6,_local_4);
            _local_7++;
         }
         if(_local_5 > 0)
         {
            this.getConsortionList(this.selfConsortionComplete,1,6,PlayerManager.Instance.Self.ConsortiaName,-1,-1,-1,PlayerManager.Instance.Self.ConsortiaID);
         }
         dispatchEvent(new ConsortionEvent("skillStateChange"));
      }
      
      private function __consortiaMailMessage(_arg_1:PkgEvent) : void
      {
         var _local_3:String = _arg_1.pkg.readUTF();
         var _local_2:BaseAlerFrame = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("AlertDialog.Info"),_local_3,LanguageMgr.GetTranslation("ok"),"",false,true,true,1);
         _local_2.moveEnable = false;
         _local_2.addEventListener("response",this.__quitConsortiaResponse);
         PlayerManager.Instance.Self.consortiaInfo.StoreLevel = 0;
      }
      
      private function __quitConsortiaResponse(_arg_1:FrameEvent) : void
      {
         SoundManager.instance.play("008");
         var _local_2:BaseAlerFrame = _arg_1.currentTarget as BaseAlerFrame;
         _local_2.removeEventListener("response",this.__quitConsortiaResponse);
         _local_2.dispose();
         _local_2 = null;
      }
      
      private function setPlayerConsortia(_arg_1:uint = 0, _arg_2:String = "") : void
      {
         PlayerManager.Instance.Self.ConsortiaName = _arg_2;
         PlayerManager.Instance.Self.ConsortiaID = _arg_1;
         if(_arg_1 == 0)
         {
            PlayerManager.Instance.Self.consortiaInfo.Level = 0;
         }
      }
      
      public function memberListComplete(_arg_1:ConsortionMemberAnalyer) : void
      {
         this._model.memberList = _arg_1.consortionMember;
         TaskManager.instance.onGuildUpdate();
      }
      
      public function clubSearchConsortions(_arg_1:ConsortionListAnalyzer) : void
      {
         this._model.consortionList = _arg_1.consortionList;
         this._model.consortionsListTotalCount = Math.ceil(_arg_1.consortionsTotalCount / 6);
      }
      
      public function selfConsortionComplete(_arg_1:ConsortionListAnalyzer) : void
      {
         if(_arg_1.consortionList.length > 0)
         {
            PlayerManager.Instance.Self.consortiaInfo = _arg_1.consortionList[0] as ConsortiaInfo;
         }
      }
      
      public function applyListComplete(_arg_1:ConsortionApplyListAnalyzer) : void
      {
         this._model.myApplyList = _arg_1.applyList;
         this._model.applyListTotalCount = _arg_1.totalCount;
      }
      
      public function InventListComplete(_arg_1:ConsortionInventListAnalyzer) : void
      {
         this._model.inventList = _arg_1.inventList;
         this._model.inventListTotalCount = _arg_1.totalCount;
      }
      
      private function levelUpInfoComplete(_arg_1:ConsortionLevelUpAnalyzer) : void
      {
         this._model.levelUpData = _arg_1.levelUpData;
      }
      
      public function eventListComplete(_arg_1:ConsortionEventListAnalyzer) : void
      {
         this._model.eventList = _arg_1.eventList;
      }
      
      public function useConditionListComplete(_arg_1:ConsortionBuildingUseConditionAnalyer) : void
      {
         this._model.useConditionList = _arg_1.useConditionList;
      }
      
      public function dutyListComplete(_arg_1:ConsortionDutyListAnalyzer) : void
      {
         this._model.dutyList = _arg_1.dutyList;
      }
      
      public function pollListComplete(_arg_1:ConsortionPollListAnalyzer) : void
      {
         this._model.pollList = _arg_1.pollList;
      }
      
      public function skillInfoListComplete(_arg_1:ConsortionSkillInfoAnalyzer) : void
      {
         this._model.skillInfoList = _arg_1.skillInfoList;
      }
      
      public function analyzeRichRank(_arg_1:ConsortiaRichRankAnalyze) : void
      {
         this._model.richRankList = _arg_1.dataList;
      }
      
      public function analyzeWeekReward(_arg_1:ConsortiaWeekRewardAnalyze) : void
      {
         this._model.weekReward = _arg_1.dataList;
      }
      
      public function getConsortionList(_arg_1:Function, _arg_2:int = 1, _arg_3:int = 6, _arg_4:String = "", _arg_5:int = -1, _arg_6:int = -1, _arg_7:int = -1, _arg_8:int = -1) : void
      {
         var _local_9:URLVariables = RequestVairableCreater.creatWidthKey(true);
         _local_9["page"] = _arg_2;
         _local_9["size"] = _arg_3;
         _local_9["name"] = _arg_4;
         _local_9["level"] = _arg_7;
         _local_9["ConsortiaID"] = _arg_8;
         _local_9["order"] = _arg_5;
         _local_9["openApply"] = _arg_6;
         var _local_10:BaseLoader = LoadResourceManager.Instance.createLoader(PathManager.solveRequestPath("ConsortiaList.ashx"),7,_local_9);
         _local_10.loadErrorMessage = LanguageMgr.GetTranslation("tank.consortia.myconsortia.frame.LoadMyconsortiaListError");
         _local_10.analyzer = new ConsortionListAnalyzer(_arg_1);
         LoadResourceManager.Instance.startLoad(_local_10);
      }
      
      public function getApplyRecordList(_arg_1:Function, _arg_2:int = -1, _arg_3:int = -1) : void
      {
         var _local_4:URLVariables = RequestVairableCreater.creatWidthKey(true);
         _local_4["page"] = 1;
         _local_4["size"] = 1000;
         _local_4["order"] = -1;
         _local_4["consortiaID"] = _arg_3;
         _local_4["applyID"] = -1;
         _local_4["userID"] = _arg_2;
         _local_4["userLevel"] = -1;
         var _local_5:BaseLoader = LoadResourceManager.Instance.createLoader(PathManager.solveRequestPath("ConsortiaApplyUsersList.ashx"),6,_local_4);
         _local_5.loadErrorMessage = LanguageMgr.GetTranslation("tank.consortia.myconsortia.frame.LoadApplyRecordError");
         _local_5.analyzer = new ConsortionApplyListAnalyzer(_arg_1);
         LoadResourceManager.Instance.startLoad(_local_5);
      }
      
      public function getInviteRecordList(_arg_1:Function) : void
      {
         var _local_2:URLVariables = RequestVairableCreater.creatWidthKey(true);
         _local_2["page"] = 1;
         _local_2["size"] = 1000;
         _local_2["order"] = -1;
         _local_2["userID"] = PlayerManager.Instance.Self.ID;
         _local_2["inviteID"] = -1;
         var _local_3:BaseLoader = LoadResourceManager.Instance.createLoader(PathManager.solveRequestPath("ConsortiaInviteUsersList.ashx"),6,_local_2);
         _local_3.loadErrorMessage = LanguageMgr.GetTranslation("tank.consortia.myconsortia.frame.LoadApplyRecordError");
         _local_3.analyzer = new ConsortionInventListAnalyzer(_arg_1);
         LoadResourceManager.Instance.startLoad(_local_3);
      }
      
      public function getConsortionMember(_arg_1:Function = null) : void
      {
         var _local_2:* = null;
         var _local_3:* = null;
         if(PlayerManager.Instance.Self.ConsortiaID == 0)
         {
            this._model.memberList.clear();
         }
         else
         {
            _local_2 = RequestVairableCreater.creatWidthKey(true);
            _local_2["page"] = 1;
            _local_2["size"] = 10000;
            _local_2["order"] = -1;
            _local_2["consortiaID"] = PlayerManager.Instance.Self.ConsortiaID;
            _local_2["userID"] = -1;
            _local_2["state"] = -1;
            _local_2["rnd"] = Math.random();
            _local_3 = LoadResourceManager.Instance.createLoader(PathManager.solveRequestPath("ConsortiaUsersList.ashx"),7,_local_2);
            _local_3.loadErrorMessage = LanguageMgr.GetTranslation("tank.consortia.myconsortia.frame.LoadMemberInfoError");
            _local_3.analyzer = new ConsortionMemberAnalyer(_arg_1);
            LoadResourceManager.Instance.startLoad(_local_3);
         }
      }
      
      public function getLevelUpInfo() : BaseLoader
      {
         var _local_1:URLVariables = RequestVairableCreater.creatWidthKey(true);
         var _local_2:BaseLoader = LoadResourceManager.Instance.createLoader(PathManager.solveRequestPath("ConsortiaLevelList.xml"),7,_local_1);
         _local_2.loadErrorMessage = LanguageMgr.GetTranslation("tank.consortia.myconsortia.frame.LoadMyconsortiaLevelError");
         _local_2.analyzer = new ConsortionLevelUpAnalyzer(this.levelUpInfoComplete);
         return _local_2;
      }
      
      public function loadEventList(_arg_1:Function, _arg_2:int = -1) : void
      {
         var _local_3:URLVariables = RequestVairableCreater.creatWidthKey(true);
         _local_3["page"] = 1;
         _local_3["size"] = 50;
         _local_3["order"] = -1;
         _local_3["consortiaID"] = _arg_2;
         var _local_4:BaseLoader = LoadResourceManager.Instance.createLoader(PathManager.solveRequestPath("ConsortiaEventList.ashx"),6,_local_3);
         _local_4.loadErrorMessage = LanguageMgr.GetTranslation("ddt.consortion.loadEventList.fail");
         _local_4.analyzer = new ConsortionEventListAnalyzer(_arg_1);
         LoadResourceManager.Instance.startLoad(_local_4);
      }
      
      public function loadUseConditionList(_arg_1:Function, _arg_2:int = -1) : void
      {
         var _local_3:URLVariables = RequestVairableCreater.creatWidthKey(true);
         _local_3["consortiaID"] = _arg_2;
         _local_3["level"] = -1;
         _local_3["type"] = -1;
         var _local_4:BaseLoader = LoadResourceManager.Instance.createLoader(PathManager.solveRequestPath("ConsortiaEquipControlList.ashx"),6,_local_3);
         _local_4.loadErrorMessage = LanguageMgr.GetTranslation("ddt.consortion.loadUseCondition.fail");
         _local_4.analyzer = new ConsortionBuildingUseConditionAnalyer(_arg_1);
         LoadResourceManager.Instance.startLoad(_local_4);
      }
      
      public function loadDutyList(_arg_1:Function, _arg_2:int = -1, _arg_3:int = -1) : void
      {
         var _local_5:URLVariables = RequestVairableCreater.creatWidthKey(true);
         _local_5["page"] = 1;
         _local_5["size"] = 1000;
         _local_5["ConsortiaID"] = _arg_2;
         _local_5["order"] = -1;
         _local_5["dutyID"] = _arg_3;
         var _local_4:BaseLoader = LoadResourceManager.Instance.createLoader(PathManager.solveRequestPath("ConsortiaDutyList.ashx"),6,_local_5);
         _local_4.loadErrorMessage = LanguageMgr.GetTranslation("tank.consortia.myconsortia.frame.LoadDutyListError");
         _local_4.analyzer = new ConsortionDutyListAnalyzer(_arg_1);
         LoadResourceManager.Instance.startLoad(_local_4);
      }
      
      public function loadPollList(_arg_1:int) : void
      {
         var _local_2:URLVariables = RequestVairableCreater.creatWidthKey(true);
         _local_2["ConsortiaID"] = _arg_1;
         var _local_3:BaseLoader = LoadResourceManager.Instance.createLoader(PathManager.solveRequestPath("ConsortiaCandidateList.ashx"),6,_local_2);
         _local_3.loadErrorMessage = LanguageMgr.GetTranslation("ddt.consortion.pollload.error");
         _local_3.analyzer = new ConsortionPollListAnalyzer(this.pollListComplete);
         LoadResourceManager.Instance.startLoad(_local_3);
      }
      
      public function loadSkillInfoList() : BaseLoader
      {
         var _local_1:BaseLoader = LoadResourceManager.Instance.createLoader(PathManager.solveRequestPath("ConsortiaBufferTemp.xml"),5);
         _local_1.loadErrorMessage = LanguageMgr.GetTranslation("ddt.consortion.skillInfo.loadError");
         _local_1.analyzer = new ConsortionSkillInfoAnalyzer(this.skillInfoListComplete);
         return _local_1;
      }
      
      private function __buyBadgeHandler(_arg_1:PkgEvent) : void
      {
         var _local_7:* = null;
         var _local_6:PackageIn = _arg_1.pkg;
         var _local_8:int = _local_6.readInt();
         var _local_4:int = _local_6.readInt();
         var _local_3:int = _local_6.readInt();
         var _local_5:Date = _local_6.readDate();
         var _local_2:Boolean = _local_6.readBoolean();
         if(_local_8 == PlayerManager.Instance.Self.ConsortiaID)
         {
            PlayerManager.Instance.Self.consortiaInfo.BadgeBuyTime = DateUtils.dateFormat(_local_5);
            PlayerManager.Instance.Self.consortiaInfo.BadgeID = _local_4;
            PlayerManager.Instance.Self.consortiaInfo.ValidDate = _local_3;
            PlayerManager.Instance.Self.badgeID = _local_4;
            _local_7 = BadgeInfoManager.instance.getBadgeInfoByID(_local_4);
            PlayerManager.Instance.Self.consortiaInfo.Riches -= _local_7.Cost;
         }
      }
      
      public function getPerRank() : void
      {
         var _local_2:URLVariables = new URLVariables();
         _local_2.UserID = PlayerManager.Instance.Self.ID;
         _local_2.ConsortiaID = PlayerManager.Instance.Self.ConsortiaID;
         var _local_1:BaseLoader = LoadResourceManager.Instance.creatAndStartLoad(PathManager.solveRequestPath("ConsortiaWarPlayerRank.ashx"),6,_local_2);
         _local_1.analyzer = new PersonalRankAnalyze(this.perRankPayHander);
      }
      
      private function perRankPayHander(_arg_1:PersonalRankAnalyze) : void
      {
         ConsortionModelManager.Instance.dispatchEvent(new ConsortionEvent("club_per_list",_arg_1.dataList));
      }
      
      public function getConsortionRank() : void
      {
         var _local_1:BaseLoader = LoadResourceManager.Instance.creatAndStartLoad(PathManager.solveRequestPath("ConsortiaWarConsortiaRank.ashx"),6);
         _local_1.analyzer = new ConsortiaAnalyze(this.ConsortiaRankPayHander);
      }
      
      private function ConsortiaRankPayHander(_arg_1:ConsortiaAnalyze) : void
      {
         ConsortionModelManager.Instance.dispatchEvent(new ConsortionEvent("club_rank_list",_arg_1.dataList));
      }
      
      public function getConsortionTaskRank() : void
      {
         var _local_2:URLVariables = RequestVairableCreater.creatWidthKey(true);
         _local_2.ConsortiaID = PlayerManager.Instance.Self.ConsortiaID;
         var _local_1:BaseLoader = LoadResourceManager.Instance.creatAndStartLoad(PathManager.solveRequestPath("ConsortiaMissionList.ashx"),7,_local_2);
         _local_1.analyzer = new ConsortiaTaskRankAnalyzer(this.ConsortiaTaskRankHander);
      }
      
      private function ConsortiaTaskRankHander(_arg_1:ConsortiaTaskRankAnalyzer) : void
      {
         ConsortionModelManager.Instance.dispatchEvent(new ConsortionEvent("task_rank_list",_arg_1.dataList));
      }
      
      public function alertTaxFrame() : void
      {
         dispatchEvent(new CEvent("cmctrl_alert_tax"));
      }
      
      public function alertRedPackageFrame() : void
      {
         RedPackageManager.getInstance().showView("red_pkg_consortia_select");
      }
      
      public function alertManagerFrame() : void
      {
         dispatchEvent(new CEvent("cmctrl_alert_manager"));
         this.loadUseConditionList(this.useConditionListComplete,PlayerManager.Instance.Self.ConsortiaID);
      }
      
      public function rankFrame() : void
      {
         dispatchEvent(new CEvent("cmctrl_rank"));
         this.getPerRank();
      }
      
      public function alertShopFrame() : void
      {
         dispatchEvent(new CEvent("cmctrl_alert_shop"));
         this.loadUseConditionList(this.useConditionListComplete,PlayerManager.Instance.Self.ConsortiaID);
      }
      
      public function alertBankFrame() : void
      {
         dispatchEvent(new CEvent("cmctrl_alert_bank"));
      }
      
      public function hideBankFrame() : void
      {
         dispatchEvent(new CEvent("cmctrl_hide_bank"));
      }
      
      public function clearReference() : void
      {
         dispatchEvent(new CEvent("cmctrl_clear_reference"));
      }
      
      public function alertTakeInFrame() : void
      {
         dispatchEvent(new CEvent("cmctrl_alert_takein"));
         this.getApplyRecordList(this.applyListComplete,-1,PlayerManager.Instance.Self.ConsortiaID);
      }
      
      public function alertQuitFrame() : void
      {
         dispatchEvent(new CEvent("cmctrl_alert_quit"));
      }
      
      public function bossConfigDataSetup(_arg_1:ConsortiaBossDataAnalyzer) : void
      {
         this._bossConfigDataList = _arg_1.dataList;
      }
      
      public function get bossMaxLv() : int
      {
         return Boolean(this._bossConfigDataList) ? int(this._bossConfigDataList.length) : 0;
      }
      
      public function get bossCallCondition() : int
      {
         if(Boolean(this._bossConfigDataList) && this._bossConfigDataList.length > 0)
         {
            return this._bossConfigDataList[0].level;
         }
         return 3;
      }
      
      public function getCallExtendBossCostRich(_arg_1:int, _arg_2:int = 0) : int
      {
         var _local_3:int = 0;
         var _local_7:int = 0;
         var _local_5:ConsortiaInfo = PlayerManager.Instance.Self.consortiaInfo;
         if(_arg_2 == 0)
         {
            _local_3 = _local_5.Level + _local_5.SmithLevel + _local_5.ShopLevel + _local_5.StoreLevel + _local_5.BufferLevel;
         }
         else
         {
            _local_3 = _arg_2;
         }
         var _local_4:* = 100001;
         var _local_6:int = int(this._bossConfigDataList.length);
         _local_7 = 0;
         while(_local_7 < _local_6)
         {
            if(_local_3 < this._bossConfigDataList[_local_7].level)
            {
               break;
            }
            if(_arg_1 == 1)
            {
               _local_4 = this._bossConfigDataList[_local_7].callBossRich;
            }
            else
            {
               _local_4 = this._bossConfigDataList[_local_7].extendTimeRich;
            }
            _local_7++;
         }
         return _local_4;
      }
      
      public function getCallBossCostRich(_arg_1:int) : int
      {
         return this._bossConfigDataList[_arg_1 - 1].callBossRich;
      }
      
      public function getCanCallBossMaxLevel(_arg_1:int = 0) : int
      {
         var _local_3:int = 0;
         var _local_6:int = 0;
         var _local_4:ConsortiaInfo = PlayerManager.Instance.Self.consortiaInfo;
         if(_arg_1 == 0)
         {
            _local_3 = _local_4.Level + _local_4.SmithLevel + _local_4.ShopLevel + _local_4.StoreLevel + _local_4.BufferLevel;
         }
         else
         {
            _local_3 = _arg_1;
         }
         var _local_5:int = int(this._bossConfigDataList.length);
         var _local_2:int = 1;
         _local_6 = _local_5 - 1;
         while(_local_6 >= 0)
         {
            if(_local_3 >= this._bossConfigDataList[_local_6].level)
            {
               _local_2 = _local_6 + 1;
               break;
            }
            _local_6--;
         }
         return _local_2;
      }
      
      public function getRequestCallBossLevel(_arg_1:int) : int
      {
         return this._bossConfigDataList[_arg_1 - 1].level;
      }
      
      private function bossOpenCloseHandler(_arg_1:PkgEvent) : void
      {
         var _local_2:PackageIn = _arg_1.pkg;
         var _local_3:int = _local_2.readByte();
         if(_local_3 == 0)
         {
            this.isShowBossOpenTip = true;
            this.isBossOpen = true;
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.consortia.bossOpenTipTxt"));
            ChatManager.Instance.sysChatYellow(LanguageMgr.GetTranslation("ddt.consortia.bossOpenTipTxt"));
            if(StateManager.currentStateType == "main")
            {
               SystemOpenPromptManager.instance.showFrame();
            }
         }
         else if(_local_3 == 3)
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.consortia.bossExtendTipTxt"));
            ChatManager.Instance.sysChatYellow(LanguageMgr.GetTranslation("ddt.consortia.bossExtendTipTxt"));
         }
         else
         {
            this.isShowBossOpenTip = false;
            this.isBossOpen = false;
            if(_local_3 == 1)
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.consortia.bossCloseTipTxt"));
               ChatManager.Instance.sysChatYellow(LanguageMgr.GetTranslation("ddt.consortia.bossCloseTipTxt"));
            }
            else
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.consortia.bossCloseTipTxt2"));
               ChatManager.Instance.sysChatYellow(LanguageMgr.GetTranslation("ddt.consortia.bossCloseTipTxt2"));
            }
            LoadResourceManager.Instance.startLoad(MailManager.Instance.getAllEmailLoader());
         }
      }
      
      public function showEnterBtnInHallStateView(_arg_1:IHallStateView) : void
      {
         if(this._enterBtn == null)
         {
            this._enterBtn = ComponentFactory.Instance.creat("hall.consortia.btn");
         }
         _arg_1.leftTopGbox.addChild(this._enterBtn);
         _arg_1.arrangeLeftGrid();
         this._enterBtn.addEventListener("click",this.onEnterBtnClick);
      }
      
      public function hideEnterBtnInHallStateView(_arg_1:IHallStateView) : void
      {
         if(this._enterBtn != null && this._enterBtn.parent != null)
         {
            _arg_1.leftTopGbox.removeChild(this._enterBtn);
            _arg_1.leftTopGbox.arrange();
            this._enterBtn.removeEventListener("click",this.onEnterBtnClick);
         }
         ObjectUtils.disposeObject(this._enterBtn);
         this._enterBtn = null;
      }
      
      protected function onEnterBtnClick(_arg_1:MouseEvent) : void
      {
         SoundManager.instance.play("008");
         new HelperDataModuleLoad().loadDataModule([LoaderCreate.Instance.createConsortiaBossTemplateLoader],this.gotoConsortia);
      }
      
      private function gotoConsortia() : void
      {
         if(!WeakGuildManager.Instance.checkOpen(15,17))
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.functionLimitTip",17));
            return;
         }
         StateManager.setState("consortia");
         ComponentSetting.SEND_USELOG_ID(5);
      }
      
      public function openBossFrame() : void
      {
         dispatchEvent(new CEvent("cmctrl_open_boss"));
      }
      
      private function onConSortiaBackAward(_arg_1:PkgEvent) : void
      {
         var _local_4:PackageIn = _arg_1.pkg;
         var _local_3:int = _local_4.readInt();
         var _local_2:CallBackModel = this._model.callBackModel;
         _local_2.awardStateMap[_local_3.toString()] = 1;
         dispatchEvent(new Event("event_consortia_back_award"));
      }
      
      private function onConSortiaBackInfo(_arg_1:PkgEvent) : void
      {
         var _local_6:int = 0;
         var _local_8:int = 0;
         var _local_5:int = 0;
         var _local_2:int = 0;
         var _local_3:int = 0;
         var _local_13:int = 0;
         var _local_9:int = 0;
         var _local_11:* = null;
         var _local_10:* = null;
         var _local_4:PackageIn = _arg_1.pkg;
         var _local_7:CallBackModel = this._model.callBackModel;
         _local_7.startTime = _local_4.readDate();
         _local_7.endTime = _local_4.readDate();
         _local_7.callBackCount = _local_4.readInt();
         _local_7.awardArr = [];
         var _local_14:int = _local_4.readInt();
         _local_6 = 0;
         while(_local_6 < _local_14)
         {
            _local_8 = _local_4.readInt();
            _local_5 = _local_4.readInt();
            _local_2 = _local_4.readInt();
            _local_3 = _local_4.readInt();
            _local_13 = _local_4.readInt();
            _local_11 = ItemManager.Instance.getTemplateById(_local_5);
            _local_10 = new InventoryItemInfo();
            ObjectUtils.copyProperties(_local_10,_local_11);
            _local_10.ValidDate = _local_3;
            _local_10.Count = _local_2;
            _local_10.IsBinds = true;
            _local_10.Property5 = "1";
            _local_7.awardArr.push({
               "itemTemplateInfo":_local_10,
               "awardID":_local_8,
               "backCount":_local_13
            });
            _local_6++;
         }
         _local_7.awardStateMap = {};
         var _local_12:int = _local_4.readInt();
         _local_6 = 0;
         while(_local_6 < _local_12)
         {
            _local_9 = _local_4.readInt();
            _local_7.awardStateMap[_local_9] = _local_4.readInt();
            _local_6++;
         }
         dispatchEvent(new Event("event_consortia_back_info"));
      }
      
      public function enterConsortiaState() : void
      {
         new HelperDataModuleLoad().loadDataModule([LoaderCreate.Instance.createConsortiaBossTemplateLoader],this.gotoConsortia);
      }
      
      public function requestSortionActiveTargetSchedule() : void
      {
         SocketManager.Instance.out.sendConsortionActiveTargetSchedule();
      }
      
      private function onConSortionActiveTargetSchedule(_arg_1:PkgEvent) : void
      {
         var _local_6:Vector.<ConsortionActiveTargetData> = null;
         var _local_4:ConsortionActiveTargetData = null;
         var _local_3:int = _arg_1.pkg.readInt();
         var _local_7:int = _arg_1.pkg.readInt();
         var _local_5:int = _arg_1.pkg.readInt();
         var _local_2:int = _arg_1.pkg.readInt();
         for each(_local_6 in this._model.activeTargetDic)
         {
            for each(_local_4 in _local_6)
            {
               if(_local_4.targetId == 1)
               {
                  _local_4.processValue = _local_3;
               }
               else if(_local_4.targetId == 2)
               {
                  _local_4.processValue = _local_7;
               }
               else if(_local_4.targetId == 3)
               {
                  _local_4.processValue = _local_5;
               }
               else if(_local_4.targetId == 4)
               {
                  _local_4.processValue = _local_2;
               }
            }
         }
         ConsortionModelManager.Instance.requestConsortionActiveTagertStatus();
         dispatchEvent(new CEvent("updatectiveTargetSchedule"));
      }
      
      public function requestConsortionActiveTagertStatus() : void
      {
         SocketManager.Instance.out.sendConsortionActiveTagertStatus();
      }
      
      public function requestConsortionActiveTagertReward(_arg_1:int) : void
      {
         SocketManager.Instance.out.sendConsortionActiveTagertReward(_arg_1);
      }
      
      private function onConSortionActiveTargetStatus(_arg_1:PkgEvent) : void
      {
         var _local_7:int = 0;
         var _local_6:Vector.<ConsortionActiveTargetData> = null;
         var _local_4:int = 0;
         var _local_3:ConsortionActiveTargetData = null;
         var _local_2:int = _arg_1.pkg.readInt();
         ConsortionModelManager.Instance.model.rewardLv = _local_2;
         var _local_5:Boolean = true;
         _local_7 = 1;
         while(_local_7 <= 3)
         {
            if(_local_7 <= _local_2)
            {
               this._model.activeTargetStautsDic[_local_7] = 3;
               _local_4 = _local_2;
            }
            else
            {
               _local_6 = ConsortionModelManager.Instance.model.activeTargetDic[_local_7];
               for each(_local_3 in _local_6)
               {
                  if(_local_3.processValue < _local_3.conditionValue)
                  {
                     _local_5 = false;
                     break;
                  }
               }
               if(_local_5)
               {
                  this._model.activeTargetStautsDic[_local_7] = 2;
                  _local_4 = _local_7;
               }
               else if(_local_7 >= 1 && _local_4 == _local_7 - 1)
               {
                  this._model.activeTargetStautsDic[_local_7] = 1;
               }
               else
               {
                  this._model.activeTargetStautsDic[_local_7] = 0;
               }
            }
            _local_7++;
         }
         ConsortionModelManager.Instance.dispatchEvent(new CEvent("updateActiveTargetStatus"));
      }
      
      public function initConsortionActiveTarget() : void
      {
         var _local_7:int = 0;
         var _local_5:int = 0;
         var _local_1:Array = null;
         var _local_3:ConsortionActiveTargetData = null;
         var _local_6:Vector.<ConsortionActiveTargetData> = null;
         if(this._model.hasActiveTargetInited)
         {
            return;
         }
         var _local_4:String = ServerConfigManager.instance.consortionActiveTarget;
         var _local_2:Array = _local_4.split("|");
         _local_7 = 1;
         while(_local_7 <= _local_2.length)
         {
            _local_6 = new Vector.<ConsortionActiveTargetData>();
            this._model.activeTargetDic[_local_7] = _local_6;
            _local_1 = _local_2[_local_7 - 1].split(",");
            _local_5 = 1;
            while(_local_5 <= _local_1.length)
            {
               _local_3 = new ConsortionActiveTargetData();
               _local_3.targetId = _local_5;
               _local_3.conditionValue = _local_1[_local_5 - 1];
               _local_3.targetLv = _local_7;
               _local_6.push(_local_3);
               _local_5++;
            }
            _local_7++;
         }
         this._model.hasActiveTargetInited = true;
      }
      
      public function checkRewardStauts() : Boolean
      {
         var _local_6:int = 0;
         var _local_5:Vector.<ConsortionActiveTargetData> = null;
         var _local_2:int = 0;
         var _local_3:ConsortionActiveTargetData = null;
         var _local_1:int = ConsortionModelManager.Instance.model.rewardLv;
         var _local_4:Boolean = true;
         _local_6 = 1;
         while(_local_6 <= 3)
         {
            if(_local_6 <= _local_1)
            {
               _local_2 += 0;
            }
            else
            {
               _local_5 = ConsortionModelManager.Instance.model.activeTargetDic[_local_6];
               for each(_local_3 in _local_5)
               {
                  if(_local_3.processValue < _local_3.conditionValue)
                  {
                     _local_4 = false;
                     break;
                  }
               }
               if(_local_4)
               {
                  _local_2 += 1;
               }
               else
               {
                  _local_2 += 0;
               }
            }
            _local_6++;
         }
         return _local_2 > 0;
      }
   }
}

