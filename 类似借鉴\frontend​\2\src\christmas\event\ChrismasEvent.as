package christmas.event
{
   import flash.events.Event;
   
   public class <PERSON><PERSON><PERSON><PERSON> extends Event
   {
      
      public static const SHOW:String = "xmas_show";
      
      public static const PLAYING_SNOWMAN:String = "xmas_playing_snowman";
      
      public static const RE_CONNECT_CHRISTMAS:String = "xmas_reconnect_christmas";
      
      public static const RE_CONNECT:String = "xmas_reconnect";
      
      public static const SNOW_IS_UPDATE:String = "xmas_snow_is_update";
      
      public static const SNOWMAN_ENTER:String = "xmas_snowman_enter";
      
      public static const CLICK_CHRISTMAS_ICON:String = "xmas_click_christmas_icon";
      
      public static const DISPOSE_ENTER_ICON:String = "xmas_dispose_enter_icon";
      
      public static const GAME_START:String = "xmas_game_start";
      
      public static const BOSS_GAME_START:String = "boss_game_start";
      
      private var _data:Object;
      
      public function ChrismasEvent(_arg_1:String, _arg_2:Object = null, _arg_3:<PERSON><PERSON><PERSON> = false, _arg_4:<PERSON><PERSON><PERSON> = false)
      {
         this._data = _arg_2;
         super(_arg_1,_arg_3,_arg_4);
      }
      
      public function get data() : Object
      {
         return this._data;
      }
   }
}

