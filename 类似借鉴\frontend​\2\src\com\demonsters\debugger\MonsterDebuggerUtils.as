package com.demonsters.debugger
{
   import flash.display.BitmapData;
   import flash.display.DisplayObject;
   import flash.display.DisplayObjectContainer;
   import flash.display.Stage;
   import flash.geom.Matrix;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import flash.system.System;
   import flash.utils.Dictionary;
   import flash.utils.getQualifiedClassName;
   import starling.display.DisplayObject;
   import starling.display.DisplayObjectContainer;
   
   internal class MonsterDebuggerUtils
   {
      
      private static var _references:Dictionary = new Dictionary(true);
      
      private static var _reference:int = 0;
      
      public function MonsterDebuggerUtils()
      {
         super();
      }
      
      public static function snapshot(_arg_1:flash.display.DisplayObject, _arg_2:Rectangle = null) : BitmapData
      {
         var _local_5:Boolean;
         var _local_13:Number;
         var _local_12:Number;
         var _local_3:Number;
         var _local_8:Rectangle;
         var _local_10:Number = NaN;
         var _local_4:Number = NaN;
         var _local_9:BitmapData = null;
         var _local_7:* = null;
         var _local_11:* = null;
         var _local_6:* = null;
         if(_arg_1 == null)
         {
            return null;
         }
         _local_5 = _arg_1.visible;
         _local_13 = _arg_1.alpha;
         _local_12 = _arg_1.rotation;
         _local_3 = _arg_1.scaleX;
         _local_4 = _arg_1.scaleY;
         try
         {
            _arg_1.visible = true;
            _arg_1.alpha = 1;
            _arg_1.rotation = 0;
            _arg_1.scaleX = 1;
            _arg_1.scaleY = 1;
         }
         catch(e1:Error)
         {
         }
         _local_8 = _arg_1.getBounds(_arg_1);
         _local_8.x += 0.5;
         _local_8.y += 0.5;
         _local_8.width += 0.5;
         _local_8.height += 0.5;
         if(_arg_1 is Stage)
         {
            _local_8.x = 0;
            _local_8.y = 0;
            _local_8.width = Stage(_arg_1).stageWidth;
            _local_8.height = Stage(_arg_1).stageHeight;
         }
         if(_local_8.width <= 0 || _local_8.height <= 0)
         {
            return null;
         }
         try
         {
            _local_9 = new BitmapData(_local_8.width,_local_8.height,false,16777215);
            _local_7 = new Matrix();
            _local_7.tx = -_local_8.x;
            _local_7.ty = -_local_8.y;
            _local_9.draw(_arg_1,_local_7,null,null,null,false);
         }
         catch(e2:Error)
         {
            _local_9 = null;
         }
         try
         {
            _arg_1.visible = _local_5;
            _arg_1.alpha = _local_13;
            _arg_1.rotation = _local_12;
            _arg_1.scaleX = _local_3;
            _arg_1.scaleY = _local_4;
         }
         catch(e3:Error)
         {
         }
         if(_local_9 == null)
         {
            return null;
         }
         if(_arg_2 != null)
         {
            if(_local_8.width <= _arg_2.width && _local_8.height <= _arg_2.height)
            {
               return _local_9;
            }
            _local_11 = _local_8.clone();
            _local_11.width = _arg_2.width;
            _local_11.height = _arg_2.width * (_local_8.height / _local_8.width);
            if(_local_11.height > _arg_2.height)
            {
               _local_11 = _local_8.clone();
               _local_11.width = _arg_2.height * (_local_8.width / _local_8.height);
               _local_11.height = _arg_2.height;
            }
            _local_10 = _local_11.width / _local_8.width;
            try
            {
               _local_6 = new BitmapData(_local_11.width,_local_11.height,false,0);
               _local_7 = new Matrix();
               _local_7.scale(_local_10,_local_10);
               _local_6.draw(_local_9,_local_7,null,null,null,true);
               _local_9.dispose();
               _local_9 = _local_6;
            }
            catch(e4:Error)
            {
               _local_9.dispose();
               _local_9 = null;
            }
         }
         return _local_9;
      }
      
      public static function getMemory() : uint
      {
         return System.totalMemory;
      }
      
      public static function pause() : Boolean
      {
         try
         {
            System.pause();
            return true;
         }
         catch(e:Error)
         {
         }
         return false;
      }
      
      public static function resume() : Boolean
      {
         try
         {
            System.resume();
            return true;
         }
         catch(e:Error)
         {
         }
         return false;
      }
      
      public static function stackTrace() : XML
      {
         var _local_12:* = undefined;
         var _local_10:* = undefined;
         var _local_4:int = 0;
         var _local_7:* = undefined;
         var _local_5:int = 0;
         var _local_6:int = 0;
         var _local_1:* = undefined;
         var _local_11:* = undefined;
         var _local_8:* = undefined;
         var _local_3:* = undefined;
         var _local_13:* = undefined;
         var _local_9:XML = null;
         _local_12 = undefined;
         _local_10 = undefined;
         _local_7 = undefined;
         _local_1 = undefined;
         _local_11 = undefined;
         _local_8 = undefined;
         _local_3 = undefined;
         _local_13 = undefined;
         _local_12 = null;
         _local_10 = null;
         _local_7 = null;
         _local_1 = null;
         _local_11 = null;
         _local_8 = null;
         _local_3 = null;
         _local_13 = null;
         var _local_2:XML = <root/>;
         _local_9 = <node/>;
         try
         {
            throw new Error();
         }
         catch(e:Error)
         {
            _local_12 = e.getStackTrace();
            if(_local_12 == null || _local_12 == "")
            {
               return <root><error>Stack unavailable</error></root>;
            }
            _local_12 = _local_12.split("\t").join("");
            _local_10 = _local_12.split("\n");
            if(_local_10.length <= 4)
            {
               return <root><error>Stack to short</error></root>;
            }
            _local_10.shift();
            _local_10.shift();
            _local_10.shift();
            _local_10.shift();
            _local_4 = 0;
            while(_local_4 < _local_10.length)
            {
               _local_7 = _local_10[_local_4];
               _local_7 = _local_7.substring(3,_local_7.length);
               _local_5 = int(_local_7.indexOf("["));
               _local_6 = int(_local_7.indexOf("/"));
               if(_local_5 == -1)
               {
                  _local_5 = int(_local_7.length);
               }
               if(_local_6 == -1)
               {
                  _local_6 = _local_5;
               }
               _local_1 = MonsterDebuggerUtils.parseType(_local_7.substring(0,_local_6));
               _local_11 = "";
               _local_8 = "";
               _local_3 = "";
               if(_local_6 != _local_7.length && _local_6 != _local_5)
               {
                  _local_11 = _local_7.substring(_local_6 + 1,_local_5);
               }
               if(_local_5 != _local_7.length)
               {
                  _local_8 = _local_7.substring(_local_5 + 1,_local_7.lastIndexOf(":"));
                  _local_3 = _local_7.substring(_local_7.lastIndexOf(":") + 1,_local_7.length - 1);
               }
               _local_13 = <node/>;
               _local_13.@classname = _local_1;
               _local_13.@method = _local_11;
               _local_13.@file = _local_8;
               _local_13.@line = _local_3;
               _local_9.appendChild(_local_13);
               _local_4 += 1;
            }
         }
         _local_2.appendChild(_local_9.children());
         return _local_2;
      }
      
      public static function getReferenceID(_arg_1:*) : String
      {
         if(_arg_1 in _references)
         {
            return _references[_arg_1];
         }
         var _local_2:String = "#" + _reference;
         _references[_arg_1] = _local_2;
         ++_reference;
         return _local_2;
      }
      
      public static function getReference(_arg_1:String) : *
      {
         var _local_3:* = undefined;
         var _local_2:* = null;
         if(_arg_1.charAt(0) != "#")
         {
            return null;
         }
         for(_local_3 in _references)
         {
            _local_2 = _references[_local_3];
            if(_local_2 == _arg_1)
            {
               return _local_3;
            }
         }
         return null;
      }
      
      public static function getObject(_arg_1:*, _arg_2:String = "", _arg_3:int = 0) : *
      {
         var _local_6:*;
         var _local_5:Array;
         var _local_7:int = 0;
         var _local_4:Number = NaN;
         if(_arg_2 == null || _arg_2 == "")
         {
            return _arg_1;
         }
         if(_arg_2.charAt(0) == "#")
         {
            return getReference(_arg_2);
         }
         _local_6 = _arg_1;
         _local_5 = _arg_2.split(".");
         _local_7 = 0;
         while(_local_7 < _local_5.length - _arg_3)
         {
            if(_local_5[_local_7] != "")
            {
               try
               {
                  if(_local_5[_local_7] == "children()")
                  {
                     _local_6 = _local_6.children();
                  }
                  else if(_local_6 is flash.display.DisplayObjectContainer && _local_5[_local_7].indexOf("getChildAt(") == 0)
                  {
                     _local_4 = Number(_local_5[_local_7].substring(11,_local_5[_local_7].indexOf(")",11)));
                     _local_6 = flash.display.DisplayObjectContainer(_local_6).getChildAt(_local_4);
                  }
                  else if(_local_6 is starling.display.DisplayObjectContainer && _local_5[_local_7].indexOf("getChildAt(") == 0)
                  {
                     _local_4 = Number(_local_5[_local_7].substring(11,_local_5[_local_7].indexOf(")",11)));
                     _local_6 = starling.display.DisplayObjectContainer(_local_6).getChildAt(_local_4);
                  }
                  else
                  {
                     _local_6 = _local_6[_local_5[_local_7]];
                  }
               }
               catch(e:Error)
               {
                  break;
               }
            }
            _local_7 += 1;
         }
         return _local_6;
      }
      
      public static function parse(_arg_1:*, _arg_2:String = "", _arg_3:int = 1, _arg_4:int = 5, _arg_5:Boolean = true) : XML
      {
         var _local_8:Boolean = false;
         var _local_9:String = null;
         var _local_11:* = null;
         var _local_7:XML = <root/>;
         var _local_13:XML = <node/>;
         var _local_12:XML = new XML();
         var _local_10:String = "";
         var _local_14:String = "";
         var _local_6:String = "iconRoot";
         if(_arg_4 != -1 && _arg_3 > _arg_4)
         {
            return _local_7;
         }
         if(_arg_1 == null)
         {
            _local_10 = "null";
            _local_9 = "null";
            _local_6 = "iconWarning";
         }
         else
         {
            _local_12 = MonsterDebuggerDescribeType.get(_arg_1);
            _local_10 = parseType(_local_12.@name);
            _local_14 = parseType(_local_12.@base);
            _local_8 = Boolean(_local_12.@isDynamic);
            if(_arg_1 is Class)
            {
               _local_9 = "Class = " + _local_10;
               _local_10 = "Class";
               _local_13.appendChild(parseClass(_arg_1,_arg_2,_local_12,_arg_3,_arg_4,_arg_5).children());
            }
            else if(_local_10 == "XML")
            {
               _local_13.appendChild(parseXML(_arg_1,_arg_2 + ".children()",_arg_3,_arg_4).children());
            }
            else if(_local_10 == "XMLList")
            {
               _local_9 = _local_10 + " [" + _arg_1.length() + "]";
               _local_13.appendChild(parseXMLList(_arg_1,_arg_2,_arg_3,_arg_4).children());
            }
            else if(_local_10 == "Array" || _local_10.indexOf("Vector.") == 0)
            {
               _local_9 = _local_10 + " [" + _arg_1["length"] + "]";
               _local_13.appendChild(parseArray(_arg_1,_arg_2,_arg_3,_arg_4).children());
            }
            else if(_local_10 == "String" || _local_10 == "Boolean" || _local_10 == "Number" || _local_10 == "int" || _local_10 == "uint")
            {
               _local_13.appendChild(parseBasics(_arg_1,_arg_2,_local_10).children());
            }
            else if(_local_10 == "Object")
            {
               _local_13.appendChild(parseObject(_arg_1,_arg_2,_arg_3,_arg_4,_arg_5).children());
            }
            else
            {
               _local_13.appendChild(parseClass(_arg_1,_arg_2,_local_12,_arg_3,_arg_4,_arg_5).children());
            }
         }
         if(_arg_3 == 1)
         {
            _local_11 = <node/>;
            _local_11.@icon = _local_6;
            _local_11.@label = _local_10;
            _local_11.@type = _local_10;
            _local_11.@target = _arg_2;
            if(_local_9 != null)
            {
               _local_11.@label = _local_9;
            }
            _local_11.appendChild(_local_13.children());
            _local_7.appendChild(_local_11);
         }
         else
         {
            _local_7.appendChild(_local_13.children());
         }
         return _local_7;
      }
      
      private static function parseBasics(_arg_1:*, _arg_2:String, _arg_3:String) : XML
      {
         var _local_5:XML = <root/>;
         var _local_4:XML = <node/>;
         _local_4.@icon = "iconVariable";
         _local_4.@access = "variable";
         _local_4.@permission = "readwrite";
         _local_4.@label = _arg_3 + " = " + printValue(_arg_1,_arg_3,true);
         _local_4.@name = "";
         _local_4.@type = _arg_3;
         _local_4.@value = printValue(_arg_1,_arg_3);
         _local_4.@target = _arg_2;
         _local_5.appendChild(_local_4);
         return _local_5;
      }
      
      private static function parseArray(_arg_1:*, _arg_2:String, _arg_3:int = 1, _arg_4:int = 5, _arg_5:Boolean = true) : XML
      {
         var _local_10:int = 0;
         var _local_13:* = undefined;
         var _local_11:* = null;
         var _local_9:XML = <root/>;
         var _local_12:String = "";
         var _local_7:String = "";
         var _local_8:Array = [];
         var _local_6:Boolean = true;
         for(_local_13 in _arg_1)
         {
            if(!(_local_13 is int))
            {
               _local_6 = false;
            }
            _local_8.push(_local_13);
         }
         if(_local_6)
         {
            _local_8.sort(16);
         }
         else
         {
            _local_8.sort(1);
         }
         _local_10 = 0;
         while(_local_10 < _local_8.length)
         {
            _local_12 = parseType(MonsterDebuggerDescribeType.get(_arg_1[_local_8[_local_10]]).@name);
            _local_7 = _arg_2 + "." + _local_8[_local_10];
            if(_local_12 == "String" || _local_12 == "Boolean" || _local_12 == "Number" || _local_12 == "int" || _local_12 == "uint" || _local_12 == "Function")
            {
               _local_11 = <node/>;
               _local_11.@icon = "iconVariable";
               _local_11.@access = "variable";
               _local_11.@permission = "readwrite";
               _local_11.@label = "[" + _local_8[_local_10] + "] (" + _local_12 + ") = " + printValue(_arg_1[_local_8[_local_10]],_local_12,true);
               _local_11.@name = "[" + _local_8[_local_10] + "]";
               _local_11.@type = _local_12;
               _local_11.@value = printValue(_arg_1[_local_8[_local_10]],_local_12);
               _local_11.@target = _local_7;
               _local_9.appendChild(_local_11);
            }
            else
            {
               _local_11 = <node/>;
               _local_11.@icon = "iconVariable";
               _local_11.@access = "variable";
               _local_11.@permission = "readwrite";
               _local_11.@label = "[" + _local_8[_local_10] + "] (" + _local_12 + ")";
               _local_11.@name = "[" + _local_8[_local_10] + "]";
               _local_11.@type = _local_12;
               _local_11.@value = "";
               _local_11.@target = _local_7;
               if(_arg_1[_local_8[_local_10]] == null)
               {
                  _local_11.@icon = "iconWarning";
                  _local_11.@label += " = null";
               }
               _local_11.appendChild(parse(_arg_1[_local_8[_local_10]],_local_7,_arg_3 + 1,_arg_4,_arg_5).children());
               _local_9.appendChild(_local_11);
            }
            _local_10++;
         }
         return _local_9;
      }
      
      public static function parseXML(_arg_1:*, _arg_2:String = "", _arg_3:int = 1, _arg_4:int = -1) : XML
      {
         var _local_9:int = 0;
         var _local_7:* = null;
         var _local_6:* = null;
         var _local_5:* = null;
         var _local_8:XML = <root/>;
         if(_arg_4 != -1 && _arg_3 > _arg_4)
         {
            return _local_8;
         }
         if(_arg_2.indexOf("@") != -1)
         {
            _local_7 = <node/>;
            _local_7.@icon = "iconXMLAttribute";
            _local_7.@type = "XMLAttribute";
            _local_7.@access = "variable";
            _local_7.@permission = "readwrite";
            _local_7.@label = _arg_1;
            _local_7.@name = "";
            _local_7.@value = _arg_1;
            _local_7.@target = _arg_2;
            _local_8.appendChild(_local_7);
         }
         else if("name" in _arg_1 && _arg_1.name() == null)
         {
            _local_7 = <node/>;
            _local_7.@icon = "iconXMLValue";
            _local_7.@type = "XMLValue";
            _local_7.@access = "variable";
            _local_7.@permission = "readwrite";
            _local_7.@label = "(XMLValue) = " + printValue(_arg_1,"XMLValue",true);
            _local_7.@name = "";
            _local_7.@value = printValue(_arg_1,"XMLValue");
            _local_7.@target = _arg_2;
            _local_8.appendChild(_local_7);
         }
         else if("hasSimpleContent" in _arg_1 && Boolean(_arg_1.hasSimpleContent()))
         {
            _local_7 = <node/>;
            _local_7.@icon = "iconXMLNode";
            _local_7.@type = "XMLNode";
            _local_7.@access = "variable";
            _local_7.@permission = "readwrite";
            _local_7.@label = _arg_1.name() + " (" + "XMLNode" + ")";
            _local_7.@name = _arg_1.name();
            _local_7.@value = "";
            _local_7.@target = _arg_2;
            if(_arg_1 != "")
            {
               _local_6 = <node/>;
               _local_6.@icon = "iconXMLValue";
               _local_6.@type = "XMLValue";
               _local_6.@access = "variable";
               _local_6.@permission = "readwrite";
               _local_6.@label = "(XMLValue) = " + printValue(_arg_1,"XMLValue");
               _local_6.@name = "";
               _local_6.@value = printValue(_arg_1,"XMLValue");
               _local_6.@target = _arg_2;
               _local_7.appendChild(_local_6);
            }
            _local_9 = 0;
            while(_local_9 < _arg_1.attributes().length())
            {
               _local_6 = <node/>;
               _local_6.@icon = "iconXMLAttribute";
               _local_6.@type = "XMLAttribute";
               _local_6.@access = "variable";
               _local_6.@permission = "readwrite";
               _local_6.@label = "@" + _arg_1.attributes()[_local_9].name() + " (" + "XMLAttribute" + ") = " + _arg_1.attributes()[_local_9];
               _local_6.@name = "";
               _local_6.@value = _arg_1.attributes()[_local_9];
               _local_6.@target = _arg_2 + "." + "@" + _arg_1.attributes()[_local_9].name();
               _local_7.appendChild(_local_6);
               _local_9++;
            }
            _local_8.appendChild(_local_7);
         }
         else
         {
            _local_7 = <node/>;
            _local_7.@icon = "iconXMLNode";
            _local_7.@type = "XMLNode";
            _local_7.@access = "variable";
            _local_7.@permission = "readwrite";
            _local_7.@label = _arg_1.name() + " (" + "XMLNode" + ")";
            _local_7.@name = _arg_1.name();
            _local_7.@value = "";
            _local_7.@target = _arg_2;
            _local_9 = 0;
            while(_local_9 < _arg_1.attributes().length())
            {
               _local_6 = <node/>;
               _local_6.@icon = "iconXMLAttribute";
               _local_6.@type = "XMLAttribute";
               _local_6.@access = "variable";
               _local_6.@permission = "readwrite";
               _local_6.@label = "@" + _arg_1.attributes()[_local_9].name() + " (" + "XMLAttribute" + ") = " + _arg_1.attributes()[_local_9];
               _local_6.@name = "";
               _local_6.@value = _arg_1.attributes()[_local_9];
               _local_6.@target = _arg_2 + "." + "@" + _arg_1.attributes()[_local_9].name();
               _local_7.appendChild(_local_6);
               _local_9++;
            }
            _local_9 = 0;
            while(_local_9 < _arg_1.children().length())
            {
               _local_5 = _arg_2 + "." + "children()" + "." + _local_9;
               _local_7.appendChild(parseXML(_arg_1.children()[_local_9],_local_5,_arg_3 + 1,_arg_4).children());
               _local_9++;
            }
            _local_8.appendChild(_local_7);
         }
         return _local_8;
      }
      
      public static function parseXMLList(_arg_1:*, _arg_2:String = "", _arg_3:int = 1, _arg_4:int = -1) : XML
      {
         var _local_6:int = 0;
         var _local_5:XML = <root/>;
         if(_arg_4 != -1 && _arg_3 > _arg_4)
         {
            return _local_5;
         }
         _local_6 = 0;
         while(_local_6 < _arg_1.length())
         {
            _local_5.appendChild(parseXML(_arg_1[_local_6],_arg_2 + "." + _local_6 + ".children()",_arg_3,_arg_4).children());
            _local_6++;
         }
         return _local_5;
      }
      
      private static function parseObject(_arg_1:*, _arg_2:String, _arg_3:int = 1, _arg_4:int = 5, _arg_5:Boolean = true) : XML
      {
         var _local_12:int = 0;
         var _local_8:* = undefined;
         var _local_13:* = null;
         var _local_9:XML = <root/>;
         var _local_10:XML = <node/>;
         var _local_14:String = "";
         var _local_7:String = "";
         var _local_11:Array = [];
         var _local_6:Boolean = true;
         for(_local_8 in _arg_1)
         {
            if(!(_local_8 is int))
            {
               _local_6 = false;
            }
            _local_11.push(_local_8);
         }
         if(_local_6)
         {
            _local_11.sort(16);
         }
         else
         {
            _local_11.sort(1);
         }
         _local_12 = 0;
         while(_local_12 < _local_11.length)
         {
            _local_14 = parseType(MonsterDebuggerDescribeType.get(_arg_1[_local_11[_local_12]]).@name);
            _local_7 = _arg_2 + "." + _local_11[_local_12];
            if(_local_14 == "String" || _local_14 == "Boolean" || _local_14 == "Number" || _local_14 == "int" || _local_14 == "uint" || _local_14 == "Function")
            {
               _local_13 = <node/>;
               _local_13.@icon = "iconVariable";
               _local_13.@access = "variable";
               _local_13.@permission = "readwrite";
               _local_13.@label = _local_11[_local_12] + " (" + _local_14 + ") = " + printValue(_arg_1[_local_11[_local_12]],_local_14,true);
               _local_13.@name = _local_11[_local_12];
               _local_13.@type = _local_14;
               _local_13.@value = printValue(_arg_1[_local_11[_local_12]],_local_14);
               _local_13.@target = _local_7;
               _local_10.appendChild(_local_13);
            }
            else
            {
               _local_13 = <node/>;
               _local_13.@icon = "iconVariable";
               _local_13.@access = "variable";
               _local_13.@permission = "readwrite";
               _local_13.@label = _local_11[_local_12] + " (" + _local_14 + ")";
               _local_13.@name = _local_11[_local_12];
               _local_13.@type = _local_14;
               _local_13.@value = "";
               _local_13.@target = _local_7;
               if(_arg_1[_local_11[_local_12]] == null)
               {
                  _local_13.@icon = "iconWarning";
                  _local_13.@label += " = null";
               }
               _local_13.appendChild(parse(_arg_1[_local_11[_local_12]],_local_7,_arg_3 + 1,_arg_4,_arg_5).children());
               _local_10.appendChild(_local_13);
            }
            _local_12++;
         }
         _local_9.appendChild(_local_10.children());
         return _local_9;
      }
      
      private static function parseClass(_arg_1:*, _arg_2:String, _arg_3:XML, _arg_4:int = 1, _arg_5:int = 5, _arg_6:Boolean = true) : XML
      {
         var _local_18:int = 0;
         var _local_31:int = 0;
         var _local_14:int = 0;
         var _local_34:* = undefined;
         var _local_24:* = undefined;
         var _local_17:* = null;
         var _local_19:* = null;
         var _local_30:* = null;
         var _local_10:* = null;
         var _local_35:* = null;
         var _local_23:* = null;
         var _local_20:* = null;
         var _local_22:* = null;
         var _local_11:* = null;
         var _local_7:* = null;
         var _local_12:* = null;
         var _local_29:* = null;
         var _local_15:* = null;
         var _local_28:XML = <root/>;
         var _local_27:XML = <node/>;
         var _local_32:XMLList = _arg_3..variable;
         var _local_9:XMLList = _arg_3..accessor;
         var _local_26:XMLList = _arg_3..constant;
         var _local_25:Boolean = Boolean(_arg_3.@isDynamic);
         var _local_13:int = int(_local_32.length());
         var _local_8:int = int(_local_9.length());
         var _local_16:int = int(_local_26.length());
         var _local_21:Object = {};
         var _local_33:Array = [];
         if(_local_25)
         {
            for(_local_24 in _arg_1)
            {
               _local_17 = String(_local_24);
               if(!_local_21.hasOwnProperty(_local_17))
               {
                  _local_21[_local_17] = _local_17;
                  _local_20 = _local_17;
                  _local_23 = parseType(getQualifiedClassName(_arg_1[_local_17]));
                  _local_22 = _arg_2 + "." + _local_17;
                  _local_30 = "variable";
                  _local_10 = "readwrite";
                  _local_35 = "iconVariable";
                  _local_33[_local_33.length] = {
                     "name":_local_20,
                     "type":_local_23,
                     "target":_local_22,
                     "access":_local_30,
                     "permission":_local_10,
                     "icon":_local_35
                  };
               }
            }
         }
         _local_31 = 0;
         while(_local_31 < _local_13)
         {
            _local_17 = _local_32[_local_31].@name;
            if(!_local_21.hasOwnProperty(_local_17))
            {
               _local_21[_local_17] = _local_17;
               _local_20 = _local_17;
               _local_23 = parseType(_local_32[_local_31].@type);
               _local_22 = _arg_2 + "." + _local_17;
               _local_30 = "variable";
               _local_10 = "readwrite";
               _local_35 = "iconVariable";
               _local_33[_local_33.length] = {
                  "name":_local_20,
                  "type":_local_23,
                  "target":_local_22,
                  "access":_local_30,
                  "permission":_local_10,
                  "icon":_local_35
               };
            }
            _local_31 += 1;
         }
         _local_31 = 0;
         while(_local_31 < _local_8)
         {
            _local_17 = _local_9[_local_31].@name;
            if(!_local_21.hasOwnProperty(_local_17))
            {
               _local_21[_local_17] = _local_17;
               _local_20 = _local_17;
               _local_23 = parseType(_local_9[_local_31].@type);
               _local_22 = _arg_2 + "." + _local_17;
               _local_30 = "accessor";
               _local_10 = "readwrite";
               _local_35 = "iconVariable";
               if(_local_9[_local_31].@access == "readonly")
               {
                  _local_10 = "readonly";
                  _local_35 = "iconVariableReadonly";
               }
               if(_local_9[_local_31].@access == "writeonly")
               {
                  _local_10 = "writeonly";
                  _local_35 = "iconVariableWriteonly";
               }
               _local_33[_local_33.length] = {
                  "name":_local_20,
                  "type":_local_23,
                  "target":_local_22,
                  "access":_local_30,
                  "permission":_local_10,
                  "icon":_local_35
               };
            }
            _local_31 += 1;
         }
         _local_31 = 0;
         while(_local_31 < _local_16)
         {
            _local_17 = _local_26[_local_31].@name;
            if(!_local_21.hasOwnProperty(_local_17))
            {
               _local_21[_local_17] = _local_17;
               _local_20 = _local_17;
               _local_23 = parseType(_local_26[_local_31].@type);
               _local_22 = _arg_2 + "." + _local_17;
               _local_30 = "constant";
               _local_10 = "readonly";
               _local_35 = "iconVariableReadonly";
               _local_33[_local_33.length] = {
                  "name":_local_20,
                  "type":_local_23,
                  "target":_local_22,
                  "access":_local_30,
                  "permission":_local_10,
                  "icon":_local_35
               };
            }
            _local_31 += 1;
         }
         _local_33.sortOn("name",1);
         if(_arg_6 && _arg_1 is flash.display.DisplayObjectContainer)
         {
            _local_11 = flash.display.DisplayObjectContainer(_arg_1);
            _local_7 = [];
            _local_14 = int(_local_11.numChildren);
            _local_31 = 0;
            while(_local_31 < _local_14)
            {
               _local_12 = null;
               try
               {
                  _local_12 = _local_11.getChildAt(_local_31);
               }
               catch(e1:Error)
               {
               }
               if(_local_12 != null)
               {
                  _local_19 = MonsterDebuggerDescribeType.get(_local_12);
                  _local_23 = parseType(_local_19.@name);
                  _local_20 = "DisplayObject";
                  if(_local_12.name != null)
                  {
                     _local_20 += " - " + _local_12.name;
                  }
                  _local_22 = _arg_2 + "." + "getChildAt(" + _local_31 + ")";
                  _local_30 = "displayObject";
                  _local_10 = "readwrite";
                  _local_35 = _local_12 is flash.display.DisplayObjectContainer ? "iconRoot" : "iconDisplayObject";
                  _local_7[_local_7.length] = {
                     "name":_local_20,
                     "type":_local_23,
                     "target":_local_22,
                     "access":_local_30,
                     "permission":_local_10,
                     "icon":_local_35,
                     "index":_local_31
                  };
               }
               _local_31 += 1;
            }
            _local_7.sortOn("name",1);
            _local_33 = _local_7.concat(_local_33);
         }
         else if(_arg_6 && _arg_1 is starling.display.DisplayObjectContainer)
         {
            _local_29 = starling.display.DisplayObjectContainer(_arg_1);
            _local_7 = [];
            _local_14 = int(_local_29.numChildren);
            _local_31 = 0;
            while(_local_31 < _local_14)
            {
               _local_15 = null;
               try
               {
                  _local_15 = _local_29.getChildAt(_local_31);
               }
               catch(e1:Error)
               {
               }
               if(_local_15 != null)
               {
                  _local_19 = MonsterDebuggerDescribeType.get(_local_15);
                  _local_23 = parseType(_local_19.@name);
                  _local_20 = "Starling DisplayObject";
                  if(_local_15.name != null)
                  {
                     _local_20 += " - " + _local_15.name;
                  }
                  _local_22 = _arg_2 + "." + "getChildAt(" + _local_31 + ")";
                  _local_30 = "displayObject";
                  _local_10 = "readwrite";
                  _local_35 = _local_15 is starling.display.DisplayObjectContainer ? "iconRoot" : "iconDisplayObject";
                  _local_7[_local_7.length] = {
                     "name":_local_20,
                     "type":_local_23,
                     "target":_local_22,
                     "access":_local_30,
                     "permission":_local_10,
                     "icon":_local_35,
                     "index":_local_31
                  };
               }
               _local_31 += 1;
            }
            _local_7.sortOn("name",1);
            _local_33 = _local_7.concat(_local_33);
         }
         _local_18 = int(_local_33.length);
         _local_31 = 0;
         while(_local_31 < _local_18)
         {
            _local_23 = _local_33[_local_31].type;
            _local_20 = _local_33[_local_31].name;
            _local_22 = _local_33[_local_31].target;
            _local_10 = _local_33[_local_31].permission;
            _local_30 = _local_33[_local_31].access;
            _local_35 = _local_33[_local_31].icon;
            if(_local_10 != "writeonly")
            {
               try
               {
                  if(_local_30 == "displayObject")
                  {
                     if(_arg_1 is flash.display.DisplayObjectContainer)
                     {
                        _local_34 = flash.display.DisplayObjectContainer(_arg_1).getChildAt(_local_33[_local_31].index);
                     }
                     else if(_arg_1 is starling.display.DisplayObjectContainer)
                     {
                        _local_34 = starling.display.DisplayObjectContainer(_arg_1).getChildAt(_local_33[_local_31].index);
                     }
                  }
                  else
                  {
                     _local_34 = _arg_1[_local_20];
                  }
               }
               catch(e2:Error)
               {
                  _local_34 = null;
               }
               if(_local_23 == "String" || _local_23 == "Boolean" || _local_23 == "Number" || _local_23 == "int" || _local_23 == "uint" || _local_23 == "Function")
               {
                  _local_27 = <node/>;
                  _local_27.@icon = _local_35;
                  _local_27.@label = _local_20 + " (" + _local_23 + ") = " + printValue(_local_34,_local_23,true);
                  _local_27.@name = _local_20;
                  _local_27.@type = _local_23;
                  _local_27.@value = printValue(_local_34,_local_23);
                  _local_27.@target = _local_22;
                  _local_27.@access = _local_30;
                  _local_27.@permission = _local_10;
                  _local_28.appendChild(_local_27);
               }
               else
               {
                  _local_27 = <node/>;
                  _local_27.@icon = _local_35;
                  _local_27.@label = _local_20 + " (" + _local_23 + ")";
                  _local_27.@name = _local_20;
                  _local_27.@type = _local_23;
                  _local_27.@target = _local_22;
                  _local_27.@access = _local_30;
                  _local_27.@permission = _local_10;
                  if(_local_34 == null)
                  {
                     _local_27.@icon = "iconWarning";
                     _local_27.@label += " = null";
                  }
                  _local_27.appendChild(parse(_local_34,_local_22,_arg_4 + 1,_arg_5,_arg_6).children());
                  _local_28.appendChild(_local_27);
               }
            }
            _local_31 += 1;
         }
         return _local_28;
      }
      
      public static function parseFunctions(_arg_1:*, _arg_2:String = "") : XML
      {
         var _local_14:int = 0;
         var _local_22:Boolean = false;
         var _local_18:int = 0;
         var _local_12:int = 0;
         var _local_3:* = null;
         var _local_24:* = null;
         var _local_16:* = null;
         var _local_23:* = null;
         var _local_9:* = null;
         var _local_6:* = null;
         var _local_5:* = null;
         var _local_17:* = null;
         var _local_13:XML = <root/>;
         var _local_21:XML = MonsterDebuggerDescribeType.get(_arg_1);
         var _local_15:String = parseType(_local_21.@name);
         var _local_10:String = "";
         var _local_7:String = "";
         var _local_11:String = "";
         var _local_8:Object = {};
         var _local_4:XMLList = _local_21..method;
         var _local_19:Array = [];
         var _local_20:int = int(_local_4.length());
         _local_3 = <node/>;
         _local_3.@icon = "iconDefault";
         _local_3.@label = "(" + _local_15 + ")";
         _local_3.@target = _arg_2;
         _local_18 = 0;
         while(_local_18 < _local_20)
         {
            _local_24 = _local_4[_local_18].@name;
            try
            {
               if(!_local_8.hasOwnProperty(_local_24))
               {
                  _local_8[_local_24] = _local_24;
                  _local_19[_local_19.length] = {
                     "name":_local_24,
                     "xml":_local_4[_local_18],
                     "access":"method"
                  };
               }
            }
            catch(e:Error)
            {
            }
            _local_18++;
         }
         _local_19.sortOn("name",1);
         _local_20 = int(_local_19.length);
         _local_18 = 0;
         while(_local_18 < _local_20)
         {
            _local_10 = "Function";
            _local_7 = _local_19[_local_18].xml.@name;
            _local_11 = _arg_2 + "." + _local_7;
            _local_16 = parseType(_local_19[_local_18].xml.@returnType);
            _local_23 = _local_19[_local_18].xml..parameter;
            _local_14 = int(_local_23.length());
            _local_9 = [];
            _local_6 = "";
            _local_22 = false;
            _local_12 = 0;
            while(_local_12 < _local_14)
            {
               if(_local_23[_local_12].@optional == "true" && !_local_22)
               {
                  _local_22 = true;
                  _local_9[_local_9.length] = "[";
               }
               _local_9[_local_9.length] = parseType(_local_23[_local_12].@type);
               _local_12++;
            }
            if(_local_22)
            {
               _local_9[_local_9.length] = "]";
            }
            _local_6 = _local_9.join(", ");
            _local_6 = _local_6.replace("[, ","[");
            _local_6 = _local_6.replace(", ]","]");
            _local_5 = <node/>;
            _local_5.@icon = "iconFunction";
            _local_5.@type = "Function";
            _local_5.@access = "method";
            _local_5.@label = _local_7 + "(" + _local_6 + "):" + _local_16;
            _local_5.@name = _local_7;
            _local_5.@target = _local_11;
            _local_5.@args = _local_6;
            _local_5.@returnType = _local_16;
            _local_12 = 0;
            while(_local_12 < _local_14)
            {
               _local_17 = <node/>;
               _local_17.@type = parseType(_local_23[_local_12].@type);
               _local_17.@index = _local_23[_local_12].@index;
               _local_17.@optional = _local_23[_local_12].@optional;
               _local_5.appendChild(_local_17);
               _local_12++;
            }
            _local_3.appendChild(_local_5);
            _local_18++;
         }
         _local_13.appendChild(_local_3);
         return _local_13;
      }
      
      public static function parseType(_arg_1:String) : String
      {
         var _local_3:* = null;
         var _local_2:* = null;
         if(_arg_1.indexOf("::") != -1)
         {
            _arg_1 = _arg_1.substring(_arg_1.indexOf("::") + 2,_arg_1.length);
         }
         if(_arg_1.indexOf("::") != -1)
         {
            _local_3 = _arg_1.substring(0,_arg_1.indexOf("<") + 1);
            _local_2 = _arg_1.substring(_arg_1.indexOf("::") + 2,_arg_1.length);
            _arg_1 = _local_3 + _local_2;
         }
         _arg_1 = _arg_1.replace("()","");
         return _arg_1.replace("MethodClosure","Function");
      }
      
      public static function isDisplayObject(_arg_1:*) : Boolean
      {
         return _arg_1 is flash.display.DisplayObject || _arg_1 is flash.display.DisplayObjectContainer;
      }
      
      public static function isStarlingDisplayObject(_arg_1:*) : Boolean
      {
         return _arg_1 is starling.display.DisplayObject || _arg_1 is starling.display.DisplayObjectContainer;
      }
      
      public static function printValue(_arg_1:*, _arg_2:String, _arg_3:Boolean = false) : String
      {
         if(_arg_2 == "ByteArray")
         {
            return _arg_1["length"] + " bytes";
         }
         if(_arg_1 == null)
         {
            return "null";
         }
         var _local_4:String = String(_arg_1);
         if(_arg_3 && _local_4.length > 140)
         {
            _local_4 = _local_4.substr(0,140) + "...";
         }
         return _local_4;
      }
      
      public static function getObjectUnderPoint(_arg_1:flash.display.DisplayObjectContainer, _arg_2:Point) : flash.display.DisplayObject
      {
         var _local_6:int = 0;
         var _local_5:* = null;
         var _local_4:* = null;
         var _local_3:* = null;
         if(_arg_1.areInaccessibleObjectsUnderPoint(_arg_2))
         {
            return _arg_1;
         }
         _local_5 = _arg_1.getObjectsUnderPoint(_arg_2);
         _local_5.reverse();
         if(_local_5 == null || _local_5.length == 0)
         {
            return _arg_1;
         }
         _local_4 = _local_5[0];
         _local_5.length = 0;
         while(true)
         {
            _local_5[_local_5.length] = _local_4;
            if(_local_4.parent == null)
            {
               break;
            }
            _local_4 = _local_4.parent;
         }
         _local_5.reverse();
         _local_6 = 0;
         while(_local_6 < _local_5.length)
         {
            _local_3 = _local_5[_local_6];
            if(!(_local_3 is flash.display.DisplayObjectContainer))
            {
               break;
            }
            _local_4 = _local_3;
            if(!flash.display.DisplayObjectContainer(_local_3).mouseChildren)
            {
               break;
            }
            _local_6++;
         }
         return _local_4;
      }
      
      public static function getStarlingObjectUnderPoint(_arg_1:starling.display.DisplayObjectContainer, _arg_2:Point) : starling.display.DisplayObject
      {
         var _local_6:int = 0;
         var _local_3:* = null;
         var _local_4:starling.display.DisplayObject = _arg_1.hitTest(_arg_2,false);
         if(_local_4 == null || _local_4 == _arg_1)
         {
            return _arg_1;
         }
         var _local_5:Array = [];
         while(true)
         {
            _local_5[_local_5.length] = _local_4;
            if(_local_4.parent == null)
            {
               break;
            }
            _local_4 = _local_4.parent;
         }
         _local_5.reverse();
         _local_6 = 0;
         while(_local_6 < _local_5.length)
         {
            _local_3 = _local_5[_local_6];
            if(!(_local_3 is starling.display.DisplayObjectContainer))
            {
               break;
            }
            _arg_1 = starling.display.DisplayObjectContainer(_local_3);
            if(!_arg_1.touchable || !_arg_1.visible)
            {
               if(_arg_1.stage == _arg_1)
               {
                  return _arg_1;
               }
               break;
            }
            _local_4 = _local_3;
            _local_6++;
         }
         return _local_4;
      }
   }
}

