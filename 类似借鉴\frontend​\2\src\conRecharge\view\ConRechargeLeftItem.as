package conRecharge.view
{
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.controls.container.VBox;
   import com.pickgliss.ui.core.Disposeable;
   import com.pickgliss.utils.ObjectUtils;
   import conRecharge.ConRechargeManager;
   import ddt.utils.PositionUtils;
   import flash.display.Sprite;
   
   public class ConRechargeLeftItem extends Sprite implements Disposeable
   {
      
      private var _vbox:VBox;
      
      private var _num1:Sprite;
      
      private var _num2:Sprite;
      
      private var _num3:Sprite;
      
      public function ConRechargeLeftItem()
      {
         super();
         this.initView();
      }
      
      private function initView() : void
      {
         var _local_2:int = 0;
         var _local_1:* = null;
         this._vbox = ComponentFactory.Instance.creatComponentByStylename("conRecharge.leftItem.vbox");
         addChild(this._vbox);
         this._num1 = ComponentFactory.Instance.creatNumberSprite(ConRechargeManager.instance.longGiftbagArray[0][0].giftConditionArr[0].remain1,"asset.conRecharge.yellow");
         addChild(this._num1);
         PositionUtils.setPos(this._num1,"asset.conRecharge.yellow1.pos");
         this._num2 = ComponentFactory.Instance.creatNumberSprite(ConRechargeManager.instance.longGiftbagArray[0][1].giftConditionArr[0].remain1,"asset.conRecharge.yellow");
         addChild(this._num2);
         PositionUtils.setPos(this._num2,"asset.conRecharge.yellow2.pos");
         this._num3 = ComponentFactory.Instance.creatNumberSprite(ConRechargeManager.instance.longGiftbagArray[0][2].giftConditionArr[0].remain1,"asset.conRecharge.yellow");
         addChild(this._num3);
         PositionUtils.setPos(this._num3,"asset.conRecharge.yellow3.pos");
         _local_2 = 0;
         while(_local_2 < 3)
         {
            _local_1 = new BoxItem(_local_2);
            this._vbox.addChild(_local_1);
            _local_2++;
         }
      }
      
      public function dispose() : void
      {
         ObjectUtils.disposeObject(this._vbox);
         this._vbox = null;
         ObjectUtils.disposeObject(this._num1);
         this._num1 = null;
         ObjectUtils.disposeObject(this._num2);
         this._num2 = null;
         ObjectUtils.disposeObject(this._num3);
         this._num3 = null;
      }
   }
}

import com.pickgliss.ui.ComponentFactory;
import com.pickgliss.ui.controls.container.HBox;
import com.pickgliss.ui.core.Component;
import com.pickgliss.ui.text.FilterFrameText;
import com.pickgliss.utils.ClassUtils;
import com.pickgliss.utils.ObjectUtils;
import conRecharge.ConRechargeManager;
import ddt.data.goods.ItemTemplateInfo;
import ddt.manager.ItemManager;
import ddt.manager.LanguageMgr;
import ddt.manager.SocketManager;
import ddt.manager.TimeManager;
import ddt.utils.PositionUtils;
import flash.display.Bitmap;
import flash.display.DisplayObject;
import flash.display.DisplayObjectContainer;
import flash.display.InteractiveObject;
import flash.display.Sprite;
import flash.events.EventDispatcher;
import flash.events.MouseEvent;
import wonderfulActivity.WonderfulActivityManager;
import wonderfulActivity.data.SendGiftInfo;

class BoxItem extends Component
{
   
   private var _titleBg:Bitmap;
   
   private var _index:int;
   
   private var _finishDayTxt:FilterFrameText;
   
   private var _moneyTxt:FilterFrameText;
   
   private var _hBox:HBox;
   
   private var _cellArr:Array;
   
   private var _boxArr:Array;
   
   public function BoxItem(_arg_1:int)
   {
      super();
      this._index = _arg_1;
      this.initView();
   }
   
   private function initView() : void
   {
      var _local_11:int = 0;
      var _local_8:int = 0;
      var _local_4:int = 0;
      var _local_10:* = null;
      var _local_1:* = null;
      var _local_6:* = null;
      var _local_9:* = null;
      var _local_3:* = null;
      var _local_7:* = null;
      var _local_5:* = null;
      var _local_2:* = null;
      this._titleBg = ComponentFactory.Instance.creatBitmap("asset.conRecharge.leftTitle.bg");
      addChild(this._titleBg);
      this._finishDayTxt = ComponentFactory.Instance.creatComponentByStylename("conRecharge.finishDay.txt");
      addChild(this._finishDayTxt);
      this._finishDayTxt.text = LanguageMgr.GetTranslation("ddt.conRecharge.finishDay",this.rechargeDayNum(ConRechargeManager.instance.longGiftbagArray[this._index][0].giftConditionArr[0].conditionValue));
      this._moneyTxt = ComponentFactory.Instance.creatComponentByStylename("conRecharge.leftItem.money.txt");
      addChild(this._moneyTxt);
      this._moneyTxt.text = LanguageMgr.GetTranslation("ddt.conRecharge.moneyTxt",ConRechargeManager.instance.longGiftbagArray[this._index][0].giftConditionArr[0].conditionValue);
      this._hBox = ComponentFactory.Instance.creatComponentByStylename("conRecharge.leftItem.hbox");
      addChild(this._hBox);
      this._cellArr = [];
      this._boxArr = [];
      _local_11 = 0;
      while(_local_11 < ConRechargeManager.instance.longGiftbagArray[this._index].length)
      {
         _local_10 = ConRechargeManager.instance.longGiftbagArray[this._index][_local_11];
         _local_1 = new Sprite();
         _local_6 = ComponentFactory.Instance.creatBitmap("asset.conRecharge.leftItem.bg");
         _local_1.addChild(_local_6);
         _local_9 = _local_10.giftRewardArr[0].property.split(",");
         _local_3 = ComponentFactory.Instance.creatComponentByStylename("conRecharge.prize.btn");
         _local_3.id = _local_11;
         _local_7 = "";
         _local_8 = 0;
         while(_local_8 < _local_10.giftRewardArr.length)
         {
            _local_5 = ItemManager.Instance.getTemplateById(_local_10.giftRewardArr[_local_8].templateId) as ItemTemplateInfo;
            _local_7 += _local_5.beadName;
            if(_local_10.giftRewardArr[_local_8].validDate > 0)
            {
               _local_7 += "(" + LanguageMgr.GetTranslation("ddt.conRecharge.leftDay",_local_10.giftRewardArr[_local_8].validDate) + ")";
            }
            _local_7 += " x " + _local_10.giftRewardArr[_local_8].count + "\n";
            _local_8++;
         }
         _local_3.tipData = _local_7;
         _local_1.addChild(_local_3);
         this._cellArr.push(_local_3);
         _local_2 = ClassUtils.CreatInstance("asset.conRecharge.box");
         if(WonderfulActivityManager.Instance.getActivityInitDataById(ConRechargeManager.instance.actId).giftInfoDic[_local_10.giftbagId].times != 0)
         {
            _local_2.gotoAndStop(1);
            _local_3.enable = false;
            _local_3.mouseEnabled = true;
         }
         else if(this.judgeRechargeDay(_local_10.giftConditionArr[0].remain1,_local_10.giftConditionArr[0].conditionValue))
         {
            _local_2.gotoAndStop(2);
            _local_3.addEventListener("click",this.clickHandler);
         }
         else
         {
            _local_2.gotoAndStop(3);
            _local_3.enable = false;
            _local_3.mouseEnabled = true;
         }
         PositionUtils.setPos(_local_2,"asset.conRecharge.box.pos");
         this._boxArr.push(_local_2);
         _local_1.addChild(_local_2);
         this._hBox.addChild(_local_1);
         _local_4++;
         _local_11++;
      }
   }
   
   private function rechargeDayNum(_arg_1:int) : int
   {
      var _local_6:int = 0;
      var _local_7:int = 0;
      var _local_10:int = 0;
      var _local_8:int = 0;
      var _local_2:int = 0;
      var _local_9:* = null;
      var _local_5:Array = [];
      _local_6 = 0;
      while(_local_6 < WonderfulActivityManager.Instance.getActivityInitDataById(ConRechargeManager.instance.actId).statusArr.length)
      {
         if(WonderfulActivityManager.Instance.getActivityInitDataById(ConRechargeManager.instance.actId).statusArr[_local_6].statusID != 0)
         {
            _local_5.push(WonderfulActivityManager.Instance.getActivityInitDataById(ConRechargeManager.instance.actId).statusArr[_local_6]);
         }
         _local_6++;
      }
      _local_5.sortOn("statusID",16);
      var _local_4:Date = TimeManager.Instance.Now();
      var _local_3:int = 10000 * _local_4.getFullYear() + 100 * (_local_4.getMonth() + 1) + _local_4.getDate();
      _local_10 = 0;
      while(_local_10 < _local_5.length)
      {
         if(_local_5[_local_10].statusID == _local_3)
         {
            if(_local_10 == 0)
            {
               if(_local_5[_local_10].statusValue == 0)
               {
                  return 0;
               }
               if(_local_5[_local_10].statusValue >= _arg_1)
               {
                  return 1;
               }
               return 0;
            }
            if(_local_5[_local_10].statusValue >= _arg_1)
            {
               _local_7 = _local_10;
            }
            else
            {
               _local_7 = _local_10 - 1;
            }
            break;
         }
         _local_10++;
      }
      _local_8 = _local_7;
      while(_local_8 >= 0)
      {
         _local_9 = _local_5[_local_8];
         if(_local_9.statusValue < _arg_1)
         {
            return _local_2;
         }
         _local_2++;
         _local_8--;
      }
      return _local_2;
   }
   
   private function judgeRechargeDay(_arg_1:int, _arg_2:int) : Boolean
   {
      var _local_7:int = 0;
      var _local_5:int = 0;
      var _local_6:* = null;
      var _local_4:Array = [];
      var _local_3:Array = WonderfulActivityManager.Instance.getActivityInitDataById(ConRechargeManager.instance.actId).statusArr;
      _local_3.sortOn("statusID",16);
      _local_7 = 0;
      while(_local_7 < _local_3.length)
      {
         _local_6 = _local_3[_local_7];
         _local_6.my = _local_7;
         if(_local_6.statusID != 0)
         {
            if(_local_6.statusValue >= _arg_2)
            {
               _local_4.push(_local_6);
            }
         }
         _local_7++;
      }
      if(_local_4.length < _arg_1)
      {
         return false;
      }
      _local_5 = _arg_1 - 1;
      while(_local_5 < _local_4.length)
      {
         if(_local_4[_local_5].my - _local_4[_local_5 - _arg_1 + 1].my == _arg_1 - 1)
         {
            return true;
         }
         _local_5++;
      }
      return false;
   }
   
   private function clickHandler(_arg_1:MouseEvent) : void
   {
      var _local_3:SendGiftInfo = new SendGiftInfo();
      _local_3.activityId = ConRechargeManager.instance.actId;
      _local_3.giftIdArr = [ConRechargeManager.instance.longGiftbagArray[this._index][_arg_1.currentTarget.id].giftbagId];
      var _local_2:Vector.<SendGiftInfo> = new Vector.<SendGiftInfo>();
      _local_2.push(_local_3);
      SocketManager.Instance.out.sendWonderfulActivityGetReward(_local_2);
      _arg_1.currentTarget.enable = false;
      _arg_1.currentTarget.mouseEnabled = true;
      this._boxArr[_arg_1.currentTarget.id].gotoAndStop(0);
   }
   
   override public function dispose() : void
   {
      var _local_1:int = 0;
      super.dispose();
      _local_1 = 0;
      while(_local_1 < this._cellArr.length)
      {
         this._cellArr[_local_1].removeEventListener("click",this.clickHandler);
         _local_1++;
      }
      ObjectUtils.disposeObject(this._titleBg);
      this._titleBg = null;
      ObjectUtils.disposeObject(this._finishDayTxt);
      this._finishDayTxt = null;
      ObjectUtils.disposeObject(this._moneyTxt);
      this._moneyTxt = null;
      ObjectUtils.disposeObject(this._hBox);
      this._hBox = null;
   }
}
