package bagAndInfo.bag
{
   import bagAndInfo.BagAndInfoManager;
   import bagAndInfo.cell.DragEffect;
   import com.pickgliss.ui.controls.BaseButton;
   import ddt.events.CellEvent;
   import ddt.interfaces.IPageChange;
   
   public class BagPageButton extends BaseButton implements IPageChange
   {
      
      public function BagPageButton()
      {
         super();
      }
      
      public function drawStop(_arg_1:DragEffect) : void
      {
         BagAndInfoManager.Instance.dispatchEvent(new CellEvent("bagpage",_arg_1));
      }
   }
}

