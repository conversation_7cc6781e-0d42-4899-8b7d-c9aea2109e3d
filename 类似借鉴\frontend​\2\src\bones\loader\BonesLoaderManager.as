package bones.loader
{
   import bones.BoneMovieFactory;
   import bones.model.BoneVo;
   import com.pickgliss.loader.LoadResourceManager;
   import com.pickgliss.loader.LoaderEvent;
   import dragonBones.objects.DataParser;
   import dragonBones.textures.NativeTextureAtlas;
   import dragonBones.textures.StarlingTextureAtlas;
   import flash.events.Event;
   import flash.events.EventDispatcher;
   import flash.utils.Dictionary;
   import road7th.DDTAssetManager;
   import road7th.data.DictionaryData;
   import starling.textures.Texture;
   
   public class BonesLoaderManager extends EventDispatcher
   {
      
      public static var RESOURCE_SITE:String;
      
      public static var SITE_MAIN:String;
      
      private static var _instance:BonesLoaderManager;
      
      private var _boneList:Array;
      
      private var _loading:DictionaryData;
      
      private var _texture2BoneVoMap:Dictionary;
      
      public function BonesLoaderManager(_arg_1:BonesLoaderMnagerEnforcer)
      {
         super();
         this._loading = new DictionaryData();
         this._boneList = [];
         this._texture2BoneVoMap = new Dictionary();
      }
      
      public static function get instance() : BonesLoaderManager
      {
         if(_instance == null)
         {
            _instance = new BonesLoaderManager(new BonesLoaderMnagerEnforcer());
         }
         return _instance;
      }
      
      public function startLoader(_arg_1:String, _arg_2:int = 0, _arg_3:String = "default") : void
      {
         var _local_4:* = null;
         var _local_5:* = null;
         var _local_6:BoneVo = BoneMovieFactory.instance.model.getBonesStyle(_arg_1);
         if(_local_6 == null)
         {
            throw new Error("未找到\'" + _arg_1 + "\'的配置，请检查!");
         }
         _local_6.useType = _arg_2;
         if(BoneMovieFactory.instance.hasBoneMovie(_local_6.styleName))
         {
            dispatchEvent(new BonesLoaderEvent("complete",_local_6));
         }
         else
         {
            _local_5 = new BonesResourceLoader(_local_6);
            _local_5.module = _arg_3;
            _local_5.addEventListener("complete",this.__onLoadBoneComplete);
            this._boneList.push(_local_5);
            _local_5.load();
         }
      }
      
      public function startLoaderByAtlas(_arg_1:String, _arg_2:String = "default") : void
      {
         var _local_3:* = null;
         var _local_4:BoneVo = BoneMovieFactory.instance.model.getBoneVoListByAtlasName(_arg_1)[0];
         if(!BoneMovieFactory.instance.checkTextureAtlas(_arg_1,_local_4.useType))
         {
            _local_3 = new BonesResourceLoader(_local_4);
            _local_3.module = _arg_2;
            _local_3.addEventListener("complete",this.__onLoadBoneComplete);
            this._boneList.push(_local_3);
            _local_3.load();
         }
      }
      
      private function __onLoadBoneComplete(_arg_1:Event) : void
      {
         var _local_2:BonesResourceLoader = _arg_1.currentTarget as BonesResourceLoader;
         if(BoneMovieFactory.instance.hasBoneMovie(_local_2.vo.styleName))
         {
            dispatchEvent(new BonesLoaderEvent("complete",_local_2.vo));
            _local_2.removeEventListener("complete",this.__onLoadBoneComplete);
            _local_2.dispose();
         }
         else
         {
            this.analysisLoader(_local_2);
            this.checkBoneListComplete(_local_2.vo);
         }
      }
      
      private function analysisLoader(_arg_1:BonesResourceLoader) : void
      {
         var i:int = 0;
         var texture:Texture = null;
         var loader:* = _arg_1;
         if(loader.vo.useType == 0 || loader.vo.useType == 2)
         {
            texture = Texture.fromBitmap(loader.image,false);
            this._texture2BoneVoMap[texture] = loader.vo;
            texture.root.onRestore = function():void
            {
               var onLoadBoneComplete:* = undefined;
               var restoreBonesResourceLoader:RestoreBonesResourceLoader = null;
               onLoadBoneComplete = function(_arg_1:Event):void
               {
                  restoreBonesResourceLoader.removeEventListener("complete",onLoadBoneComplete);
                  texture.root.uploadBitmap(restoreBonesResourceLoader.image);
                  restoreBonesResourceLoader.dispose();
               };
               var reStoreVo:BoneVo = _texture2BoneVoMap[texture];
               restoreBonesResourceLoader = new RestoreBonesResourceLoader(reStoreVo);
               restoreBonesResourceLoader.addEventListener("complete",onLoadBoneComplete);
               restoreBonesResourceLoader.load();
            };
         }
         while(i < loader.skeletonList.length)
         {
            if(DDTAssetManager.instance.getSkeletonData(loader.skeletonList[i].name) == null)
            {
               DDTAssetManager.instance.addSkeletonData(DataParser.parseData(JSON.parse(loader.skeletonList[i].data)),loader.skeletonList[i].name);
            }
            i++;
         }
         if(loader.vo.useType == 0)
         {
            DDTAssetManager.instance.addTextureAtlas(loader.vo.atlasName,new StarlingTextureAtlas(texture,loader.atlas),loader.module);
         }
         else if(loader.vo.useType == 1)
         {
            DDTAssetManager.instance.addBitmapDataAtlas(loader.vo.atlasName,new NativeTextureAtlas(loader.image.bitmapData.clone(),loader.atlas),loader.module);
         }
         else
         {
            DDTAssetManager.instance.addBitmapDataAtlas(loader.vo.atlasName,new NativeTextureAtlas(loader.image.bitmapData.clone(),loader.atlas),loader.module);
            DDTAssetManager.instance.addTextureAtlas(loader.vo.atlasName,new StarlingTextureAtlas(texture,loader.atlas),loader.module);
         }
      }
      
      private function checkBoneListComplete(_arg_1:BoneVo) : void
      {
         var _local_6:int = 0;
         var _local_5:int = 0;
         var _local_2:int = 0;
         var _local_3:* = null;
         var _local_4:Array = [];
         _local_6 = 0;
         while(_local_6 < this._boneList.length)
         {
            _local_3 = this._boneList[_local_6] as BonesResourceLoader;
            if(_arg_1.atlasName == _local_3.vo.atlasName)
            {
               _local_4.push(_local_3);
               _local_3.loaderComplete();
            }
            _local_6++;
         }
         _local_5 = 0;
         while(_local_5 < _local_4.length)
         {
            _local_2 = int(this._boneList.indexOf(_local_4[_local_5]));
            if(_local_2 != -1)
            {
               this._boneList.splice(_local_2,1);
            }
            _local_5++;
         }
         _local_4.splice(0,_local_4.length);
      }
      
      public function saveBoneLoaderData(_arg_1:BoneVo) : void
      {
         this._loading.add(_arg_1.atlasName,true);
      }
      
      public function getBoneLoaderComplete(_arg_1:BoneVo) : Boolean
      {
         return this._loading.hasKey(_arg_1.atlasName);
      }
      
      public function clearBoneLoaderAtlas(_arg_1:String) : void
      {
         this._loading.remove(_arg_1);
      }
      
      public function loadBonesStyle(_arg_1:String, _arg_2:String) : void
      {
         var _local_3:* = null;
         if(BoneMovieFactory.instance.model.hasLoadingBones(_arg_1) == false)
         {
            _local_3 = LoadResourceManager.Instance.createLoader(_arg_2,2);
            _local_3.addEventListener("complete",this.__onLoaderBonesComplete);
            LoadResourceManager.Instance.startLoad(_local_3);
         }
      }
      
      private function __onLoaderBonesComplete(_arg_1:LoaderEvent) : void
      {
         _arg_1.loader.removeEventListener("complete",this.__onLoaderBonesComplete);
         var _local_3:XML = new XML(_arg_1.loader.content);
         var _local_2:String = _local_3.@name;
         BoneMovieFactory.instance.model.parasBonesStyle(_local_3..bone,_local_2);
         dispatchEvent(new BonesLoaderEvent("bonesstylecompelete",_local_2));
      }
      
      public function clear() : void
      {
         this._loading.clear();
         this._boneList.splice(0,this._boneList.length);
         this._boneList = [];
         this._texture2BoneVoMap = new Dictionary();
      }
   }
}

class BonesLoaderMnagerEnforcer
{
   
   public function BonesLoaderMnagerEnforcer()
   {
      super();
   }
}
