package consortion.view.selfConsortia
{
   import bagAndInfo.bag.BagView;
   import bagAndInfo.bag.CellMenu;
   import bagAndInfo.cell.BagCell;
   import baglocked.BaglockedManager;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.controls.SimpleBitmapButton;
   import com.pickgliss.ui.text.FilterFrameText;
   import com.pickgliss.utils.ObjectUtils;
   import consortion.ConsortionModelManager;
   import ddt.data.BagInfo;
   import ddt.data.EquipType;
   import ddt.data.goods.InventoryItemInfo;
   import ddt.data.goods.ItemTemplateInfo;
   import ddt.data.player.SelfInfo;
   import ddt.events.CellEvent;
   import ddt.events.PlayerPropertyEvent;
   import ddt.manager.ItemManager;
   import ddt.manager.LanguageMgr;
   import ddt.manager.MessageTipManager;
   import ddt.manager.PlayerManager;
   import ddt.manager.SocketManager;
   import ddt.manager.SoundManager;
   import ddt.utils.PositionUtils;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import magicHouse.MagicHouseManager;
   import magicHouse.MagicHouseModel;
   import playerDress.PlayerDressManager;
   import playerDress.components.DressUtils;
   
   public class ConsortionBankBagView extends BagView
   {
      
      private static var LIST_WIDTH:int = 330;
      
      private static var LIST_HEIGHT:int = 320;
      
      private const MAX_HEIGHT:int = 455;
      
      private var _bank:ConsortionBankListView;
      
      private var _titleText2:FilterFrameText;
      
      private var _changeToConsortion:SimpleBitmapButton;
      
      public function ConsortionBankBagView()
      {
         super();
      }
      
      override protected function init() : void
      {
         super.init();
         this._changeToConsortion = ComponentFactory.Instance.creatComponentByStylename("consortion.bankView.stateChangeBtn");
         addChild(this._changeToConsortion);
         this.setInit();
         this.setData(PlayerManager.Instance.Self);
      }
      
      private function setInit() : void
      {
         _tabBtn3.buttonMode = false;
         _tabBtn3.mouseEnabled = false;
         _tabBtn3.mouseChildren = false;
         bagLockBtn.visible = false;
      }
      
      override public function setBagType(_arg_1:int) : void
      {
         super.setBagType(_arg_1);
      }
      
      override protected function set_breakBtn_enable() : void
      {
         if(Boolean(_keySortBtn) && _isSkillCanUse())
         {
            _keySortBtn.enable = true;
         }
      }
      
      override protected function initEvent() : void
      {
         super.initEvent();
         this._bank.addEventListener("itemclick",this.__bankCellClick);
         this._bank.addEventListener("doubleclick",this.__bankCellDoubleClick);
         addEventListener("addedToStage",this.__addToStageHandler);
         _proplist.addEventListener("doubleclick",this.__cellDoubleClick);
         _equiplist.addEventListener("change",this.__listChange);
         _proplist.addEventListener("change",this.__listChange);
         PlayerManager.Instance.Self.addEventListener("propertychange",this.__upConsortiaStroeLevel);
         this._changeToConsortion.addEventListener("click",this.__jumpToMagicHouse);
      }
      
      override protected function removeEvents() : void
      {
         super.removeEvents();
         this._bank.removeEventListener("itemclick",this.__bankCellClick);
         this._bank.removeEventListener("doubleclick",this.__bankCellDoubleClick);
         removeEventListener("addedToStage",this.__addToStageHandler);
         _proplist.removeEventListener("doubleclick",this.__cellDoubleClick);
         _equiplist.removeEventListener("change",this.__listChange);
         _proplist.removeEventListener("change",this.__listChange);
         PlayerManager.Instance.Self.removeEventListener("propertychange",this.__upConsortiaStroeLevel);
         this._changeToConsortion.removeEventListener("click",this.__jumpToMagicHouse);
      }
      
      override protected function initBackGround() : void
      {
         super.initBackGround();
         this._bank = new ConsortionBankListView(11,PlayerManager.Instance.Self.consortiaInfo.StoreLevel);
         PositionUtils.setPos(this._bank,"consortion.bank.Pos");
         this._titleText2 = ComponentFactory.Instance.creatComponentByStylename("consortion.bankBagView.titleText2");
         this._titleText2.text = LanguageMgr.GetTranslation("consortion.bankBagView.titleText2");
         addChild(this._bank);
         addChild(this._titleText2);
      }
      
      override protected function __listChange(_arg_1:Event) : void
      {
         if(Boolean(_dressbagView) && _dressbagView.visible == true)
         {
            return;
         }
         if(_arg_1.currentTarget == _equiplist)
         {
            this.setBagType(0);
         }
         else
         {
            this.setBagType(1);
         }
      }
      
      private function get __isCanClick() : Boolean
      {
         if(PlayerManager.Instance.Self.ConsortiaID != 0)
         {
            return true;
         }
         MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("magichouse.treasureView.noConsortion"));
         return false;
      }
      
      override protected function __cellDoubleClick(_arg_1:CellEvent) : void
      {
         if(!this.__isCanClick)
         {
            return;
         }
         SoundManager.instance.play("008");
         var _local_3:int = this._bank.checkBankCell();
         if(_local_3 > 0)
         {
            if(_local_3 == 1)
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.consortia.club.ConsortiaClubView.cellDoubleClick"));
            }
            else if(_local_3 == 2 || _local_3 == 3)
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.consortia.club.ConsortiaClubView.cellDoubleClick.msg"));
            }
            return;
         }
         if(PlayerManager.Instance.Self.bagLocked)
         {
            BaglockedManager.Instance.show();
            return;
         }
         _arg_1.stopImmediatePropagation();
         var _local_4:BagCell = _arg_1.data as BagCell;
         var _local_6:InventoryItemInfo = _local_4.info as InventoryItemInfo;
         if(this.checkDressSaved(_local_6))
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("playerDress.cannotStore"));
            return;
         }
         var _local_5:ItemTemplateInfo = ItemManager.Instance.getTemplateById(_local_6.TemplateID);
         var _local_2:int = PlayerManager.Instance.Self.Sex ? 1 : 2;
         if(!_local_4.locked)
         {
            SocketManager.Instance.out.sendMoveGoods(_local_6.BagType,_local_6.Place,11,-1);
         }
      }
      
      private function __jumpToMagicHouse(_arg_1:MouseEvent) : void
      {
         SoundManager.instance.play("008");
         if(PlayerManager.Instance.Self.Grade >= 17)
         {
            ConsortionModelManager.Instance.hideBankFrame();
            MagicHouseModel.instance.viewIndex = 1;
            MagicHouseManager.instance.show();
         }
         else
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.functionLimitTip",17));
         }
      }
      
      private function __bankCellClick(_arg_1:CellEvent) : void
      {
         var _local_2:* = null;
         var _local_3:* = null;
         if(!_sellBtn.isActive)
         {
            _arg_1.stopImmediatePropagation();
            _local_2 = _arg_1.data as BagCell;
            if(_local_2)
            {
               _local_3 = _local_2.info as InventoryItemInfo;
            }
            if(_local_3 == null)
            {
               return;
            }
            if(!_local_2.locked)
            {
               SoundManager.instance.play("008");
               _local_2.dragStart();
            }
         }
      }
      
      private function __bankCellDoubleClick(_arg_1:CellEvent) : void
      {
         SoundManager.instance.play("008");
         _arg_1.stopImmediatePropagation();
         if(PlayerManager.Instance.Self.bagLocked)
         {
            BaglockedManager.Instance.show();
            return;
         }
         var _local_2:BagCell = _arg_1.data as BagCell;
         var _local_3:InventoryItemInfo = _local_2.itemInfo;
         SocketManager.Instance.out.sendMoveGoods(_local_3.BagType,_local_3.Place,this.getItemBagType(_local_3),-1,_local_3.Count);
      }
      
      private function getItemBagType(_arg_1:InventoryItemInfo) : int
      {
         if(EquipType.isBelongToPropBag(_arg_1))
         {
            return 1;
         }
         return 0;
      }
      
      private function __upConsortiaStroeLevel(_arg_1:PlayerPropertyEvent) : void
      {
         if(Boolean(_arg_1.changedProperties["StoreLevel"]))
         {
            this.__addToStageHandler(null);
         }
      }
      
      private function __addToStageHandler(_arg_1:Event) : void
      {
         this._bank.updateBankBag(PlayerManager.Instance.Self.consortiaInfo.StoreLevel);
      }
      
      public function setData(_arg_1:SelfInfo) : void
      {
         _equiplist.setData(_arg_1.Bag);
         _proplist.setData(_arg_1.PropBag);
         this._bank.setData(_arg_1.ConsortiaBag);
      }
      
      override protected function __cellClick(_arg_1:CellEvent) : void
      {
         var _local_2:* = null;
         var _local_4:* = null;
         var _local_3:* = null;
         if(!_sellBtn.isActive)
         {
            _arg_1.stopImmediatePropagation();
            _local_2 = _arg_1.data as BagCell;
            if(_local_2)
            {
               _local_4 = _local_2.info as InventoryItemInfo;
            }
            if(_local_4 == null)
            {
               return;
            }
            if(!_local_2.locked)
            {
               SoundManager.instance.play("008");
               if(!DressUtils.isDress(_local_4) && (_local_4.getRemainDate() <= 0 && !EquipType.isProp(_local_4) || EquipType.isPackage(_local_4) || _local_4.getRemainDate() <= 0 && _local_4.TemplateID == 10200 || EquipType.canBeUsed(_local_4)))
               {
                  _local_3 = localToGlobal(new Point(_local_2.x,_local_2.y));
                  CellMenu.instance.show(_local_2,_local_3.x + 35,_local_3.y + 77);
               }
               else
               {
                  if(this.checkDressSaved(_local_4))
                  {
                     MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("playerDress.cannotStore"));
                     return;
                  }
                  _local_2.dragStart();
               }
            }
         }
      }
      
      override protected function __cellMove(_arg_1:Event) : void
      {
         var _local_2:BagCell = CellMenu.instance.cell;
         var _local_3:InventoryItemInfo = _local_2.info as InventoryItemInfo;
         if(this.checkDressSaved(_local_3))
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("playerDress.cannotStore"));
            return;
         }
         super.__cellMove(_arg_1);
      }
      
      private function checkDressSaved(_arg_1:InventoryItemInfo) : Boolean
      {
         var _local_9:int = 0;
         var _local_5:int = 0;
         var _local_7:int = 0;
         var _local_3:* = null;
         var _local_4:* = null;
         var _local_8:* = null;
         if(!DressUtils.isDress(_arg_1))
         {
            return false;
         }
         var _local_2:BagInfo = PlayerManager.Instance.Self.Bag;
         _local_9 = 0;
         while(_local_9 <= 8 - 1)
         {
            _local_3 = _local_2.items[DressUtils.getBagItems(_local_9)];
            if(_local_3 && _arg_1.ItemID == _local_3.ItemID)
            {
               return true;
            }
            _local_9++;
         }
         var _local_6:Array = PlayerDressManager.instance.modelArr;
         _local_5 = 0;
         while(_local_5 <= _local_6.length - 1)
         {
            _local_4 = _local_6[_local_5];
            if(_local_4)
            {
               _local_7 = 0;
               while(_local_7 <= _local_4.length - 1)
               {
                  _local_8 = _local_4[_local_7];
                  if(_arg_1.ItemID == _local_8.itemId)
                  {
                     return true;
                  }
                  _local_7++;
               }
            }
            _local_5++;
         }
         return false;
      }
      
      override protected function __sortBagClick(_arg_1:MouseEvent) : void
      {
         SoundManager.instance.play("008");
         var _local_2:BagInfo = PlayerManager.Instance.Self.getBag(11);
         PlayerManager.Instance.Self.PropBag.sortBag(3,_local_2,0,100,_bagArrangeSprite.arrangeAdd);
      }
      
      override public function dispose() : void
      {
         super.dispose();
         if(Boolean(this._bank))
         {
            this._bank.dispose();
         }
         this._bank = null;
         if(Boolean(this._titleText2))
         {
            ObjectUtils.disposeObject(this._titleText2);
         }
         this._titleText2 = null;
         if(Boolean(this.parent))
         {
            this.parent.removeChild(this);
         }
      }
   }
}

