package consortion.analyze
{
   import com.pickgliss.loader.DataAnalyzer;
   import com.pickgliss.utils.ObjectUtils;
   import consortion.data.ConsortiaLevelInfo;
   
   public class ConsortionLevelUpAnalyzer extends DataAnalyzer
   {
      
      public var levelUpData:Vector.<ConsortiaLevelInfo>;
      
      public function ConsortionLevelUpAnalyzer(_arg_1:Function)
      {
         super(_arg_1);
      }
      
      override public function analyze(_arg_1:*) : void
      {
         var _local_5:int = 0;
         var _local_3:* = null;
         var _local_4:* = null;
         var _local_2:XML = new XML(_arg_1);
         this.levelUpData = new Vector.<ConsortiaLevelInfo>();
         if(_local_2.@value == "true")
         {
            _local_3 = XML(_local_2)..Item;
            _local_5 = 0;
            while(_local_5 < _local_3.length())
            {
               _local_4 = new ConsortiaLevelInfo();
               ObjectUtils.copyPorpertiesByXML(_local_4,_local_3[_local_5]);
               this.levelUpData.push(_local_4);
               _local_5++;
            }
            onAnalyzeComplete();
         }
         else
         {
            message = _local_2.@message;
            onAnalyzeError();
            onAnalyzeComplete();
         }
      }
   }
}

