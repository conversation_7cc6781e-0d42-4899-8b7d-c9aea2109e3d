package com.pickgliss.loader
{
   import com.pickgliss.events.LoaderResourceEvent;
   import com.pickgliss.events.UIModuleEvent;
   import flash.events.EventDispatcher;
   import flash.net.URLVariables;
   import flash.system.ApplicationDomain;
   import flash.system.LoaderContext;
   import flash.utils.Dictionary;
   
   [Event(name="init complete",type="com.pickgliss.events.LoaderResourceEvent")]
   [Event(name="complete",type="com.pickgliss.events.LoaderResourceEvent")]
   [Event(name="delete",type="com.pickgliss.events.LoaderResourceEvent")]
   [Event(name="loadError",type="com.pickgliss.events.LoaderResourceEvent")]
   [Event(name="progress",type="com.pickgliss.events.LoaderResourceEvent")]
   public class LoadResourceManager extends EventDispatcher
   {
      
      private static var _instance:LoadResourceManager;
      
      private var _infoSite:String = "";
      
      private var _loadingUrl:String = "";
      
      private var _clientType:int;
      
      private var _loadDic:Dictionary;
      
      private var _loadUrlDic:Dictionary;
      
      private var _deleteList:Vector.<String>;
      
      private var _currentDeletePath:String;
      
      private var _isLoading:Boolean;
      
      private var _progress:Number;
      
      public function LoadResourceManager(_arg_1:Singleton)
      {
         super();
         if(!_arg_1)
         {
            throw Error("单例无法实例化");
         }
      }
      
      public static function get Instance() : LoadResourceManager
      {
         return _instance = _instance || new LoadResourceManager(new Singleton());
      }
      
      public function init(_arg_1:String = "") : void
      {
         this._infoSite = _arg_1;
         var _local_2:LoaderContext = new LoaderContext(false,ApplicationDomain.currentDomain);
         LoaderManager.Instance.setup(_local_2,String(Math.random()));
         this.addMicroClientEvent();
         LoadInterfaceManager.initAppInterface();
      }
      
      public function addMicroClientEvent() : void
      {
         LoadInterfaceManager.eventDispatcher.addEventListener("checkComplete",this.__checkComplete);
         LoadInterfaceManager.eventDispatcher.addEventListener("deleteComplete",this.__deleteComplete);
         LoadInterfaceManager.eventDispatcher.addEventListener("flashGotoAndPlay",this.__flashGotoAndPlay);
      }
      
      public function setLoginType(_arg_1:Number, _arg_2:String = "", _arg_3:String = "-1") : void
      {
         this._clientType = _arg_1;
         this._loadingUrl = _arg_2;
         LoaderSavingManager.Version = int(_arg_3);
      }
      
      public function setup(_arg_1:LoaderContext, _arg_2:String) : void
      {
         this._loadDic = new Dictionary();
         this._loadUrlDic = new Dictionary();
         this._deleteList = new Vector.<String>();
      }
      
      public function createLoader(_arg_1:String, _arg_2:int, _arg_3:URLVariables = null, _arg_4:String = "GET", _arg_5:ApplicationDomain = null, _arg_6:Boolean = true, _arg_7:Boolean = false) : *
      {
         return this.createOriginLoader(_arg_1,this._infoSite,_arg_2,_arg_3,_arg_4,_arg_5,_arg_6,_arg_7);
      }
      
      public function createOriginLoader(_arg_1:String, _arg_2:String, _arg_3:int, _arg_4:URLVariables = null, _arg_5:String = "GET", _arg_6:ApplicationDomain = null, _arg_7:Boolean = false, _arg_8:Boolean = false) : *
      {
         var _local_9:int = 0;
         var _local_10:* = null;
         var _local_11:* = null;
         if(_arg_7 && this._clientType == 1 && [2,5,6,7].indexOf(_arg_3) == -1)
         {
            _arg_1 = this.fixedVariablesURL(_arg_1.toLowerCase(),_arg_3,_arg_4);
            _local_9 = _arg_2.length;
            if(_arg_1.indexOf(_arg_2) == -1)
            {
               LoadInterfaceManager.traceMsg("filePath = " + _arg_1 + "路径有问题");
            }
            _local_11 = _arg_1.substring(_local_9,_arg_1.length);
            _local_10 = LoaderManager.Instance.creatLoaderByType(_local_11,_arg_3,_arg_4,_arg_5,_arg_6);
            this._loadDic[_local_10.id] = _local_10;
            this._loadUrlDic[_local_10.id] = _arg_1;
         }
         else
         {
            _local_10 = LoaderManager.Instance.creatLoader(_arg_1,_arg_3,_arg_4,_arg_5,_arg_6);
         }
         return _local_10;
      }
      
      private function __onLoadComplete(_arg_1:LoaderEvent) : void
      {
         _arg_1.loader.removeEventListener("complete",this.__onLoadComplete);
         _arg_1.loader.removeEventListener("loadError",this.__onLoadError);
      }
      
      public function __onLoadError(_arg_1:LoaderEvent) : void
      {
         _arg_1.loader.removeEventListener("complete",this.__onLoadComplete);
         _arg_1.loader.removeEventListener("loadError",this.__onLoadError);
         var _local_2:LoaderResourceEvent = new LoaderResourceEvent("loadError");
         _local_2.data = _arg_1.loader;
         dispatchEvent(_local_2);
      }
      
      public function creatAndStartLoad(_arg_1:String, _arg_2:int, _arg_3:URLVariables = null) : BaseLoader
      {
         var _local_4:BaseLoader = this.createLoader(_arg_1,_arg_2,_arg_3);
         this.startLoad(_local_4);
         return _local_4;
      }
      
      public function startLoad(_arg_1:BaseLoader, _arg_2:Boolean = false, _arg_3:Boolean = true) : void
      {
         this.startLoadFromLoadingUrl(_arg_1,this._infoSite,_arg_2,_arg_3);
      }
      
      public function startLoadFromLoadingUrl(_arg_1:BaseLoader, _arg_2:String, _arg_3:Boolean = false, _arg_4:Boolean = true) : void
      {
         var _local_5:String = _arg_1.url;
         _local_5 = _local_5.replace(/\?.*/,"");
         if(_arg_4 && this._clientType == 1 && [2,5,6,7].indexOf(_arg_1.type) == -1)
         {
            LoadInterfaceManager.checkResource(_arg_1.id,_arg_2,_local_5,_arg_3);
         }
         else
         {
            this.beginLoad(_arg_1,_arg_3);
         }
      }
      
      private function beginLoad(_arg_1:BaseLoader, _arg_2:Boolean = false) : void
      {
         LoaderManager.Instance.startLoad(_arg_1,_arg_2);
      }
      
      public function addDeleteRequest(_arg_1:String) : void
      {
         if(!this._deleteList)
         {
            this._deleteList = new Vector.<String>();
         }
         this._deleteList.push(_arg_1);
      }
      
      public function startDelete() : void
      {
         if(this._clientType != 1)
         {
            if(Boolean(this._deleteList))
            {
               this._deleteList.length = 0;
            }
         }
         this.deleteNext();
      }
      
      private function deleteNext() : void
      {
         var _local_1:* = null;
         if(Boolean(this._deleteList))
         {
            if(this._deleteList.length > 0)
            {
               this._currentDeletePath = this._deleteList.shift();
               this.deleteResource(this._currentDeletePath);
            }
            else
            {
               _local_1 = new LoaderResourceEvent("delete");
               dispatchEvent(_local_1);
            }
         }
      }
      
      public function deleteResource(_arg_1:String) : void
      {
         LoadInterfaceManager.deleteResource(_arg_1);
      }
      
      protected function __checkComplete(_arg_1:LoadInterfaceEvent) : void
      {
         this.checkComplete(_arg_1.paras[0],_arg_1.paras[1],_arg_1.paras[2],_arg_1.paras[3]);
      }
      
      protected function __deleteComplete(_arg_1:LoadInterfaceEvent) : void
      {
         if(this._currentDeletePath == _arg_1.paras[1])
         {
            this.deleteComlete(_arg_1.paras[0],_arg_1.paras[1]);
         }
      }
      
      protected function __flashGotoAndPlay(_arg_1:LoadInterfaceEvent) : void
      {
         this.flashGotoAndPlay(_arg_1.paras[0],_arg_1.paras[1]);
      }
      
      public function checkComplete(_arg_1:String, _arg_2:String, _arg_3:String, _arg_4:String) : void
      {
         var _local_6:* = null;
         if(!this._loadDic)
         {
            return;
         }
         var _local_5:BaseLoader = this._loadDic[int(_arg_1)];
         if(Boolean(_local_5))
         {
            LoaderManager.Instance.setFlashLoadWeb();
            if(_arg_2 == "true")
            {
               this.beginLoad(_local_5);
            }
            else
            {
               _local_5.url = this._loadUrlDic[_local_5.id];
               this.beginLoad(_local_5);
            }
            if(Boolean(this._loadDic))
            {
               delete this._loadDic[_local_5.id];
               delete this._loadUrlDic[_local_5.id];
            }
            if(_local_5.url.indexOf("2.png") != -1)
            {
               this._isLoading = false;
               this._progress = 1;
            }
         }
         else
         {
            LoadInterfaceManager.traceMsg("loader为空：" + _arg_1 + "* " + _arg_4);
         }
      }
      
      public function deleteComlete(_arg_1:String, _arg_2:String) : void
      {
         this.deleteNext();
      }
      
      public function flashGotoAndPlay(_arg_1:int, _arg_2:Number) : void
      {
         if(!this._loadDic)
         {
            return;
         }
         var _local_3:BaseLoader = this._loadDic[int(_arg_1)];
         if(Boolean(_local_3))
         {
            if(_local_3.url.indexOf("2.png") != -1)
            {
               this._isLoading = true;
               this._progress = _arg_2 * 0.01;
            }
            else
            {
               UIModuleLoader.Instance.dispatchEvent(new UIModuleEvent("uiMoudleProgress",_local_3));
            }
         }
      }
      
      public function fixedVariablesURL(_arg_1:String, _arg_2:int, _arg_3:URLVariables) : String
      {
         var _local_6:int = 0;
         var _local_4:String = null;
         var _local_5:* = null;
         if(_arg_2 != 6 && _arg_2 != 7)
         {
            _local_5 = "";
            if(_arg_3 == null)
            {
               _arg_3 = new URLVariables();
            }
            if(_arg_2 == 3 || _arg_2 == 1 || _arg_2 == 0 || _arg_2 == 8 || _arg_2 == 10)
            {
               if(!_arg_3["lv"])
               {
                  _arg_3["lv"] = LoaderSavingManager.Version;
               }
            }
            else if(_arg_2 == 5 || _arg_2 == 2)
            {
               if(!_arg_3["rnd"])
               {
                  _arg_3["rnd"] = TextLoader.TextLoaderKey;
               }
            }
            else if(_arg_2 == 4 || _arg_2 == 9)
            {
               if(!_arg_3["lv"])
               {
                  _arg_3["lv"] = LoaderSavingManager.Version;
               }
               if(!_arg_3["rnd"])
               {
                  _arg_3["rnd"] = TextLoader.TextLoaderKey;
               }
            }
            _local_6 = 0;
            for(_local_4 in _arg_3)
            {
               if(_local_6 >= 1)
               {
                  _local_5 += "&" + _local_4 + "=" + _arg_3[_local_4];
               }
               else
               {
                  _local_5 += _local_4 + "=" + _arg_3[_local_4];
               }
               _local_6++;
            }
            return _arg_1 + "?" + _local_5;
         }
         return _arg_1;
      }
      
      public function get isMicroClient() : Boolean
      {
         return this._clientType == 1;
      }
      
      public function get clientType() : int
      {
         return this._clientType;
      }
      
      public function get infoSite() : String
      {
         return this._infoSite;
      }
      
      public function set infoSite(_arg_1:String) : void
      {
         this._infoSite = _arg_1;
      }
      
      public function get loadingUrl() : String
      {
         return this._loadingUrl;
      }
      
      public function get progress() : Number
      {
         return this._progress;
      }
      
      public function get isLoading() : Boolean
      {
         return this._isLoading;
      }
   }
}

class Singleton
{
   
   public function Singleton()
   {
      super();
   }
}
