package cityBattle.view
{
   import cityBattle.CityBattleManager;
   import cityBattle.event.CityBattleEvent;
   import com.pickgliss.toplevel.StageReferance;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.LayerManager;
   import com.pickgliss.ui.controls.BaseButton;
   import com.pickgliss.ui.controls.Frame;
   import com.pickgliss.ui.text.FilterFrameText;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.manager.LanguageMgr;
   import ddt.manager.SocketManager;
   import ddt.manager.SoundManager;
   import flash.display.Bitmap;
   import flash.display.DisplayObject;
   import flash.events.MouseEvent;
   
   public class JoinCityBattleView extends Frame
   {
      
      private var _enterBg:Bitmap;
      
      private var _joinBlueBtn:BaseButton;
      
      private var _joinRedBtn:BaseButton;
      
      private var _randomBtn:BaseButton;
      
      private var _joinInfoTxt:FilterFrameText;
      
      private var _closeBnt:BaseButton;
      
      public function JoinCityBattleView()
      {
         super();
         this.initView();
         this.initEvent();
      }
      
      private function initView() : void
      {
         this._enterBg = ComponentFactory.Instance.creatBitmap("asset.cityBattle.bg");
         addToContent(this._enterBg);
         this._closeBnt = ComponentFactory.Instance.creatComponentByStylename("joinCity.closeBtn");
         addToContent(this._closeBnt);
         this._joinBlueBtn = ComponentFactory.Instance.creatComponentByStylename("cityBattle.joinBlueBtn");
         this._joinRedBtn = ComponentFactory.Instance.creatComponentByStylename("cityBattle.joinRedBtn");
         addToContent(this._joinBlueBtn);
         addToContent(this._joinRedBtn);
         this._randomBtn = ComponentFactory.Instance.creatComponentByStylename("cityBattle.randomBtn");
         addToContent(this._randomBtn);
         this._joinInfoTxt = ComponentFactory.Instance.creatComponentByStylename("joinView.info.txt");
         addToContent(this._joinInfoTxt);
         this._joinInfoTxt.text = LanguageMgr.GetTranslation("ddt.cityBattle.joinView.info");
      }
      
      private function initEvent() : void
      {
         this._joinBlueBtn.addEventListener("click",this.__joinHandler);
         this._joinRedBtn.addEventListener("click",this.__joinHandler);
         this._randomBtn.addEventListener("click",this.__joinHandler);
         CityBattleManager.instance.addEventListener("joinBattle",this._joinBattleHandler);
         this._closeBnt.addEventListener("click",this._closeClick);
      }
      
      private function __joinHandler(_arg_1:MouseEvent) : void
      {
         switch(_arg_1.currentTarget)
         {
            case this._joinBlueBtn:
               SocketManager.Instance.out.cityBattleJoin(1);
               return;
            case this._joinRedBtn:
               SocketManager.Instance.out.cityBattleJoin(2);
               return;
            case this._randomBtn:
               SocketManager.Instance.out.cityBattleJoin(0);
               return;
            default:
               return;
         }
      }
      
      private function _joinBattleHandler(_arg_1:CityBattleEvent) : void
      {
         this.dispose();
         CityBattleManager.instance._mainFrame = ComponentFactory.Instance.creatComponentByStylename("cityBattle.mainFrame");
         LayerManager.Instance.addToLayer(CityBattleManager.instance._mainFrame,3,true,1);
      }
      
      private function _closeClick(_arg_1:MouseEvent) : void
      {
         SoundManager.instance.play("008");
         this.dispose();
      }
      
      private function removeEvent() : void
      {
         this._joinBlueBtn.removeEventListener("click",this.__joinHandler);
         this._joinRedBtn.removeEventListener("click",this.__joinHandler);
         this._randomBtn.removeEventListener("click",this.__joinHandler);
         CityBattleManager.instance.removeEventListener("joinBattle",this._joinBattleHandler);
         this._closeBnt.removeEventListener("click",this._closeClick);
      }
      
      override public function dispose() : void
      {
         this.removeEvent();
         var _local_1:DisplayObject = StageReferance.stage.focus as DisplayObject;
         if(Boolean(_local_1) && contains(_local_1))
         {
            StageReferance.stage.focus = null;
         }
         ObjectUtils.disposeObject(this._enterBg);
         this._enterBg = null;
         ObjectUtils.disposeObject(this._closeBnt);
         this._closeBnt = null;
         ObjectUtils.disposeObject(this._joinBlueBtn);
         this._joinBlueBtn = null;
         ObjectUtils.disposeObject(this._joinRedBtn);
         this._joinRedBtn = null;
         ObjectUtils.disposeObject(this._randomBtn);
         this._randomBtn = null;
         ObjectUtils.disposeObject(this._joinInfoTxt);
         this._joinInfoTxt = null;
         super.dispose();
      }
   }
}

