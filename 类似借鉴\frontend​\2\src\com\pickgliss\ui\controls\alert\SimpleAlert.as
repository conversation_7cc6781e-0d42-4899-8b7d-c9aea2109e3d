package com.pickgliss.ui.controls.alert
{
   import com.pickgliss.geom.InnerRectangle;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.controls.SelectedCheckButton;
   import com.pickgliss.ui.vo.AlertInfo;
   import com.pickgliss.utils.ClassUtils;
   import com.pickgliss.utils.DisplayUtils;
   import com.pickgliss.utils.ObjectUtils;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.geom.Rectangle;
   import flash.text.TextField;
   
   public class SimpleAlert extends BaseAlerFrame
   {
      
      public static const P_frameInnerRect:String = "frameInnerRect";
      
      public static const P_frameMiniH:String = "frameMiniH";
      
      public static const P_frameMiniW:String = "frameMiniW";
      
      public static const P_textField:String = "textFieldStyle";
      
      protected var _frameMiniH:int = -2147483648;
      
      protected var _frameMiniW:int = -2147483648;
      
      protected var _textField:TextField;
      
      protected var _textFieldStyle:String;
      
      private var _frameInnerRect:InnerRectangle;
      
      private var _frameInnerRectString:String;
      
      protected var _selectedBandBtn:SelectedCheckButton;
      
      protected var _selectedBtn:SelectedCheckButton;
      
      protected var _back:MovieClip;
      
      protected var _seleContent:Sprite;
      
      public function SimpleAlert()
      {
         super();
      }
      
      override public function dispose() : void
      {
         if(Boolean(this._textField))
         {
            ObjectUtils.disposeObject(this._textField);
         }
         this._textField = null;
         if(Boolean(this._back))
         {
            ObjectUtils.disposeObject(this._back);
         }
         this._back = null;
         if(Boolean(this._selectedBandBtn))
         {
            this._selectedBandBtn.removeEventListener("click",this.selectedBandHander);
            ObjectUtils.disposeObject(this._selectedBandBtn);
         }
         if(Boolean(this._selectedBtn))
         {
            this._selectedBtn.removeEventListener("click",this.selectedHander);
            ObjectUtils.disposeObject(this._selectedBtn);
         }
         ObjectUtils.disposeAllChildren(this._seleContent);
         ObjectUtils.disposeObject(this._seleContent);
         this._seleContent = null;
         this._selectedBandBtn = null;
         this._selectedBtn = null;
         this._frameInnerRect = null;
         while(Boolean(numChildren))
         {
            ObjectUtils.disposeObject(getChildAt(0));
         }
         super.dispose();
      }
      
      override public function set info(_arg_1:AlertInfo) : void
      {
         super.info = _arg_1;
         onPropertiesChanged("info");
         this._seleContent = new Sprite();
         if(info.type == 0)
         {
            return;
         }
         addToContent(this._seleContent);
         this._back = ComponentFactory.Instance.creat("asset.core.stranDown2");
         this._back.x = 5;
         this._back.y = 46;
         this._back.width = this.width;
         this._seleContent.addChild(this._back);
         _isBand = true;
         this._selectedBandBtn = ComponentFactory.Instance.creatComponentByStylename("simpleAlertFrame.moneySelectBtn");
         this._selectedBandBtn.text = "綁定點券";
         this._selectedBandBtn.x = 155;
         this._selectedBandBtn.y = 49;
         this._selectedBandBtn.selected = true;
         this._selectedBandBtn.enable = false;
         this._seleContent.addChild(this._selectedBandBtn);
         this._selectedBandBtn.addEventListener("click",this.selectedBandHander);
         this._selectedBtn = ComponentFactory.Instance.creatComponentByStylename("simpleAlertFrame.moneySelectBtn");
         this._selectedBtn.text = "點券";
         this._selectedBtn.x = 63;
         this._selectedBtn.y = 49;
         this._seleContent.addChild(this._selectedBtn);
         this._selectedBtn.addEventListener("click",this.selectedHander);
         this._selectedBtn.x = this._back.width / 2 - 62 - this._back.width / 15;
         this._selectedBandBtn.x = this._back.width / 2 + this._back.width / 15;
         this._seleContent.x = this.width / 2 - this._seleContent.width / 2 - this._seleContent.parent.x;
         if(_info.selectBtnY != 0)
         {
            this._seleContent.y = _info.selectBtnY;
         }
         else
         {
            this._seleContent.y = this._textField.height + this._textField.y - 95;
         }
      }
      
      override protected function creatTheLog() : void
      {
         if(Boolean(_selectedCheckBtn))
         {
            _selectedCheckBtn.visible = _isShowTheLog;
         }
         else
         {
            this.layoutFrameRect();
            _changedPropeties["width"] = true;
            _changedPropeties["height"] = true;
            super.onProppertiesUpdate();
            _selectedCheckBtn = ComponentFactory.Instance.creatComponentByStylename("ddt.simpleAlert.selectedCheckButton");
            _selectedCheckBtn.text = info.logText;
            _selectedCheckBtn.x = (this.width - _selectedCheckBtn.width) / 2 - 40;
            _selectedCheckBtn.y = this.height - 77;
            addChild(_selectedCheckBtn);
         }
      }
      
      protected function selectedBandHander(_arg_1:MouseEvent) : void
      {
         if(this._selectedBandBtn.selected)
         {
            _isBand = true;
            this._selectedBandBtn.enable = false;
            this._selectedBtn.enable = true;
            this._selectedBtn.selected = false;
         }
         else
         {
            _isBand = false;
         }
      }
      
      protected function selectedHander(_arg_1:MouseEvent) : void
      {
         if(this._selectedBtn.selected)
         {
            _isBand = false;
            this._selectedBtn.enable = false;
            this._selectedBandBtn.enable = true;
            this._selectedBandBtn.selected = false;
         }
         else
         {
            _isBand = true;
         }
      }
      
      public function set frameInnerRectString(_arg_1:String) : void
      {
         if(this._frameInnerRectString == _arg_1)
         {
            return;
         }
         this._frameInnerRectString = _arg_1;
         this._frameInnerRect = ClassUtils.CreatInstance("com.pickgliss.geom.InnerRectangle",ComponentFactory.parasArgs(this._frameInnerRectString));
         onPropertiesChanged("frameInnerRect");
      }
      
      public function set frameMiniH(_arg_1:int) : void
      {
         if(this._frameMiniH == _arg_1)
         {
            return;
         }
         this._frameMiniH = _arg_1;
         onPropertiesChanged("frameMiniH");
      }
      
      public function set frameMiniW(_arg_1:int) : void
      {
         if(this._frameMiniW == _arg_1)
         {
            return;
         }
         this._frameMiniW = _arg_1;
         onPropertiesChanged("frameMiniW");
      }
      
      public function set textStyle(_arg_1:String) : void
      {
         if(this._textFieldStyle == _arg_1)
         {
            return;
         }
         if(Boolean(this._textField))
         {
            ObjectUtils.disposeObject(this._textField);
         }
         this._textFieldStyle = _arg_1;
         this._textField = ComponentFactory.Instance.creat(this._textFieldStyle);
         onPropertiesChanged("textFieldStyle");
      }
      
      override protected function addChildren() : void
      {
         super.addChildren();
         if(Boolean(this._textField))
         {
            addChild(this._textField);
         }
      }
      
      protected function layoutFrameRect() : void
      {
         var _local_2:int = Boolean(this._seleContent) ? int(this._textField.height + this._seleContent.height) : int(this._textField.height);
         var _local_1:Rectangle = this._frameInnerRect.getInnerRect(this._textField.width,_local_2);
         if(_local_1.width > this._frameMiniW)
         {
            this._textField.x = this._frameInnerRect.para1;
            _width = _local_1.width;
         }
         else
         {
            this._textField.x = this._frameInnerRect.para1 + (this._frameMiniW - _local_1.width) / 2;
            _width = this._frameMiniW;
         }
         if(_local_1.height > this._frameMiniH)
         {
            this._textField.y = this._frameInnerRect.para3;
            _height = _local_1.height;
         }
         else
         {
            this._textField.y = this._frameInnerRect.para3 + (this._frameMiniH - _local_1.height) / 2;
            _height = this._frameMiniH;
         }
      }
      
      override protected function onProppertiesUpdate() : void
      {
         if(Boolean(_changedPropeties["info"]))
         {
            this.updateMsg();
            if(Boolean(this._frameInnerRect))
            {
               this.layoutFrameRect();
               _changedPropeties["width"] = true;
               _changedPropeties["height"] = true;
            }
         }
         super.onProppertiesUpdate();
      }
      
      protected function updateMsg() : void
      {
         this._textField.autoSize = "left";
         if(_info.mutiline)
         {
            this._textField.multiline = true;
            if(!info.enableHtml)
            {
               this._textField.wordWrap = true;
            }
            if(_info.textShowWidth > 0)
            {
               this._textField.width = _info.textShowWidth;
            }
            else
            {
               this._textField.width = DisplayUtils.getTextFieldMaxLineWidth(String(_info.data),this._textField.defaultTextFormat,info.enableHtml);
            }
         }
         if(_info.enableHtml)
         {
            this._textField.htmlText = String(_info.data);
         }
         else
         {
            this._textField.text = String(_info.data);
         }
      }
   }
}

