package bagAndInfo.amulet
{
   import bagAndInfo.amulet.vo.EquipAmuletActivateGradeVo;
   import com.pickgliss.loader.DataAnalyzer;
   import road7th.data.DictionaryData;
   
   public class EquipAmuletActivateGradeDataAnalyzer extends DataAnalyzer
   {
      
      private var _data:DictionaryData;
      
      public function EquipAmuletActivateGradeDataAnalyzer(_arg_1:Function)
      {
         super(_arg_1);
      }
      
      override public function analyze(_arg_1:*) : void
      {
         var _local_5:int = 0;
         var _local_4:* = null;
         var _local_2:* = null;
         this._data = new DictionaryData();
         var _local_3:XML = new XML(_arg_1);
         if(_local_3.@value == "true")
         {
            _local_4 = _local_3..Item;
            _local_5 = 0;
            while(_local_5 < _local_4.length())
            {
               _local_2 = new EquipAmuletActivateGradeVo();
               _local_2.wahsGrade = _local_4[_local_5].@WahsLevel;
               _local_2.WahsTimes = _local_4[_local_5].@WahsTimes;
               _local_2.minValue = _local_4[_local_5].@Minvalue;
               _local_2.maxValue = _local_4[_local_5].@Maxvalue;
               this._data.add(_local_2.wahsGrade,_local_2);
               _local_5++;
            }
            onAnalyzeComplete();
         }
         else
         {
            message = _local_3.@message;
            onAnalyzeError();
         }
         this._data = null;
      }
      
      public function get data() : DictionaryData
      {
         return this._data;
      }
   }
}

