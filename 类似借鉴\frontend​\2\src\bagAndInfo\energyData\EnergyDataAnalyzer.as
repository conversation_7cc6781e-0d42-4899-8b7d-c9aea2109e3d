package bagAndInfo.energyData
{
   import com.pickgliss.loader.DataAnalyzer;
   import com.pickgliss.utils.ObjectUtils;
   
   public class EnergyDataAnalyzer extends DataAnalyzer
   {
      
      private var _data:Object;
      
      public function EnergyDataAnalyzer(_arg_1:Function)
      {
         super(_arg_1);
      }
      
      override public function analyze(_arg_1:*) : void
      {
         var _local_6:int = 0;
         var _local_2:int = 0;
         var _local_5:* = null;
         var _local_3:* = null;
         var _local_4:XML = new XML(_arg_1);
         this._data = {};
         if(_local_4.@value == "true")
         {
            _local_5 = _local_4..Item;
            _local_6 = 0;
            while(_local_6 < _local_5.length())
            {
               _local_3 = new EnergyData();
               ObjectUtils.copyPorpertiesByXML(_local_3,_local_5[_local_6]);
               _local_2 = int(_local_5[_local_6].@Count);
               if(!this._data[_local_2])
               {
                  this._data[_local_2] = _local_3;
               }
               _local_6++;
            }
            onAnalyzeComplete();
         }
         else
         {
            message = _local_4.@message;
            onAnalyzeError();
         }
      }
      
      public function get data() : Object
      {
         return this._data;
      }
   }
}

