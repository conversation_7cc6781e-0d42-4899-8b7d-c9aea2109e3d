package consortion.analyze
{
   import com.pickgliss.loader.DataAnalyzer;
   import consortion.data.ConsortiaBossConfigVo;
   
   public class ConsortiaBossDataAnalyzer extends DataAnalyzer
   {
      
      private var _dataList:Array;
      
      public function ConsortiaBossDataAnalyzer(_arg_1:Function)
      {
         super(_arg_1);
      }
      
      override public function analyze(_arg_1:*) : void
      {
         var _local_5:int = 0;
         var _local_3:* = null;
         var _local_4:* = null;
         var _local_2:XML = new XML(_arg_1);
         this._dataList = [];
         if(_local_2.@value == "true")
         {
            _local_3 = _local_2..item;
            _local_5 = 0;
            while(_local_5 < _local_3.length())
            {
               _local_4 = new ConsortiaBossConfigVo();
               _local_4.level = _local_3[_local_5].@Level;
               _local_4.callBossRich = _local_3[_local_5].@CostRich;
               _local_4.extendTimeRich = _local_3[_local_5].@ProlongRich;
               this._dataList.push(_local_4);
               _local_5++;
            }
            this._dataList.sortOn("level",16);
            onAnalyzeComplete();
         }
         else
         {
            message = _local_2.@message;
            onAnalyzeError();
            onAnalyzeError();
         }
      }
      
      public function get dataList() : Array
      {
         return this._dataList;
      }
   }
}

