package register
{
   import com.hurlant.crypto.rsa.RSAKey;
   import com.hurlant.math.BigInteger;
   import com.pickgliss.loader.BaseLoader;
   import com.pickgliss.loader.LoadResourceManager;
   import com.pickgliss.loader.LoaderEvent;
   import com.pickgliss.loader.RequestLoader;
   import com.pickgliss.ui.LayerManager;
   import com.pickgliss.utils.Base64;
   import com.pickgliss.utils.ClassUtils;
   import com.pickgliss.utils.MD5;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.GlobalData;
   import ddt.loading.DDTPassInfoGameLoading;
   import flash.events.Event;
   import flash.external.ExternalInterface;
   import flash.net.URLRequest;
   import flash.net.URLVariables;
   import flash.net.navigateToURL;
   import flash.utils.ByteArray;
   import flash.utils.getDefinitionByName;
   import register.view.RegisterChoiceFigure;
   import register.view.RegisterChoiceLevel;
   
   public class RegisterLuncher
   {
      
      private static const Version:int = 2612558;
      
      public var sex:Boolean;
      
      public var nickName:String;
      
      public var phone:String = "";
      
      private var _call:Function;
      
      public var type:int;
      
      public var time:String;
      
      public var smsKey:String = "";
      
      private var _choiceLevel:RegisterChoiceLevel;
      
      private var _registerChoiceFigureVIew:RegisterChoiceFigure;
      
      private var _tempPassword:String;
      
      public function RegisterLuncher()
      {
         super();
      }
      
      public function lunch(param1:Function, param2:int = 1, param3:String = "") : void
      {
         type = param2;
         time = param3;
         _call = param1;
         RegisterSoundManager.instance.setup();
         showChoiceLevel();
      }
      
      private function showChoiceLevel() : void
      {
         _choiceLevel = new RegisterChoiceLevel();
         _choiceLevel.addEventListener("select",__onClickSelect);
         LayerManager.Instance.addToLayer(_choiceLevel,3);
      }
      
      private function __onClickSelect(param1:Event) : void
      {
         _choiceLevel.removeEventListener("select",__onClickSelect);
         DDTPassInfoGameLoading.instance.waitLoading("3.png",enterFinght);
      }
      
      private function enterFinght() : void
      {
         GlobalData.registerSelectLevel = _choiceLevel.registerSelectLevel;
         ObjectUtils.disposeObject(_choiceLevel);
         _choiceLevel = null;
         showGameView();
      }
      
      private function showGameView() : void
      {
         ClassUtils.CreatInstance("trainGame.TrainGameLunch").lunch(register);
      }
      
      private function register() : void
      {
         DDTPassInfoGameLoading.instance.waitLoading("choicefigure.swf",choiceFigure);
      }
      
      private function choiceFigure() : void
      {
         _registerChoiceFigureVIew = new RegisterChoiceFigure(this);
         LayerManager.Instance.addToLayer(_registerChoiceFigureVIew,3);
      }
      
      public function disposeFigure() : void
      {
         if(_registerChoiceFigureVIew)
         {
            _registerChoiceFigureVIew.dispose();
         }
         _registerChoiceFigureVIew = null;
      }
      
      public function login() : void
      {
         var _loc12_:int = 0;
         var _loc1_:String = "abcdefghijklmnopqrstuvwxyz";
         var _loc5_:String = "zRSdzFcnZjOCxDMkWUbuRgiOZIQlk7frZMhElQ0a7VqZI9VgU3+lwo0ghZLU3Gg63kOY2UyJ5vFpQdwJUQydsF337ZAUJz4rwGRt/MNL70wm71nGfmdPv4ING+DyJ3ZxFawwE1zSMjMOqQtY4IV8his/HlgXuUfIHVDK87nMNLc=";
         var _loc7_:String = "AQAB";
         var _loc9_:RSAKey = generateRsaKey(_loc5_,_loc7_);
         var _loc8_:Date = new Date();
         var _loc6_:ByteArray = new ByteArray();
         _loc6_.writeShort(_loc8_.fullYearUTC);
         _loc6_.writeByte(_loc8_.monthUTC + 1);
         _loc6_.writeByte(_loc8_.dateUTC);
         _loc6_.writeByte(_loc8_.hoursUTC);
         _loc6_.writeByte(_loc8_.minutesUTC);
         _loc6_.writeByte(_loc8_.secondsUTC);
         _tempPassword = "";
         _loc12_ = 0;
         while(_loc12_ < 6)
         {
            _tempPassword += _loc1_.charAt(int(Math.random() * 26));
            _loc12_++;
         }
         var _loc2_:String = GlobalData.user;
         var _loc11_:String = GlobalData.key;
         _loc6_.writeUTFBytes(_loc2_ + "," + _loc11_ + "," + _tempPassword + "," + "");
         var _loc4_:String = rsaEncry4(_loc9_,_loc6_);
         var _loc10_:URLVariables = creatWidthKey(false);
         _loc10_["p"] = _loc4_;
         _loc10_["v"] = 2612558;
         _loc10_["site"] = GlobalData.site;
         _loc10_["rid"] = GlobalData.rid;
         _loc10_["s"] = MD5.hash(_loc4_ + "LKJASDFHGUENXA");
         if(type == 2)
         {
            _loc10_["phone"] = phone;
            _loc10_["smsKey"] = smsKey;
         }
         var _loc3_:RequestLoader = LoadResourceManager.Instance.createLoader(GlobalData.requestPath + "Login.ashx",6,_loc10_);
         _loc3_.addEventListener("loadError",__onLoadLoginError);
         _loc3_.addEventListener("complete",__onLoadLoginComplete);
         LoadResourceManager.Instance.startLoad(_loc3_);
      }
      
      private function __onLoadLoginError(param1:LoaderEvent) : void
      {
         param1.loader.removeEventListener("loadError",__onLoadLoginError);
         param1.loader.removeEventListener("complete",__onLoadLoginComplete);
         if(ExternalInterface.available)
         {
            ExternalInterface.call("alert","登录已失效，请重新登录");
         }
         navigateToURL(new URLRequest(GlobalData.loginPath),"_self");
      }
      
      private function __onLoadLoginComplete(param1:LoaderEvent) : void
      {
         param1.loader.removeEventListener("loadError",__onLoadLoginError);
         param1.loader.removeEventListener("complete",__onLoadLoginComplete);
         var _loc2_:XML = new XML(param1.loader.content);
         if(_loc2_.@value == "false")
         {
            if(ExternalInterface.available)
            {
               ExternalInterface.call("alert",_loc2_.@message);
            }
         }
         else
         {
            GlobalData.key = _tempPassword;
            creatCharacter();
         }
      }
      
      private function creatCharacter() : void
      {
         var _loc2_:URLVariables = creatWidthKey(true);
         _loc2_["Sex"] = sex;
         _loc2_["NickName"] = nickName;
         _loc2_["Name"] = GlobalData.user;
         _loc2_["Pass"] = GlobalData.key;
         _loc2_["site"] = GlobalData.site;
         var _loc1_:RequestLoader = LoadResourceManager.Instance.createLoader(GlobalData.requestPath + "VisualizeRegister.ashx",6,_loc2_);
         _loc1_.addEventListener("loadError",__onCreatCharacterError);
         _loc1_.addEventListener("complete",__onCreatCharacterComplete);
         LoadResourceManager.Instance.startLoad(_loc1_);
      }
      
      private function __onCreatCharacterError(param1:LoaderEvent) : void
      {
         param1.loader.removeEventListener("loadError",__onCreatCharacterError);
         param1.loader.removeEventListener("complete",__onCreatCharacterComplete);
         if(ExternalInterface.available)
         {
            ExternalInterface.call("alert","注册形象失败");
         }
         navigateToURL(new URLRequest(GlobalData.loginPath),"_self");
         sendTrainLog("01");
      }
      
      private function __onCreatCharacterComplete(param1:LoaderEvent) : void
      {
         param1.loader.removeEventListener("loadError",__onCreatCharacterError);
         param1.loader.removeEventListener("complete",__onCreatCharacterComplete);
         var _loc2_:XML = new XML(param1.loader.content);
         if(_loc2_.@value == "false")
         {
            if(ExternalInterface.available)
            {
               ExternalInterface.call("alert","注册形象失败1");
            }
            sendTrainLog("01");
         }
         else
         {
            sendTrainLog("10");
            DDTPassInfoGameLoading.instance.waitLoading("2.png",enterGame);
         }
      }
      
      private function sendTrainLog(param1:String) : void
      {
         var _loc3_:URLVariables = new URLVariables();
         _loc3_["userId"] = GlobalData.user;
         _loc3_["target"] = param1;
         if(GlobalData.registerSelectLevel)
         {
            _loc3_["state"] = 14;
         }
         else
         {
            _loc3_["state"] = 22;
         }
         _loc3_["rnd"] = Math.random();
         var _loc2_:BaseLoader = LoadResourceManager.Instance.createLoader(GlobalData.requestPath + "FightNoviceRecord.ashx",6,_loc3_);
         LoadResourceManager.Instance.startLoad(_loc2_);
      }
      
      private function enterGame() : void
      {
         getDefinitionByName("MainLoadingLuncher").Instance.show();
         _call.call();
         _call = null;
      }
      
      private function creatWidthKey(param1:Boolean) : URLVariables
      {
         var _loc2_:URLVariables = new URLVariables();
         _loc2_["selfid"] = "";
         _loc2_["key"] = MD5.hash(GlobalData.key);
         if(param1)
         {
            _loc2_["rnd"] = Math.random();
         }
         return _loc2_;
      }
      
      private function rsaEncry4(param1:RSAKey, param2:ByteArray) : String
      {
         var _loc3_:ByteArray = new ByteArray();
         param1.encrypt(param2,_loc3_,param2.length);
         return Base64.encodeByteArray(_loc3_);
      }
      
      private function generateRsaKey(param1:String, param2:String) : RSAKey
      {
         return generateRsaKey2(Base64.decodeToByteArray(param1),Base64.decodeToByteArray(param2));
      }
      
      private function generateRsaKey2(param1:ByteArray, param2:ByteArray) : RSAKey
      {
         var _loc4_:BigInteger = new BigInteger(param1);
         var _loc3_:BigInteger = new BigInteger(param2);
         return new RSAKey(_loc4_,_loc3_.intValue());
      }
   }
}

