package character
{
   import flash.events.Event;
   
   public class CharacterEvent extends Event
   {
      
      public static const ADD_ACTION:String = "addAction";
      
      public static const REMOVE_ACTION:String = "removeAction";
      
      private var _data:Object;
      
      public function CharacterEvent(_arg_1:String, _arg_2:Object = null)
      {
         this._data = _arg_2;
         super(_arg_1,bubbles,cancelable);
      }
      
      public function get data() : Object
      {
         return this._data;
      }
   }
}

