package activity.perfectCouple
{
   import flash.events.Event;
   
   public class PerfectCoupleEvent extends Event
   {
      
      public static const UPDATE_INFO:String = "updateinfo";
      
      public static const UPDATE_WIFE_INFO:String = "updatewifeinfo";
      
      public function PerfectCoupleEvent(_arg_1:String, _arg_2:<PERSON><PERSON>an = false, _arg_3:<PERSON><PERSON><PERSON> = false)
      {
         super(_arg_1,_arg_2,_arg_3);
      }
   }
}

