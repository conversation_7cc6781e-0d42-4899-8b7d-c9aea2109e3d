package activity
{
   import flash.events.Event;
   
   public class ActivityNormalEvent extends Event
   {
      
      public static var OLDPLAYERNEW_DRAW:String = "oldplayernewdraw";
      
      public static var PRAY_UPDATE:String = "pray_update";
      
      public static var PRAY_FIND:String = "pray_find";
      
      public static var PRAY_GOOD:String = "pray_good";
      
      public static var EGG_GAME:String = "eeg_game";
      
      public static var FIGHTCORNER_UPDATE:String = "fightcorner_update";
      
      public static var FIGHTCORNER_TURNCOMPLATE:String = "fightcorner_turncomplate";
      
      public static var FIGHTCORNER_ADDRECORD:String = "fightcorner_addrecord";
      
      private var _data:*;
      
      public function ActivityNormalEvent(param1:String, param2:* = null, param3:Boolean = false, param4:Boolean = false)
      {
         super(param1,param3,param4);
         this._data = param2;
      }
      
      public function get data() : *
      {
         return this._data;
      }
   }
}

