package consortion.analyze
{
   import com.pickgliss.loader.DataAnalyzer;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.data.goods.InventoryItemInfo;
   import ddt.manager.ItemManager;
   import road7th.data.DictionaryData;
   
   public class ConsortiaWeekRewardAnalyze extends DataAnalyzer
   {
      
      private var _dataList:DictionaryData;
      
      public function ConsortiaWeekRewardAnalyze(_arg_1:Function)
      {
         super(_arg_1);
      }
      
      public function get dataList() : DictionaryData
      {
         return this._dataList;
      }
      
      override public function analyze(_arg_1:*) : void
      {
         var _local_6:int = 0;
         var _local_2:int = 0;
         var _local_4:* = null;
         var _local_5:* = null;
         this._dataList = new DictionaryData();
         var _local_3:XML = new XML(_arg_1);
         if(_local_3.@value == "true")
         {
            _local_4 = _local_3.Item;
            _local_6 = 0;
            while(_local_6 < _local_4.length())
            {
               _local_2 = int(_local_4[_local_6].@Rank);
               _local_5 = new InventoryItemInfo();
               _local_5.TemplateID = int(_local_4[_local_6].@TemplateID);
               ItemManager.fill(_local_5);
               ObjectUtils.copyPorpertiesByXML(_local_5,_local_4[_local_6]);
               _local_5.IsBinds = _local_4[_local_6].@IsBind == "true";
               this._dataList[_local_2] = _local_5;
               _local_6++;
            }
            onAnalyzeComplete();
         }
         else
         {
            message = _local_3.@message;
            onAnalyzeError();
         }
      }
   }
}

