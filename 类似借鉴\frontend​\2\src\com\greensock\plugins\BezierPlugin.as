package com.greensock.plugins
{
   import com.greensock.*;
   
   public class BezierPlugin extends TweenPlugin
   {
      
      public static const API:Number = 1;
      
      protected static const _RAD2DEG:Number = 57.2957795130823;
      
      protected var _target:Object;
      
      protected var _orientData:Array;
      
      protected var _orient:Boolean;
      
      protected var _future:Object = {};
      
      protected var _beziers:Object;
      
      public function BezierPlugin()
      {
         super();
         this.propName = "bezier";
         this.overwriteProps = [];
      }
      
      public static function parseBeziers(_arg_1:Object, _arg_2:Boolean = false) : Object
      {
         var _local_7:int = 0;
         var _temp_1:* = undefined;
         var _temp_2:* = undefined;
         var _temp_3:* = undefined;
         var _temp_4:* = undefined;
         var _local_5:* = null;
         var _local_3:* = null;
         var _local_4:* = null;
         var _local_6:Object = {};
         if(_arg_2)
         {
            for(_local_4 in _arg_1)
            {
               _local_5 = _arg_1[_local_4];
               _temp_1 = [];
               _temp_2 = _temp_1;
               _local_3 = _temp_2;
               _local_6[_local_4] = _temp_1;
               if(_local_5.length > 2)
               {
                  _local_3[_local_3.length] = [_local_5[0],_local_5[1] - (_local_5[2] - _local_5[0]) / 4,_local_5[1]];
                  _local_7 = 1;
                  while(_local_7 < _local_5.length - 1)
                  {
                     _local_3[_local_3.length] = [_local_5[_local_7],_local_5[_local_7] + (_local_5[_local_7] - _local_3[_local_7 - 1][1]),_local_5[_local_7 + 1]];
                     _local_7 += 1;
                  }
               }
               else
               {
                  _local_3[_local_3.length] = [_local_5[0],(_local_5[0] + _local_5[1]) / 2,_local_5[1]];
               }
            }
         }
         else
         {
            for(_local_4 in _arg_1)
            {
               _local_5 = _arg_1[_local_4];
               _temp_3 = [];
               _temp_4 = _temp_3;
               _local_3 = _temp_4;
               _local_6[_local_4] = _temp_3;
               if(_local_5.length > 3)
               {
                  _local_3[_local_3.length] = [_local_5[0],_local_5[1],(_local_5[1] + _local_5[2]) / 2];
                  _local_7 = 2;
                  while(_local_7 < _local_5.length - 2)
                  {
                     _local_3[_local_3.length] = [_local_3[_local_7 - 2][2],_local_5[_local_7],(_local_5[_local_7] + _local_5[_local_7 + 1]) / 2];
                     _local_7 += 1;
                  }
                  _local_3[_local_3.length] = [_local_3[_local_3.length - 1][2],_local_5[_local_5.length - 2],_local_5[_local_5.length - 1]];
               }
               else if(_local_5.length == 3)
               {
                  _local_3[_local_3.length] = [_local_5[0],_local_5[1],_local_5[2]];
               }
               else if(_local_5.length == 2)
               {
                  _local_3[_local_3.length] = [_local_5[0],(_local_5[0] + _local_5[1]) / 2,_local_5[1]];
               }
            }
         }
         return _local_6;
      }
      
      override public function onInitTween(_arg_1:Object, _arg_2:*, _arg_3:TweenLite) : Boolean
      {
         if(!(_arg_2 is Array))
         {
            return false;
         }
         this.init(_arg_3,_arg_2 as Array,false);
         return true;
      }
      
      protected function init(_arg_1:TweenLite, _arg_2:Array, _arg_3:Boolean) : void
      {
         var _local_8:int = 0;
         var _local_5:* = null;
         var _local_6:* = null;
         this._target = _arg_1.target;
         var _local_4:Object = _arg_1.vars.isTV == true ? _arg_1.vars.exposedVars : _arg_1.vars;
         if(_local_4.orientToBezier == true)
         {
            this._orientData = [["x","y","rotation",0,0.01]];
            this._orient = true;
         }
         else if(_local_4.orientToBezier is Array)
         {
            this._orientData = _local_4.orientToBezier;
            this._orient = true;
         }
         var _local_7:Object = {};
         _local_8 = 0;
         while(_local_8 < _arg_2.length)
         {
            for(_local_5 in _arg_2[_local_8])
            {
               if(_local_7[_local_5] == undefined)
               {
                  _local_7[_local_5] = [_arg_1.target[_local_5]];
               }
               if(typeof _arg_2[_local_8][_local_5] == "number")
               {
                  _local_7[_local_5].push(_arg_2[_local_8][_local_5]);
               }
               else
               {
                  _local_7[_local_5].push(_arg_1.target[_local_5] + _arg_2[_local_8][_local_5]);
               }
            }
            _local_8 += 1;
         }
         for(_local_5 in _local_7)
         {
            this.overwriteProps[this.overwriteProps.length] = _local_5;
            if(_local_4[_local_5] != undefined)
            {
               if(typeof _local_4[_local_5] == "number")
               {
                  _local_7[_local_5].push(_local_4[_local_5]);
               }
               else
               {
                  _local_7[_local_5].push(_arg_1.target[_local_5] + _local_4[_local_5]);
               }
               _local_6 = {};
               _local_6[_local_5] = true;
               _arg_1.killVars(_local_6,false);
               delete _local_4[_local_5];
            }
         }
         this._beziers = parseBeziers(_local_7,_arg_3);
      }
      
      override public function killProps(_arg_1:Object) : void
      {
         var _local_2:String = null;
         for(_local_2 in this._beziers)
         {
            if(_local_2 in _arg_1)
            {
               delete this._beziers[_local_2];
            }
         }
         super.killProps(_arg_1);
      }
      
      override public function set changeFactor(_arg_1:Number) : void
      {
         var _local_10:Number = NaN;
         var _local_6:int = 0;
         var _local_2:Number = NaN;
         var _local_9:int = 0;
         var _local_7:Number = NaN;
         var _local_8:Number = NaN;
         var _local_11:Number = NaN;
         var _local_4:Boolean = false;
         var _local_12:* = null;
         var _local_5:* = null;
         var _local_14:* = null;
         var _local_3:* = null;
         var _local_13:* = null;
         _changeFactor = _arg_1;
         if(_arg_1 == 1)
         {
            for(_local_12 in this._beziers)
            {
               _local_9 = this._beziers[_local_12].length - 1;
               this._target[_local_12] = this._beziers[_local_12][_local_9][2];
            }
         }
         else
         {
            for(_local_12 in this._beziers)
            {
               _local_6 = int(this._beziers[_local_12].length);
               if(_arg_1 < 0)
               {
                  _local_9 = 0;
               }
               else if(_arg_1 >= 1)
               {
                  _local_9 = _local_6 - 1;
               }
               else
               {
                  _local_9 = _local_6 * _arg_1 >> 0;
               }
               _local_10 = (_arg_1 - _local_9 * (1 / _local_6)) * _local_6;
               _local_5 = this._beziers[_local_12][_local_9];
               if(this.round)
               {
                  _local_2 = _local_5[0] + _local_10 * (2 * (1 - _local_10) * (_local_5[1] - _local_5[0]) + _local_10 * (_local_5[2] - _local_5[0]));
                  if(_local_2 > 0)
                  {
                     this._target[_local_12] = _local_2 + 0.5 >> 0;
                  }
                  else
                  {
                     this._target[_local_12] = _local_2 - 0.5 >> 0;
                  }
               }
               else
               {
                  this._target[_local_12] = _local_5[0] + _local_10 * (2 * (1 - _local_10) * (_local_5[1] - _local_5[0]) + _local_10 * (_local_5[2] - _local_5[0]));
               }
            }
         }
         if(this._orient)
         {
            _local_9 = int(this._orientData.length);
            _local_3 = {};
            while(Boolean(_local_9--))
            {
               _local_14 = this._orientData[_local_9];
               _local_3[_local_14[0]] = this._target[_local_14[0]];
               _local_3[_local_14[1]] = this._target[_local_14[1]];
            }
            _local_13 = this._target;
            _local_4 = this.round;
            this._target = this._future;
            this.round = false;
            this._orient = false;
            _local_9 = int(this._orientData.length);
            while(Boolean(_local_9--))
            {
               _local_14 = this._orientData[_local_9];
               this.changeFactor = _arg_1 + (_local_14[4] || 0.01);
               _local_11 = Number(Number(_local_14[3]) || 0);
               _local_7 = this._future[_local_14[0]] - _local_3[_local_14[0]];
               _local_8 = this._future[_local_14[1]] - _local_3[_local_14[1]];
               _local_13[_local_14[2]] = Math.atan2(_local_8,_local_7) * 57.2957795130823 + _local_11;
            }
            this._target = _local_13;
            this.round = _local_4;
            this._orient = true;
         }
      }
   }
}

