package condiscount.analyzer
{
   import com.pickgliss.loader.DataAnalyzer;
   import condiscount.data.CondiscountInfo;
   
   public class CondiscountListAnalyzer extends DataAnalyzer
   {
      
      public var itemList:Vector.<CondiscountInfo>;
      
      public function CondiscountListAnalyzer(_arg_1:Function)
      {
         super(_arg_1);
      }
      
      override public function analyze(_arg_1:*) : void
      {
         var _local_6:int = 0;
         var _local_2:* = null;
         this.itemList = new Vector.<CondiscountInfo>();
         var _local_3:XML = new XML(_arg_1);
         var _local_5:int = int(_local_3.Item.length());
         var _local_4:XMLList = _local_3..Item;
         _local_6 = 0;
         while(_local_6 < _local_4.length())
         {
            _local_2 = new CondiscountInfo();
            this.itemList.push(_local_2);
            _local_6++;
         }
         onAnalyzeComplete();
      }
   }
}

