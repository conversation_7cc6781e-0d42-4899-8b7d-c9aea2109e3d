package bagAndInfo.cell
{
   import beadSystem.beadSystemManager;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.ShowTipManager;
   import com.pickgliss.ui.controls.container.HBox;
   import com.pickgliss.ui.core.Disposeable;
   import com.pickgliss.ui.core.ITipedDisplay;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.data.EquipType;
   import ddt.data.goods.InventoryItemInfo;
   import ddt.data.goods.ItemTemplateInfo;
   import ddt.events.CellEvent;
   import ddt.interfaces.ICell;
   import ddt.interfaces.IDragable;
   import ddt.manager.BeadTemplateManager;
   import ddt.manager.DragManager;
   import ddt.manager.ServerConfigManager;
   import ddt.utils.PositionUtils;
   import ddt.view.tips.GoodTipInfo;
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import magicStone.MagicStoneManager;
   import mark.data.MarkChipData;
   import mark.data.MarkModel;
   
   [Event(name="change",type="flash.events.Event")]
   public class BaseCell extends Sprite implements ICell, ITipedDisplay, Disposeable
   {
      
      protected var _bg:DisplayObject;
      
      protected var _contentHeight:Number;
      
      protected var _contentWidth:Number;
      
      protected var _info:ItemTemplateInfo;
      
      protected var _loadingasset:MovieClip;
      
      protected var _pic:CellContentCreator;
      
      protected var _effect:CellMCSpecialEffectCreator;
      
      protected var _picPos:Point;
      
      protected var _showLoading:Boolean;
      
      protected var _showTip:Boolean;
      
      protected var _smallPic:Sprite;
      
      protected var _tipData:Object;
      
      protected var _tipDirection:String;
      
      protected var _tipGapH:int;
      
      protected var _tipGapV:int;
      
      protected var _tipStyle:String;
      
      protected var _allowDrag:Boolean;
      
      private var _overBg:DisplayObject;
      
      protected var _locked:Boolean;
      
      private var _grayFlag:Boolean;
      
      protected var _markStarContainer:HBox;
      
      protected var _markChip:MarkChipData;
      
      public var showStarContainer:Boolean = true;
      
      private var _isSurpriseRouletteCellGQ:Boolean = false;
      
      public function BaseCell(_arg_1:DisplayObject, _arg_2:ItemTemplateInfo = null, _arg_3:Boolean = true, _arg_4:Boolean = true)
      {
         super();
         this._bg = _arg_1;
         this._showLoading = _arg_3;
         this._showTip = _arg_4;
         this.init();
         this.initTip();
         this.initEvent();
         this.info = _arg_2;
      }
      
      public function set overBg(_arg_1:DisplayObject) : void
      {
         ObjectUtils.disposeObject(this._overBg);
         this._overBg = _arg_1;
         if(Boolean(this._overBg))
         {
            this._overBg.visible = false;
            addChildAt(this._overBg,1);
         }
      }
      
      public function get overBg() : DisplayObject
      {
         return this._overBg;
      }
      
      public function set PicPos(_arg_1:Point) : void
      {
         this._picPos = _arg_1;
         this.updateSize(this._pic);
      }
      
      public function get allowDrag() : Boolean
      {
         return this._allowDrag;
      }
      
      public function set allowDrag(_arg_1:Boolean) : void
      {
         this._allowDrag = _arg_1;
      }
      
      public function asDisplayObject() : DisplayObject
      {
         return this;
      }
      
      public function dispose() : void
      {
         this.removeEvent();
         if(Boolean(this._markStarContainer))
         {
            this._markStarContainer.clearAllChild();
            ObjectUtils.disposeObject(this._markStarContainer);
         }
         this._markStarContainer = null;
         ObjectUtils.disposeObject(this._bg);
         this._bg = null;
         this.clearLoading();
         this.clearCreatingContent();
         this._info = null;
         ShowTipManager.Instance.removeTip(this);
         if(Boolean(parent))
         {
            parent.removeChild(this);
         }
      }
      
      public function dragDrop(_arg_1:DragEffect) : void
      {
      }
      
      public function dragStart() : void
      {
         if(this._info && !this.locked && stage && this._allowDrag)
         {
            if(DragManager.startDrag(this,this._info,this.createDragImg(),stage.mouseX,stage.mouseY,"move"))
            {
               this.locked = true;
            }
         }
      }
      
      public function dragStop(_arg_1:DragEffect) : void
      {
         if(_arg_1.action == "none")
         {
            this.locked = false;
         }
      }
      
      public function get editLayer() : int
      {
         return this._pic.editLayer;
      }
      
      public function getContent() : Sprite
      {
         return this._pic;
      }
      
      public function getSmallContent() : Sprite
      {
         var _local_1:* = 40;
         this._pic.height = _local_1;
         this._pic.width = _local_1;
         return this._pic;
      }
      
      public function getSource() : IDragable
      {
         return this;
      }
      
      public function set grayFilters(_arg_1:Boolean) : void
      {
         this._grayFlag = _arg_1;
         if(_arg_1)
         {
            filters = ComponentFactory.Instance.creatFilters("grayFilter");
         }
         else
         {
            filters = null;
         }
      }
      
      public function get grayFlag() : Boolean
      {
         return this._grayFlag;
      }
      
      override public function get height() : Number
      {
         return this._bg.height + this._bg.y * 2;
      }
      
      public function get info() : ItemTemplateInfo
      {
         if(this._info == null)
         {
            return null;
         }
         return this._info;
      }
      
      public function set info(_arg_1:ItemTemplateInfo) : void
      {
         if(this._info == _arg_1 && !this._info)
         {
            return;
         }
         if(Boolean(this._info))
         {
            this.clearCreatingContent();
            ObjectUtils.disposeObject(this._pic);
            this._pic = null;
            ObjectUtils.disposeObject(this._effect);
            this._effect = null;
            this.clearLoading();
            this._tipData = null;
            this.locked = false;
            if(Boolean(this._markStarContainer))
            {
               this._markStarContainer.clearAllChild();
               ObjectUtils.disposeObject(this._markStarContainer);
            }
            this._markStarContainer = null;
         }
         this._info = _arg_1;
         if(Boolean(this._info) && this._info.CategoryID == 74)
         {
            this.tipStyle = "mark.MarkChipTip";
            this.tipDirctions = "7,6,2,1,5,4,0,3,6";
         }
         if(Boolean(this._info))
         {
            if(this._showLoading)
            {
               this.createLoading();
            }
            this._pic = new CellContentCreator();
            this._pic.info = this._info;
            this._pic.loadSync(this.createContentComplete);
            addChild(this._pic);
            if((int(this._info.Property1) == 9 || int(this._info.Property1) == 10) && ((this._info.CategoryID == 8 || this._info.CategoryID == 9) && this.isNotWeddingRing(this._info)))
            {
               this._effect = new CellMCSpecialEffectCreator();
               this._effect.info = this._info;
               this._effect.loadSync(this.createEffectComplete);
               addChild(this._effect);
            }
            this.setDefaultTipData();
         }
         dispatchEvent(new Event("change"));
      }
      
      public function resetLoadIcon() : void
      {
         this.clearCreatingContent();
         this._pic = new CellContentCreator();
         this._pic.info = this._info;
         this._pic.loadSync(this.createContentComplete);
         addChild(this._pic);
      }
      
      protected function setDefaultTipData() : void
      {
         var _local_1:* = null;
         if(EquipType.isCardBox(this._info))
         {
            this.tipStyle = "core.CardBoxTipPanel";
            this.tipData = this._info;
         }
         else if(this._info.CategoryID != 26 && this._info.CategoryID != 74)
         {
            this.tipStyle = "core.GoodsTip";
            this._tipData = new GoodTipInfo();
            GoodTipInfo(this._tipData).itemInfo = this._info;
            _local_1 = this._info as InventoryItemInfo;
            if(this._info.CategoryID == 75)
            {
               return;
            }
            if(this._info.Property1 == "31")
            {
               if(_local_1 && _local_1.Hole2 > 0)
               {
                  GoodTipInfo(this._tipData).exp = _local_1.Hole2;
                  GoodTipInfo(this._tipData).upExp = ServerConfigManager.instance.getBeadUpgradeExp()[_local_1.Hole1 + 1];
                  GoodTipInfo(this._tipData).beadName = _local_1.Name + "-" + beadSystemManager.Instance.getBeadName(_local_1);
               }
               else
               {
                  GoodTipInfo(this._tipData).exp = ServerConfigManager.instance.getBeadUpgradeExp()[BeadTemplateManager.Instance.GetBeadInfobyID(this.info.TemplateID).BaseLevel];
                  GoodTipInfo(this._tipData).upExp = ServerConfigManager.instance.getBeadUpgradeExp()[BeadTemplateManager.Instance.GetBeadInfobyID(this.info.TemplateID).BaseLevel + 1];
                  GoodTipInfo(this._tipData).beadName = this._info.Name + "-" + BeadTemplateManager.Instance.GetBeadInfobyID(this._info.TemplateID).Name + "Lv" + BeadTemplateManager.Instance.GetBeadInfobyID(this.info.TemplateID).BaseLevel;
               }
            }
            else if(this.info.Property1 == "81" && this.info.CategoryID != 80)
            {
               if(_local_1 && _local_1.StrengthenExp > 0)
               {
                  GoodTipInfo(this._tipData).exp = _local_1.StrengthenExp - MagicStoneManager.instance.getNeedExp(this.info.TemplateID,_local_1.StrengthenLevel);
               }
               else
               {
                  GoodTipInfo(this._tipData).exp = 0;
               }
               GoodTipInfo(this._tipData).upExp = MagicStoneManager.instance.getNeedExpPerLevel(this.info.TemplateID,this.info.Level + 1);
               GoodTipInfo(this._tipData).beadName = this.info.Name + "Lv" + this.info.Level;
            }
         }
         else if(this._info.CategoryID == 74 && this._info is InventoryItemInfo)
         {
            this._tipData = new MarkChipData();
            this._tipData = MarkModel.exchangeMark(this._info);
            this.updateCellStar();
         }
      }
      
      private function isNotWeddingRing(_arg_1:ItemTemplateInfo) : Boolean
      {
         switch(_arg_1.TemplateID)
         {
            case 9022:
            case 9122:
            case 9222:
            case 9322:
            case 9422:
            case 9522:
            case 9900:
            case 101000214:
            case 101000215:
            case 101000216:
            case 101000217:
               return false;
            default:
               return true;
         }
      }
      
      public function get locked() : Boolean
      {
         return this._locked;
      }
      
      public function set locked(_arg_1:Boolean) : void
      {
         if(this._locked == _arg_1)
         {
            return;
         }
         this._locked = _arg_1;
         this.updateLockState();
         if(this._info is InventoryItemInfo)
         {
            this._info["lock"] = this._locked;
         }
         dispatchEvent(new CellEvent("lockChanged"));
      }
      
      public function setColor(_arg_1:*) : Boolean
      {
         return this._pic.setColor(_arg_1);
      }
      
      public function setContentSize(_arg_1:Number, _arg_2:Number) : void
      {
         this._contentWidth = _arg_1;
         this._contentHeight = _arg_2;
         this.updateSize(this._pic);
         if(Boolean(this._effect) && this._effect.numChildren > 0)
         {
            this._effect.getChildAt(0).scaleX = 1.1;
            this._effect.getChildAt(0).scaleY = 1.1;
         }
      }
      
      public function get tipData() : Object
      {
         return this._tipData;
      }
      
      public function set tipData(_arg_1:Object) : void
      {
         this._tipData = _arg_1;
      }
      
      public function get tipDirctions() : String
      {
         return this._tipDirection;
      }
      
      public function set tipDirctions(_arg_1:String) : void
      {
         this._tipDirection = _arg_1;
      }
      
      public function get tipGapH() : int
      {
         return this._tipGapH;
      }
      
      public function set tipGapH(_arg_1:int) : void
      {
         this._tipGapH = _arg_1;
      }
      
      public function get tipGapV() : int
      {
         return this._tipGapV;
      }
      
      public function set tipGapV(_arg_1:int) : void
      {
         this._tipGapV = _arg_1;
      }
      
      public function get tipStyle() : String
      {
         return this._tipStyle;
      }
      
      public function set tipStyle(_arg_1:String) : void
      {
         this._tipStyle = _arg_1;
      }
      
      override public function get width() : Number
      {
         return this._bg.width;
      }
      
      protected function clearCreatingContent() : void
      {
         if(this._pic == null)
         {
            return;
         }
         if(Boolean(this._pic.parent))
         {
            this._pic.parent.removeChild(this._pic);
         }
         this._pic.clearLoader();
         this._pic.dispose();
         this._pic = null;
      }
      
      protected function createChildren() : void
      {
         this._contentWidth = this._bg.width - 2;
         this._contentHeight = this._bg.height - 2;
         addChildAt(this._bg,0);
         this._pic = new CellContentCreator();
      }
      
      protected function createContentComplete() : void
      {
         this.clearLoading();
         this.updateSize(this._pic);
      }
      
      protected function createEffectComplete() : void
      {
         if(this._effect.width > 0)
         {
            this.updateSize(this._effect);
         }
      }
      
      public function createDragImg() : DisplayObject
      {
         var _local_1:* = null;
         if(this._pic && this._pic.width > 0 && this._pic.height > 0)
         {
            _local_1 = new Bitmap(new BitmapData(this._pic.width / this._pic.scaleX,this._pic.height / this._pic.scaleY,true,0));
            _local_1.bitmapData.draw(this._pic);
            return _local_1;
         }
         return null;
      }
      
      protected function createLoading() : void
      {
         if(this._loadingasset == null)
         {
            this._loadingasset = ComponentFactory.Instance.creat("bagAndInfo.cell.BaseCellLoadingAsset");
         }
         this.updateSizeII(this._loadingasset);
         PositionUtils.setPos(this._loadingasset,"ddt.core.baseCell.loadingPos");
         addChild(this._loadingasset);
      }
      
      protected function init() : void
      {
         this._allowDrag = true;
         if(this._showTip)
         {
            ShowTipManager.Instance.addTip(this);
         }
         this.createChildren();
      }
      
      protected function initTip() : void
      {
         this.tipDirctions = "7,6,2,1,5,4,0,3,6";
         this.tipGapV = 10;
         this.tipGapH = 10;
      }
      
      protected function initEvent() : void
      {
         addEventListener("click",this.onMouseClick);
         addEventListener("rollOut",this.onMouseOut);
         addEventListener("mouseOver",this.onMouseOver);
      }
      
      protected function onMouseClick(_arg_1:MouseEvent) : void
      {
      }
      
      protected function onMouseOut(_arg_1:MouseEvent) : void
      {
         if(Boolean(this._overBg))
         {
            this._overBg.visible = false;
         }
      }
      
      protected function onMouseOver(_arg_1:MouseEvent) : void
      {
         if(Boolean(this._overBg))
         {
            this._overBg.visible = true;
         }
      }
      
      protected function removeEvent() : void
      {
         removeEventListener("click",this.onMouseClick);
         removeEventListener("rollOut",this.onMouseOut);
         removeEventListener("rollOver",this.onMouseOver);
      }
      
      protected function updateSize(_arg_1:Sprite) : void
      {
         if(Boolean(_arg_1))
         {
            _arg_1.width = this._contentWidth - 2;
            _arg_1.height = this._contentHeight - 2;
            if(this._picPos != null)
            {
               _arg_1.x = this._picPos.x;
            }
            else
            {
               _arg_1.x = Math.abs(_arg_1.width - this._contentWidth) / 2;
            }
            if(this._picPos != null)
            {
               _arg_1.y = this._picPos.y;
            }
            else
            {
               _arg_1.y = Math.abs(_arg_1.height - this._contentHeight) / 2;
            }
         }
      }
      
      protected function clearLoading() : void
      {
         if(Boolean(this._loadingasset))
         {
            this._loadingasset.stop();
         }
         ObjectUtils.disposeObject(this._loadingasset);
         this._loadingasset = null;
      }
      
      public function updateCellStar() : void
      {
         var _local_1:int = 0;
         if(Boolean(this._info) && this._info.CategoryID == 74)
         {
            if(!this._markStarContainer)
            {
               this._markStarContainer = ComponentFactory.Instance.creatComponentByStylename("ddtcorei.cell.starHBox");
            }
            addChild(this._markStarContainer);
            _local_1 = 0;
            if(this._info is InventoryItemInfo)
            {
               _local_1 = (this._info as InventoryItemInfo).StrengthenLevel;
            }
            if(this._tipData is MarkChipData)
            {
               _local_1 = (this._tipData as MarkChipData).bornLv;
            }
            while(this._markStarContainer.numChildren > _local_1)
            {
               ObjectUtils.disposeObject(this._markStarContainer.getChildAt(0));
            }
            while(this._markStarContainer.numChildren < _local_1)
            {
               this._markStarContainer.addChild(ComponentFactory.Instance.creatBitmap("asset.mark.littleStar"));
            }
            this._markStarContainer.x = this.width / 2 - this._markStarContainer.realWidth / 2;
            if(!this.showStarContainer)
            {
               this._markStarContainer.visible = false;
            }
         }
         else if(Boolean(this._markStarContainer))
         {
            while(Boolean(this._markStarContainer.numChildren))
            {
               ObjectUtils.disposeObject(this._markStarContainer.getChildAt(0));
            }
         }
      }
      
      private function updateLockState() : void
      {
         if(this._locked)
         {
            filters = ComponentFactory.Instance.creatFilters("grayFilter");
         }
         else
         {
            filters = null;
         }
      }
      
      public function set surpriseRouletteCellGQ(_arg_1:Boolean) : void
      {
         this._isSurpriseRouletteCellGQ = _arg_1;
      }
      
      protected function updateSizeII(_arg_1:Sprite) : void
      {
         if(this._isSurpriseRouletteCellGQ && Boolean(_arg_1))
         {
            if(this._picPos != null)
            {
               _arg_1.x = this._picPos.x - 2;
            }
            else
            {
               _arg_1.x = Math.abs(_arg_1.width - this._contentWidth) / 2;
            }
            if(this._picPos != null)
            {
               _arg_1.y = this._picPos.y - 2;
            }
            else
            {
               _arg_1.y = Math.abs(_arg_1.height - this._contentHeight) / 2;
            }
         }
      }
   }
}

