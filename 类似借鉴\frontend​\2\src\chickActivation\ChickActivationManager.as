package chickActivation
{
   import chickActivation.model.ChickActivationModel;
   import com.pickgliss.events.UIModuleEvent;
   import com.pickgliss.loader.BaseLoader;
   import com.pickgliss.loader.LoadResourceManager;
   import com.pickgliss.loader.LoaderEvent;
   import com.pickgliss.loader.UIModuleLoader;
   import ddt.events.CrazyTankSocketEvent;
   import ddt.loader.LoaderCreate;
   import ddt.manager.ServerConfigManager;
   import ddt.manager.SocketManager;
   import ddt.view.UIModuleSmallLoading;
   import flash.events.Event;
   import flash.events.EventDispatcher;
   import flash.events.IEventDispatcher;
   import flash.utils.Dictionary;
   import hallIcon.HallIconManager;
   import road7th.comm.PackageIn;
   
   public class ChickActivationManager extends EventDispatcher
   {
      
      private static var _instance:ChickActivationManager;
      
      public static const CHICKACTIVATION_SHOWFRAME:String = "ChickActivationShowFrame";
      
      private var _model:ChickActivationModel;
      
      public function ChickActivationManager(_arg_1:IEventDispatcher = null)
      {
         super(_arg_1);
      }
      
      public static function get instance() : ChickActivationManager
      {
         if(_instance == null)
         {
            _instance = new ChickActivationManager();
         }
         return _instance;
      }
      
      public function get model() : ChickActivationModel
      {
         return this._model;
      }
      
      public function setup() : void
      {
         this._model = new ChickActivationModel();
         this.initData();
         SocketManager.Instance.addEventListener("chickActivation_system",this.__chickActivationHandler);
      }
      
      private function initData() : void
      {
         var _local_1:Dictionary = new Dictionary();
         _local_1["0,0,1"] = 1;
         _local_1["0,0,2"] = 1;
         _local_1["0,0,3"] = 1;
         _local_1["0,0,4"] = 1;
         _local_1["0,0,5"] = 1;
         _local_1["0,0,6"] = 1;
         _local_1["0,0,0"] = 1;
         _local_1["0,2,5"] = 2;
         _local_1["0,2,6"] = 2;
         _local_1["0,2,0"] = 2;
         _local_1["0,1"] = 3;
         _local_1["0,3"] = 12;
         _local_1["1,0,1"] = 101;
         _local_1["1,0,2"] = 101;
         _local_1["1,0,3"] = 101;
         _local_1["1,0,4"] = 101;
         _local_1["1,0,5"] = 101;
         _local_1["1,0,6"] = 101;
         _local_1["1,0,0"] = 101;
         _local_1["1,2,5"] = 102;
         _local_1["1,2,6"] = 102;
         _local_1["1,2,0"] = 102;
         _local_1["1,1"] = 103;
         this._model.qualityDic = _local_1;
      }
      
      private function __chickActivationHandler(_arg_1:CrazyTankSocketEvent) : void
      {
         var _local_3:PackageIn = _arg_1.pkg;
         var _local_2:int = _local_3.readInt();
         if(_local_2 == 1)
         {
            this.loginDataUpdate(_local_3);
         }
         else if(_local_2 == 2)
         {
            this.dataUpdate(_local_3);
         }
      }
      
      private function loginDataUpdate(_arg_1:PackageIn) : void
      {
         var _local_3:int = 0;
         this.model.isKeyOpened = _arg_1.readInt();
         this.model.keyIndex = _arg_1.readInt();
         this.model.keyOpenedTime = _arg_1.readDate();
         this.model.keyOpenedType = _arg_1.readInt();
         var _local_2:Array = [];
         _local_3 = 0;
         while(_local_3 < 12)
         {
            _local_2.push(_arg_1.readInt());
            _local_3++;
         }
         this.model.gainArr = _local_2;
         this.model.dataChange("updateData");
      }
      
      private function dataUpdate(_arg_1:PackageIn) : void
      {
         var _local_5:int = 0;
         var _local_2:int = 0;
         this.model.isKeyOpened = _arg_1.readInt();
         this.model.keyIndex = _arg_1.readInt();
         this.model.keyOpenedTime = _arg_1.readDate();
         this.model.keyOpenedType = _arg_1.readInt();
         var _local_4:Array = [];
         _local_5 = 0;
         while(_local_5 < 12)
         {
            _local_4.push(_arg_1.readInt());
            _local_5++;
         }
         var _local_3:int = -1;
         if(this.model.gainArr.length == 12)
         {
            _local_2 = 0;
            while(_local_2 < this.model.gainArr.length - 1)
            {
               if(this.model.gainArr[_local_2] != _local_4[_local_2] && _local_4[_local_2] > 0)
               {
                  _local_3 = _local_2;
                  break;
               }
               _local_2++;
            }
            if(_local_3 != -1)
            {
               this.model.dataChange("getReward",_local_3);
            }
         }
         this.model.gainArr = _local_4;
         this.model.dataChange("updateData");
      }
      
      public function templateDataSetup(_arg_1:Array) : void
      {
         this.model.itemInfoList = _arg_1;
      }
      
      public function checkShowIcon() : void
      {
         this.model.isOpen = ServerConfigManager.instance.chickActivationIsOpen;
         HallIconManager.instance.updateSwitchHandler("chickActivation",this.model.isOpen);
      }
      
      public function showFrame() : void
      {
         var _local_1:* = null;
         if(Boolean(this.model.itemInfoList))
         {
            UIModuleSmallLoading.Instance.progress = 0;
            UIModuleSmallLoading.Instance.show();
            UIModuleLoader.Instance.addEventListener("uiModuleComplete",this.loadCompleteHandler);
            UIModuleLoader.Instance.addEventListener("uiMoudleProgress",this.onUimoduleLoadProgress);
            UIModuleLoader.Instance.addUIModuleImp("chickActivation");
         }
         else
         {
            _local_1 = LoaderCreate.Instance.createActivitySystemItemsLoader();
            _local_1.addEventListener("complete",this.__dataLoaderCompleteHandler);
            LoadResourceManager.Instance.startLoad(_local_1);
         }
      }
      
      private function __dataLoaderCompleteHandler(_arg_1:LoaderEvent) : void
      {
         var _local_2:BaseLoader = _arg_1.loader;
         _local_2.removeEventListener("complete",this.__dataLoaderCompleteHandler);
         UIModuleSmallLoading.Instance.progress = 0;
         UIModuleSmallLoading.Instance.show();
         UIModuleLoader.Instance.addEventListener("uiModuleComplete",this.loadCompleteHandler);
         UIModuleLoader.Instance.addEventListener("uiMoudleProgress",this.onUimoduleLoadProgress);
         UIModuleLoader.Instance.addUIModuleImp("chickActivation");
      }
      
      private function onUimoduleLoadProgress(_arg_1:UIModuleEvent) : void
      {
         if(_arg_1.module == "chickActivation")
         {
            UIModuleSmallLoading.Instance.progress = _arg_1.loader.progress * 100;
         }
      }
      
      private function loadCompleteHandler(_arg_1:UIModuleEvent) : void
      {
         if(_arg_1.module == "chickActivation")
         {
            UIModuleSmallLoading.Instance.hide();
            UIModuleLoader.Instance.removeEventListener("uiModuleComplete",this.loadCompleteHandler);
            UIModuleLoader.Instance.removeEventListener("uiMoudleProgress",this.onUimoduleLoadProgress);
            dispatchEvent(new Event("ChickActivationShowFrame"));
            SocketManager.Instance.out.sendChickActivationQuery();
         }
      }
   }
}

