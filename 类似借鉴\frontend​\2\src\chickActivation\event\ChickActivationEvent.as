package chickActivation.event
{
   import flash.events.Event;
   
   public class ChickActivationEvent extends Event
   {
      
      public static const UPDATE_DATA:String = "updateData";
      
      public static const CLICK_LEVELPACKS:String = "clickLevelPacks";
      
      public static const GET_REWARD:String = "getReward";
      
      public var resultData:Object;
      
      public function ChickActivationEvent(_arg_1:String, _arg_2:Object = null, _arg_3:<PERSON><PERSON><PERSON> = false, _arg_4:<PERSON><PERSON><PERSON> = false)
      {
         this.resultData = _arg_2;
         super(_arg_1,_arg_3,_arg_4);
      }
   }
}

