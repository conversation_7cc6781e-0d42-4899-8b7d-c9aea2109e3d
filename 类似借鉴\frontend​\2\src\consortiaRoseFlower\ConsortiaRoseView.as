package consortiaRoseFlower
{
   import com.pickgliss.toplevel.StageReferance;
   import com.pickgliss.ui.ComponentFactory;
   import flash.display.BitmapData;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.geom.Point;
   
   public class ConsortiaRoseView extends Sprite
   {
      
      private var _resVec:Vector.<BitmapData>;
      
      private var _roseRow1Vec:Vector.<ConsortiaRose>;
      
      private var _roseRow2Vec:Vector.<ConsortiaRose>;
      
      private var _roseRow3Vec:Vector.<ConsortiaRose>;
      
      private var _row1StepPt:Vector.<Point>;
      
      private var _row2StepPt:Vector.<Point>;
      
      private var _row3StepPt:Vector.<Point>;
      
      private var _frameRate:Number;
      
      private var _totalFrames:Number;
      
      private var _row1Number:Number;
      
      private var _row2Number:Number;
      
      private var _row3Number:Number;
      
      private var _speed1:Number;
      
      private var _speed2:Number;
      
      private var _speed3:Number;
      
      private var _scale1:Number;
      
      private var _scale2:Number;
      
      private var _scale3:Number;
      
      private var _count:Number = 0;
      
      public function ConsortiaRoseView()
      {
         var _local_3:int = 0;
         var _local_4:* = undefined;
         super();
         this.mouseChildren = false;
         this.mouseEnabled = false;
         var _local_2:ConsortiaRoseConfig = ComponentFactory.Instance.creatCustomObject("consortia.rose.config");
         this._frameRate = _local_2.frameRate;
         this._totalFrames = _local_2.totalFrames;
         this._row1Number = _local_2.row1Number;
         this._row2Number = _local_2.row2Number;
         this._row3Number = _local_2.row3Number;
         this._speed1 = _local_2.speed1;
         this._speed2 = _local_2.speed2;
         this._speed3 = _local_2.speed3;
         this._scale1 = _local_2.scale1;
         this._scale2 = _local_2.scale2;
         this._scale3 = _local_2.scale3;
         var _local_1:* = 0;
         this._resVec = new Vector.<BitmapData>();
         _local_3 = 0;
         while(_local_3 < this._totalFrames)
         {
            this._resVec[_local_3] = ComponentFactory.Instance.creatBitmapData("ast.rose.f" + _local_3.toString());
            _local_3++;
         }
         this._roseRow3Vec = new Vector.<ConsortiaRose>();
         _local_3 = 0;
         while(_local_3 < this._row3Number)
         {
            _local_1 = int(Math.random() * this._totalFrames);
            this._roseRow3Vec[_local_3] = new ConsortiaRose(this._resVec[_local_1]);
            this._roseRow3Vec[_local_3].rotation = Math.random() * 360;
            this._roseRow3Vec[_local_3].curFrame = _local_1;
            _local_4 = this._scale3;
            this._roseRow3Vec[_local_3].scaleY = _local_4;
            this._roseRow3Vec[_local_3].scaleX = _local_4;
            this._roseRow3Vec[_local_3].alpha = 1;
            this._roseRow3Vec[_local_3].x = Math.random() * 860 + 70;
            this._roseRow3Vec[_local_3].y = -730 * Math.random();
            addChild(this._roseRow3Vec[_local_3]);
            _local_3++;
         }
         this._row3StepPt = new Vector.<Point>();
         _local_3 = 0;
         while(_local_3 < this._row3Number)
         {
            this._row3StepPt[_local_3] = new Point(0,this._speed3);
            _local_3++;
         }
         this._roseRow2Vec = new Vector.<ConsortiaRose>();
         _local_3 = 0;
         while(_local_3 < this._row2Number)
         {
            _local_1 = int(Math.random() * this._totalFrames);
            this._roseRow2Vec[_local_3] = new ConsortiaRose(this._resVec[_local_1]);
            this._roseRow2Vec[_local_3].rotation = Math.random() * 360;
            this._roseRow2Vec[_local_3].curFrame = _local_1;
            _local_4 = this._scale2;
            this._roseRow2Vec[_local_3].scaleY = _local_4;
            this._roseRow2Vec[_local_3].scaleX = _local_4;
            this._roseRow2Vec[_local_3].alpha = 1;
            this._roseRow2Vec[_local_3].x = Math.random() * 860 + 70;
            this._roseRow2Vec[_local_3].y = -730 * Math.random();
            addChild(this._roseRow2Vec[_local_3]);
            _local_3++;
         }
         this._row2StepPt = new Vector.<Point>();
         _local_3 = 0;
         while(_local_3 < this._row2Number)
         {
            this._row2StepPt[_local_3] = new Point(int(Math.random() * 3 - 1.5),this._speed2);
            _local_3++;
         }
         this._roseRow1Vec = new Vector.<ConsortiaRose>();
         _local_3 = 0;
         while(_local_3 < this._row1Number)
         {
            _local_1 = int(Math.random() * this._totalFrames);
            this._roseRow1Vec[_local_3] = new ConsortiaRose(this._resVec[_local_1]);
            this._roseRow1Vec[_local_3].rotation = Math.random() * 360;
            this._roseRow1Vec[_local_3].curFrame = _local_1;
            _local_4 = this._scale1;
            this._roseRow1Vec[_local_3].scaleY = _local_4;
            this._roseRow1Vec[_local_3].scaleX = _local_4;
            this._roseRow1Vec[_local_3].alpha = 1;
            this._roseRow1Vec[_local_3].x = Math.random() * 860 + 70;
            this._roseRow1Vec[_local_3].y = -730 * Math.random();
            addChild(this._roseRow1Vec[_local_3]);
            _local_3++;
         }
         this._row1StepPt = new Vector.<Point>();
         _local_3 = 0;
         while(_local_3 < this._row1Number)
         {
            this._row1StepPt[_local_3] = new Point(int(Math.random() * 4 - 2),this._speed1);
            _local_3++;
         }
      }
      
      protected function onEF(_arg_1:Event) : void
      {
         var _local_3:int = 0;
         var _local_4:int = 0;
         var _local_2:* = null;
         ++this._count;
         this._count %= 2;
         if(this._count == 0)
         {
            return;
         }
         _local_3 = int(this._roseRow1Vec.length);
         _local_4 = 0;
         while(_local_4 < _local_3)
         {
            _local_2 = this._roseRow1Vec[_local_4];
            ++_local_2.curFrame;
            _local_2.curFrame %= this._totalFrames;
            _local_2.bitmapData = this._resVec[_local_2.curFrame];
            _local_2.x += this._row1StepPt[_local_4].x;
            _local_2.y += this._row1StepPt[_local_4].y;
            if(_local_2.y > 600)
            {
               _local_2.y = -150;
               _local_2.rotation = Math.random() * 180;
            }
            if(_local_2.x < -150 || _local_2.y > 1150)
            {
               _local_2.x = Math.random() * 860 + 70;
            }
            _local_4++;
         }
         _local_3 = int(this._roseRow2Vec.length);
         _local_4 = 0;
         while(_local_4 < _local_3)
         {
            _local_2 = this._roseRow2Vec[_local_4];
            ++_local_2.curFrame;
            _local_2.curFrame %= this._totalFrames;
            _local_2.bitmapData = this._resVec[_local_2.curFrame];
            _local_2.x += this._row2StepPt[_local_4].x;
            _local_2.y += this._row2StepPt[_local_4].y;
            if(_local_2.y > 600)
            {
               _local_2.y = -150;
               _local_2.rotation = Math.random() * 180;
            }
            if(_local_2.x < -150 || _local_2.y > 1150)
            {
               _local_2.x = Math.random() * 860 + 70;
            }
            _local_4++;
         }
         _local_3 = int(this._roseRow3Vec.length);
         _local_4 = 0;
         while(_local_4 < _local_3)
         {
            _local_2 = this._roseRow3Vec[_local_4];
            ++_local_2.curFrame;
            _local_2.curFrame %= this._totalFrames;
            _local_2.bitmapData = this._resVec[_local_2.curFrame];
            _local_2.x += this._row3StepPt[_local_4].x;
            _local_2.y += this._row3StepPt[_local_4].y;
            if(_local_2.y > 600)
            {
               _local_2.y = -150;
               _local_2.rotation = Math.random() * 180;
            }
            if(_local_2.x < -150 || _local_2.y > 1150)
            {
               _local_2.x = Math.random() * 860 + 70;
            }
            _local_4++;
         }
      }
      
      public function startFly() : void
      {
         this._frameRate = StageReferance.stage.frameRate;
         StageReferance.stage.frameRate = 24;
         StageReferance.stage.addEventListener("enterFrame",this.onEF);
      }
      
      public function stopFly() : void
      {
         StageReferance.stage.frameRate = this._frameRate;
         StageReferance.stage.removeEventListener("enterFrame",this.onEF);
      }
   }
}

