package activity.adventure.data
{
   public class AdventureModel
   {
      
      public static const nearSpeed:int = 10;
      
      public static const gridSpeed:Number = 0.5;
      
      public static const nearMoveStep:int = 20;
      
      private var _isLockAction:Boolean;
      
      public var totalStep:int = 5;
      
      public var curLevel:int = 1;
      
      public var openLevel:int = 3;
      
      public var gameLevel:int = 1;
      
      public var gridStep:int;
      
      public var gateEventList:Vector.<AdventureEventInfo>;
      
      public var eventList:Vector.<AdventureEventInfo>;
      
      public var thisWeakExploreArr:Array = [];
      
      public var lastWeakExploreArr:Array = [];
      
      public var rankArr:Array = [[],[],[]];
      
      public var turnCount:int = 0;
      
      public var hasBuyCount:int = 0;
      
      public var curEvent:AdventureEventInfo;
      
      public var treasureBoxList:Array;
      
      public var shopList:Vector.<AdventureShopItemInfo>;
      
      public var resetCount:int;
      
      public function AdventureModel()
      {
         super();
         this.gateEventList = new Vector.<AdventureEventInfo>();
         this.treasureBoxList = [];
      }
      
      public function clearEvent() : void
      {
         while(Boolean(this.gateEventList.length))
         {
            this.gateEventList.shift();
         }
      }
      
      public function clearBox() : void
      {
         while(Boolean(this.treasureBoxList.length))
         {
            this.treasureBoxList.shift();
         }
      }
      
      public function get isLockAction() : Boolean
      {
         return this._isLockAction;
      }
      
      public function getEventItemConfig(_arg_1:int) : AdventureEventInfo
      {
         var _local_3:int = 0;
         var _local_2:int = int(this.eventList.length);
         _local_3 = 0;
         while(_local_3 < _local_2)
         {
            if(this.eventList[_local_3].ID == _arg_1)
            {
               return this.eventList[_local_3];
            }
            _local_3++;
         }
         return null;
      }
      
      public function getEventInfoByBoxStr(_arg_1:String) : AdventureEventInfo
      {
         var _local_6:int = 0;
         var _local_2:Array = _arg_1.split("_");
         var _local_5:int = int(_local_2[0]);
         var _local_4:int = int(_local_2[1]);
         var _local_3:int = int(this.eventList.length);
         _local_6 = 0;
         while(_local_6 < _local_3)
         {
            if(this.eventList[_local_6].EventType == _local_5 && this.eventList[_local_6].EventValue == _local_4)
            {
               return this.eventList[_local_6];
            }
            _local_6++;
         }
         return null;
      }
      
      public function getShopArrByType(_arg_1:int) : Array
      {
         var _local_3:int = 0;
         var _local_2:Array = [];
         _local_3 = 0;
         while(_local_3 < this.shopList.length)
         {
            if(this.shopList[_local_3].GroupId == _arg_1)
            {
               _local_2.push(this.shopList[_local_3]);
            }
            _local_3++;
         }
         return _local_2;
      }
      
      public function set isLockAction(_arg_1:Boolean) : void
      {
         this._isLockAction = _arg_1;
      }
   }
}

