package consortion.analyze
{
   import com.pickgliss.loader.DataAnalyzer;
   import com.pickgliss.utils.ObjectUtils;
   import consortion.data.ConsortiaWeekRewardPlayerVo;
   
   public class ConsortiaRichRankAnalyze extends DataAnalyzer
   {
      
      private var _dataList:Array;
      
      public function ConsortiaRichRankAnalyze(_arg_1:Function)
      {
         super(_arg_1);
      }
      
      public function get dataList() : Array
      {
         return this._dataList;
      }
      
      override public function analyze(_arg_1:*) : void
      {
         var _local_5:int = 0;
         var _local_3:* = null;
         var _local_4:* = null;
         this._dataList = [];
         var _local_2:XML = new XML(_arg_1);
         if(_local_2.@value == "true")
         {
            _local_3 = _local_2.Item;
            _local_5 = 0;
            while(_local_5 < _local_3.length())
            {
               _local_4 = new ConsortiaWeekRewardPlayerVo();
               ObjectUtils.copyPorpertiesByXML(_local_4,_local_3[_local_5]);
               this._dataList.push(_local_4);
               _local_5++;
            }
            this._dataList.sortOn("Rank",16);
            onAnalyzeComplete();
         }
         else
         {
            message = _local_2.@message;
            onAnalyzeComplete();
            onAnalyzeError();
         }
      }
   }
}

