package bagAndInfo.ddtKingGrade
{
   import com.pickgliss.loader.DataAnalyzer;
   import com.pickgliss.utils.ObjectUtils;
   import road7th.data.DictionaryData;
   
   public class DDTKingGradeAnalyzer extends DataAnalyzer
   {
      
      private var _data:DictionaryData;
      
      public function DDTKingGradeAnalyzer(_arg_1:Function)
      {
         super(_arg_1);
      }
      
      override public function analyze(_arg_1:*) : void
      {
         var _local_4:int = 0;
         var _local_3:* = null;
         var _local_5:* = null;
         var _local_2:XML = new XML(_arg_1);
         if(_local_2.@value == "true")
         {
            this._data = new DictionaryData();
            _local_3 = _local_2..Item;
            _local_5 = new DDTKingGradeInfo();
            this._data.add(_local_5.Level,_local_5);
            _local_4 = 0;
            while(_local_4 < _local_3.length())
            {
               _local_5 = new DDTKingGradeInfo();
               ObjectUtils.copyPorpertiesByXML(_local_5,_local_3[_local_4]);
               this._data.add(_local_5.Level,_local_5);
               _local_4++;
            }
            onAnalyzeComplete();
         }
         else
         {
            message = _local_2.@message;
            onAnalyzeError();
            onAnalyzeError();
         }
      }
      
      public function get data() : DictionaryData
      {
         return this._data;
      }
   }
}

