package activity.firstkill.data
{
   public class FirstKillDungeonInfo
   {
      
      public var CopyName:String;
      
      public var MissionID:int;
      
      public var BossID:int;
      
      public var TemplateIDA:int;
      
      public var ValidDateA:int;
      
      public var CountA:int;
      
      public var TemplateIDB:int;
      
      public var ValidDateB:int;
      
      public var CountB:int;
      
      public var TemplateIDC:int;
      
      public var ValidDateC:int;
      
      public var CountC:int;
      
      public function FirstKillDungeonInfo()
      {
         super();
      }
      
      public function get key() : String
      {
         return this.MissionID + "_" + this.BossID;
      }
      
      public function get rewardList() : Array
      {
         return [this.getRewardObject(this.TemplateIDA,this.ValidDateA,this.CountA),this.getRewardObject(this.TemplateIDB,this.ValidDateB,this.CountB),this.getRewardObject(this.TemplateIDC,this.ValidDateC,this.CountC)];
      }
      
      private function getRewardObject(_arg_1:int, _arg_2:int, _arg_3:int) : Object
      {
         return {
            "template":_arg_1,
            "validDate":_arg_2,
            "count":_arg_3
         };
      }
   }
}

