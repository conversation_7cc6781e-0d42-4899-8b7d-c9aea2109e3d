package consortion.analyze
{
   import com.pickgliss.loader.DataAnalyzer;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.data.ConsortiaInfo;
   
   public class ConsortionListAnalyzer extends DataAnalyzer
   {
      
      public var consortionList:Vector.<ConsortiaInfo>;
      
      public var consortionsTotalCount:int;
      
      public function ConsortionListAnalyzer(_arg_1:Function)
      {
         super(_arg_1);
      }
      
      override public function analyze(_arg_1:*) : void
      {
         var _local_5:int = 0;
         var _local_3:* = null;
         var _local_4:* = null;
         this.consortionList = new Vector.<ConsortiaInfo>();
         var _local_2:XML = new XML(_arg_1);
         if(_local_2.@value == "true")
         {
            this.consortionsTotalCount = int(_local_2.@total);
            _local_3 = _local_2..Item;
            _local_5 = 0;
            while(_local_5 < _local_3.length())
            {
               _local_4 = new ConsortiaInfo();
               _local_4.beginChanges();
               ObjectUtils.copyPorpertiesByXML(_local_4,_local_3[_local_5]);
               _local_4.commitChanges();
               this.consortionList.push(_local_4);
               _local_5++;
            }
            onAnalyzeComplete();
         }
         else
         {
            message = _local_2.@message;
            onAnalyzeError();
            onAnalyzeComplete();
         }
      }
   }
}

