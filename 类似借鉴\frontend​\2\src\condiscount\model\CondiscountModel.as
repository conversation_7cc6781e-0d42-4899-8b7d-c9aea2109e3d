package condiscount.model
{
   import flash.events.EventDispatcher;
   import flash.events.IEventDispatcher;
   
   public class CondiscountModel extends EventDispatcher
   {
      
      public var giftbagArray:Array;
      
      public var beginTime:String;
      
      public var endTime:String;
      
      public var actId:String;
      
      public var isOpen:Boolean;
      
      public function CondiscountModel(_arg_1:IEventDispatcher = null)
      {
         super(_arg_1);
      }
   }
}

