package church.view
{
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.controls.alert.BaseAlerFrame;
   import com.pickgliss.ui.text.FilterFrameText;
   import com.pickgliss.ui.vo.AlertInfo;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.manager.LanguageMgr;
   
   public class ChurchAlertFrame extends BaseAlerFrame
   {
      
      private var _txt:FilterFrameText;
      
      public function ChurchAlertFrame()
      {
         super();
         var _local_2:String = LanguageMgr.GetTranslation("ddt.church.divorcePayment.titleName");
         var _local_1:AlertInfo = new AlertInfo(_local_2,LanguageMgr.GetTranslation("shop.PresentFrame.OkBtnText"),LanguageMgr.GetTranslation("shop.PresentFrame.CancelBtnText"));
         info = _local_1;
         this._txt = ComponentFactory.Instance.creatComponentByStylename("FrameTitleTextStyle");
         this._txt.autoSize = "none";
         this._txt.width = 300;
         this._txt.height = 150;
         this._txt.x = 48;
         this._txt.y = 48;
         this._txt.wordWrap = true;
         this._txt.multiline = true;
         addToContent(this._txt);
      }
      
      public function setTxt(_arg_1:String) : void
      {
         this._txt.text = _arg_1;
      }
      
      override public function dispose() : void
      {
         ObjectUtils.disposeObject(this._txt);
         this._txt = null;
         super.dispose();
      }
   }
}

