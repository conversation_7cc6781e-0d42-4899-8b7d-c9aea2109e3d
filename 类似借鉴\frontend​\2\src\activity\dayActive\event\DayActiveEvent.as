package activity.dayActive.event
{
   import flash.events.Event;
   
   public class DayActiveEvent extends Event
   {
      
      public static const UPDATE_LIST:String = "updatelist";
      
      public static const UPDATE_ITME:String = "updateitemo";
      
      public static const CLOSE:String = "close";
      
      public static const UPDATE_SHOPLIST:String = "updateshop";
      
      public static const UPDATE_SCORE:String = "updatescore";
      
      private var _data:Object;
      
      public function DayActiveEvent(_arg_1:String, _arg_2:Object = null)
      {
         this._data = _arg_2;
         super(_arg_1);
      }
      
      public function get data() : Object
      {
         return this._data;
      }
   }
}

