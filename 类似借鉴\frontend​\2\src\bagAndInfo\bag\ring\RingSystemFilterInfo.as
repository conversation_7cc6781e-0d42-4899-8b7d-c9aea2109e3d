package bagAndInfo.bag.ring
{
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.core.Component;
   import com.pickgliss.ui.text.FilterFrameText;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.manager.LanguageMgr;
   
   public class RingSystemFilterInfo extends Component
   {
      
      private var _info:FilterFrameText;
      
      private var _index:int;
      
      public function RingSystemFilterInfo(_arg_1:int)
      {
         super();
         this._index = _arg_1;
         tipStyle = "ddt.view.tips.OneLineTip";
         tipDirctions = "2,3,5,1";
         tipGapV = 4;
         this._info = ComponentFactory.Instance.creatComponentByStylename("bagAndInfo.bag.RingSystemView.fightData");
         addChild(this._info);
         this._info.setFrame(_arg_1);
      }
      
      public function setInfoText(_arg_1:Object) : void
      {
         this._info.text = _arg_1.info + LanguageMgr.GetTranslation(this._index == 1 ? "ddt.vip.PrivilegeViewItem.TimesUnit" : "ddt.vip.PrivilegeViewItem.Times");
         tipData = _arg_1.tipData;
         this.width = this._info.width;
         this.height = this._info.height;
      }
      
      override public function dispose() : void
      {
         super.dispose();
         ObjectUtils.disposeObject(this._info);
         this._info = null;
      }
   }
}

