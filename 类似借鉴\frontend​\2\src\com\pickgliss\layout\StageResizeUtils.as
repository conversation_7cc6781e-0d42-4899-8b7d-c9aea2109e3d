package com.pickgliss.layout
{
   import com.pickgliss.toplevel.StageReferance;
   import com.pickgliss.ui.ComponentFactory;
   import flash.display.DisplayObject;
   import flash.events.Event;
   
   public class StageResizeUtils
   {
      
      private static var _instance:StageResizeUtils;
      
      private var _autoResizeDic:Array = [];
      
      private var isOpen:Boolean = true;
      
      private var stageWidth:Number;
      
      private var stageHeight:Number;
      
      public function StageResizeUtils()
      {
         super();
      }
      
      public static function get Instance() : StageResizeUtils
      {
         if(_instance == null)
         {
            _instance = new StageResizeUtils();
         }
         return _instance;
      }
      
      public function setup() : void
      {
         if(!this.isOpen)
         {
            return;
         }
         StageReferance.stage.scaleMode = "noScale";
         StageReferance.stage.align = "TL";
         StageReferance.stage.addEventListener("resize",this.__onResize);
         this.stageWidth = StageReferance.stageWidth;
         this.stageHeight = StageReferance.stageHeight;
      }
      
      private function __onResize(_arg_1:Event) : void
      {
         this.stageWidth = StageReferance.stage.stageWidth;
         this.stageHeight = StageReferance.stage.stageHeight;
         this.autoResizeClear();
         this.autoResize();
      }
      
      public function addAutoResize(_arg_1:DisplayObject, _arg_2:Boolean = true) : void
      {
         if(!this.isOpen)
         {
            return;
         }
         this.autoResizeClear();
         var _local_5:ResizeObjInfo = new ResizeObjInfo();
         _local_5.target = _arg_1;
         _local_5.customStyleName = "";
         _local_5.isPercent = true;
         _local_5.isPStage = _arg_2;
         _local_5.targetWidth = _arg_1.width;
         _local_5.targetHeight = _arg_1.height;
         var _local_3:* = 0;
         var _local_4:* = 0;
         if(_arg_2)
         {
            _local_3 = StageReferance.defaultWidth;
            _local_4 = StageReferance.defaultHeight;
         }
         else
         {
            _local_3 = _arg_1.parent.width;
            _local_4 = _arg_1.parent.height;
         }
         _local_5.xPercent = _arg_1.x / ((_local_3 - _local_5.targetWidth) / 2);
         _local_5.yPercent = _arg_1.y / ((_local_4 - _local_5.targetHeight) / 2);
         this.addResizeObjInfo(_local_5);
      }
      
      public function addAutoResizeByStyle(_arg_1:DisplayObject, _arg_2:String, _arg_3:Boolean = true) : void
      {
         if(!this.isOpen)
         {
            return;
         }
         this.removeResizeObj(_arg_1);
         this.autoResizeClear();
         var _local_4:ResizeObjInfo = new ResizeObjInfo();
         _local_4.target = _arg_1;
         _local_4.customStyleName = _arg_2;
         _local_4.isPercent = false;
         _local_4.isPStage = _arg_3;
         _local_4.targetWidth = _arg_1.width;
         _local_4.targetHeight = _arg_1.height;
         this.addResizeObjInfo(_local_4);
      }
      
      public function autoCenterResizeByStyle(_arg_1:DisplayObject, _arg_2:Boolean = true) : void
      {
         if(!this.isOpen)
         {
            return;
         }
         this.addAutoResizeByStyle(_arg_1,"core.boxlayoutInfo.CMAlign",_arg_2);
      }
      
      private function removeAutoResize(_arg_1:String) : void
      {
         this._autoResizeDic.splice(_arg_1,1);
      }
      
      private function autoResize() : void
      {
         var _local_2:String = null;
         var _local_1:* = null;
         for(_local_2 in this._autoResizeDic)
         {
            _local_1 = this._autoResizeDic[_local_2] as ResizeObjInfo;
            if(_local_1.target == null || _local_1.target.parent == null)
            {
               this.removeAutoResize(_local_2);
            }
            else
            {
               this.resizeTarget(_local_1);
            }
         }
      }
      
      private function resizeTarget(_arg_1:ResizeObjInfo) : void
      {
         var _local_6:Number = NaN;
         var _local_7:Number = NaN;
         var _local_5:* = null;
         var _local_13:DisplayObject = _arg_1.target;
         if(_local_13.parent == null)
         {
            return;
         }
         var _local_2:String = _arg_1.customStyleName;
         var _local_3:Boolean = _arg_1.isPercent;
         var _local_9:Boolean = _arg_1.isPStage;
         var _local_15:Number = _arg_1.xPercent;
         var _local_8:Number = _arg_1.yPercent;
         var _local_4:Number = _arg_1.targetWidth;
         var _local_10:Number = _arg_1.targetHeight;
         var _local_11:* = 0;
         var _local_14:* = 0;
         if(_local_9)
         {
            _local_11 = this.stageWidth;
            _local_14 = this.stageHeight;
         }
         else
         {
            _local_11 = _local_13.parent.width;
            _local_14 = _local_13.parent.height;
         }
         var _local_12:Array = [];
         if(_local_3)
         {
            _local_12[0] = (_local_11 - _local_4) / 2 * _local_15;
            _local_12[1] = (_local_14 - _local_10) / 2 * _local_8;
         }
         else
         {
            _local_5 = ComponentFactory.Instance.creatCustomObject(_local_2);
            _local_6 = this.convertPercent(_local_5.horizontalX,_local_4);
            _local_7 = this.convertPercent(_local_5.verticalY,_local_10);
            if(_local_5.horizontalAlign == "left")
            {
               _local_12[0] = _local_6 + 0;
            }
            else if(_local_5.horizontalAlign == "center")
            {
               _local_12[0] = _local_6 + (_local_11 - _local_4) / 2;
            }
            else if(_local_5.horizontalAlign == "right")
            {
               _local_12[0] = _local_6 + (_local_11 - _local_4);
            }
            if(_local_5.verticalAlign == "top")
            {
               _local_12[1] = _local_7 + 0;
            }
            else if(_local_5.verticalAlign == "middle")
            {
               _local_12[1] = _local_7 + (_local_14 - _local_10) / 2;
            }
            else if(_local_5.verticalAlign == "bottom")
            {
               _local_12[1] = _local_7 + (_local_14 - _local_10);
            }
         }
         _local_13.x = _local_12[0];
         _local_13.y = _local_12[1];
      }
      
      private function convertPercent(_arg_1:String, _arg_2:Number) : Number
      {
         var _local_5:Number = NaN;
         if(_arg_1 == null || _arg_1 == "")
         {
            return 0;
         }
         var _local_4:int = int(_arg_1.indexOf("%"));
         var _local_3:* = 0;
         if(_local_4 == -1)
         {
            _local_3 = Number(_arg_1);
         }
         else
         {
            _local_5 = Number(_arg_1.substring(0,_local_4));
            _local_3 = _arg_2 * (_local_5 / 100);
         }
         return _local_3;
      }
      
      private function autoResizeClear() : void
      {
         var _local_2:String = null;
         var _local_1:* = null;
         for(_local_2 in this._autoResizeDic)
         {
            _local_1 = this._autoResizeDic[_local_2]["target"];
            if(_local_1 == null || _local_1.parent == null)
            {
               this.removeAutoResize(_local_2);
            }
         }
      }
      
      private function addResizeObjInfo(_arg_1:ResizeObjInfo) : void
      {
         var _local_2:String = this.targetKey(_arg_1.target);
         if(_local_2 == "")
         {
            _local_2 = "" + this.getIndex();
         }
         this._autoResizeDic[_local_2] = _arg_1;
         if(StageReferance.isStageResize())
         {
            this.resizeTarget(_arg_1);
         }
      }
      
      private function getIndex() : int
      {
         return this._autoResizeDic.length;
      }
      
      private function targetKey(_arg_1:DisplayObject) : String
      {
         var _local_3:String = null;
         var _local_2:* = null;
         for(_local_3 in this._autoResizeDic)
         {
            _local_2 = this._autoResizeDic[_local_3]["target"];
            if(_arg_1 == _local_2)
            {
               return _local_3;
            }
         }
         return "";
      }
      
      public function removeResizeObj(_arg_1:DisplayObject) : void
      {
         if(_arg_1 == null)
         {
            return;
         }
         var _local_2:String = this.targetKey(_arg_1);
         if(_local_2 != "")
         {
            this.removeAutoResize(_local_2);
         }
      }
   }
}

import flash.display.DisplayObject;

class ResizeObjInfo
{
   
   public var target:DisplayObject;
   
   public var customStyleName:String;
   
   public var isPercent:Boolean;
   
   public var xPercent:Number = 1;
   
   public var yPercent:Number = 1;
   
   public var isPStage:Boolean;
   
   public var targetWidth:Number = 0;
   
   public var targetHeight:Number = 0;
   
   public function ResizeObjInfo()
   {
      super();
   }
}
