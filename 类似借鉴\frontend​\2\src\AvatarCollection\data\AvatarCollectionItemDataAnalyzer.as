package AvatarCollection.data
{
   import com.pickgliss.loader.DataAnalyzer;
   import road7th.data.DictionaryData;
   
   public class AvatarCollectionItemDataAnalyzer extends DataAnalyzer
   {
      
      private var _maleItemDic:DictionaryData;
      
      private var _femaleItemDic:DictionaryData;
      
      private var _weaponItemDic:DictionaryData;
      
      private var _maleItemList:Vector.<AvatarCollectionItemVo>;
      
      private var _femaleItemList:Vector.<AvatarCollectionItemVo>;
      
      private var _weaponItemList:Vector.<AvatarCollectionItemVo>;
      
      private var _allGoodsTemplateIDlist:DictionaryData;
      
      private var _realItemIdDic:DictionaryData;
      
      public function AvatarCollectionItemDataAnalyzer(_arg_1:Function)
      {
         super(_arg_1);
      }
      
      override public function analyze(_arg_1:*) : void
      {
         var index:int = 0;
         var listInfo:* = undefined;
         var _local_5:int = 0;
         var _local_20:int = 0;
         var _local_3:AvatarCollectionItemVo = null;
         var _local_6:int = 0;
         var _local_4:Array = null;
         var _local_2:XML = new XML(_arg_1);
         this._maleItemDic = new DictionaryData();
         this._femaleItemDic = new DictionaryData();
         this._weaponItemDic = new DictionaryData();
         this._allGoodsTemplateIDlist = new DictionaryData();
         this._maleItemList = new Vector.<AvatarCollectionItemVo>();
         this._femaleItemList = new Vector.<AvatarCollectionItemVo>();
         this._weaponItemList = new Vector.<AvatarCollectionItemVo>();
         this._realItemIdDic = new DictionaryData();
         if(_local_2.@value == "true")
         {
            listInfo = _local_2..Item;
            index = 0;
            while(index < listInfo.length())
            {
               _local_3 = new AvatarCollectionItemVo();
               _local_3.id = listInfo[index].@ID;
               _local_3.itemId = listInfo[index].@TemplateID;
               _local_3.proArea = listInfo[index].@Description;
               _local_3.needGold = listInfo[index].@Cost;
               _local_3.sex = listInfo[index].@Sex;
               _local_3.Type = listInfo[index].@Type;
               _local_3.OtherTemplateID = listInfo[index].@OtherTemplateID;
               _local_6 = _local_3.id;
               if(_local_3.Type == 1)
               {
                  if(_local_3.sex == 1)
                  {
                     if(!this._maleItemDic[_local_6])
                     {
                        this._maleItemDic[_local_6] = new DictionaryData();
                     }
                     this._maleItemDic[_local_6].add(_local_3.itemId,_local_3);
                     this._maleItemList.push(_local_3);
                  }
                  else
                  {
                     if(!this._femaleItemDic[_local_6])
                     {
                        this._femaleItemDic[_local_6] = new DictionaryData();
                     }
                     this._femaleItemDic[_local_6].add(_local_3.itemId,_local_3);
                     this._femaleItemList.push(_local_3);
                  }
               }
               else
               {
                  if(!this._weaponItemDic[_local_6])
                  {
                     this._weaponItemDic[_local_6] = new DictionaryData();
                  }
                  this._weaponItemDic[_local_6].add(_local_3.itemId,_local_3);
                  this._weaponItemList.push(_local_3);
               }
               this._allGoodsTemplateIDlist[_local_3.itemId] = true;
               _local_4 = _local_3.OtherTemplateID == "" ? [] : _local_3.OtherTemplateID.split("|");
               _local_5 = 0;
               while(_local_5 < _local_4.length)
               {
                  _local_20 = int(_local_4[_local_5]);
                  if(_local_20 != 0)
                  {
                     this._realItemIdDic.add(_local_20,_local_3.itemId);
                  }
                  _local_5++;
               }
               this._realItemIdDic.add(_local_3.itemId,_local_3.itemId);
               index++;
            }
            onAnalyzeComplete();
         }
         else
         {
            message = _local_2.@message;
            onAnalyzeError();
         }
      }
      
      public function get weaponItemDic() : DictionaryData
      {
         return this._weaponItemDic;
      }
      
      public function get maleItemDic() : DictionaryData
      {
         return this._maleItemDic;
      }
      
      public function get femaleItemDic() : DictionaryData
      {
         return this._femaleItemDic;
      }
      
      public function get maleItemList() : Vector.<AvatarCollectionItemVo>
      {
         return this._maleItemList;
      }
      
      public function get femaleItemList() : Vector.<AvatarCollectionItemVo>
      {
         return this._femaleItemList;
      }
      
      public function get weaponItemList() : Vector.<AvatarCollectionItemVo>
      {
         return this._weaponItemList;
      }
      
      public function get allGoodsTemplateIDlist() : DictionaryData
      {
         return this._allGoodsTemplateIDlist;
      }
      
      public function get realItemIdDic() : DictionaryData
      {
         return this._realItemIdDic;
      }
   }
}

