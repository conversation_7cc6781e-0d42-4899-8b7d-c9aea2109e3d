package com.pickgliss.utils
{
   import bones.display.BoneMovieFastStarling;
   import bones.display.IBoneMovie;
   import com.pickgliss.ui.core.Disposeable;
   import starling.display.DisplayObject;
   import starling.display.DisplayObjectContainer;
   import starling.display.Image;
   import starling.textures.Texture;
   
   public class StarlingObjectUtils
   {
      
      public function StarlingObjectUtils()
      {
         super();
      }
      
      public static function disposeObject(_arg_1:Object, _arg_2:Bo<PERSON>an = false) : void
      {
         var _local_5:* = null;
         var _local_4:* = null;
         var _local_3:* = null;
         if(_arg_1 == null)
         {
            return;
         }
         if(_arg_1 is Image)
         {
            _local_5 = _arg_1 as Image;
            if(<PERSON><PERSON><PERSON>(_local_5.parent))
            {
               _local_5.parent.removeChild(_local_5);
            }
            if(_arg_2)
            {
               _local_5.texture.dispose();
            }
            _local_5.dispose();
         }
         else if(_arg_1 is DisplayObject)
         {
            _local_4 = _arg_1 as DisplayObject;
            if(<PERSON><PERSON><PERSON>(_local_4.parent))
            {
               _local_4.parent.removeChild(_local_4);
            }
            _local_4.dispose();
         }
         else if(_arg_1 is Texture)
         {
            _local_3 = _arg_1 as Texture;
            _local_3.dispose();
         }
         else if(_arg_1 is Disposeable)
         {
            Disposeable(_arg_1).dispose();
         }
      }
      
      public static function disposeAllChildren(_arg_1:DisplayObjectContainer, _arg_2:Boolean = false) : void
      {
         var _local_3:* = null;
         if(_arg_1 == null)
         {
            return;
         }
         while(_arg_1.numChildren > 0)
         {
            _local_3 = _arg_1.getChildAt(0);
            disposeObject(_local_3,_arg_2);
         }
      }
      
      public static function removeObject(_arg_1:DisplayObject) : void
      {
         if(_arg_1 == null)
         {
            return;
         }
         if(_arg_1 is IBoneMovie)
         {
            (_arg_1 as IBoneMovie).stop();
         }
         if(_arg_1 is BoneMovieFastStarling)
         {
            (_arg_1 as BoneMovieFastStarling).stop();
         }
         if(Boolean(_arg_1.parent))
         {
            _arg_1.parent.removeChild(_arg_1);
         }
      }
      
      public static function removeChildAllChildren(_arg_1:DisplayObjectContainer, _arg_2:Boolean = true) : void
      {
         if(_arg_1 == null)
         {
            return;
         }
         while(_arg_1.numChildren > 0)
         {
            _arg_1.removeChildAt(0,_arg_2);
         }
      }
   }
}

