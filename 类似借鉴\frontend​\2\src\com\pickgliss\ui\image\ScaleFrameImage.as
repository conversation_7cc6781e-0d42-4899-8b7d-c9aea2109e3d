package com.pickgliss.ui.image
{
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.utils.ObjectUtils;
   import com.pickgliss.utils.StringUtils;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   
   public class ScaleFrameImage extends Image
   {
      
      public static const P_fillAlphaRect:String = "fillAlphaRect";
      
      internal var _currentFrame:uint;
      
      private var _fillAlphaRect:Boolean;
      
      private var _imageLinks:Array;
      
      private var _images:Vector.<DisplayObject>;
      
      public function ScaleFrameImage()
      {
         super();
      }
      
      override public function dispose() : void
      {
         this.removeImages();
         this._images = null;
         this._imageLinks = null;
         super.dispose();
      }
      
      public function set fillAlphaRect(_arg_1:Boolean) : void
      {
         if(this._fillAlphaRect == _arg_1)
         {
            return;
         }
         this._fillAlphaRect = _arg_1;
         onPropertiesChanged("fillAlphaRect");
      }
      
      public function get getFrame() : uint
      {
         return this._currentFrame;
      }
      
      override public function setFrame(_arg_1:int) : void
      {
         var _local_2:int = 0;
         super.setFrame(_arg_1);
         this._currentFrame = _arg_1;
         _local_2 = 0;
         while(_local_2 < this._images.length)
         {
            if(this._images[_local_2] != null)
            {
               if(_arg_1 - 1 == _local_2)
               {
                  addChild(this._images[_local_2]);
                  if(this._images[_local_2] is MovieImage)
                  {
                     ((this._images[_local_2] as MovieImage).display as MovieClip).gotoAndPlay(1);
                  }
                  if(_width != Math.round(this._images[_local_2].width))
                  {
                     _width = Math.round(this._images[_local_2].width);
                     _changedPropeties["width"] = true;
                  }
               }
               else if(Boolean(this._images[_local_2]) && Boolean(this._images[_local_2].parent))
               {
                  removeChild(this._images[_local_2]);
               }
            }
            _local_2++;
         }
         this.fillRect();
      }
      
      internal function fillRect() : void
      {
         if(this._fillAlphaRect)
         {
            graphics.beginFill(16711935,0);
            graphics.drawRect(0,0,_width,_height);
            graphics.endFill();
         }
      }
      
      override protected function init() : void
      {
         super.init();
      }
      
      override protected function resetDisplay() : void
      {
         this._imageLinks = ComponentFactory.parasArgs(_resourceLink);
         this.removeImages();
         this.fillImages();
         this.creatFrameImage(0);
      }
      
      override protected function updateSize() : void
      {
         var _local_1:int = 0;
         if(this._images == null)
         {
            return;
         }
         if(Boolean(_changedPropeties["width"]) || Boolean(_changedPropeties["height"]))
         {
            _local_1 = 0;
            while(_local_1 < this._images.length)
            {
               if(this._images[_local_1] != null)
               {
                  this._images[_local_1].width = _width;
                  this._images[_local_1].height = _height;
               }
               _local_1++;
            }
         }
      }
      
      private function fillImages() : void
      {
         var _local_1:int = 0;
         this._images = new Vector.<DisplayObject>();
         _local_1 = 0;
         while(_local_1 < this._imageLinks.length)
         {
            this._images.push(null);
            _local_1++;
         }
      }
      
      public function creatFrameImage(_arg_1:int) : void
      {
         var _local_3:int = 0;
         var _local_2:* = undefined;
         _local_2 = null;
         _local_3 = 0;
         while(_local_3 < this._imageLinks.length)
         {
            if(!StringUtils.isEmpty(this._imageLinks[_local_3]) && this._images[_local_3] == null)
            {
               try
               {
                  _local_2 = ComponentFactory.Instance.creat(this._imageLinks[_local_3]);
                  _width = Math.max(_width,_local_2.width);
                  _height = Math.max(_height,_local_2.height);
                  this._images[_local_3] = _local_2;
                  addChild(_local_2);
               }
               catch(e:*)
               {
               }
            }
            _local_3++;
         }
      }
      
      public function getFrameImage(_arg_1:int) : DisplayObject
      {
         return this._images[_arg_1];
      }
      
      private function removeImages() : void
      {
         var _local_1:int = 0;
         if(this._images == null)
         {
            return;
         }
         _local_1 = 0;
         while(_local_1 < this._images.length)
         {
            ObjectUtils.disposeObject(this._images[_local_1]);
            _local_1++;
         }
      }
      
      public function get totalFrames() : int
      {
         return this._images.length;
      }
   }
}

