using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EntityDatabase.PlayerModels
{
	// Token: 0x020000A7 RID: 167
	public class Sys_Users_RelicInfo
	{
		// Token: 0x1700063C RID: 1596
		// (get) Token: 0x06000D21 RID: 3361 RVA: 0x00009313 File Offset: 0x00007513
		// (set) Token: 0x06000D22 RID: 3362 RVA: 0x0000931B File Offset: 0x0000751B
		[Key]
		[DatabaseGenerated(DatabaseGeneratedOption.Identity)]
		public int ID { get; set; }

		// Token: 0x1700063D RID: 1597
		// (get) Token: 0x06000D23 RID: 3363 RVA: 0x00009324 File Offset: 0x00007524
		// (set) Token: 0x06000D24 RID: 3364 RVA: 0x0000932C File Offset: 0x0000752C
		public int UserID { get; set; }

		// Token: 0x1700063E RID: 1598
		// (get) Token: 0x06000D25 RID: 3365 RVA: 0x00009335 File Offset: 0x00007535
		// (set) Token: 0x06000D26 RID: 3366 RVA: 0x0000933D File Offset: 0x0000753D
		public int RelicScore { get; set; }

		// Token: 0x1700063F RID: 1599
		// (get) Token: 0x06000D27 RID: 3367 RVA: 0x00009346 File Offset: 0x00007546
		// (set) Token: 0x06000D28 RID: 3368 RVA: 0x0000934E File Offset: 0x0000754E
		public int RelicNorMalCount { get; set; }

		// Token: 0x17000640 RID: 1600
		// (get) Token: 0x06000D29 RID: 3369 RVA: 0x00009357 File Offset: 0x00007557
		// (set) Token: 0x06000D2A RID: 3370 RVA: 0x0000935F File Offset: 0x0000755F
		public int RelicRareCount { get; set; }

		// Token: 0x17000641 RID: 1601
		// (get) Token: 0x06000D2B RID: 3371 RVA: 0x00009368 File Offset: 0x00007568
		// (set) Token: 0x06000D2C RID: 3372 RVA: 0x00009370 File Offset: 0x00007570
		public int RelicHoleIndex { get; set; }

		// Token: 0x17000642 RID: 1602
		// (get) Token: 0x06000D2D RID: 3373 RVA: 0x00009379 File Offset: 0x00007579
		// (set) Token: 0x06000D2E RID: 3374 RVA: 0x00009381 File Offset: 0x00007581
		public string RelicHoleArr { get; set; }

		// Token: 0x17000643 RID: 1603
		// (get) Token: 0x06000D2F RID: 3375 RVA: 0x0000938A File Offset: 0x0000758A
		// (set) Token: 0x06000D30 RID: 3376 RVA: 0x00009392 File Offset: 0x00007592
		public string RelicSuitArr { get; set; }

		// Token: 0x17000644 RID: 1604
		// (get) Token: 0x06000D31 RID: 3377 RVA: 0x0000939B File Offset: 0x0000759B
		// (set) Token: 0x06000D32 RID: 3378 RVA: 0x000093A3 File Offset: 0x000075A3
		public string ManualInfo { get; set; }
	}
}
