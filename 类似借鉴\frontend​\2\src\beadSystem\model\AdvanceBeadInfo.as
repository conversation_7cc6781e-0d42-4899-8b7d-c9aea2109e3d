package beadSystem.model
{
   import flash.events.EventDispatcher;
   import road7th.data.DictionaryData;
   
   public class AdvanceBeadInfo extends EventDispatcher
   {
      
      public var advancedTempId:int;
      
      public var runeName:String;
      
      public var advanceDesc:String;
      
      public var mainMaterials:String;
      
      public var auxiliaryMaterials:String;
      
      public var quality:int;
      
      public var maxLevelTempRunId:int;
      
      public function AdvanceBeadInfo()
      {
         super();
      }
      
      public function getAllBead() : Array
      {
         var _local_3:int = 0;
         var _local_1:Array = [];
         if(this.mainMaterials != "")
         {
            _local_1 = _local_1.concat(this.mainMaterials.split("|"));
         }
         if(this.auxiliaryMaterials != "")
         {
            _local_1 = _local_1.concat(this.auxiliaryMaterials.split("|"));
         }
         var _local_2:DictionaryData = new DictionaryData();
         _local_3 = 0;
         while(_local_3 < _local_1.length)
         {
            _local_2.add(_local_1[_local_3],_local_1[_local_3]);
            _local_3++;
         }
         return _local_2.list;
      }
      
      public function verificationMaterials(_arg_1:int, _arg_2:int) : Boolean
      {
         var _local_3:Boolean = false;
         var _local_4:int = -1;
         switch(_arg_2)
         {
            case 0:
               _local_4 = int(this.mainMaterials.indexOf(_arg_1.toString()));
               break;
            case 1:
               _local_4 = int(this.auxiliaryMaterials.indexOf(_arg_1.toString()));
         }
         return _local_4 >= 0;
      }
   }
}

