package consortion.view.selfConsortia.consortiaTask
{
   public class ConsortiaTaskInfo
   {
      
      public var itemList:Vector.<Object>;
      
      public var exp:int;
      
      public var offer:int;
      
      public var riches:int;
      
      public var buffID:int;
      
      public var beginTime:Date;
      
      public var time:int;
      
      public var contribution:int;
      
      public var level:int;
      
      private var sortKey:Array = [3,4,6,1,5,2];
      
      public function ConsortiaTaskInfo()
      {
         super();
         this.itemList = new Vector.<Object>();
      }
      
      public function addItemData(_arg_1:int, _arg_2:String, _arg_3:int = 0, _arg_4:Number = 0, _arg_5:int = 0, _arg_6:int = 0) : void
      {
         var _local_7:Object = {};
         _local_7["id"] = _arg_1;
         _local_7["taskType"] = _arg_3;
         _local_7["content"] = _arg_2;
         _local_7["currenValue"] = _arg_4;
         _local_7["targetValue"] = _arg_5;
         _local_7["finishValue"] = _arg_6;
         this.itemList.push(_local_7);
      }
      
      public function sortItem() : void
      {
         var _local_2:int = 0;
         var _local_1:Object = null;
         var _local_3:Vector.<Object> = new Vector.<Object>();
         while(_local_2 < this.sortKey.length)
         {
            for each(_local_1 in this.itemList)
            {
               if(this.sortKey[_local_2] == _local_1["taskType"])
               {
                  _local_3.push(_local_1);
               }
            }
            _local_2++;
         }
         this.itemList = _local_3;
      }
      
      public function updateItemData(_arg_1:int, _arg_2:Number = 0, _arg_3:int = 0) : void
      {
         var _local_4:Object = null;
         for each(_local_4 in this.itemList)
         {
            if(_local_4["id"] == _arg_1)
            {
               _local_4["currenValue"] = _arg_2;
               _local_4["finishValue"] = _arg_3;
            }
         }
      }
   }
}

