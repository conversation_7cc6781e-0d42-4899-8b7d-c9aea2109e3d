package com.pickgliss.loader
{
   import flash.display.Shape;
   import flash.events.Event;
   import flash.events.EventDispatcher;
   import flash.events.NetStatusEvent;
   import flash.net.SharedObject;
   import flash.utils.ByteArray;
   import flash.utils.Timer;
   import flash.utils.getTimer;
   
   public class LoaderSavingManager extends EventDispatcher
   {
      
      private static var _version:int;
      
      private static var _files:Object;
      
      private static var _saveTimer:Timer;
      
      private static var _so:SharedObject;
      
      private static var _changed:Boolean;
      
      private static var _save:Array;
      
      private static const LOCAL_FILE:String = "7road/files";
      
      private static const READ_ERROR_ID:int = 2030;
      
      private static var _cacheFile:Boolean = false;
      
      public static var ReadShareError:Boolean = false;
      
      private static var _isSaving:Boolean = false;
      
      private static var _retryCount:int = 0;
      
      public static var eventDispatcher:EventDispatcher = new EventDispatcher();
      
      private static const _reg1:RegExp = /http:\/\/[\w|.|:]+\//i;
      
      private static const _reg2:RegExp = /[:|.|\/]/g;
      
      private static var _shape:Shape = new Shape();
      
      public function LoaderSavingManager()
      {
         super();
      }
      
      public static function get Version() : int
      {
         return _version;
      }
      
      public static function set Version(_arg_1:int) : void
      {
         _version = _arg_1;
      }
      
      public static function set cacheAble(_arg_1:Boolean) : void
      {
         _cacheFile = _arg_1;
      }
      
      public static function get cacheAble() : Boolean
      {
         return _cacheFile;
      }
      
      public static function setup() : void
      {
         _cacheFile = false;
         _save = [];
         loadFilesInLocal();
      }
      
      public static function applyUpdate(_arg_1:int, _arg_2:int, _arg_3:Array) : void
      {
         var _local_6:String = null;
         var _local_4:String = null;
         var _local_8:String = null;
         var _local_7:* = null;
         var _local_5:* = null;
         var _local_9:* = null;
         if(_arg_2 <= _arg_1)
         {
            return;
         }
         if(_version < _arg_2)
         {
            if(_version < _arg_1)
            {
               _so.data["data"] = _files = {};
               LoadResourceManager.Instance.addDeleteRequest("*");
            }
            else
            {
               _local_7 = [];
               for each(_local_6 in _arg_3)
               {
                  _local_5 = getPath(_local_6);
                  _local_5 = _local_5.replace("*","\\w*");
                  _local_7.push(new RegExp(_local_5));
                  LoadResourceManager.Instance.addDeleteRequest(_local_6);
               }
               _local_9 = [];
               for(_local_4 in _files)
               {
                  _local_4 = _local_4.toLocaleLowerCase();
                  if(hasUpdate(_local_4,_local_7))
                  {
                     _local_9.push(_local_4);
                  }
               }
               for each(_local_8 in _local_9)
               {
                  delete _files[_local_8];
               }
            }
            _version = _arg_2;
            _files["version"] = _arg_2;
            _changed = true;
         }
      }
      
      public static function clearFiles(_arg_1:String) : void
      {
         var _local_2:String = null;
         var _local_5:String = null;
         var _local_4:* = null;
         var _local_3:* = null;
         var _local_6:* = null;
         if(Boolean(_files))
         {
            _local_4 = [];
            _local_3 = getPath(_arg_1);
            _local_3 = _local_3.replace("*","\\w*");
            _local_4.push(new RegExp(_local_3));
            _local_6 = [];
            for(_local_2 in _files)
            {
               _local_2 = _local_2.toLocaleLowerCase();
               if(hasUpdate(_local_2,_local_4))
               {
                  _local_6.push(_local_2);
               }
            }
            for each(_local_5 in _local_6)
            {
               delete _files[_local_5];
            }
            try
            {
               if(_cacheFile)
               {
                  _so.flush(20971520);
               }
            }
            catch(e:Error)
            {
            }
         }
      }
      
      public static function loadFilesInLocal() : void
      {
         try
         {
            _so = SharedObject.getLocal("7road/files","/");
            _so.addEventListener("netStatus",__netStatus);
            _files = _so.data["data"];
            if(_files == null)
            {
               _files = {};
               _so.data["data"] = _files;
               _files["version"] = _version = -1;
               _cacheFile = false;
            }
            else
            {
               _version = _files["version"];
               _cacheFile = true;
            }
         }
         catch(e:Error)
         {
            if(e.errorID == 2030)
            {
               resetErrorVersion();
            }
         }
      }
      
      public static function resetErrorVersion() : void
      {
         _version = Math.random() * -777777;
         ReadShareError = true;
      }
      
      private static function getPath(_arg_1:String) : String
      {
         _arg_1 = LoaderNameFilter.getRealFilePath(_arg_1);
         var _local_2:int = int(_arg_1.indexOf("?"));
         if(_local_2 != -1)
         {
            _arg_1 = _arg_1.substring(0,_local_2);
         }
         _arg_1 = _arg_1.replace(_reg1,"");
         return _arg_1.replace(_reg2,"-").toLocaleLowerCase();
      }
      
      public static function saveFilesToLocal() : void
      {
         try
         {
            if(_files && _changed && _cacheFile && !_isSaving)
            {
               _isSaving = true;
               _shape.addEventListener("enterFrame",save);
            }
         }
         catch(e:Error)
         {
         }
      }
      
      private static function save(_arg_1:Event) : void
      {
         var _local_2:int = 0;
         var _local_4:* = null;
         var _local_5:* = null;
         var _local_3:* = null;
         try
         {
            _local_4 = _so.flush(20971520);
            if(_local_4 != "pending")
            {
               _local_2 = getTimer();
               if(_save.length > 0)
               {
                  _local_5 = _save[0];
                  _local_3 = SharedObject.getLocal(_local_5.p,"/");
                  _local_3.data["data"] = _local_5.d;
                  _local_3.flush();
                  _files[_local_5.p] = true;
                  _so.flush();
                  _save.shift();
               }
               if(_save.length == 0)
               {
                  _shape.removeEventListener("enterFrame",save);
                  _changed = false;
                  _isSaving = false;
               }
            }
         }
         catch(e:Error)
         {
            _shape.removeEventListener("enterFrame",save);
         }
      }
      
      private static function hasUpdate(_arg_1:String, _arg_2:Array) : Boolean
      {
         var _local_3:RegExp = null;
         for each(_local_3 in _arg_2)
         {
            if(Boolean(_arg_1.match(_local_3)))
            {
               return true;
            }
         }
         return false;
      }
      
      public static function loadCachedFile(_arg_1:String, _arg_2:Boolean) : ByteArray
      {
         var _local_4:int = 0;
         var _local_6:* = null;
         var _local_3:* = null;
         var _local_5:* = null;
         if(Boolean(_files))
         {
            _local_6 = getPath(_arg_1);
            _local_4 = getTimer();
            _local_3 = findInSave(_local_6);
            if(_local_3 == null && Boolean(_files[_local_6]))
            {
               _local_5 = SharedObject.getLocal(_local_6,"/");
               _local_3 = ByteArray(_local_5.data["data"]);
            }
            if(_local_3)
            {
               trace("get{local:",getTimer() - _local_4,"ms}",_arg_1);
               return _local_3;
            }
         }
         trace("get{network}",_arg_1);
         return null;
      }
      
      private static function findInSave(_arg_1:String) : ByteArray
      {
         var _local_2:Object = null;
         for each(_local_2 in _save)
         {
            if(_local_2.p == _arg_1)
            {
               return ByteArray(_local_2.d);
            }
         }
         return null;
      }
      
      public static function cacheFile(_arg_1:String, _arg_2:ByteArray, _arg_3:Boolean) : void
      {
         var _local_4:* = null;
         if(!LoadResourceManager.Instance.isMicroClient && Boolean(_files))
         {
            _local_4 = getPath(_arg_1);
            _save.push({
               "p":_local_4,
               "d":_arg_2
            });
            _changed = true;
         }
      }
      
      private static function __netStatus(_arg_1:NetStatusEvent) : void
      {
         trace(_arg_1.info.code);
         switch(_arg_1.info.code)
         {
            case "SharedObject.Flush.Failed":
               if(_retryCount < 1)
               {
                  _so.flush(20971520);
                  ++_retryCount;
               }
               else
               {
                  cacheAble = false;
               }
               return;
            default:
               _retryCount = 0;
               return;
         }
      }
      
      public static function updateList(_arg_1:XMLList) : void
      {
         var _local_2:XML = null;
         for each(_local_2 in _arg_1)
         {
            parseUpdate(_local_2);
         }
         LoadResourceManager.Instance.addEventListener("delete",__deleteComplete);
         LoadResourceManager.Instance.startDelete();
      }
      
      protected static function __deleteComplete(_arg_1:Event) : void
      {
         eventDispatcher.dispatchEvent(new Event("complete"));
      }
      
      public static function parseUpdate(_arg_1:XML) : void
      {
         var _local_2:int = 0;
         var _local_8:int = 0;
         var _local_3:XML = null;
         var _local_7:XML = null;
         var _local_6:* = null;
         var _local_4:* = null;
         var _local_5:* = null;
         try
         {
            _local_6 = _arg_1..version;
            for each(_local_3 in _local_6)
            {
               _local_2 = int(_local_3.@to);
               if(_version <= _local_2)
               {
                  _local_8 = int(_local_3.@from);
                  _local_4 = _local_3..file;
                  _local_5 = [];
                  for each(_local_7 in _local_4)
                  {
                     _local_5.push(String(_local_7.@value));
                  }
                  applyUpdate(_local_8,_local_2,_local_5);
               }
            }
         }
         catch(e:Error)
         {
            _version = -1;
            if(Boolean(_so))
            {
               _so.data["data"] = _files = {};
            }
            LoadResourceManager.Instance.addDeleteRequest("*");
            _changed = true;
         }
         saveFilesToLocal();
      }
      
      public static function get hasFileToSave() : Boolean
      {
         return _cacheFile && _changed;
      }
      
      public static function clearAllCache() : void
      {
         var _local_4:String = null;
         var _local_3:* = null;
         var _local_2:* = null;
         if(LoadResourceManager.Instance.isMicroClient)
         {
            LoadResourceManager.Instance.deleteResource("*");
         }
         if(!_so)
         {
            return;
         }
         var _local_1:Array = [];
         for(_local_4 in _files)
         {
            if(_local_4 != "version")
            {
               _local_4 = _local_4.toLocaleLowerCase();
               _local_1.push(_local_4);
               delete _files[_local_4];
            }
         }
         while(_local_3 = _local_1.pop())
         {
            _local_2 = SharedObject.getLocal(_local_3,"/");
            _local_2.data["data"] = {};
            _local_2.flush();
         }
         _version = -1;
         _so.data["data"] = _files = {};
         _so["version"] = -1;
         _so.flush();
      }
   }
}

