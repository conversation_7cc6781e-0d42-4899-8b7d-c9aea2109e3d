package condiscount.view
{
   import com.pickgliss.events.FrameEvent;
   import com.pickgliss.ui.AlertManager;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.controls.BaseButton;
   import com.pickgliss.ui.controls.alert.BaseAlerFrame;
   import com.pickgliss.ui.core.Component;
   import com.pickgliss.ui.core.Disposeable;
   import com.pickgliss.ui.text.FilterFrameText;
   import com.pickgliss.utils.ClassUtils;
   import com.pickgliss.utils.ObjectUtils;
   import condiscount.CondiscountManager;
   import ddt.manager.ItemManager;
   import ddt.manager.LanguageMgr;
   import ddt.manager.LeavePageManager;
   import ddt.manager.MessageTipManager;
   import ddt.manager.PlayerManager;
   import ddt.manager.SocketManager;
   import ddt.utils.CheckMoneyUtils;
   import ddt.utils.PositionUtils;
   import flash.display.Bitmap;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import wonderfulActivity.data.GiftBagInfo;
   import wonderfulActivity.data.GiftChildInfo;
   import wonderfulActivity.data.SendGiftInfo;
   
   public class CondiscountItem extends Sprite implements Disposeable
   {
      
      private var _bg:Bitmap;
      
      private var _normalPriceTxt:FilterFrameText;
      
      private var _disCountPriceTxt:FilterFrameText;
      
      private var buyBtn:BaseButton;
      
      private var buyIcon:Bitmap;
      
      private var _info:GiftBagInfo;
      
      private var icon:Component;
      
      private var _goodsIcon:MovieClip;
      
      private var arrow:MovieClip;
      
      private var _type:String;
      
      private var frame:BaseAlerFrame;
      
      public function CondiscountItem()
      {
         super();
         this.init();
      }
      
      public function get info() : GiftBagInfo
      {
         return this._info;
      }
      
      private function init() : void
      {
         this._bg = ComponentFactory.Instance.creatBitmap("condiscount.view.item.bg");
         addChild(this._bg);
         this.icon = new Component();
         addChild(this.icon);
         this.icon.tipStyle = "ddt.view.tips.WordWrapLineTip";
         this._goodsIcon = ClassUtils.CreatInstance("condiscount.view.goods.icon");
         this._goodsIcon.gotoAndStop(1);
         PositionUtils.setPos(this._goodsIcon,"condiscount.view.goods.icon.pos");
         this.icon.addChild(this._goodsIcon);
         this.icon.tipDirctions = "7,5";
         PositionUtils.setPos(this.icon,"condiscount.view.goods.icon.component.pos");
         this._normalPriceTxt = ComponentFactory.Instance.creatComponentByStylename("condiscount.item.normalPriceText");
         addChild(this._normalPriceTxt);
         this._disCountPriceTxt = ComponentFactory.Instance.creatComponentByStylename("condiscount.item.discountPriceText");
         addChild(this._disCountPriceTxt);
         this.buyBtn = ComponentFactory.Instance.creatComponentByStylename("condiscount.view.buyBtn");
         addChild(this.buyBtn);
         this.buyBtn.addEventListener("click",this.clickHandler);
         this.buyIcon = ComponentFactory.Instance.creatBitmap("condiscount.view.buyIcon");
         addChild(this.buyIcon);
         this.buyIcon.visible = false;
         this.arrow = ClassUtils.CreatInstance("condiscount.view.arrow");
         this.arrow.gotoAndStop(1);
         PositionUtils.setPos(this.arrow,"condiscount.view.arrow.pos");
         addChild(this.arrow);
      }
      
      private function clickHandler(_arg_1:MouseEvent) : void
      {
         var _local_2:String = this._info.giftConditionArr[0].remain2.split("|")[0];
         if(_local_2 == "-1")
         {
            this.frame = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("tips"),LanguageMgr.GetTranslation("ddt.condiscount.item.buyalart",this._info.giftConditionArr[0].conditionValue),LanguageMgr.GetTranslation("ok"),LanguageMgr.GetTranslation("cancel"),true,true,true,2,null,"SimpleAlert",60,false,1);
            this.frame.addEventListener("response",this.onBuyConfirmResponse);
         }
         else if(_local_2 == "-8")
         {
            this.frame = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("tips"),LanguageMgr.GetTranslation("ddt.condiscount.item.buyalart",this._info.giftConditionArr[0].conditionValue),LanguageMgr.GetTranslation("ok"),LanguageMgr.GetTranslation("cancel"),true,true,true,2,null,"SimpleAlert",60,false);
            this.frame.isBand = false;
            this.frame.addEventListener("response",this.onBuyConfirmResponse);
         }
         else if(_local_2 == "-9")
         {
            this.frame = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("tips"),LanguageMgr.GetTranslation("ddt.condiscount.item.buyalart.band",this._info.giftConditionArr[0].conditionValue),LanguageMgr.GetTranslation("ok"),LanguageMgr.GetTranslation("cancel"),true,true,true,2,null,"SimpleAlert",60,false);
            this.frame.isBand = true;
            this.frame.addEventListener("response",this.onBuyConfirmResponse);
         }
      }
      
      private function onBuyConfirmResponse(_arg_1:FrameEvent) : void
      {
         this.frame.removeEventListener("response",this.onBuyConfirmResponse);
         if(_arg_1.responseCode == 2 || _arg_1.responseCode == 3)
         {
            if(this._type == "-1")
            {
               CheckMoneyUtils.instance.checkMoney(this.frame.isBand,this._info.giftConditionArr[0].conditionValue,this.onCheckComplete);
               return;
            }
            if(this.frame.isBand && PlayerManager.Instance.Self.BandMoney < this._info.giftConditionArr[0].conditionValue)
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("bindMoneyPoorNote"));
               return;
            }
            if(!this.frame.isBand && PlayerManager.Instance.Self.Money < this._info.giftConditionArr[0].conditionValue)
            {
               LeavePageManager.showFillFrame();
               return;
            }
            this.onCheckComplete();
         }
      }
      
      protected function onCheckComplete() : void
      {
         var _local_4:Vector.<SendGiftInfo> = new Vector.<SendGiftInfo>();
         var _local_2:SendGiftInfo = new SendGiftInfo();
         _local_2.activityId = CondiscountManager.instance.model.actId;
         var _local_3:Array = [];
         var _local_1:GiftChildInfo = new GiftChildInfo();
         _local_1.giftId = this._info.giftbagId;
         if(this._type == "-1")
         {
            _local_1.index = CheckMoneyUtils.instance.isBind ? -9 : -8;
         }
         else
         {
            _local_1.index = this.frame.isBand ? -9 : -8;
         }
         _local_3.push(_local_1);
         _local_2.giftIdArr = _local_3;
         _local_4.push(_local_2);
         SocketManager.Instance.out.sendWonderfulActivityGetReward(_local_4);
      }
      
      public function setInfo(_arg_1:GiftBagInfo) : void
      {
         var _local_5:int = 0;
         var _local_4:* = null;
         var _local_2:* = null;
         this._info = _arg_1;
         this._goodsIcon.gotoAndStop(this._info.rewardMark + 1);
         var _local_3:String = "";
         _local_5 = 0;
         while(_local_5 < this._info.giftRewardArr.length)
         {
            _local_4 = ItemManager.Instance.getTemplateById(this._info.giftRewardArr[_local_5].templateId).beadName;
            _local_2 = "*" + this._info.giftRewardArr[_local_5].count;
            _local_3 += _local_4 + _local_2 + "\n";
            _local_5++;
         }
         this.icon.tipData = _local_3;
         this._normalPriceTxt.text = LanguageMgr.GetTranslation("ddt.condiscount.item.normalPrice",this._info.giftConditionArr[0].remain1);
         this._disCountPriceTxt.text = LanguageMgr.GetTranslation("ddt.condiscount.item.discountPrice",this._info.giftConditionArr[0].conditionValue);
         this._type = this._info.giftConditionArr[0].remain2.split("|")[0];
      }
      
      public function changeData(_arg_1:int) : void
      {
         this.buyBtn.enable = _arg_1 == 2;
         if(_arg_1 == 0)
         {
            this.arrow.gotoAndStop(1);
            this.buyIcon.visible = true;
         }
         else
         {
            this.arrow.gotoAndStop(2);
            this.buyIcon.visible = false;
         }
         if(this._info.giftbagOrder == 3)
         {
            this.arrow.visible = false;
         }
      }
      
      public function dispose() : void
      {
         ObjectUtils.disposeAllChildren(this);
         this._bg = null;
         this._normalPriceTxt = null;
         this._disCountPriceTxt = null;
         this.buyBtn = null;
         this.buyIcon = null;
      }
   }
}

