package com.greensock.plugins
{
   import com.greensock.*;
   import flash.display.*;
   import flash.geom.ColorTransform;
   
   public class ColorTransformPlugin extends TintPlugin
   {
      
      public static const API:Number = 1;
      
      public function ColorTransformPlugin()
      {
         super();
         this.propName = "colorTransform";
      }
      
      override public function onInitTween(_arg_1:Object, _arg_2:*, _arg_3:TweenLite) : Boolean
      {
         var _local_4:Number = NaN;
         var _local_5:String = null;
         var _local_8:* = undefined;
         var _local_7:* = undefined;
         if(!(_arg_1 is DisplayObject))
         {
            return false;
         }
         var _local_6:ColorTransform = _arg_1.transform.colorTransform;
         for(_local_5 in _arg_2)
         {
            if(_local_5 == "tint" || _local_5 == "color")
            {
               if(_arg_2[_local_5] != null)
               {
                  _local_6.color = _arg_2[_local_5];
               }
            }
            else if(!(_local_5 == "tintAmount" || _local_5 == "exposure" || _local_5 == "brightness"))
            {
               _local_6[_local_5] = _arg_2[_local_5];
            }
         }
         if(!isNaN(_arg_2.tintAmount))
         {
            _local_4 = _arg_2.tintAmount / (1 - (_local_6.redMultiplier + _local_6.greenMultiplier + _local_6.blueMultiplier) / 3);
            _local_6.redOffset *= _local_4;
            _local_6.greenOffset *= _local_4;
            _local_6.blueOffset *= _local_4;
            _local_8 = 1 - _arg_2.tintAmount;
            _local_6.blueMultiplier = _local_8;
            _local_7 = _local_8;
            _local_6.greenMultiplier = _local_7;
            _local_6.redMultiplier = _local_7;
         }
         else if(!isNaN(_arg_2.exposure))
         {
            _local_8 = 255 * (_arg_2.exposure - 1);
            _local_6.blueOffset = _local_8;
            _local_7 = _local_8;
            _local_6.greenOffset = _local_7;
            _local_6.redOffset = _local_7;
            _local_8 = 1;
            _local_6.blueMultiplier = _local_8;
            _local_7 = _local_8;
            _local_6.greenMultiplier = _local_7;
            _local_6.redMultiplier = _local_7;
         }
         else if(!isNaN(_arg_2.brightness))
         {
            _local_8 = Math.max(0,(_arg_2.brightness - 1) * 255);
            _local_6.blueOffset = _local_8;
            _local_7 = _local_8;
            _local_6.greenOffset = _local_7;
            _local_6.redOffset = _local_7;
            _local_8 = 1 - Math.abs(_arg_2.brightness - 1);
            _local_6.blueMultiplier = _local_8;
            _local_7 = _local_8;
            _local_6.greenMultiplier = _local_7;
            _local_6.redMultiplier = _local_7;
         }
         _ignoreAlpha = _arg_3.vars.alpha != undefined && _arg_2.alphaMultiplier == undefined;
         init(_arg_1 as DisplayObject,_local_6);
         return true;
      }
   }
}

