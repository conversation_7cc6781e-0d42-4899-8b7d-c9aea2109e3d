package com.pickgliss.loader
{
   import com.crypto.NewCrypto;
   import flash.display.Loader;
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.system.LoaderContext;
   import flash.utils.ByteArray;
   
   public class DisplayLoader extends BaseLoader
   {
      
      public static var Context:LoaderContext;
      
      public static var isDebug:Boolean = false;
      
      protected var _displayLoader:Loader = new Loader();
      
      public function DisplayLoader(_arg_1:int, _arg_2:String)
      {
         super(_arg_1,_arg_2,null);
      }
      
      override public function loadFromBytes(_arg_1:ByteArray) : void
      {
         var _local_2:ByteArray = NewCrypto.decry(_arg_1);
         super.loadFromBytes(_local_2);
         if(!this._displayLoader)
         {
            return;
         }
         this._displayLoader.contentLoaderInfo.addEventListener("complete",this.__onContentLoadComplete);
         this._displayLoader.contentLoaderInfo.addEventListener("ioError",this.__onDisplayIoError);
         if(this.type == 4 || this.type == 8)
         {
            this._displayLoader.loadBytes(_local_2,Context);
         }
         else
         {
            this._displayLoader.loadBytes(_local_2);
         }
      }
      
      protected function __onDisplayIoError(_arg_1:IOErrorEvent) : void
      {
         this._displayLoader.contentLoaderInfo.removeEventListener("ioError",this.__onDisplayIoError);
         throw new Error(_arg_1.text + " url: " + _url);
      }
      
      protected function __onContentLoadComplete(_arg_1:Event) : void
      {
         this._displayLoader.contentLoaderInfo.removeEventListener("complete",this.__onContentLoadComplete);
         this._displayLoader.contentLoaderInfo.removeEventListener("ioError",this.__onDisplayIoError);
         fireCompleteEvent();
         this._displayLoader.unload();
         this._displayLoader = null;
      }
      
      override protected function __onDataLoadComplete(_arg_1:Event) : void
      {
         removeEvent();
         unload();
         if(_loader.data.length == 0)
         {
            return;
         }
         var _local_2:ByteArray = _loader.data;
         LoaderSavingManager.cacheFile(_url,_local_2,true);
         this.loadFromBytes(_local_2);
      }
      
      override public function get content() : *
      {
         return this._displayLoader.content;
      }
      
      override protected function getLoadDataFormat() : String
      {
         return "binary";
      }
      
      override public function get type() : int
      {
         return 1;
      }
   }
}

