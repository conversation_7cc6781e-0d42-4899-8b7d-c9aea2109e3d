package braveDoor.data
{
   import flash.events.EventDispatcher;
   
   public class BraveDoorDuplicateInfo extends EventDispatcher
   {
      
      private var _mapUrl:String;
      
      private var _mapWidth:int;
      
      private var _mapHeight:int;
      
      private var _mapX:int;
      
      private var _mapY:int;
      
      private var _page:int;
      
      private var _duplicateInfo:Vector.<DuplicateInfo> = null;
      
      public function BraveDoorDuplicateInfo()
      {
         super();
         this._duplicateInfo = new Vector.<DuplicateInfo>();
      }
      
      public function get duplicateInfo() : Vector.<DuplicateInfo>
      {
         return this._duplicateInfo;
      }
      
      public function addDuplicateInfo(_arg_1:DuplicateInfo) : void
      {
         this._duplicateInfo.push(_arg_1);
      }
      
      public function getDunplicateInfoByDupID(_arg_1:int) : DuplicateInfo
      {
         var _local_2:DuplicateInfo = null;
         var _local_3:DuplicateInfo = null;
         if(this._duplicateInfo != null && this._duplicateInfo.length > 0)
         {
            for each(_local_3 in this._duplicateInfo)
            {
               if(_local_3.id == _arg_1)
               {
                  _local_2 = _local_3;
                  break;
               }
            }
         }
         return _local_2;
      }
      
      public function get page() : int
      {
         return this._page;
      }
      
      public function set page(_arg_1:int) : void
      {
         this._page = _arg_1;
      }
      
      public function get mapY() : int
      {
         return this._mapY;
      }
      
      public function set mapY(_arg_1:int) : void
      {
         this._mapY = _arg_1;
      }
      
      public function get mapX() : int
      {
         return this._mapX;
      }
      
      public function set mapX(_arg_1:int) : void
      {
         this._mapX = _arg_1;
      }
      
      public function get mapHeight() : int
      {
         return this._mapHeight;
      }
      
      public function set mapHeight(_arg_1:int) : void
      {
         this._mapHeight = _arg_1;
      }
      
      public function get mapWidth() : int
      {
         return this._mapWidth;
      }
      
      public function set mapWidth(_arg_1:int) : void
      {
         this._mapWidth = _arg_1;
      }
      
      public function get mapUrl() : String
      {
         return this._mapUrl;
      }
      
      public function set mapUrl(_arg_1:String) : void
      {
         this._mapUrl = _arg_1;
      }
   }
}

