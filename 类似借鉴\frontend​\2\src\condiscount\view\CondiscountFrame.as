package condiscount.view
{
   import com.pickgliss.events.FrameEvent;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.controls.BaseButton;
   import com.pickgliss.ui.controls.Frame;
   import com.pickgliss.ui.image.NumberImage;
   import com.pickgliss.ui.text.FilterFrameText;
   import com.pickgliss.utils.ObjectUtils;
   import condiscount.CondiscountManager;
   import ddt.manager.LanguageMgr;
   import ddt.manager.SoundManager;
   import ddt.utils.HelpFrameUtils;
   import ddt.utils.PositionUtils;
   import flash.display.Bitmap;
   import flash.display.Sprite;
   import wonderfulActivity.WonderfulActivityManager;
   import wonderfulActivity.data.GiftBagInfo;
   import wonderfulActivity.event.WonderfulActivityEvent;
   
   public class CondiscountFrame extends Frame
   {
      
      private var _timeTxt:FilterFrameText;
      
      private var _2dis:Bitmap;
      
      private var _3dis:Bitmap;
      
      private var _4dis:Bitmap;
      
      private var _5dis:Bitmap;
      
      private var itemList:Array;
      
      private var box:Sprite;
      
      private var _helpBtn:BaseButton;
      
      private var _dis1:int;
      
      private var _dis2:int;
      
      private var _dis3:int;
      
      private var _dis4:int;
      
      private var _disNumber1:NumberImage;
      
      private var _disNumber2:NumberImage;
      
      private var _disNumber3:NumberImage;
      
      private var _disNumber4:NumberImage;
      
      public function CondiscountFrame()
      {
         super();
         this.initView();
         this.addEvent();
      }
      
      private function initView() : void
      {
         var _local_5:int = 0;
         var _local_4:int = 0;
         var _local_1:int = 0;
         var _local_3:GiftBagInfo = null;
         var _local_2:* = null;
         this._timeTxt = ComponentFactory.Instance.creatComponentByStylename("condiscount.view.timeText");
         addToContent(this._timeTxt);
         this._timeTxt.text = LanguageMgr.GetTranslation("ddt.condiscount.view.time",CondiscountManager.instance.model.beginTime,CondiscountManager.instance.model.endTime);
         this._helpBtn = HelpFrameUtils.Instance.simpleHelpButton(this,"condiscount.view.helpbtn",{
            "x":743,
            "y":50
         },LanguageMgr.GetTranslation("store.view.HelpButtonText"),"condiscount.content.help",438,520);
         this._2dis = ComponentFactory.Instance.creatBitmap("condiscount.view.discount.bg");
         this._3dis = ComponentFactory.Instance.creatBitmap("condiscount.view.discount.bg");
         this._4dis = ComponentFactory.Instance.creatBitmap("condiscount.view.discount.bg");
         this._5dis = ComponentFactory.Instance.creatBitmap("condiscount.view.discount.bg");
         PositionUtils.setPos(this._2dis,"condiscount.view.discount.bg.pos1");
         PositionUtils.setPos(this._3dis,"condiscount.view.discount.bg.pos2");
         PositionUtils.setPos(this._4dis,"condiscount.view.discount.bg.pos3");
         PositionUtils.setPos(this._5dis,"condiscount.view.discount.bg.pos4");
         addToContent(this._2dis);
         addToContent(this._3dis);
         addToContent(this._4dis);
         addToContent(this._5dis);
         this._disNumber1 = ComponentFactory.Instance.creatComponentByStylename("condiscount.number");
         this._disNumber2 = ComponentFactory.Instance.creatComponentByStylename("condiscount.number");
         this._disNumber3 = ComponentFactory.Instance.creatComponentByStylename("condiscount.number");
         this._disNumber4 = ComponentFactory.Instance.creatComponentByStylename("condiscount.number");
         PositionUtils.setPos(this._disNumber1,"condiscount.view.discount.number.pos1");
         PositionUtils.setPos(this._disNumber2,"condiscount.view.discount.number.pos2");
         PositionUtils.setPos(this._disNumber3,"condiscount.view.discount.number.pos3");
         PositionUtils.setPos(this._disNumber4,"condiscount.view.discount.number.pos4");
         addToContent(this._disNumber1);
         addToContent(this._disNumber2);
         addToContent(this._disNumber3);
         addToContent(this._disNumber4);
         this.itemList = [];
         this.box = new Sprite();
         addToContent(this.box);
         _local_5 = 0;
         while(_local_5 < 4)
         {
            this.itemList[_local_5] = [];
            _local_4 = 0;
            while(_local_4 < 4)
            {
               _local_2 = new CondiscountItem();
               this.box.addChild(_local_2);
               for each(_local_3 in CondiscountManager.instance.model.giftbagArray)
               {
                  if(_local_3.rewardMark == _local_5 && _local_3.giftbagOrder == _local_4)
                  {
                     _local_2.setInfo(_local_3);
                     if(_local_3.rewardMark == _local_3.giftbagOrder)
                     {
                        _local_1 = int(_local_3.giftConditionArr[0].remain2.split("|")[1]);
                        switch(_local_5)
                        {
                           case 0:
                              this._disNumber1.count = _local_1;
                              break;
                           case 1:
                              this._disNumber2.count = _local_1;
                              break;
                           case 2:
                              this._disNumber3.count = _local_1;
                              break;
                           case 3:
                              this._disNumber4.count = _local_1;
                        }
                     }
                  }
               }
               this.itemList[_local_5].push(_local_2);
               _local_2.x = _local_5 * 171 + 112;
               _local_2.y = _local_4 * 109 + 112;
               _local_4++;
            }
            _local_5++;
         }
         this.setItemData();
      }
      
      private function setItemData() : void
      {
         var _local_4:int = 0;
         var _local_3:int = 0;
         var _local_1:GiftBagInfo = null;
         var _local_2:* = null;
         _local_4 = 0;
         while(_local_4 < 4)
         {
            _local_3 = 0;
            while(_local_3 < 4)
            {
               _local_2 = this.itemList[_local_4][_local_3];
               if(WonderfulActivityManager.Instance.activityInitData[_local_2.info.activityId].giftInfoDic[_local_2.info.giftbagId].times == 1)
               {
                  _local_2.changeData(0);
               }
               else if(_local_3 == 0)
               {
                  _local_2.changeData(2);
               }
               else
               {
                  for each(_local_1 in CondiscountManager.instance.model.giftbagArray)
                  {
                     if(_local_1.rewardMark == _local_4 && _local_1.giftbagOrder == _local_3 - 1)
                     {
                        if(WonderfulActivityManager.Instance.activityInitData[_local_1.activityId].giftInfoDic[_local_1.giftbagId].times == 1)
                        {
                           _local_2.changeData(2);
                        }
                        else
                        {
                           _local_2.changeData(1);
                        }
                     }
                  }
               }
               _local_3++;
            }
            _local_4++;
         }
      }
      
      private function removeEvent() : void
      {
         removeEventListener("response",this._responseHandle);
         WonderfulActivityManager.Instance.removeEventListener("refresh",this.refreshActivity);
      }
      
      private function addEvent() : void
      {
         addEventListener("response",this._responseHandle);
         WonderfulActivityManager.Instance.addEventListener("refresh",this.refreshActivity);
      }
      
      private function refreshActivity(_arg_1:WonderfulActivityEvent = null) : void
      {
         this.setItemData();
      }
      
      protected function _responseHandle(_arg_1:FrameEvent) : void
      {
         SoundManager.instance.play("008");
         switch(_arg_1.responseCode)
         {
            case 4:
               return;
            case 1:
               this.dispose();
               return;
            case 0:
               this.dispose();
         }
      }
      
      override public function dispose() : void
      {
         this.removeEvent();
         super.dispose();
         ObjectUtils.disposeObject(this._timeTxt);
         this._timeTxt = null;
         ObjectUtils.disposeObject(this._5dis);
         this._5dis = null;
         ObjectUtils.disposeObject(this._4dis);
         this._4dis = null;
         ObjectUtils.disposeObject(this._3dis);
         this._3dis = null;
         ObjectUtils.disposeObject(this._2dis);
         this._2dis = null;
         ObjectUtils.disposeObject(this.box);
         this.box = null;
         ObjectUtils.disposeObject(this._helpBtn);
         this._helpBtn = null;
         this.itemList = null;
      }
   }
}

