package beadSystem.model
{
   import beadSystem.controls.DrillItemInfo;
   import com.pickgliss.ui.ComponentFactory;
   import ddt.data.EquipType;
   import ddt.data.goods.InventoryItemInfo;
   import ddt.manager.PlayerManager;
   import ddt.manager.ServerConfigManager;
   import road7th.data.DictionaryData;
   import store.data.HoleExpModel;
   
   public class BeadModel
   {
      
      private static var _holeExpModel:HoleExpModel;
      
      public static var upgradeCellInfo:InventoryItemInfo;
      
      public static var _allBeadList:Vector.<InventoryItemInfo> = new Vector.<InventoryItemInfo>();
      
      public static var drillInfo:DictionaryData = new DictionaryData();
      
      public static var beadRequestBtnIndex:int = -1;
      
      public static var beadCanUpgrade:Boolean = false;
      
      public static var upgradeCellBeadLv:int = -1;
      
      public static var isHoleOpendComplete:Boolean = false;
      
      public static var tempHoleLv:int = -1;
      
      public static var _BeadCells:DictionaryData = new DictionaryData();
      
      public static var isBeadCellIsBind:Boolean = false;
      
      public function BeadModel()
      {
         super();
      }
      
      public static function getDrills() : DictionaryData
      {
         var _local_1:InventoryItemInfo = null;
         var _local_2:DictionaryData = new DictionaryData();
         for each(_local_1 in PlayerManager.Instance.Self.PropBag.items)
         {
            if(EquipType.isDrill(_local_1))
            {
               _local_2.add(_local_2.length,_local_1);
            }
         }
         return _local_2;
      }
      
      public static function getDrillsIgnoreBindState() : DictionaryData
      {
         var _local_2:InventoryItemInfo = null;
         var _local_3:* = null;
         var _local_1:DictionaryData = new DictionaryData();
         for each(_local_2 in PlayerManager.Instance.Self.PropBag.items)
         {
            if(EquipType.isDrill(_local_2))
            {
               if(_local_1[_local_2.TemplateID] != null)
               {
                  DrillItemInfo(_local_1[_local_2.TemplateID]).amount = DrillItemInfo(_local_1[_local_2.TemplateID]).amount + _local_2.Count;
               }
               else
               {
                  _local_3 = new DrillItemInfo();
                  _local_3.itemInfo = _local_2;
                  _local_3.amount = _local_2.Count;
                  _local_1.add(_local_2.TemplateID,_local_3);
               }
            }
         }
         return _local_1;
      }
      
      public static function getHoleMaxOpLv() : int
      {
         if(_holeExpModel == null)
         {
            _holeExpModel = ComponentFactory.Instance.creatCustomObject("BeadHoleModel");
         }
         return _holeExpModel.getMaxOpLv();
      }
      
      public static function getHoleExpByLv(_arg_1:int) : int
      {
         return ServerConfigManager.instance.getBeadHoleUpExp()[_arg_1];
      }
   }
}

