package activity.warOrder
{
   import com.pickgliss.ui.LayerManager;
   import com.pickgliss.utils.ClassUtils;
   import ddt.events.PkgEvent;
   import ddt.loader.LoaderCreate;
   import ddt.manager.ServerConfigManager;
   import ddt.manager.SocketManager;
   import ddt.manager.TimeManager;
   import ddt.utils.AssetModuleLoader;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.EventDispatcher;
   import flash.external.ExternalInterface;
   import flash.utils.Dictionary;
   import hallIcon.HallIconManager;
   import newActivity.NewActivityCnfInfo;
   import newActivity.NewActivityManager;
   import road7th.comm.PackageIn;
   import road7th.data.DictionaryData;
   import templateData.TemplateManager;
   import templateData.TemplateUpGradDataVo;
   
   public class WarOrderManager extends EventDispatcher
   {
      
      private static var _ins:WarOrderManager;
      
      public static var INFOCHANGE:String = "infoChange";
      
      public static var SHOW_MAIN_VIEW:String = "showMainView";
      
      public static var UPDATE_SHOP_VIEW:String = "updateShopView";
      
      public static var LIMIT_ORDER_TYPE:int = 7;
      
      public var dayTaskArr:Array = [1,2,3];
      
      public var weekTaskArr:Array = [4,5,6];
      
      public var type:int;
      
      public var petID:int;
      
      public var warLevel:int = 15;
      
      public var exp:int;
      
      public var awardGrade:int = 12;
      
      public var isOpenView:Boolean;
      
      public var taskArr:Array = [];
      
      public var rechargeMoney:int;
      
      public var curLimitWarID:int = 1;
      
      public var shopCoin:int = 0;
      
      public var limitOpenTime:Date;
      
      public var limitEndTime:Date;
      
      public var maxLevel:int;
      
      public var taskDic:Dictionary;
      
      private var _model:WarOrderModel;
      
      public function WarOrderManager()
      {
         super();
      }
      
      public static function get ins() : WarOrderManager
      {
         if(!_ins)
         {
            _ins = new WarOrderManager();
         }
         return _ins;
      }
      
      public function initEvent() : void
      {
         var message:String = "我执行了哈，我是WarOrder!";
         ExternalInterface.call("console.log",message);
         SocketManager.Instance.addEventListener(PkgEvent.format(704,WarOrderEpackageType.BaseInfo),this.__activeInfo);
         SocketManager.Instance.addEventListener(PkgEvent.format(704,WarOrderEpackageType.UPDATESHOPCOIN),this.__onUpdateShopCoin);
         NewActivityManager.instance.addEventListener("loadCNFXmlComplete",this.loadXmlComplete);
      }
      
      private function __activeInfo(param1:PkgEvent) : void
      {
         var _loc2_:int = 0;
         var _loc3_:WarOrderTaskInfo = null;
         this.taskArr = [];
         var _loc4_:PackageIn = param1.pkg;
         var _loc5_:int = _loc4_.readInt();
         this.type = _loc4_.readInt();
         this.petID = _loc4_.readInt();
         this.curLimitWarID = _loc4_.readInt();
         this.shopCoin = _loc4_.readInt();
         this.limitOpenTime = _loc4_.readDate();
         this.limitEndTime = _loc4_.readDate();
         this.warLevel = _loc4_.readInt();
         this.exp = _loc4_.readInt();
         this.awardGrade = _loc4_.readInt();
         this.maxLevel = TemplateManager.ins.warOrderDic.length;
         if(this.type == LIMIT_ORDER_TYPE)
         {
            this.maxLevel = TemplateManager.ins.warOrderLimitDic[this.curLimitWarID].length;
         }
         var _loc6_:int = _loc4_.readInt();
         _loc2_ = 0;
         while(_loc2_ < _loc6_)
         {
            _loc3_ = new WarOrderTaskInfo();
            _loc3_.isGet = _loc4_.readBoolean();
            _loc3_.ID = _loc4_.readLong();
            _loc3_.sType = _loc4_.readInt();
            _loc3_.taskID = _loc4_.readInt();
            _loc3_.progress = _loc4_.readInt();
            this.taskArr.push(_loc3_);
            _loc2_++;
         }
         var _loc7_:Date = _loc4_.readDate();
         var _loc8_:Date = _loc4_.readDate();
         this.rechargeMoney = _loc4_.readInt();
         if(this.awardGrade >= this.maxLevel && this.type != LIMIT_ORDER_TYPE)
         {
            this.type = 0;
            dispatchEvent(new Event(SHOW_MAIN_VIEW));
            this.loadRes();
            return;
         }
         if(this.isOpenView)
         {
            this.isOpenView = false;
            this.loadRes();
         }
         else
         {
            if(this.type == 0)
            {
               dispatchEvent(new Event(SHOW_MAIN_VIEW));
               this.loadRes();
               return;
            }
            if(this.type == LIMIT_ORDER_TYPE && this.awardGrade >= this.maxLevel)
            {
               dispatchEvent(new Event(SHOW_MAIN_VIEW));
               this.loadRes();
               return;
            }
            dispatchEvent(new Event(INFOCHANGE));
         }
      }
      
      public function requestInfo() : void
      {
         this.isOpenView = true;
         SocketManager.Instance.out.sendWarOrderInfo();
      }
      
      public function loadRes() : void
      {
         AssetModuleLoader.addRequestLoader(LoaderCreate.Instance.creatWarOrderTaskTemplate());
         AssetModuleLoader.addRequestLoader(LoaderCreate.Instance.createNewActivityCnfLoader());
         AssetModuleLoader.addModelLoader("warOrder",5);
         AssetModuleLoader.startCodeLoader(this.showMainView);
      }
      
      public function showMainView() : void
      {
         var _loc1_:Sprite = null;
         var _loc2_:int = 0;
         var _loc3_:Sprite = null;
         if(this.type == 0)
         {
            _loc1_ = ClassUtils.CreatInstance("activity.warOrder.view.WarOrderTypeSelectView");
            LayerManager.Instance.addToLayer(_loc1_,3,false,1);
            _loc1_.x = 155;
            _loc1_.y = 40;
         }
         else if(this.type >= LIMIT_ORDER_TYPE)
         {
            _loc2_ = this.getLimitWarOrderState();
            switch(_loc2_ - 1)
            {
               case 0:
                  this.showTaskView();
                  break;
               case 1:
               case 2:
               case 3:
               case 4:
                  _loc3_ = ClassUtils.CreatInstance("activity.warOrder.view.WarOrderTypeSelectView");
                  LayerManager.Instance.addToLayer(_loc3_,3,false,1);
                  _loc3_.x = 155;
                  _loc3_.y = 40;
            }
         }
         else
         {
            this.showTaskView();
         }
      }
      
      public function templateDataSetup(param1:WarOrderAnalyzer) : void
      {
         this.taskDic = param1.data;
      }
      
      public function showTaskView() : void
      {
         var _loc1_:int = 0;
         var _loc2_:int = this.type;
         if(this.type == 0)
         {
            _loc1_ = this.getLimitWarOrderState();
            if(_loc1_ == 1)
            {
               _loc2_ = 7;
            }
         }
         var _loc3_:Sprite = ClassUtils.CreatInstance("activity.warOrder.view.WarOrderTaskView",[_loc2_]);
         LayerManager.Instance.addToLayer(_loc3_,3,false,1);
         _loc3_.x = 1;
         _loc3_.y = -31;
      }
      
      public function getPetIDArr() : Array
      {
         var _loc1_:int = 0;
         var _loc2_:Array = null;
         var _loc3_:int = 0;
         var _loc4_:int = 0;
         var _loc5_:Array = [];
         var _loc6_:Array = ServerConfigManager.instance.WarOrderPetSetInfo;
         _loc1_ = 1;
         while(_loc1_ < 3)
         {
            _loc2_ = (_loc6_[_loc1_] as String).split(",");
            _loc3_ = 1;
            while(_loc3_ < _loc2_.length)
            {
               _loc4_ = int((_loc2_[_loc3_] as String).split("_")[0]);
               _loc5_.push(_loc4_);
               _loc3_++;
            }
            _loc1_++;
         }
         return _loc5_;
      }
      
      public function getItemIDArr() : Array
      {
         var _loc1_:int = 0;
         var _loc2_:int = 0;
         var _loc3_:Array = [];
         var _loc4_:Array = ServerConfigManager.instance.WarOrderPetSetInfo;
         var _loc5_:Array = (_loc4_[3] as String).split(",");
         _loc1_ = 1;
         while(_loc1_ < _loc5_.length)
         {
            _loc2_ = int((_loc5_[_loc1_] as String).split("_")[0]);
            _loc3_.push(_loc2_);
            _loc1_++;
         }
         return _loc3_;
      }
      
      public function getPetIsGodType(param1:int) : Boolean
      {
         var _loc6_:int = 0;
         var _loc3_:int = 0;
         var _loc4_:String = String(ServerConfigManager.instance.WarOrderPetSetInfo[2]);
         var _loc5_:Array = _loc4_.split(",");
         _loc6_ = 1;
         while(_loc6_ < _loc5_.length)
         {
            _loc3_ = int((_loc5_[_loc6_] as String).split("_")[0]);
            if(_loc3_ == param1)
            {
               return true;
            }
            _loc6_++;
         }
         return false;
      }
      
      public function getNormalPetCount() : int
      {
         var _loc1_:Array = (ServerConfigManager.instance.WarOrderPetSetInfo[0] as String).split(",");
         return _loc1_.length - 1;
      }
      
      public function getGiveUpReturnMoney() : int
      {
         var _loc1_:int = 0;
         var _loc2_:Array = null;
         var _loc3_:String = null;
         var _loc4_:Array = null;
         var _loc5_:int = 0;
         var _loc6_:int = 0;
         var _loc7_:Array = null;
         var _loc8_:String = null;
         var _loc9_:Array = null;
         var _loc10_:int = 0;
         var _loc11_:int = -1;
         var _loc12_:Array = ServerConfigManager.instance.limitWarPassQuitConfig.split("|");
         _loc1_ = 0;
         while(_loc1_ < _loc12_.length)
         {
            _loc5_ = int((_loc4_ = (_loc3_ = String((_loc2_ = (_loc12_[_loc1_] as String).split(","))[0])).split("-"))[0]);
            _loc6_ = int(_loc4_[1]);
            if(this.warLevel >= _loc5_ && this.warLevel <= _loc6_)
            {
               _loc7_ = ServerConfigManager.instance.WarOrderPrice;
               _loc8_ = String(_loc7_[this.type - 1]);
               _loc10_ = int((_loc9_ = _loc8_.split(","))[3]);
               _loc11_ = Math.floor(_loc10_ * int(_loc2_[1]) / 100);
               break;
            }
            _loc1_++;
         }
         return _loc11_;
      }
      
      public function getLimitOrderRewardListByRound(param1:int, param2:int) : Array
      {
         var _loc3_:int = 0;
         var _loc4_:TemplateUpGradDataVo = null;
         var _loc5_:int = 0;
         var _loc6_:Array = (TemplateManager.ins.warOrderLimitDic[param1] as DictionaryData).list;
         var _loc7_:DictionaryData = new DictionaryData();
         _loc3_ = 0;
         while(_loc3_ < _loc6_.length)
         {
            _loc4_ = _loc6_[_loc3_];
            if(_loc4_.Grades > param2)
            {
               if(!_loc7_.hasKey(_loc4_.ItemTempId2))
               {
                  _loc7_.add(_loc4_.ItemTempId2,{
                     "tid":_loc4_.ItemTempId2,
                     "count":_loc4_.Param2
                  });
               }
               else
               {
                  _loc5_ = int(_loc7_[_loc4_.ItemTempId2]["count"]);
                  _loc7_.add(_loc4_.ItemTempId2,{
                     "tid":_loc4_.ItemTempId2,
                     "count":_loc4_.Param2 + _loc5_
                  });
               }
            }
            _loc3_++;
         }
         return _loc7_.list;
      }
      
      public function getLimitWarOrderState() : int
      {
         var _loc1_:int = -1;
         if(this.curLimitWarID < ServerConfigManager.instance.limitWarPassSeasonEndID)
         {
            if(this.curLimitWarID < ServerConfigManager.instance.limitWarPassCurSeasonID)
            {
               _loc1_ = 4;
            }
            else
            {
               _loc1_ = 3;
            }
         }
         else if(this.curLimitWarID < ServerConfigManager.instance.limitWarPassCurSeasonID)
         {
            if(this.limitEndTime.getTime() <= TimeManager.Instance.Now().getTime())
            {
               _loc1_ = 4;
            }
            else if(this.awardGrade >= this.maxLevel)
            {
               _loc1_ = 5;
               this.maxLevel = TemplateManager.ins.warOrderLimitDic[ServerConfigManager.instance.limitWarPassCurSeasonID].length;
            }
            else
            {
               _loc1_ = 1;
            }
         }
         else if(this.limitEndTime.getTime() <= TimeManager.Instance.Now().getTime())
         {
            _loc1_ = 3;
         }
         else if(this.awardGrade >= this.maxLevel)
         {
            _loc1_ = 2;
         }
         else
         {
            _loc1_ = 1;
         }
         return _loc1_;
      }
      
      private function __onUpdateShopCoin(param1:PkgEvent) : void
      {
         var _loc2_:PackageIn = param1.pkg;
         this.shopCoin = _loc2_.readInt();
         dispatchEvent(new Event(UPDATE_SHOP_VIEW));
      }
      
      public function loadXmlComplete(param1:Event = null) : void
      {
         NewActivityManager.instance.removeEventListener("loadCNFXmlComplete",this.loadXmlComplete);
         var _loc2_:NewActivityCnfInfo = NewActivityManager.instance.getActCnfByNum(7);
      }
      
      public function showIcon() : void
      {
         HallIconManager.instance.updateSwitchHandler("warorder",true);
      }
   }
}

