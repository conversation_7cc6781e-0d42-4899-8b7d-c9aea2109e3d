package consortion.analyze
{
   import com.pickgliss.loader.DataAnalyzer;
   import com.pickgliss.utils.ObjectUtils;
   import consortion.data.ConsortiaInventData;
   
   public class ConsortionInventListAnalyzer extends DataAnalyzer
   {
      
      public var inventList:Vector.<ConsortiaInventData>;
      
      public var totalCount:int;
      
      public function ConsortionInventListAnalyzer(_arg_1:Function)
      {
         super(_arg_1);
      }
      
      override public function analyze(_arg_1:*) : void
      {
         var _local_5:int = 0;
         var _local_4:* = null;
         var _local_2:* = null;
         this.inventList = new Vector.<ConsortiaInventData>();
         var _local_3:XML = new XML(_arg_1);
         if(_local_3.@value == "true")
         {
            this.totalCount = int(_local_3.@total);
            _local_4 = XML(_local_3)..Item;
            _local_5 = 0;
            while(_local_5 < _local_4.length())
            {
               _local_2 = new ConsortiaInventData();
               ObjectUtils.copyPorpertiesByXML(_local_2,_local_4[_local_5]);
               this.inventList.push(_local_2);
               _local_5++;
            }
            onAnalyzeComplete();
         }
         else
         {
            message = _local_3.@message;
            onAnalyzeError();
            onAnalyzeComplete();
         }
      }
   }
}

