package beadSystem.views
{
   import beadSystem.model.BeadInfo;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.core.Component;
   import com.pickgliss.ui.image.Image;
   import com.pickgliss.ui.image.MovieImage;
   import com.pickgliss.ui.image.MutipleImage;
   import com.pickgliss.ui.image.ScaleFrameImage;
   import com.pickgliss.ui.text.FilterFrameText;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.data.EquipType;
   import ddt.data.goods.InventoryItemInfo;
   import ddt.data.goods.ItemTemplateInfo;
   import ddt.data.goods.QualityType;
   import ddt.manager.BeadTemplateManager;
   import ddt.manager.ItemManager;
   import ddt.manager.LanguageMgr;
   import ddt.manager.ServerConfigManager;
   import ddt.utils.PositionUtils;
   import ddt.view.SimpleItem;
   import ddt.view.tips.GoodTipInfo;
   import flash.display.Bitmap;
   import flash.display.DisplayObject;
   import flash.geom.Point;
   import road7th.utils.StringHelper;
   
   public class BeadUpgradeTipView extends Component
   {
      
      private var _strengthenLevelImage:MovieImage;
      
      private var _fusionLevelImage:MovieImage;
      
      private var _boundImage:ScaleFrameImage;
      
      private var _nameTxt:FilterFrameText;
      
      private var _qualityItem:SimpleItem;
      
      private var _typeItem:SimpleItem;
      
      private var _expItem:SimpleItem;
      
      private var _descriptionTxt:FilterFrameText;
      
      private var _bindTypeTxt:FilterFrameText;
      
      private var _remainTimeTxt:FilterFrameText;
      
      private var _info:ItemTemplateInfo;
      
      private var _bindImageOriginalPos:Point;
      
      private var _maxWidth:int;
      
      private var _minWidth:int = 240;
      
      private var _displayList:Vector.<DisplayObject>;
      
      private var _displayIdx:int;
      
      private var _lines:Vector.<Image>;
      
      private var _lineIdx:int;
      
      private var _isReAdd:Boolean;
      
      private var _remainTimeBg:Bitmap;
      
      private var _tipbackgound:MutipleImage;
      
      private var _rightArrows:Bitmap;
      
      private var _exp:int;
      
      private var _UpExp:int;
      
      public function BeadUpgradeTipView()
      {
         super();
      }
      
      override protected function init() : void
      {
         this._lines = new Vector.<Image>();
         this._displayList = new Vector.<DisplayObject>();
         this._rightArrows = ComponentFactory.Instance.creatBitmap("asset.ddtstore.rightArrows");
         this._tipbackgound = ComponentFactory.Instance.creat("ddtstore.strengthTips.strengthenImageBG");
         this._strengthenLevelImage = ComponentFactory.Instance.creatComponentByStylename("core.GoodsTipItemNameMc");
         this._fusionLevelImage = ComponentFactory.Instance.creatComponentByStylename("core.GoodsTipItemTrinketLevelMc");
         this._boundImage = ComponentFactory.Instance.creatComponentByStylename("core.goodTip.BoundImage");
         this._bindImageOriginalPos = new Point(this._boundImage.x,this._boundImage.y);
         this._expItem = ComponentFactory.Instance.creatComponentByStylename("core.goodTip.EXPItem");
         this._nameTxt = ComponentFactory.Instance.creatComponentByStylename("core.GoodsTipItemNameTxt");
         this._qualityItem = ComponentFactory.Instance.creatComponentByStylename("core.goodTip.QualityItem");
         this._typeItem = ComponentFactory.Instance.creatComponentByStylename("core.goodTip.TypeItem");
         this._descriptionTxt = ComponentFactory.Instance.creatComponentByStylename("core.goodTip.DescriptionTxt");
         this._bindTypeTxt = ComponentFactory.Instance.creatComponentByStylename("core.GoodsTipItemTxt");
         this._remainTimeTxt = ComponentFactory.Instance.creatComponentByStylename("core.GoodsTipItemDateTxt");
         this._remainTimeBg = ComponentFactory.Instance.creatBitmap("asset.core.tip.restTime");
      }
      
      override public function get tipData() : Object
      {
         return _tipData;
      }
      
      override public function set tipData(_arg_1:Object) : void
      {
         if(Boolean(_arg_1))
         {
            if(_arg_1 is GoodTipInfo)
            {
               _tipData = _arg_1 as GoodTipInfo;
               this.showTip(_tipData.itemInfo,_tipData.typeIsSecond);
            }
            visible = true;
         }
         else
         {
            _tipData = null;
            visible = false;
         }
      }
      
      public function showTip(_arg_1:ItemTemplateInfo, _arg_2:Boolean = false) : void
      {
         this._displayIdx = 0;
         this._displayList = new Vector.<DisplayObject>();
         this._lineIdx = 0;
         this._isReAdd = false;
         this._maxWidth = 0;
         this._info = _arg_1;
         this.updateView();
      }
      
      private function updateView() : void
      {
         if(this._info == null)
         {
            return;
         }
         this.clear();
         this.createItemName();
         this.createCategoryItem();
         this.careteEXP();
         this.seperateLine();
         this.createDescript();
         this.createBindType();
         this.createRemainTime();
         this.addChildren();
      }
      
      private function clear() : void
      {
         var _local_1:* = null;
         while(numChildren > 0)
         {
            _local_1 = getChildAt(0) as DisplayObject;
            if(Boolean(_local_1.parent))
            {
               _local_1.parent.removeChild(_local_1);
            }
         }
      }
      
      override protected function addChildren() : void
      {
         var _local_4:int = 0;
         var _local_7:* = undefined;
         var _local_1:* = null;
         var _local_2:int = int(this._displayList.length);
         var _local_6:Point = new Point(4,4);
         var _local_3:int = 6;
         var _local_5:int = this._maxWidth;
         _local_4 = 0;
         while(_local_4 < _local_2)
         {
            _local_1 = this._displayList[_local_4] as DisplayObject;
            if(this._lines.indexOf(_local_1 as Image) < 0 && _local_1 != this._descriptionTxt)
            {
               _local_5 = Math.max(_local_1.width,_local_5);
            }
            PositionUtils.setPos(_local_1,_local_6);
            addChild(_local_1);
            _local_6.y = _local_1.y + _local_1.height + _local_3;
            _local_4++;
         }
         this._maxWidth = Math.max(this._minWidth,_local_5);
         this._maxWidth -= 20;
         if(this._descriptionTxt.width != this._maxWidth)
         {
            this._descriptionTxt.width = this._maxWidth;
            this._descriptionTxt.height = this._descriptionTxt.textHeight + 10;
            this.addChildren();
            return;
         }
         if(!this._isReAdd)
         {
            _local_4 = 0;
            while(_local_4 < this._lines.length)
            {
               this._lines[_local_4].width = this._maxWidth;
               if(_local_4 + 1 < this._lines.length && this._lines[_local_4 + 1].parent != null && Math.abs(this._lines[_local_4 + 1].y - this._lines[_local_4].y) <= 10)
               {
                  this._displayList.splice(this._displayList.indexOf(this._lines[_local_4 + 1]),1);
                  this._lines[_local_4 + 1].parent.removeChild(this._lines[_local_4 + 1]);
                  this._isReAdd = true;
               }
               _local_4++;
            }
            if(this._isReAdd)
            {
               this.addChildren();
               return;
            }
         }
         if(Boolean(this._rightArrows))
         {
            addChildAt(this._rightArrows,0);
         }
         if(_local_2 > 0)
         {
            this._tipbackgound.y = -5;
            _local_7 = this._maxWidth + 35;
            this._tipbackgound.width = _local_7;
            _width = _local_7;
            _local_7 = _local_1.y + _local_1.height + 18;
            this._tipbackgound.height = _local_7;
            _height = _local_7;
         }
         if(Boolean(this._tipbackgound))
         {
            addChildAt(this._tipbackgound,0);
         }
         if(Boolean(this._remainTimeBg.parent))
         {
            this._remainTimeBg.x = this._remainTimeTxt.x + 2;
            this._remainTimeBg.y = this._remainTimeTxt.y + 2;
            this._remainTimeBg.parent.addChildAt(this._remainTimeBg,1);
         }
         this._rightArrows.x = 5 - this._rightArrows.width;
         this._rightArrows.y = (this.height - this._rightArrows.height) / 2;
      }
      
      private function createItemName() : void
      {
         this._nameTxt.text = _tipData.beadName;
         this._nameTxt.textColor = QualityType.QUALITY_COLOR[this._info.Quality];
         var _local_1:* = this._displayIdx++;
         this._displayList[_local_1] = this._nameTxt;
      }
      
      private function careteEXP() : void
      {
         var _local_2:* = undefined;
         var _local_1:FilterFrameText = this._expItem.foreItems[0] as FilterFrameText;
         if(EquipType.isBead(int(this._info.Property1)))
         {
            this._exp = ServerConfigManager.instance.getBeadUpgradeExp()[(this._info as InventoryItemInfo).Hole1];
            this._UpExp = ServerConfigManager.instance.getBeadUpgradeExp()[(this._info as InventoryItemInfo).Hole1 + 1];
            _local_1.text = this._exp + "/" + this._UpExp;
            _local_2 = this._displayIdx++;
            this._displayList[_local_2] = this._expItem;
         }
      }
      
      private function createCategoryItem() : void
      {
         var _local_1:FilterFrameText = this._typeItem.foreItems[0] as FilterFrameText;
         switch(this._info.Property2)
         {
            case "1":
               _local_1.text = LanguageMgr.GetTranslation("tank.data.EquipType.atacckt");
               break;
            case "2":
               _local_1.text = LanguageMgr.GetTranslation("tank.data.EquipType.defent");
               break;
            case "3":
               _local_1.text = LanguageMgr.GetTranslation("tank.data.EquipType.attribute");
         }
         _local_1.textColor = 65406;
         var _local_2:* = this._displayIdx++;
         this._displayList[_local_2] = this._typeItem;
      }
      
      private function setPurpleHtmlTxt(_arg_1:String, _arg_2:int, _arg_3:String) : String
      {
         return LanguageMgr.GetTranslation("tank.view.bagII.GoodsTipPanel.setPurpleHtmlTxt",_arg_1,_arg_2,_arg_3);
      }
      
      private function createDescript() : void
      {
         var _local_2:ItemTemplateInfo = ItemManager.Instance.getTemplateById(this._info.TemplateID);
         if(this._info.Description == "")
         {
            return;
         }
         this._info.Description = _local_2.Description;
         var _local_1:InventoryItemInfo = this._info as InventoryItemInfo;
         var _local_3:BeadInfo = BeadTemplateManager.Instance.GetBeadInfobyID(this._info.TemplateID);
         if(_local_3.Attribute1 == "0" && _local_3.Attribute2 == "0")
         {
            this._descriptionTxt.text = StringHelper.format(this._info.Description);
         }
         else if(_local_3.Attribute1 == "0" && _local_3.Attribute2 != "0")
         {
            if(_local_3.Att2.length > 1)
            {
               if(Boolean(_local_1) && _local_1.Hole1 > _local_3.BaseLevel)
               {
                  this._descriptionTxt.text = StringHelper.format(this._info.Description,_local_3.Att2[1]);
               }
               else
               {
                  this._descriptionTxt.text = StringHelper.format(this._info.Description,_local_3.Att2[0]);
               }
            }
            else
            {
               this._descriptionTxt.text = StringHelper.format(this._info.Description,_local_3.Attribute2);
            }
         }
         else if(_local_3.Attribute1 != "0" && _local_3.Attribute2 == "0")
         {
            if(_local_3.Att1.length > 1)
            {
               if(Boolean(_local_1) && _local_1.Hole1 > _local_3.BaseLevel)
               {
                  this._descriptionTxt.text = StringHelper.format(this._info.Description,_local_3.Att1[1]);
               }
               else
               {
                  this._descriptionTxt.text = StringHelper.format(this._info.Description,_local_3.Att1[0]);
               }
            }
            else
            {
               this._descriptionTxt.text = StringHelper.format(this._info.Description,_local_3.Attribute1);
            }
         }
         else if(_local_3.Attribute1 != "0" && _local_3.Attribute2 != "0" && _local_3.Attribute3 == "0")
         {
            if(_local_3.Att1.length > 1 && _local_3.Att2.length == 1)
            {
               if(Boolean(_local_1) && _local_1.Hole1 > _local_3.BaseLevel)
               {
                  this._descriptionTxt.text = StringHelper.format(this._info.Description,_local_3.Att1[1],_local_3.Attribute2);
               }
               else
               {
                  this._descriptionTxt.text = StringHelper.format(this._info.Description,_local_3.Att1[0],_local_3.Attribute2);
               }
            }
            else if(_local_3.Att1.length == 1 && _local_3.Att2.length > 1)
            {
               if(Boolean(_local_1) && _local_1.Hole1 > _local_3.BaseLevel)
               {
                  this._descriptionTxt.text = StringHelper.format(this._info.Description,_local_3.Attribute1,_local_3.Att2[1]);
               }
               else
               {
                  this._descriptionTxt.text = StringHelper.format(this._info.Description,_local_3.Attribute1,_local_3.Att2[0]);
               }
            }
            else if(Boolean(_local_1) && _local_1.Hole1 > _local_3.BaseLevel)
            {
               this._descriptionTxt.text = StringHelper.format(this._info.Description,_local_3.Att1[1],_local_3.Att2[1]);
            }
            else
            {
               this._descriptionTxt.text = StringHelper.format(this._info.Description,_local_3.Att1[0],_local_3.Att2[0]);
            }
         }
         else if(_local_3.Attribute1 != "0" && _local_3.Attribute2 != "0" && _local_3.Attribute3 != "0")
         {
            if(_local_3.Att1.length != 1)
            {
               if(Boolean(_local_1) && _local_1.Hole1 > _local_3.BaseLevel)
               {
                  this._descriptionTxt.text = StringHelper.format(this._info.Description,_local_3.Att1[1],_local_3.Att2[1],_local_3.Att3[1]);
               }
               else
               {
                  this._descriptionTxt.text = StringHelper.format(this._info.Description,_local_3.Att1[0],_local_3.Att2[0],_local_3.Att3[0]);
               }
            }
            else
            {
               this._descriptionTxt.text = StringHelper.format(this._info.Description,_local_3.Att1[0],_local_3.Att2[0],_local_3.Att3[0]);
            }
         }
         this._descriptionTxt.height = this._descriptionTxt.textHeight + 10;
         var _local_4:* = this._displayIdx++;
         this._displayList[_local_4] = this._descriptionTxt;
      }
      
      private function ShowBound(_arg_1:InventoryItemInfo) : Boolean
      {
         return _arg_1.CategoryID != 32 && _arg_1.CategoryID != 33 && _arg_1.CategoryID != 36;
      }
      
      private function createBindType() : void
      {
         var _local_2:* = undefined;
         var _local_1:InventoryItemInfo = this._info as InventoryItemInfo;
         if(Boolean(_local_1) && this.ShowBound(_local_1))
         {
            this._boundImage.setFrame(_local_1.IsBinds ? 1 : 2);
            PositionUtils.setPos(this._boundImage,this._bindImageOriginalPos);
            addChild(this._boundImage);
            if(!_local_1.IsBinds)
            {
               if(_local_1.BindType == 3)
               {
                  this._bindTypeTxt.text = LanguageMgr.GetTranslation("tank.view.bagII.GoodsTipPanel.bangding");
                  this._bindTypeTxt.textColor = 16777215;
                  _local_2 = this._displayIdx++;
                  this._displayList[_local_2] = this._bindTypeTxt;
               }
               else if(this._info.BindType == 2)
               {
                  this._bindTypeTxt.text = LanguageMgr.GetTranslation("tank.view.bagII.GoodsTipPanel.zhuangbei");
                  this._bindTypeTxt.textColor = 16777215;
                  _local_2 = this._displayIdx++;
                  this._displayList[_local_2] = this._bindTypeTxt;
               }
               else if(this._info.BindType == 4)
               {
                  if(Boolean(this._boundImage.parent))
                  {
                     this._boundImage.parent.removeChild(this._boundImage);
                  }
               }
            }
         }
         else if(Boolean(this._boundImage.parent))
         {
            this._boundImage.parent.removeChild(this._boundImage);
         }
      }
      
      private function createRemainTime() : void
      {
         var _local_4:Number = NaN;
         var _local_2:Number = NaN;
         var _local_5:Number = NaN;
         var _local_6:Number = NaN;
         var _local_7:* = undefined;
         var _local_3:* = null;
         var _local_1:* = null;
         if(Boolean(this._remainTimeBg.parent))
         {
            this._remainTimeBg.parent.removeChild(this._remainTimeBg);
         }
         if(this._info is InventoryItemInfo)
         {
            _local_3 = this._info as InventoryItemInfo;
            _local_2 = Number(_local_3.getRemainDate());
            _local_5 = Number(_local_3.getColorValidDate());
            _local_1 = _local_3.CategoryID == 7 ? LanguageMgr.GetTranslation("bag.changeColor.tips.armName") : "";
            if(_local_5 > 0 && _local_5 != 2147483647)
            {
               if(_local_5 >= 1)
               {
                  this._remainTimeTxt.text = (Boolean(_local_3.IsUsed) ? LanguageMgr.GetTranslation("bag.changeColor.tips.name") + LanguageMgr.GetTranslation("tank.view.bagII.GoodsTipPanel.less") : LanguageMgr.GetTranslation("tank.view.bagII.GoodsTipPanel.time")) + Math.ceil(_local_5) + LanguageMgr.GetTranslation("shop.ShopIIShoppingCarItem.day");
                  this._remainTimeTxt.textColor = 16777215;
                  _local_7 = this._displayIdx++;
                  this._displayList[_local_7] = this._remainTimeTxt;
               }
               else
               {
                  _local_6 = Math.floor(_local_5 * 24);
                  if(_local_6 < 1)
                  {
                     _local_6 = 1;
                  }
                  this._remainTimeTxt.text = (Boolean(_local_3.IsUsed) ? LanguageMgr.GetTranslation("bag.changeColor.tips.name") + LanguageMgr.GetTranslation("tank.view.bagII.GoodsTipPanel.less") : LanguageMgr.GetTranslation("tank.view.bagII.GoodsTipPanel.time")) + _local_6 + LanguageMgr.GetTranslation("hours");
                  this._remainTimeTxt.textColor = 16777215;
                  _local_7 = this._displayIdx++;
                  this._displayList[_local_7] = this._remainTimeTxt;
               }
            }
            if(_local_2 == 2147483647)
            {
               this._remainTimeTxt.text = LanguageMgr.GetTranslation("tank.view.bagII.GoodsTipPanel.use");
               this._remainTimeTxt.textColor = 16776960;
               _local_7 = this._displayIdx++;
               this._displayList[_local_7] = this._remainTimeTxt;
            }
            else if(_local_2 > 0)
            {
               if(_local_2 >= 1)
               {
                  _local_4 = Math.ceil(_local_2);
                  this._remainTimeTxt.text = (Boolean(_local_3.IsUsed) ? _local_1 + LanguageMgr.GetTranslation("tank.view.bagII.GoodsTipPanel.less") : LanguageMgr.GetTranslation("tank.view.bagII.GoodsTipPanel.time")) + _local_4 + LanguageMgr.GetTranslation("shop.ShopIIShoppingCarItem.day");
                  this._remainTimeTxt.textColor = 16777215;
                  _local_7 = this._displayIdx++;
                  this._displayList[_local_7] = this._remainTimeTxt;
               }
               else
               {
                  _local_4 = Math.floor(_local_2 * 24);
                  _local_4 = _local_4 < 1 ? 1 : _local_4;
                  this._remainTimeTxt.text = (Boolean(_local_3.IsUsed) ? _local_1 + LanguageMgr.GetTranslation("tank.view.bagII.GoodsTipPanel.less") : LanguageMgr.GetTranslation("tank.view.bagII.GoodsTipPanel.time")) + _local_4 + LanguageMgr.GetTranslation("hours");
                  this._remainTimeTxt.textColor = 16777215;
                  _local_7 = this._displayIdx++;
                  this._displayList[_local_7] = this._remainTimeTxt;
               }
               addChild(this._remainTimeBg);
            }
            else if(!isNaN(_local_2))
            {
               this._remainTimeTxt.text = LanguageMgr.GetTranslation("tank.view.bagII.GoodsTipPanel.over");
               this._remainTimeTxt.textColor = 16711680;
               _local_7 = this._displayIdx++;
               this._displayList[_local_7] = this._remainTimeTxt;
            }
         }
      }
      
      private function seperateLine() : void
      {
         var _local_1:* = null;
         ++this._lineIdx;
         if(this._lines.length < this._lineIdx)
         {
            _local_1 = ComponentFactory.Instance.creatComponentByStylename("HRuleAsset");
            this._lines.push(_local_1);
         }
         var _local_2:* = this._displayIdx++;
         this._displayList[_local_2] = this._lines[this._lineIdx - 1];
      }
      
      override public function dispose() : void
      {
         super.dispose();
         if(Boolean(this._rightArrows))
         {
            ObjectUtils.disposeObject(this._rightArrows);
         }
         this._rightArrows = null;
         if(Boolean(this._tipbackgound))
         {
            ObjectUtils.disposeObject(this._tipbackgound);
         }
         this._tipbackgound = null;
         if(Boolean(this._strengthenLevelImage))
         {
            ObjectUtils.disposeObject(this._strengthenLevelImage);
         }
         this._strengthenLevelImage = null;
         if(Boolean(this._fusionLevelImage))
         {
            ObjectUtils.disposeObject(this._fusionLevelImage);
         }
         this._fusionLevelImage = null;
         if(Boolean(this._boundImage))
         {
            ObjectUtils.disposeObject(this._boundImage);
         }
         this._boundImage = null;
         if(Boolean(this._nameTxt))
         {
            ObjectUtils.disposeObject(this._nameTxt);
         }
         this._nameTxt = null;
         if(Boolean(this._qualityItem))
         {
            ObjectUtils.disposeObject(this._qualityItem);
         }
         this._qualityItem = null;
         if(Boolean(this._typeItem))
         {
            ObjectUtils.disposeObject(this._typeItem);
         }
         this._typeItem = null;
         if(Boolean(this._descriptionTxt))
         {
            ObjectUtils.disposeObject(this._descriptionTxt);
         }
         this._descriptionTxt = null;
         if(Boolean(this._bindTypeTxt))
         {
            ObjectUtils.disposeObject(this._bindTypeTxt);
         }
         this._bindTypeTxt = null;
         if(Boolean(this._remainTimeTxt))
         {
            ObjectUtils.disposeObject(this._remainTimeTxt);
         }
         this._remainTimeTxt = null;
         if(Boolean(this._remainTimeBg))
         {
            ObjectUtils.disposeObject(this._remainTimeBg);
         }
         this._remainTimeBg = null;
         if(Boolean(this._tipbackgound))
         {
            ObjectUtils.disposeObject(this._tipbackgound);
         }
         this._tipbackgound = null;
         if(Boolean(parent))
         {
            parent.removeChild(this);
         }
      }
   }
}

