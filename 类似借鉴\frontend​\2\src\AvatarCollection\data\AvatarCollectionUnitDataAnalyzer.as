package AvatarCollection.data
{
   import com.pickgliss.loader.DataAnalyzer;
   import road7th.data.DictionaryData;
   
   public class AvatarCollectionUnitDataAnalyzer extends DataAnalyzer
   {
      
      private var _maleUnitDic:DictionaryData;
      
      private var _femaleUnitDic:DictionaryData;
      
      private var _weaponUnitDic:DictionaryData;
      
      public function AvatarCollectionUnitDataAnalyzer(_arg_1:Function)
      {
         super(_arg_1);
      }
      
      override public function analyze(_arg_1:*) : void
      {
         var _local_5:int = 0;
         var _local_4:* = null;
         var _local_2:* = null;
         var _local_3:XML = new XML(_arg_1);
         this._maleUnitDic = new DictionaryData();
         this._femaleUnitDic = new DictionaryData();
         this._weaponUnitDic = new DictionaryData();
         if(_local_3.@value == "true")
         {
            _local_4 = _local_3..Item;
            _local_5 = 0;
            while(_local_5 < _local_4.length())
            {
               _local_2 = new AvatarCollectionUnitVo();
               _local_2.id = _local_4[_local_5].@ID;
               _local_2.sex = _local_4[_local_5].@Sex;
               _local_2.name = _local_4[_local_5].@Name;
               _local_2.Attack = _local_4[_local_5].@Attack;
               _local_2.Defence = _local_4[_local_5].@Defend;
               _local_2.Agility = _local_4[_local_5].@Agility;
               _local_2.Luck = _local_4[_local_5].@Luck;
               _local_2.Damage = _local_4[_local_5].@Damage;
               _local_2.Guard = _local_4[_local_5].@Guard;
               _local_2.Blood = _local_4[_local_5].@Blood;
               _local_2.needHonor = _local_4[_local_5].@Cost;
               _local_2.Type = _local_4[_local_5].@Type;
               if(_local_2.Type == 1)
               {
                  if(_local_2.sex == 1)
                  {
                     this._maleUnitDic.add(_local_2.id,_local_2);
                  }
                  else
                  {
                     this._femaleUnitDic.add(_local_2.id,_local_2);
                  }
               }
               else if(_local_2.Type == 2)
               {
                  this._weaponUnitDic.add(_local_2.id,_local_2);
               }
               _local_5++;
            }
            onAnalyzeComplete();
         }
         else
         {
            message = _local_3.@message;
            onAnalyzeError();
         }
      }
      
      public function get maleUnitDic() : DictionaryData
      {
         return this._maleUnitDic;
      }
      
      public function get femaleUnitDic() : DictionaryData
      {
         return this._femaleUnitDic;
      }
      
      public function get weaponUnitDic() : DictionaryData
      {
         return this._weaponUnitDic;
      }
   }
}

