package conRecharge
{
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.LayerManager;
   import conRecharge.view.ConRechargeFrame;
   import ddt.utils.HelperUIModuleLoad;
   import flash.events.EventDispatcher;
   import flash.events.IEventDispatcher;
   import hallIcon.HallIconManager;
   
   public class ConRechargeManager extends EventDispatcher
   {
      
      private static var _instance:ConRechargeManager;
      
      public var isOpen:Boolean;
      
      private var _frame:ConRechargeFrame;
      
      private var _giftbagArray:Array;
      
      public var dayGiftbagArray:Array;
      
      public var longGiftbagArray:Array;
      
      public var beginTime:String;
      
      public var endTime:String;
      
      public var actId:String;
      
      public function ConRechargeManager(_arg_1:IEventDispatcher = null)
      {
         super(_arg_1);
      }
      
      public static function get instance() : ConRechargeManager
      {
         if(!_instance)
         {
            _instance = new ConRechargeManager();
         }
         return _instance;
      }
      
      public function setup() : void
      {
      }
      
      public function show() : void
      {
         new HelperUIModuleLoad().loadUIModule(["conRecharge"],this.onLoaded);
      }
      
      private function onLoaded() : void
      {
         this._frame = ComponentFactory.Instance.creatCustomObject("conRecharge.ConRechargeFrame");
         LayerManager.Instance.addToLayer(this._frame,3,true,1);
      }
      
      public function showIcon() : void
      {
         HallIconManager.instance.updateSwitchHandler("conRecharge",this.isOpen);
      }
      
      public function set giftbagArray(_arg_1:Array) : void
      {
         var _local_5:int = 0;
         var _local_3:int = 0;
         var _local_2:* = null;
         var _local_4:* = null;
         this._giftbagArray = _arg_1;
         this.dayGiftbagArray = [];
         this.longGiftbagArray = [];
         for(_local_5 = 0; _local_5 < this._giftbagArray.length; )
         {
            _local_4 = this._giftbagArray[_local_5];
            if(_local_4.giftConditionArr[0].conditionIndex == 1)
            {
               this.dayGiftbagArray.push(_local_4);
            }
            else
            {
               _local_2 = [];
               _local_3 = 0;
               while(true)
               {
                  if(_local_3 >= this.longGiftbagArray.length)
                  {
                     _local_2.push(_local_4);
                     this.longGiftbagArray.push(_local_2);
                     break;
                  }
                  if(_local_4.giftConditionArr[0].conditionValue == this.longGiftbagArray[_local_3][0].giftConditionArr[0].conditionValue)
                  {
                     this.longGiftbagArray[_local_3].push(_local_4);
                     break;
                  }
                  _local_3++;
               }
            }
            _local_5++;
         }
      }
   }
}

