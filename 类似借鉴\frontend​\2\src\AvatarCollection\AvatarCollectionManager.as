package AvatarCollection
{
   import AvatarCollection.data.AvatarCollPetTalentItemDataAnalyze;
   import AvatarCollection.data.AvatarCollPetTalentItemVo;
   import AvatarCollection.data.AvatarCollPetTalentSuitDataAnalyze;
   import AvatarCollection.data.AvatarCollPetTalentSuitVo;
   import AvatarCollection.data.AvatarCollectionItemDataAnalyzer;
   import AvatarCollection.data.AvatarCollectionItemVo;
   import AvatarCollection.data.AvatarCollectionUnitDataAnalyzer;
   import AvatarCollection.data.AvatarCollectionUnitVo;
   import ddt.data.goods.ShopItemInfo;
   import ddt.events.CEvent;
   import ddt.events.PkgEvent;
   import ddt.manager.LanguageMgr;
   import ddt.manager.ShopManager;
   import ddt.manager.SocketManager;
   import ddt.utils.AssetModuleLoader;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.EventDispatcher;
   import flash.events.IEventDispatcher;
   import petsSystem.PetsManager;
   import petsSystem.data.PetsSystemTalentSkillInfo;
   import road7th.comm.PackageIn;
   import road7th.data.DictionaryData;
   
   public class AvatarCollectionManager extends EventDispatcher
   {
      
      private static var _instance:AvatarCollectionManager;
      
      public static const REFRESH_VIEW:String = "avatar_collection_refresh_view";
      
      public static const DATA_COMPLETE:String = "avatar_collection_data_complete";
      
      public static const SELECT_ALL:String = "avatar_collection_select_all";
      
      public static const VISIBLE:String = "visible";
      
      public static const RESET_LEFT:String = "reset_left";
      
      public static const SETPAGE_VISABLE:String = "setPage_visible";
      
      public static const REFRESH_VIEW_PETTALENT:String = "refresh_view_petTalent";
      
      public static const PETTALENTITEMMAXLV:int = 10;
      
      private var _realItemIdDic:DictionaryData;
      
      private var _maleItemDic:DictionaryData;
      
      private var _femaleItemDic:DictionaryData;
      
      private var _weaponItemDic:DictionaryData;
      
      private var _avatarEndTimeList:DictionaryData;
      
      private var _allGoodsTemplateIDlist:DictionaryData;
      
      private var _maleItemList:Vector.<AvatarCollectionItemVo>;
      
      private var _femaleItemList:Vector.<AvatarCollectionItemVo>;
      
      private var _weaponItemList:Vector.<AvatarCollectionItemVo>;
      
      private var _maleUnitList:Array;
      
      private var _femaleUnitList:Array;
      
      private var _weaponUnitList:Array;
      
      private var _maleUnitDic:DictionaryData;
      
      private var _femaleUnitDic:DictionaryData;
      
      private var _weaponUnitDic:DictionaryData;
      
      private var _maleShopItemInfoList:Vector.<ShopItemInfo>;
      
      private var _femaleShopItemInfoList:Vector.<ShopItemInfo>;
      
      private var _weaponShopItemInfoList:Vector.<ShopItemInfo>;
      
      private var _isHasCheckedBuy:Boolean = false;
      
      public var isSkipFromHall:Boolean = false;
      
      public var skipId:int;
      
      private var _petTalentTemplateDic:DictionaryData;
      
      private var _petTalentSuitDic:DictionaryData;
      
      private var _petTalentDataDic:DictionaryData;
      
      private var _selectedPTGroupID:int;
      
      private var _selectedPTSkillID:int;
      
      private var _isSelectAll:Boolean = false;
      
      private var _pageType:int;
      
      private var _listState:String = "normal";
      
      private var _gotAllInfo:Boolean = false;
      
      private var cevent:CEvent;
      
      private var _petTalentTotalProDic:DictionaryData;
      
      public var isDataComplete:Boolean;
      
      public var isCheckedAvatarTime:Boolean = false;
      
      public function AvatarCollectionManager(_arg_1:IEventDispatcher = null)
      {
         super(_arg_1);
      }
      
      public static function get instance() : AvatarCollectionManager
      {
         if(_instance == null)
         {
            _instance = new AvatarCollectionManager();
         }
         return _instance;
      }
      
      private function honourNeedPerPage(_arg_1:AvatarCollectionUnitVo) : Number
      {
         var _local_4:int = 0;
         if(!_arg_1)
         {
            return 0;
         }
         var _local_3:int = int(_arg_1.totalItemList.length);
         var _local_2:int = _arg_1.totalActivityItemCount;
         if(_local_2 < _local_3 / 2)
         {
            return 0;
         }
         if(_local_2 == _local_3)
         {
            return _arg_1.needHonor * 2;
         }
         return _arg_1.needHonor;
      }
      
      public function honourNeedTotalPerDay() : Number
      {
         var _local_2:AvatarCollectionUnitVo = null;
         var _local_1:AvatarCollectionUnitVo = null;
         var _local_3:AvatarCollectionUnitVo = null;
         var _local_4:* = 0;
         for each(_local_2 in this._maleUnitList)
         {
            if(_local_2.selected)
            {
               _local_4 += this.honourNeedPerPage(_local_2);
            }
         }
         for each(_local_1 in this._femaleUnitList)
         {
            if(_local_1.selected)
            {
               _local_4 += this.honourNeedPerPage(_local_1);
            }
         }
         for each(_local_3 in this._weaponUnitList)
         {
            if(_local_3.selected)
            {
               _local_4 += this.honourNeedPerPage(_local_3);
            }
         }
         return _local_4;
      }
      
      public function onListCellClick(_arg_1:AvatarCollectionUnitVo, _arg_2:Boolean) : void
      {
         var _local_6:* = undefined;
         var _local_4:AvatarCollectionItemVo = null;
         var _local_3:AvatarCollectionUnitVo = null;
         var _local_5:* = null;
         if(_arg_1.Type == 1)
         {
            _local_6 = _arg_1.sex == 1 ? this._maleItemList : this._femaleItemList;
         }
         else
         {
            _local_6 = this._weaponItemList;
         }
         if(_arg_1.Type == 1)
         {
            _local_5 = _arg_1.sex == 1 ? this._maleUnitList : this._femaleUnitList;
         }
         else
         {
            _local_5 = this._weaponUnitList;
         }
         for each(_local_4 in _local_6)
         {
            if(_local_4.id == _arg_1.id)
            {
               _local_4.selected = _arg_2;
               break;
            }
         }
         for each(_local_3 in _local_5)
         {
            if(_local_3.id == _arg_1.id)
            {
               _local_3.selected = _arg_2;
               return;
            }
         }
      }
      
      public function get isSelectAll() : Boolean
      {
         return this._isSelectAll;
      }
      
      public function set isSelectAll(_arg_1:Boolean) : void
      {
         var _local_3:AvatarCollectionItemVo = null;
         var _local_2:AvatarCollectionItemVo = null;
         var _local_7:AvatarCollectionUnitVo = null;
         var _local_6:AvatarCollectionUnitVo = null;
         var _local_5:AvatarCollectionUnitVo = null;
         var _local_4:AvatarCollectionItemVo = null;
         this._isSelectAll = _arg_1;
         if(this._pageType == 0)
         {
            for each(_local_3 in this._maleItemList)
            {
               _local_3.selected = _arg_1;
            }
            for each(_local_2 in this._femaleItemList)
            {
               _local_2.selected = _arg_1;
            }
            for each(_local_7 in this._maleUnitList)
            {
               _local_7.selected = _arg_1;
            }
            for each(_local_6 in this._femaleUnitList)
            {
               _local_6.selected = _arg_1;
            }
         }
         else
         {
            for each(_local_5 in this._weaponUnitList)
            {
               _local_5.selected = _arg_1;
            }
            for each(_local_4 in this._weaponItemList)
            {
               _local_4.selected = _arg_1;
            }
         }
      }
      
      public function getSelectState(_arg_1:AvatarCollectionUnitVo) : Boolean
      {
         var _local_3:* = undefined;
         var _local_2:AvatarCollectionItemVo = null;
         if(_arg_1.Type == 1)
         {
            _local_3 = _arg_1.sex == 1 ? this._maleItemList : this._femaleItemList;
         }
         else
         {
            _local_3 = this._weaponItemList;
         }
         for each(_local_2 in _local_3)
         {
            if(_local_2.id == _arg_1.id)
            {
               return _local_2.selected;
            }
         }
         return false;
      }
      
      public function get pageType() : int
      {
         return this._pageType;
      }
      
      public function set pageType(_arg_1:int) : void
      {
         this._pageType = _arg_1;
      }
      
      public function selectAllClicked(_arg_1:Object = null) : void
      {
         if(_arg_1 != null)
         {
            this.isSelectAll = _arg_1;
         }
         else if(this._listState != "normal")
         {
            dispatchEvent(new CEvent("reset_left"));
            this.isSelectAll = true;
            this._listState = "normal";
         }
         else
         {
            this.isSelectAll = !this.isSelectAll;
         }
         dispatchEvent(new CEvent("avatar_collection_select_all",this._isSelectAll));
      }
      
      public function getDelayTimeCollectionCount() : Number
      {
         var _local_2:AvatarCollectionUnitVo = null;
         var _local_1:AvatarCollectionUnitVo = null;
         var _local_4:AvatarCollectionUnitVo = null;
         var _local_3:* = 0;
         for each(_local_2 in this._maleUnitList)
         {
            if(_local_2.selected == true)
            {
               _local_3++;
            }
         }
         for each(_local_1 in this._femaleUnitList)
         {
            if(_local_1.selected == true)
            {
               _local_3++;
            }
         }
         for each(_local_4 in this._weaponUnitList)
         {
            if(_local_4.selected == true)
            {
               _local_3++;
            }
         }
         return _local_3;
      }
      
      public function delayTheTimeConfirmed(_arg_1:int) : void
      {
         var _local_3:AvatarCollectionUnitVo = null;
         var _local_2:AvatarCollectionUnitVo = null;
         var _local_4:AvatarCollectionUnitVo = null;
         for each(_local_3 in this._maleUnitList)
         {
            if(_local_3.selected)
            {
               SocketManager.Instance.out.sendAvatarCollectionDelayTime(_local_3.id,_arg_1,1);
            }
         }
         for each(_local_2 in this._femaleUnitList)
         {
            if(_local_2.selected)
            {
               SocketManager.Instance.out.sendAvatarCollectionDelayTime(_local_2.id,_arg_1,1);
            }
         }
         for each(_local_4 in this._weaponUnitList)
         {
            if(_local_4.selected)
            {
               SocketManager.Instance.out.sendAvatarCollectionDelayTime(_local_4.id,_arg_1,2);
            }
         }
      }
      
      public function listState(_arg_1:String) : void
      {
         var _local_7:* = undefined;
         var _local_6:* = undefined;
         var _local_4:* = undefined;
         var _local_3:AvatarCollectionItemVo = null;
         var _local_2:AvatarCollectionItemVo = null;
         var _local_5:AvatarCollectionItemVo = null;
         this._isSelectAll = true;
         this._listState = _arg_1;
         for(_local_7 in this._maleUnitList)
         {
            this._maleUnitList[_local_7].selected = false;
         }
         for(_local_6 in this._femaleUnitList)
         {
            this._femaleUnitList[_local_6].selected = false;
         }
         for(_local_4 in this._weaponUnitList)
         {
            this._weaponUnitList[_local_4].selected = false;
         }
         for each(_local_3 in this._maleItemList)
         {
            _local_3.selected = false;
         }
         for each(_local_2 in this._femaleItemList)
         {
            _local_2.selected = false;
         }
         for each(_local_5 in this._weaponItemList)
         {
            _local_5.selected = false;
         }
      }
      
      public function getListState() : String
      {
         return this._listState;
      }
      
      public function resetListCellData() : void
      {
         var _local_6:* = undefined;
         var _local_5:* = undefined;
         var _local_3:* = undefined;
         var _local_2:AvatarCollectionItemVo = null;
         var _local_1:AvatarCollectionItemVo = null;
         var _local_4:AvatarCollectionItemVo = null;
         this._isSelectAll = false;
         this._listState = "normal";
         for(_local_6 in this._maleUnitList)
         {
            this._maleUnitList[_local_6].selected = false;
         }
         for(_local_5 in this._femaleUnitList)
         {
            this._femaleUnitList[_local_5].selected = false;
         }
         for(_local_3 in this._weaponUnitList)
         {
            this._weaponUnitList[_local_3].selected = false;
         }
         for each(_local_2 in this._maleItemList)
         {
            _local_2.selected = false;
         }
         for each(_local_1 in this._femaleItemList)
         {
            _local_1.selected = false;
         }
         for each(_local_4 in this._weaponItemList)
         {
            _local_4.selected = false;
         }
      }
      
      public function get maleUnitList() : Array
      {
         return this._maleUnitList;
      }
      
      public function get femaleUnitList() : Array
      {
         return this._femaleUnitList;
      }
      
      public function get weaponUnitList() : Array
      {
         return this._weaponUnitList;
      }
      
      public function getItemListById(_arg_1:int, _arg_2:int, _arg_3:int = 1) : Array
      {
         if(_arg_3 == 1)
         {
            if(_arg_1 == 1)
            {
               return this._maleItemDic[_arg_2].list;
            }
            return this._femaleItemDic[_arg_2].list;
         }
         if(_arg_3 == 2)
         {
            return this._weaponItemDic[_arg_2].list;
         }
         return null;
      }
      
      public function unitListDataSetup(_arg_1:AvatarCollectionUnitDataAnalyzer) : void
      {
         if(Boolean(this._maleUnitDic))
         {
            this.unitDicDataConvert(this._maleUnitDic,_arg_1.maleUnitDic);
         }
         else
         {
            this._maleUnitDic = _arg_1.maleUnitDic;
         }
         if(Boolean(this._femaleUnitDic))
         {
            this.unitDicDataConvert(this._femaleUnitDic,_arg_1.femaleUnitDic);
         }
         else
         {
            this._femaleUnitDic = _arg_1.femaleUnitDic;
         }
         if(Boolean(this._weaponUnitDic))
         {
            this.unitDicDataConvert(this._weaponUnitDic,_arg_1.weaponUnitDic);
         }
         else
         {
            this._weaponUnitDic = _arg_1.weaponUnitDic;
         }
         this._maleUnitList = this._maleUnitDic.list;
         this._femaleUnitList = this._femaleUnitDic.list;
         this._weaponUnitList = this._weaponUnitDic.list;
      }
      
      private function unitDicDataConvert(_arg_1:DictionaryData, _arg_2:DictionaryData) : void
      {
         var _local_5:int = 0;
         var _local_3:* = null;
         var _local_4:* = null;
         _local_5 = 0;
         while(_local_5 < _arg_2.list.length)
         {
            _local_3 = _arg_2.list[_local_5];
            _local_4 = _arg_1[_local_3.id];
            if(_local_4)
            {
               _local_3.endTime = _local_4.endTime;
            }
            _arg_1.add(_local_3.id,_local_3);
            _local_5++;
         }
      }
      
      public function itemListDataSetup(_arg_1:AvatarCollectionItemDataAnalyzer) : void
      {
         if(Boolean(this._maleItemDic))
         {
            this.itemDicDataConvert(this._maleItemDic,_arg_1.maleItemDic);
         }
         else
         {
            this._maleItemDic = _arg_1.maleItemDic;
         }
         if(Boolean(this._femaleItemDic))
         {
            this.itemDicDataConvert(this._femaleItemDic,_arg_1.femaleItemDic);
         }
         else
         {
            this._femaleItemDic = _arg_1.femaleItemDic;
         }
         if(Boolean(this._weaponItemDic))
         {
            this.itemDicDataConvert(this._weaponItemDic,_arg_1.weaponItemDic);
         }
         else
         {
            this._weaponItemDic = _arg_1.weaponItemDic;
         }
         this._maleItemList = _arg_1.maleItemList;
         this._femaleItemList = _arg_1.femaleItemList;
         this._weaponItemList = _arg_1.weaponItemList;
         this._allGoodsTemplateIDlist = _arg_1.allGoodsTemplateIDlist;
         this._realItemIdDic = _arg_1.realItemIdDic;
      }
      
      public function getAvatarCollectionUnitVoByID(_arg_1:int) : AvatarCollectionUnitVo
      {
         return this._maleUnitDic[_arg_1] || this._femaleUnitDic[_arg_1] || this._weaponUnitDic[_arg_1];
      }
      
      private function itemDicDataConvert(_arg_1:DictionaryData, _arg_2:DictionaryData) : void
      {
         var _local_8:String = null;
         var _local_5:String = null;
         var _local_6:* = null;
         var _local_7:* = null;
         var _local_3:* = null;
         var _local_4:* = null;
         for(_local_8 in _arg_2)
         {
            _local_6 = _arg_2[_local_8];
            _local_7 = _arg_1[_local_8];
            if(_local_7)
            {
               for(_local_5 in _local_6)
               {
                  _local_3 = _local_6[_local_5];
                  _local_4 = _local_7[_local_5];
                  if(_local_4)
                  {
                     _local_3.isActivity = _local_4.isActivity;
                  }
                  _local_7.add(_local_5,_local_3);
               }
            }
            _arg_1.add(_local_8,_local_6);
         }
      }
      
      public function initShopItemInfoList() : void
      {
         var _local_3:int = 0;
         var _local_9:int = 0;
         var _local_8:int = 0;
         var _local_7:int = 0;
         var _local_6:int = 0;
         var _local_5:int = 0;
         var _local_1:* = null;
         var _local_4:* = null;
         var _local_2:* = null;
         if(!this._maleShopItemInfoList)
         {
            this._maleShopItemInfoList = new Vector.<ShopItemInfo>();
            _local_1 = [9,11,13,17,19,21];
            _local_3 = int(_local_1.length);
            _local_9 = 0;
            while(_local_9 < _local_3)
            {
               this._maleShopItemInfoList = this._maleShopItemInfoList.concat(ShopManager.Instance.getValidGoodByType(_local_1[_local_9]));
               _local_9++;
            }
            this._maleShopItemInfoList = this._maleShopItemInfoList.concat(ShopManager.Instance.getDisCountValidGoodByType(1));
         }
         if(!this._femaleShopItemInfoList)
         {
            this._femaleShopItemInfoList = new Vector.<ShopItemInfo>();
            _local_4 = [10,12,14,18,20,22];
            _local_8 = int(_local_4.length);
            _local_7 = 0;
            while(_local_7 < _local_8)
            {
               this._femaleShopItemInfoList = this._femaleShopItemInfoList.concat(ShopManager.Instance.getValidGoodByType(_local_4[_local_7]));
               _local_7++;
            }
            this._femaleShopItemInfoList = this._femaleShopItemInfoList.concat(ShopManager.Instance.getDisCountValidGoodByType(1));
         }
         if(!this._weaponShopItemInfoList)
         {
            this._weaponShopItemInfoList = new Vector.<ShopItemInfo>();
            _local_2 = [7,8];
            _local_6 = int(_local_2.length);
            _local_5 = 0;
            while(_local_5 < _local_6)
            {
               this._weaponShopItemInfoList = this._weaponShopItemInfoList.concat(ShopManager.Instance.getValidGoodByType(_local_2[_local_5]));
               _local_5++;
            }
            this._weaponShopItemInfoList = this._weaponShopItemInfoList.concat(ShopManager.Instance.getDisCountValidGoodByType(1));
         }
      }
      
      public function getTotleProgress() : Number
      {
         var _local_2:int = 0;
         var _local_6:int = 0;
         var _local_7:int = 0;
         var _local_3:DictionaryData = null;
         var _local_5:* = null;
         var _local_4:* = null;
         var _local_1:* = 0;
         for each(_local_3 in this._maleItemDic)
         {
            _local_5 = _local_3.list;
            _local_2 = int(_local_5.length);
            _local_6 = 0;
            _local_7 = 0;
            while(_local_7 < _local_5.length)
            {
               _local_4 = _local_5[_local_7] as AvatarCollectionItemVo;
               if(Boolean(_local_4.isActivity))
               {
                  _local_6++;
               }
               _local_7++;
            }
            if(_local_6 == _local_2)
            {
               _local_1 += 1;
            }
            else if(_local_6 >= _local_2 / 2)
            {
               _local_1 += 0.5;
            }
         }
         return _local_1;
      }
      
      public function checkItemCanBuy() : void
      {
         var _local_3:AvatarCollectionItemVo = null;
         var _local_5:AvatarCollectionItemVo = null;
         var _local_4:AvatarCollectionItemVo = null;
         var _local_6:ShopItemInfo = null;
         var _local_2:ShopItemInfo = null;
         var _local_1:ShopItemInfo = null;
         if(this._isHasCheckedBuy)
         {
            return;
         }
         for each(_local_3 in this._maleItemList)
         {
            _local_3.canBuyStatus = 0;
            for each(_local_6 in this._maleShopItemInfoList)
            {
               if(_local_3.itemId == _local_6.TemplateID)
               {
                  _local_3.canBuyStatus = 1;
                  _local_3.buyPrice = _local_6.getItemPrice(1).moneyValue;
                  _local_3.isDiscount = _local_6.isDiscount;
                  _local_3.goodsId = _local_6.GoodsID;
                  break;
               }
            }
         }
         for each(_local_5 in this._femaleItemList)
         {
            _local_5.canBuyStatus = 0;
            for each(_local_2 in this._femaleShopItemInfoList)
            {
               if(_local_5.itemId == _local_2.TemplateID)
               {
                  _local_5.canBuyStatus = 1;
                  _local_5.buyPrice = _local_2.getItemPrice(1).moneyValue;
                  _local_5.isDiscount = _local_2.isDiscount;
                  _local_5.goodsId = _local_2.GoodsID;
                  break;
               }
            }
         }
         for each(_local_4 in this._weaponItemList)
         {
            _local_4.canBuyStatus = 0;
            for each(_local_1 in this._weaponShopItemInfoList)
            {
               if(_local_4.itemId == _local_1.TemplateID)
               {
                  _local_4.canBuyStatus = 1;
                  _local_4.buyPrice = _local_1.getItemPrice(1).moneyValue;
                  _local_4.isDiscount = _local_1.isDiscount;
                  _local_4.goodsId = _local_1.GoodsID;
                  break;
               }
            }
         }
         this._isHasCheckedBuy = true;
      }
      
      public function getShopItemInfoByItemId(_arg_1:int, _arg_2:int, _arg_3:int) : ShopItemInfo
      {
         var _local_5:* = undefined;
         var _local_4:ShopItemInfo = null;
         if(_arg_3 == 1)
         {
            if(_arg_2 == 1)
            {
               _local_5 = this._maleShopItemInfoList;
            }
            else
            {
               _local_5 = this._femaleShopItemInfoList;
            }
         }
         else
         {
            _local_5 = this._weaponShopItemInfoList;
         }
         for each(_local_4 in _local_5)
         {
            if(_arg_1 == _local_4.TemplateID)
            {
               return _local_4;
            }
         }
         return null;
      }
      
      public function setup() : void
      {
         this._maleUnitDic = new DictionaryData();
         this._maleItemDic = new DictionaryData();
         this._femaleUnitDic = new DictionaryData();
         this._femaleItemDic = new DictionaryData();
         this._weaponUnitDic = new DictionaryData();
         this._weaponItemDic = new DictionaryData();
         this._petTalentTemplateDic = new DictionaryData();
         this._petTalentSuitDic = new DictionaryData();
         this._petTalentDataDic = new DictionaryData();
         SocketManager.Instance.addEventListener(PkgEvent.format(402),this.pkgHandler);
         SocketManager.Instance.addEventListener(PkgEvent.format(717,1),this.updatePetTalentAllInfo);
      }
      
      private function pkgHandler(_arg_1:PkgEvent) : void
      {
         var _local_3:PackageIn = _arg_1.pkg;
         var _local_2:int = _local_3.readByte();
         switch(_local_2)
         {
            case 5:
               if(this._gotAllInfo == false)
               {
                  this._gotAllInfo = true;
                  this.getAllInfoHandler(_local_3);
               }
               return;
            case 3:
               this.activeHandler(_local_3);
               return;
            case 4:
               this.delayTimeHandler(_local_3);
         }
      }
      
      private function getAllInfoHandler(_arg_1:PackageIn) : void
      {
         var _local_10:int = 0;
         var _local_6:int = 0;
         var _local_3:int = 0;
         var _local_7:int = 0;
         var _local_11:int = 0;
         var _local_2:int = 0;
         var _local_15:int = 0;
         var _local_9:int = 0;
         var _local_8:int = 0;
         var _local_4:int = 0;
         var _local_14:* = null;
         var _local_13:* = null;
         var _local_5:* = null;
         if(Boolean(this._avatarEndTimeList))
         {
            this._avatarEndTimeList.clear();
         }
         this._avatarEndTimeList = new DictionaryData();
         var _local_12:int = _arg_1.readInt();
         _local_10 = 0;
         while(_local_10 < _local_12)
         {
            _local_6 = _arg_1.readInt();
            _local_3 = _arg_1.readInt();
            _local_7 = 0;
            while(_local_7 < _local_3)
            {
               _local_11 = _arg_1.readInt();
               _local_2 = _arg_1.readInt();
               _local_14 = new AvatarCollectionUnitVo();
               if(_local_6 == 1)
               {
                  if(_local_2 == 1)
                  {
                     _local_14 = this._maleUnitDic[_local_11];
                     _local_13 = this._maleItemDic[_local_11];
                  }
                  else
                  {
                     _local_14 = this._femaleUnitDic[_local_11];
                     _local_13 = this._femaleItemDic[_local_11];
                  }
               }
               else if(_local_6 == 2)
               {
                  _local_14 = this._weaponUnitDic[_local_11];
                  _local_13 = this._weaponItemDic[_local_11];
               }
               _local_15 = _arg_1.readInt();
               _local_9 = 0;
               while(_local_9 < _local_15)
               {
                  _local_8 = _arg_1.readInt();
                  _local_4 = int(this._realItemIdDic[_local_8]);
                  if(Boolean(_local_13[_local_4]))
                  {
                     (_local_13[_local_4] as AvatarCollectionItemVo).isActivity = true;
                  }
                  _local_9++;
               }
               _local_5 = _arg_1.readDate();
               _local_14.endTime = _local_5;
               this._avatarEndTimeList.add(_local_11,{
                  "id":_local_11,
                  "endTime":_local_5.time
               });
               _local_7++;
            }
            _local_10++;
         }
         this.isDataComplete = true;
         dispatchEvent(new Event("avatar_collection_data_complete"));
      }
      
      private function activeHandler(_arg_1:PackageIn) : void
      {
         var _local_2:int = _arg_1.readInt();
         var _local_5:int = _arg_1.readInt();
         _local_5 = int(this._realItemIdDic[_local_5]);
         var _local_3:int = _arg_1.readInt();
         var _local_4:int = _arg_1.readInt();
         if(_local_4 == 1)
         {
            if(_local_3 == 1)
            {
               (this._maleItemDic[_local_2][_local_5] as AvatarCollectionItemVo).isActivity = true;
            }
            else
            {
               (this._femaleItemDic[_local_2][_local_5] as AvatarCollectionItemVo).isActivity = true;
            }
         }
         else if(_local_4 == 2)
         {
            (this._weaponItemDic[_local_2][_local_5] as AvatarCollectionItemVo).isActivity = true;
         }
         dispatchEvent(new Event("avatar_collection_refresh_view"));
      }
      
      private function delayTimeHandler(_arg_1:PackageIn) : void
      {
         var _local_2:int = _arg_1.readInt();
         var _local_3:int = _arg_1.readInt();
         var _local_5:int = _arg_1.readInt();
         var _local_4:Date = _arg_1.readDate();
         if(_local_5 == 1)
         {
            if(_local_3 == 1)
            {
               (this._maleUnitDic[_local_2] as AvatarCollectionUnitVo).endTime = _local_4;
            }
            else
            {
               (this._femaleUnitDic[_local_2] as AvatarCollectionUnitVo).endTime = _local_4;
            }
         }
         else if(_local_5 == 2)
         {
            (this._weaponUnitDic[_local_2] as AvatarCollectionUnitVo).endTime = _local_4;
         }
         this._avatarEndTimeList.add(_local_2,{
            "id":_local_2,
            "endTime":_local_4.time
         });
         dispatchEvent(new Event("avatar_collection_refresh_view"));
      }
      
      public function isCollectionGoodsByTemplateID(_arg_1:int) : Boolean
      {
         return this._allGoodsTemplateIDlist[_arg_1];
      }
      
      public function showFrame(_arg_1:Sprite) : void
      {
         this.cevent = new CEvent("openview",{"parent":_arg_1});
         AssetModuleLoader.addModelLoader("avatarcollection",7);
         AssetModuleLoader.startCodeLoader(this.start);
      }
      
      protected function start() : void
      {
         dispatchEvent(this.cevent);
         this.cevent = null;
      }
      
      public function closeFrame() : void
      {
         this.resetListCellData();
         dispatchEvent(new CEvent("closeView"));
      }
      
      public function visible(_arg_1:Boolean) : void
      {
         dispatchEvent(new CEvent("visible",{"visible":_arg_1}));
      }
      
      public function get avatarEndTimeList() : DictionaryData
      {
         return this._avatarEndTimeList;
      }
      
      public function get petTalentDataDic() : DictionaryData
      {
         if(!this._petTalentDataDic)
         {
            this._petTalentDataDic = new DictionaryData();
         }
         return this._petTalentDataDic;
      }
      
      public function getPetTalentSuitTemplateArr() : Array
      {
         var _local_2:AvatarCollPetTalentSuitVo = null;
         var _local_1:Array = [];
         if(!this._petTalentDataDic)
         {
            this._petTalentDataDic = new DictionaryData();
         }
         for each(_local_2 in this._petTalentSuitDic)
         {
            if(Boolean(_local_2))
            {
               _local_1.push(_local_2.GroupID);
            }
         }
         return _local_1;
      }
      
      public function petTalentItemDataAnalyze(_arg_1:AvatarCollPetTalentItemDataAnalyze) : void
      {
         this._petTalentTemplateDic = _arg_1.talentItemDic;
      }
      
      public function petTalentSuitDataAnalyze(_arg_1:AvatarCollPetTalentSuitDataAnalyze) : void
      {
         this._petTalentSuitDic = _arg_1.suitDic;
      }
      
      public function getPetTalentItemVoByTypeAndLv(_arg_1:int, _arg_2:int) : AvatarCollPetTalentItemVo
      {
         var _local_3:* = null;
         if(!this._petTalentTemplateDic[_arg_1])
         {
            return null;
         }
         return this._petTalentTemplateDic[_arg_1][_arg_2];
      }
      
      public function getPetTalentSuitTemplateById(_arg_1:int) : AvatarCollPetTalentSuitVo
      {
         var _local_2:* = null;
         return this._petTalentSuitDic[_arg_1];
      }
      
      public function getPetTalentMaxLvByType(_arg_1:int) : int
      {
         var _local_2:int = 0;
         var _local_3:PetsSystemTalentSkillInfo = PetsManager.instance.petsModel.getTalentSkillInfoByType(_arg_1);
         if(Boolean(_local_3))
         {
            _local_2 = _local_3.level;
         }
         else
         {
            _local_2 = 0;
         }
         return _local_2;
      }
      
      public function getCurSuitSkillId(_arg_1:int) : int
      {
         var _local_2:int = 0;
         var _local_4:AvatarCollPetTalentSuitVo = this.getPetTalentSuitTemplateById(_arg_1);
         var _local_3:int = this.getCurGroupTotalActiveLV(_arg_1);
         if(_local_3 >= _local_4.Count1)
         {
            if(_local_3 >= _local_4.Count2)
            {
               _local_2 = _local_4.Skill2;
            }
            else
            {
               _local_2 = _local_4.Skill1;
            }
         }
         else
         {
            _local_2 = _local_4.Skill1;
         }
         return _local_2;
      }
      
      public function getOneKeyLvUpPetTalentCount(_arg_1:int) : int
      {
         var _local_10:int = 0;
         var _local_8:int = 0;
         var _local_4:int = 0;
         var _local_2:int = 0;
         var _local_7:int = 0;
         var _local_6:int = 0;
         var _local_3:int = 0;
         var _local_9:* = null;
         var _local_5:Array = (this._petTalentSuitDic[_arg_1] as AvatarCollPetTalentSuitVo).SuitElement;
         _local_10 = 0;
         while(_local_10 < _local_5.length)
         {
            _local_8 = int(_local_5[_local_10]);
            _local_4 = this.getCurPetTalentItemLV(_local_8);
            _local_2 = this.getPetTalentMaxLvByType(_local_8);
            if(_local_4 < _local_2)
            {
               _local_7 = _local_4;
               while(_local_7 < _local_2)
               {
                  _local_9 = this.getPetTalentItemVoByTypeAndLv(_local_8,_local_7);
                  _local_6 = int(_local_9.needCount);
                  _local_3 += _local_6;
                  _local_7++;
               }
            }
            _local_10++;
         }
         return _local_3;
      }
      
      public function getCurPetTalentItemLV(_arg_1:int) : int
      {
         var _local_2:int = 0;
         if(Boolean(this._petTalentDataDic[_arg_1]))
         {
            _local_2 = int(this._petTalentDataDic[_arg_1]);
         }
         return _local_2;
      }
      
      public function getCurGroupTotalActiveLV(_arg_1:int) : int
      {
         var _local_5:int = 0;
         var _local_4:int = 0;
         var _local_2:int = 0;
         var _local_3:Array = this.getPetTalentSuitTemplateById(_arg_1).SuitElement;
         _local_5 = 0;
         while(_local_5 < _local_3.length)
         {
            _local_4 = this.getCurPetTalentItemLV(_local_3[_local_5]);
            _local_2 += _local_4;
            _local_5++;
         }
         return _local_2;
      }
      
      public function getCurPetTalentSuitTotalLv(_arg_1:int) : int
      {
         var _local_2:int = 0;
         var _local_3:AvatarCollPetTalentSuitVo = this.getPetTalentSuitTemplateById(_arg_1);
         return _local_3.Count2;
      }
      
      public function getPetTalentTotalPropertyArr(_arg_1:int) : Array
      {
         var _local_7:int = 0;
         var _local_2:int = 0;
         var _local_9:int = 0;
         var _local_5:int = 0;
         var _local_6:int = 0;
         var _local_4:* = undefined;
         var _local_8:* = null;
         if(!this._petTalentTotalProDic)
         {
            this._petTalentTotalProDic = new DictionaryData();
         }
         if(Boolean(this._petTalentTotalProDic[_arg_1]))
         {
            return this._petTalentTotalProDic[_arg_1];
         }
         var _local_3:Array = [];
         for each(_local_4 in this._petTalentTemplateDic)
         {
            _local_8 = _local_4[10];
            if(_local_8)
            {
               if(_local_8.groupID == _arg_1)
               {
                  _local_7 += _local_8.attack;
                  _local_2 += _local_8.defence;
                  _local_9 += _local_8.agility;
                  _local_5 += _local_8.luck;
                  _local_6 += _local_8.blood;
               }
            }
         }
         _local_3 = [_local_7,_local_2,_local_9,_local_5,_local_6];
         this._petTalentTotalProDic.add(_arg_1,_local_3);
         return this._petTalentTotalProDic[_arg_1];
      }
      
      public function getCurTotalPropertyArrByGroupID(_arg_1:int) : Array
      {
         var _local_9:int = 0;
         var _local_11:int = 0;
         var _local_14:int = 0;
         var _local_13:int = 0;
         var _local_12:int = 0;
         var _local_7:int = 0;
         var _local_4:int = 0;
         var _local_5:int = 0;
         var _local_10:* = null;
         var _local_3:Array = [];
         var _local_2:Array = this.getPetTalentTotalPropertyArr(_arg_1);
         var _local_8:Array = this.getPetTalentSuitTemplateById(_arg_1).SuitElement;
         _local_9 = 0;
         while(_local_9 < _local_8.length)
         {
            _local_11 = int(_local_8[_local_9]);
            _local_14 = this.getCurPetTalentItemLV(_local_11);
            _local_10 = this.getPetTalentItemVoByTypeAndLv(_local_11,_local_14);
            _local_13 += _local_10.attack;
            _local_12 += _local_10.defence;
            _local_7 += _local_10.agility;
            _local_4 += _local_10.luck;
            _local_5 += _local_10.blood;
            _local_9++;
         }
         var _local_6:Array = LanguageMgr.GetTranslation("tank.newCard.ProName").split(",");
         if(_local_13 >= 0)
         {
            _local_3.push({
               "proName":_local_6[1],
               "count":_local_13 + "/" + _local_2[0]
            });
         }
         if(_local_12 >= 0)
         {
            _local_3.push({
               "proName":_local_6[2],
               "count":_local_12 + "/" + _local_2[1]
            });
         }
         if(_local_7 >= 0)
         {
            _local_3.push({
               "proName":_local_6[3],
               "count":_local_7 + "/" + _local_2[2]
            });
         }
         if(_local_4 >= 0)
         {
            _local_3.push({
               "proName":_local_6[4],
               "count":_local_4 + "/" + _local_2[3]
            });
         }
         if(_local_5 >= 0)
         {
            _local_3.push({
               "proName":_local_6[0],
               "count":_local_5 + "/" + _local_2[4]
            });
         }
         return _local_3;
      }
      
      public function getCurTotalPropertyArr() : Array
      {
         var _local_7:int = 0;
         var _local_1:int = 0;
         var _local_8:int = 0;
         var _local_5:int = 0;
         var _local_6:int = 0;
         var _local_4:AvatarCollPetTalentSuitVo = null;
         var _local_2:Array = [0,0,0,0,0,0];
         var _local_3:Array = [];
         for each(_local_4 in this._petTalentSuitDic)
         {
            if(Boolean(_local_4))
            {
               _local_3 = this.getCurTotalPropertyArrByGroupID(_local_4.GroupID);
               _local_7 += int((_local_3[0]["count"] as String).split("/")[0]);
               _local_1 += int((_local_3[1]["count"] as String).split("/")[0]);
               _local_8 += int((_local_3[2]["count"] as String).split("/")[0]);
               _local_5 += int((_local_3[3]["count"] as String).split("/")[0]);
               _local_6 += int((_local_3[4]["count"] as String).split("/")[0]);
            }
         }
         return [_local_7,_local_1,_local_8,_local_5,_local_6];
      }
      
      public function setPageVisible(_arg_1:Boolean) : void
      {
         dispatchEvent(new CEvent("setPage_visible",_arg_1));
      }
      
      public function get selectedPTGroupID() : int
      {
         return this._selectedPTGroupID;
      }
      
      public function updatePetTalentAllInfo(_arg_1:PkgEvent) : void
      {
         var index:int = 0;
         var _local_8:int = 0;
         var _local_5:int = 0;
         var _local_3:int = 0;
         var _local_4:PackageIn = _arg_1.pkg;
         this._selectedPTGroupID = _local_4.readInt();
         this._selectedPTSkillID = _local_4.readInt();
         var len:int = _local_4.readInt();
         index = 0;
         while(index < len)
         {
            _local_8 = _local_4.readInt();
            _local_5 = _local_4.readInt();
            _local_3 = _local_4.readInt();
            if(!this._petTalentDataDic)
            {
               this._petTalentDataDic = new DictionaryData();
            }
            this._petTalentDataDic[_local_8] = _local_3;
            index++;
         }
         dispatchEvent(new CEvent("refresh_view_petTalent"));
      }
      
      public function sendGetPetTalentAllInfo() : void
      {
         SocketManager.Instance.out.sendGetPetTalentAllInfo();
      }
      
      public function sendUpGradeOnePetTalent(_arg_1:int, _arg_2:*, _arg_3:int) : void
      {
         SocketManager.Instance.out.sendUpGradeOnePetTalent(_arg_1,_arg_2,_arg_3);
      }
      
      public function sendSelectPetTalentSkill(_arg_1:int, _arg_2:int) : void
      {
         SocketManager.Instance.out.sendSelectPetTalentSkill(_arg_1,_arg_2);
      }
   }
}

