package consortion.view.selfConsortia
{
   import baglocked.BaglockedManager;
   import com.pickgliss.events.FrameEvent;
   import com.pickgliss.ui.AlertManager;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.ShowTipManager;
   import com.pickgliss.ui.controls.SimpleBitmapButton;
   import com.pickgliss.ui.controls.alert.BaseAlerFrame;
   import com.pickgliss.ui.core.Disposeable;
   import com.pickgliss.ui.image.MutipleImage;
   import com.pickgliss.ui.image.ScaleBitmapImage;
   import com.pickgliss.ui.text.FilterFrameText;
   import com.pickgliss.utils.ObjectUtils;
   import consortion.ConsortionModelManager;
   import consortion.data.BadgeInfo;
   import ddt.manager.LanguageMgr;
   import ddt.manager.MessageTipManager;
   import ddt.manager.PlayerManager;
   import ddt.manager.SocketManager;
   import ddt.manager.SoundManager;
   import ddt.utils.PositionUtils;
   import flash.display.DisplayObject;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class BadgeShopItem extends Sprite implements Disposeable
   {
      
      private var _badge:Badge;
      
      private var _btn:SimpleBitmapButton;
      
      private var _nametxt:FilterFrameText;
      
      private var _day:FilterFrameText;
      
      private var _pay:FilterFrameText;
      
      private var _info:BadgeInfo;
      
      private var _bg:ScaleBitmapImage;
      
      private var _cellBG:DisplayObject;
      
      private var _line:MutipleImage;
      
      private var _alert:BaseAlerFrame;
      
      public function BadgeShopItem(_arg_1:BadgeInfo)
      {
         super();
         this._info = _arg_1;
         this.initView();
         this.initEvent();
      }
      
      private function initView() : void
      {
         this._bg = ComponentFactory.Instance.creatComponentByStylename("badgeShopFrame.ItemBG");
         addChild(this._bg);
         this._cellBG = ComponentFactory.Instance.creatCustomObject("badgeShopFrame.ItemCellBG");
         addChild(this._cellBG);
         this._badge = new Badge("normal");
         this._badge.badgeID = this._info.BadgeID;
         PositionUtils.setPos(this._badge,"badgeshopItem.pos");
         ShowTipManager.Instance.addTip(this._badge);
         this._nametxt = ComponentFactory.Instance.creatComponentByStylename("consortion.badgeShopItem.name");
         addChild(this._nametxt);
         this._nametxt.text = this._info.BadgeName;
         this._btn = ComponentFactory.Instance.creatComponentByStylename("consortion.buyBadgeItemBtn");
         this._day = ComponentFactory.Instance.creatComponentByStylename("consortion.shopItemBtn.day");
         this._pay = ComponentFactory.Instance.creatComponentByStylename("consortion.shopItemBtn.Pay");
         PositionUtils.setPos(this._pay,"consortion.shopItemBtn.Pay.pos");
         addChild(this._badge);
         addChild(this._btn);
         this._btn.addChild(this._day);
         this._btn.addChild(this._pay);
         this._pay.text = this._info.Cost.toString() + LanguageMgr.GetTranslation("consortia.Money");
         this._day.text = this._info.ValidDate.toString() + LanguageMgr.GetTranslation("shop.ShopIIShoppingCarItem.day");
         this._line = ComponentFactory.Instance.creatComponentByStylename("consortion.badgeShop.VerticalLine");
         addChild(this._line);
         if(PlayerManager.Instance.Self.consortiaInfo.Level < this._info.LimitLevel)
         {
            filters = ComponentFactory.Instance.creatFilters("grayFilter");
         }
         else
         {
            filters = null;
         }
      }
      
      private function initEvent() : void
      {
         this._btn.addEventListener("click",this.onClick);
      }
      
      private function removeEvent() : void
      {
         this._btn.removeEventListener("click",this.onClick);
      }
      
      private function onClick(_arg_1:MouseEvent) : void
      {
         SoundManager.instance.playButtonSound();
         if(PlayerManager.Instance.Self.bagLocked)
         {
            BaglockedManager.Instance.show();
            return;
         }
         if(PlayerManager.Instance.Self.consortiaInfo.Level >= this._info.LimitLevel)
         {
            if(PlayerManager.Instance.Self.consortiaInfo.Riches >= this._info.Cost)
            {
               if(Boolean(this._alert))
               {
                  this._alert.removeEventListener("response",this.onResponse);
                  ObjectUtils.disposeObject(this._alert);
               }
               this._alert = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("AlertDialog.Info"),LanguageMgr.GetTranslation("tank.view.common.AddPricePanel.pay"),LanguageMgr.GetTranslation("ok"),LanguageMgr.GetTranslation("cancel"),false,false,false,2);
               this._alert.addEventListener("response",this.onResponse);
            }
            else
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.consortion.skillItem.click.enough1"));
               ConsortionModelManager.Instance.alertTaxFrame();
            }
         }
         else
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("consortia.buyBadge.levelTooLow"));
         }
      }
      
      private function onResponse(_arg_1:FrameEvent) : void
      {
         this._alert.removeEventListener("response",this.onResponse);
         this._alert.dispose();
         this._alert = null;
         SoundManager.instance.playButtonSound();
         switch(_arg_1.responseCode)
         {
            case 2:
            case 3:
               SocketManager.Instance.out.sendBuyBadge(this._info.BadgeID);
         }
      }
      
      public function dispose() : void
      {
         this.removeEvent();
         if(Boolean(this._alert))
         {
            this._alert.removeEventListener("response",this.onResponse);
            this._alert.dispose();
         }
         this._alert = null;
         ShowTipManager.Instance.removeTip(this._badge);
         ObjectUtils.disposeObject(this._badge);
         this._badge = null;
         ObjectUtils.disposeObject(this._btn);
         this._btn = null;
         ObjectUtils.disposeObject(this._nametxt);
         this._nametxt = null;
         ObjectUtils.disposeObject(this._day);
         this._day = null;
         ObjectUtils.disposeObject(this._pay);
         this._pay = null;
         ObjectUtils.disposeObject(this._bg);
         this._bg = null;
         ObjectUtils.disposeObject(this._cellBG);
         this._cellBG = null;
         ObjectUtils.disposeObject(this._line);
         this._line = null;
         this._info = null;
         if(Boolean(parent))
         {
            parent.removeChild(this);
         }
      }
   }
}

