package cityBattle.view
{
   import bagAndInfo.cell.BagCell;
   import baglocked.BaglockedManager;
   import cityBattle.CityBattleManager;
   import cityBattle.data.WelfareInfo;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.LayerManager;
   import com.pickgliss.ui.controls.SimpleBitmapButton;
   import com.pickgliss.ui.core.Disposeable;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.manager.ItemManager;
   import ddt.manager.PlayerManager;
   import ddt.manager.SoundManager;
   import flash.display.Bitmap;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class WelfareCell extends Sprite implements Disposeable
   {
      
      private var _exchangeBtn:SimpleBitmapButton;
      
      private var _bagCell:BagCell;
      
      private var _info:WelfareInfo;
      
      private var _lockIcon:Bitmap;
      
      private var open:Boolean = true;
      
      public function WelfareCell()
      {
         super();
         this.initView();
         this.addEvent();
      }
      
      private function initView() : void
      {
         this._bagCell = new BagCell(0);
         addChild(this._bagCell);
         this._exchangeBtn = ComponentFactory.Instance.creatComponentByStylename("welfare.exchangebtn");
         addChild(this._exchangeBtn);
         this._exchangeBtn.visible = false;
         this._lockIcon = ComponentFactory.Instance.creatBitmap("asset.cityBattle.lock");
         addChild(this._lockIcon);
      }
      
      private function addEvent() : void
      {
         this._exchangeBtn.addEventListener("click",this.__exchangeHandler);
         addEventListener("rollOver",this.__overHandler);
         addEventListener("rollOut",this.__outHandler);
      }
      
      protected function __outHandler(_arg_1:MouseEvent) : void
      {
         this._exchangeBtn.visible = false;
      }
      
      protected function __overHandler(_arg_1:MouseEvent) : void
      {
         if(this.open)
         {
            this._exchangeBtn.visible = true;
         }
      }
      
      private function removeEvent() : void
      {
         this._exchangeBtn.removeEventListener("click",this.__exchangeHandler);
         removeEventListener("rollOver",this.__overHandler);
         removeEventListener("rollOut",this.__outHandler);
      }
      
      private function __exchangeHandler(_arg_1:MouseEvent) : void
      {
         SoundManager.instance.playButtonSound();
         if(PlayerManager.Instance.Self.bagLocked)
         {
            BaglockedManager.Instance.show();
            return;
         }
         var _local_2:QuickExchangeFrame = ComponentFactory.Instance.creatComponentByStylename("welfare.exchangeFrame");
         _local_2.setData(this._info.TemplateID,this._info.ID,this._info.NeedScore);
         _local_2.type = this._info.Quality;
         LayerManager.Instance.addToLayer(_local_2,3,true,1);
      }
      
      public function set info(_arg_1:WelfareInfo) : void
      {
         this._info = _arg_1;
         this._bagCell.info = ItemManager.Instance.getTemplateById(this._info.TemplateID);
         var _local_2:* = 77;
         this._bagCell.height = _local_2;
         this._bagCell.width = _local_2;
         if(this._info.Quality == 3)
         {
            if(CityBattleManager.instance.mySide == CityBattleManager.instance.winnerExchangeInfo[this._info.Probability - 1])
            {
               this.open = true;
            }
            else
            {
               this.open = false;
            }
         }
         else
         {
            this.open = true;
         }
         this._lockIcon.visible = !this.open;
      }
      
      public function get info() : WelfareInfo
      {
         return this._info;
      }
      
      public function dispose() : void
      {
         this.removeEvent();
         while(Boolean(this.numChildren))
         {
            ObjectUtils.disposeObject(this.getChildAt(0));
         }
         if(Boolean(parent))
         {
            parent.removeChild(this);
         }
      }
   }
}

