package com.pickgliss.ui.controls
{
   import com.pickgliss.geom.InnerRectangle;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.utils.ClassUtils;
   import com.pickgliss.utils.DisplayUtils;
   import com.pickgliss.utils.ObjectUtils;
   import flash.display.DisplayObject;
   import flash.events.Event;
   
   public class SelectedIconButton extends SelectedButton
   {
      
      public static const P_icon:String = "icon";
      
      public static const P_iconInnerRect:String = "iconInnerRect";
      
      protected var _selectedIcon:DisplayObject;
      
      protected var _selectedIconInnerRect:InnerRectangle;
      
      protected var _selectedIconInnerRectString:String;
      
      protected var _selectedIconStyle:String;
      
      protected var _unselectedIcon:DisplayObject;
      
      protected var _unselectedIconInnerRect:InnerRectangle;
      
      protected var _unselectedIconInnerRectString:String;
      
      protected var _unselectedIconStyle:String;
      
      public function SelectedIconButton()
      {
         super();
      }
      
      override public function dispose() : void
      {
         super.dispose();
      }
      
      override public function set selected(_arg_1:<PERSON><PERSON>an) : void
      {
         _selected = _arg_1;
         if(<PERSON><PERSON><PERSON>(_selectedButton))
         {
            _selectedButton.visible = _selected;
         }
         if(Boolean(_unSelectedButton))
         {
            _unSelectedButton.visible = !_selected;
         }
         if(Boolean(this._selectedIcon))
         {
            this._selectedIcon.visible = _selected;
         }
         if(Boolean(this._unselectedIcon))
         {
            this._unselectedIcon.visible = !_selected;
         }
         dispatchEvent(new Event("select"));
         drawHitArea();
      }
      
      public function set selectedIcon(_arg_1:DisplayObject) : void
      {
         if(this._selectedIcon == _arg_1)
         {
            return;
         }
         ObjectUtils.disposeObject(this._selectedIcon);
         this._selectedIcon = _arg_1;
         onPropertiesChanged("icon");
      }
      
      public function set selectedIconInnerRectString(_arg_1:String) : void
      {
         if(this._selectedIconInnerRectString == _arg_1)
         {
            return;
         }
         this._selectedIconInnerRectString = _arg_1;
         this._selectedIconInnerRect = ClassUtils.CreatInstance("com.pickgliss.geom.InnerRectangle",ComponentFactory.parasArgs(this._selectedIconInnerRectString));
         onPropertiesChanged("iconInnerRect");
      }
      
      public function set selectedIconStyle(_arg_1:String) : void
      {
         if(this._selectedIconStyle == _arg_1)
         {
            return;
         }
         this._selectedIconStyle = _arg_1;
         this._selectedIcon = ComponentFactory.Instance.creat(this._selectedIconStyle);
      }
      
      public function set unselectedIcon(_arg_1:DisplayObject) : void
      {
         if(this._unselectedIcon == _arg_1)
         {
            return;
         }
         ObjectUtils.disposeObject(this._unselectedIcon);
         this._unselectedIcon = _arg_1;
         onPropertiesChanged("icon");
      }
      
      public function set unselectedIconInnerRectString(_arg_1:String) : void
      {
         if(this._unselectedIconInnerRectString == _arg_1)
         {
            return;
         }
         this._unselectedIconInnerRectString = _arg_1;
         this._unselectedIconInnerRect = ClassUtils.CreatInstance("com.pickgliss.geom.InnerRectangle",ComponentFactory.parasArgs(this._unselectedIconInnerRectString));
         onPropertiesChanged("iconInnerRect");
      }
      
      public function set unselectedIconStyle(_arg_1:String) : void
      {
         if(this._unselectedIconStyle == _arg_1)
         {
            return;
         }
         this._unselectedIconStyle = _arg_1;
         this._unselectedIcon = ComponentFactory.Instance.creat(this._unselectedIconStyle);
      }
      
      override protected function addChildren() : void
      {
         super.addChildren();
         if(Boolean(this._selectedIcon))
         {
            addChild(this._selectedIcon);
         }
         if(Boolean(this._unselectedIcon))
         {
            addChild(this._unselectedIcon);
         }
      }
      
      override protected function onProppertiesUpdate() : void
      {
         super.onProppertiesUpdate();
         if(Boolean(_changedPropeties["width"]) || Boolean(_changedPropeties["height"]) || Boolean(_changedPropeties["iconInnerRect"]) || Boolean(_changedPropeties["icon"]))
         {
            this.updateIconPos();
         }
      }
      
      override public function setFrame(_arg_1:int) : void
      {
         super.setFrame(_arg_1);
         DisplayUtils.setFrame(this._selectedIcon,_arg_1);
         DisplayUtils.setFrame(this._unselectedIcon,_arg_1);
      }
      
      protected function updateIconPos() : void
      {
         if(Boolean(this._unselectedIcon) && Boolean(this._unselectedIconInnerRect))
         {
            DisplayUtils.layoutDisplayWithInnerRect(this._unselectedIcon,this._unselectedIconInnerRect,_width,_height);
         }
         if(Boolean(this._selectedIcon) && Boolean(this._selectedIconInnerRect))
         {
            DisplayUtils.layoutDisplayWithInnerRect(this._selectedIcon,this._selectedIconInnerRect,_width,_height);
         }
      }
   }
}

