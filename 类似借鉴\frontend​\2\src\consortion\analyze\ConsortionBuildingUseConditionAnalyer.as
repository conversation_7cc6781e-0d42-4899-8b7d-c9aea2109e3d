package consortion.analyze
{
   import com.pickgliss.loader.DataAnalyzer;
   import com.pickgliss.utils.ObjectUtils;
   import consortion.data.ConsortiaAssetLevelOffer;
   
   public class ConsortionBuildingUseConditionAnalyer extends DataAnalyzer
   {
      
      public var useConditionList:Vector.<ConsortiaAssetLevelOffer>;
      
      public function ConsortionBuildingUseConditionAnalyer(_arg_1:Function)
      {
         super(_arg_1);
      }
      
      override public function analyze(_arg_1:*) : void
      {
         var _local_5:int = 0;
         var _local_4:* = null;
         var _local_3:* = null;
         this.useConditionList = new Vector.<ConsortiaAssetLevelOffer>();
         var _local_2:XML = new XML(_arg_1);
         if(_local_2.@value == "true")
         {
            _local_4 = XML(_local_2)..Item;
            _local_5 = 0;
            while(_local_5 < _local_4.length())
            {
               _local_3 = new ConsortiaAssetLevelOffer();
               ObjectUtils.copyPorpertiesByXML(_local_3,_local_4[_local_5]);
               this.useConditionList.push(_local_3);
               _local_5++;
            }
            onAnalyzeComplete();
         }
         else
         {
            message = _local_2.@message;
            onAnalyzeError();
            onAnalyzeComplete();
         }
      }
   }
}

