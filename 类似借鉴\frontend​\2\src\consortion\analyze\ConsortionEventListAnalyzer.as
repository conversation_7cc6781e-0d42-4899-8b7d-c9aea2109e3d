package consortion.analyze
{
   import com.pickgliss.loader.DataAnalyzer;
   import ddt.data.ConsortiaEventInfo;
   
   public class ConsortionEventListAnalyzer extends DataAnalyzer
   {
      
      public var eventList:Vector.<ConsortiaEventInfo>;
      
      public function ConsortionEventListAnalyzer(_arg_1:Function = null)
      {
         super(_arg_1);
      }
      
      override public function analyze(_arg_1:*) : void
      {
         var _local_5:int = 0;
         var _local_3:* = null;
         var _local_4:* = null;
         this.eventList = new Vector.<ConsortiaEventInfo>();
         var _local_2:XML = new XML(_arg_1);
         if(_local_2.@value == "true")
         {
            _local_3 = _local_2..Item;
            _local_5 = 0;
            while(_local_5 < _local_3.length())
            {
               _local_4 = new ConsortiaEventInfo();
               _local_4.ID = _local_3[_local_5].@ID;
               _local_4.ConsortiaID = _local_3[_local_5].@ConsortiaID;
               _local_4.Date = _local_3[_local_5].@Date;
               _local_4.Type = _local_3[_local_5].@Type;
               _local_4.NickName = _local_3[_local_5].@NickName;
               _local_4.EventValue = _local_3[_local_5].@EventValue;
               _local_4.ManagerName = _local_3[_local_5].@ManagerName;
               this.eventList.push(_local_4);
               _local_5++;
            }
            onAnalyzeComplete();
         }
         else
         {
            message = _local_2.@message;
            onAnalyzeError();
            onAnalyzeComplete();
         }
      }
   }
}

