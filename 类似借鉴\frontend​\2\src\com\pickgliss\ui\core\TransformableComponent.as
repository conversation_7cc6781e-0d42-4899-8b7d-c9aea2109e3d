package com.pickgliss.ui.core
{
   import com.pickgliss.ui.ShowTipManager;
   
   public class TransformableComponent extends Component implements ITransformableTipedDisplay
   {
      
      public static const P_tipWidth:String = "tipWidth";
      
      public static const P_tipHeight:String = "tipHeight";
      
      protected var _tipWidth:int;
      
      protected var _tipHeight:int;
      
      public function TransformableComponent()
      {
         super();
      }
      
      public function get tipWidth() : int
      {
         return this._tipWidth;
      }
      
      public function set tipWidth(_arg_1:int) : void
      {
         if(this._tipWidth == _arg_1)
         {
            return;
         }
         this._tipWidth = _arg_1;
         onPropertiesChanged("tipWidth");
      }
      
      public function get tipHeight() : int
      {
         return this._tipHeight;
      }
      
      public function set tipHeight(_arg_1:int) : void
      {
         if(this._tipHeight == _arg_1)
         {
            return;
         }
         this._tipHeight = _arg_1;
         onPropertiesChanged("tipHeight");
      }
      
      override protected function onProppertiesUpdate() : void
      {
         if(Boolean(_changedPropeties["tipWidth"]) || Boolean(_changedPropeties["tipHeight"]) || Boolean(_changedPropeties["tipDirction"]) || Boolean(_changedPropeties["tipGap"]) || Boolean(_changedPropeties["tipStyle"]) || Boolean(_changedPropeties["tipData"]))
         {
            ShowTipManager.Instance.addTip(this);
         }
      }
   }
}

