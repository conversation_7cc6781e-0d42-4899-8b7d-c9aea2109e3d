package consortion.analyze
{
   import com.pickgliss.loader.DataAnalyzer;
   import com.pickgliss.utils.ObjectUtils;
   import consortion.rank.RankData;
   
   public class PersonalRankAnalyze extends DataAnalyzer
   {
      
      private var _dataList:Array;
      
      public function PersonalRankAnalyze(_arg_1:Function)
      {
         super(_arg_1);
      }
      
      public function get dataList() : Array
      {
         return this._dataList;
      }
      
      override public function analyze(_arg_1:*) : void
      {
         var _local_5:int = 0;
         var _local_2:* = null;
         this._dataList = [];
         var _local_3:XML = new XML(_arg_1);
         var _local_4:XMLList = _local_3.Item;
         _local_5 = 0;
         while(_local_5 < _local_4.length())
         {
            _local_2 = new RankData();
            ObjectUtils.copyPorpertiesByXML(_local_2,_local_4[_local_5]);
            this._dataList.push(_local_2);
            _local_5++;
         }
         this._dataList.sortOn("Rank",16);
         onAnalyzeComplete();
      }
   }
}

