package chickActivation.model
{
   import chickActivation.data.ChickActivationInfo;
   import chickActivation.event.ChickActivationEvent;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.data.goods.InventoryItemInfo;
   import ddt.data.goods.ItemTemplateInfo;
   import ddt.manager.ItemManager;
   import ddt.manager.TimeManager;
   import flash.events.EventDispatcher;
   import flash.events.IEventDispatcher;
   import flash.utils.Dictionary;
   
   public class ChickActivationModel extends EventDispatcher
   {
      
      public var isOpen:Boolean = true;
      
      private var _itemInfoList:Array;
      
      public var qualityDic:Dictionary;
      
      public var isKeyOpened:int;
      
      public var keyIndex:int;
      
      public var keyOpenedTime:Date;
      
      public var keyOpenedType:int;
      
      public var gainArr:Array = [];
      
      public function ChickActivationModel(_arg_1:IEventDispatcher = null)
      {
         super(_arg_1);
      }
      
      public function getInventoryItemInfo(_arg_1:ChickActivationInfo) : InventoryItemInfo
      {
         var _local_3:ItemTemplateInfo = ItemManager.Instance.getTemplateById(_arg_1.TemplateID);
         var _local_2:InventoryItemInfo = new InventoryItemInfo();
         ObjectUtils.copyProperties(_local_2,_local_3);
         _local_2.LuckCompose = _arg_1.TemplateID;
         _local_2.ValidDate = _arg_1.ValidDate;
         _local_2.Count = _arg_1.Count;
         _local_2.IsBinds = _arg_1.IsBind;
         _local_2.StrengthenLevel = _arg_1.StrengthLevel;
         _local_2.AttackCompose = _arg_1.AttackCompose;
         _local_2.DefendCompose = _arg_1.DefendCompose;
         _local_2.AgilityCompose = _arg_1.AgilityCompose;
         _local_2.LuckCompose = _arg_1.LuckCompose;
         return _local_2;
      }
      
      public function findQualityValue(_arg_1:String) : int
      {
         var _local_2:int = 0;
         if(this.qualityDic.hasOwnProperty(_arg_1))
         {
            _local_2 = int(this.qualityDic[_arg_1]);
         }
         return _local_2;
      }
      
      public function getRemainingDay() : int
      {
         var _local_2:int = 0;
         var _local_1:* = 86400000;
         if(Boolean(this.isKeyOpened) && Boolean(this.keyOpenedTime))
         {
            _local_2 = int(int(Math.ceil((60 * _local_1 - (TimeManager.Instance.Now().time - this.keyOpenedTime.time)) / _local_1)));
            if(_local_2 > 60)
            {
               _local_2 = 60;
            }
         }
         return _local_2;
      }
      
      public function getGainLevel(_arg_1:int) : Boolean
      {
         return (this.gainArr[11] & 1 << _arg_1 - 1) > 0;
      }
      
      public function get itemInfoList() : Array
      {
         return this._itemInfoList;
      }
      
      public function set itemInfoList(_arg_1:Array) : void
      {
         this._itemInfoList = _arg_1;
      }
      
      public function dataChange(_arg_1:String, _arg_2:Object = null) : void
      {
         dispatchEvent(new ChickActivationEvent(_arg_1,_arg_2));
      }
   }
}

