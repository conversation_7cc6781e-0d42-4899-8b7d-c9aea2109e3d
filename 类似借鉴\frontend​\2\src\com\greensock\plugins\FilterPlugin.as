package com.greensock.plugins
{
   import com.greensock.core.*;
   import flash.filters.*;
   
   public class FilterPlugin extends TweenPlugin
   {
      
      public static const VERSION:Number = 2.03;
      
      public static const API:Number = 1;
      
      protected var _target:Object;
      
      protected var _type:Class;
      
      protected var _filter:BitmapFilter;
      
      protected var _index:int;
      
      protected var _remove:<PERSON><PERSON>an;
      
      public function FilterPlugin()
      {
         super();
      }
      
      protected function initFilter(_arg_1:Object, _arg_2:BitmapFilter, _arg_3:Array) : void
      {
         var _local_8:int = 0;
         var _local_5:* = null;
         var _local_4:* = null;
         var _local_6:Array = this._target.filters;
         var _local_7:Object = _arg_1 is BitmapFilter ? {} : _arg_1;
         this._index = -1;
         if(_local_7.index != null)
         {
            this._index = _local_7.index;
         }
         else
         {
            _local_8 = int(_local_6.length);
            while(<PERSON><PERSON><PERSON>(_local_8--))
            {
               if(_local_6[_local_8] is this._type)
               {
                  this._index = _local_8;
                  break;
               }
            }
         }
         if(this._index == -1 || _local_6[this._index] == null || _local_7.addFilter == true)
         {
            this._index = _local_7.index != null ? int(_local_7.index) : int(_local_6.length);
            _local_6[this._index] = _arg_2;
            this._target.filters = _local_6;
         }
         this._filter = _local_6[this._index];
         if(_local_7.remove == true)
         {
            this._remove = true;
            this.onComplete = this.onCompleteTween;
         }
         _local_8 = int(_arg_3.length);
         while(Boolean(_local_8--))
         {
            _local_5 = _arg_3[_local_8];
            if(_local_5 in _arg_1 && this._filter[_local_5] != _arg_1[_local_5])
            {
               if(_local_5 == "color" || _local_5 == "highlightColor" || _local_5 == "shadowColor")
               {
                  _local_4 = new HexColorsPlugin();
                  _local_4.initColor(this._filter,_local_5,this._filter[_local_5],_arg_1[_local_5]);
                  _tweens[_tweens.length] = new PropTween(_local_4,"changeFactor",0,1,_local_5,false);
               }
               else if(_local_5 == "quality" || _local_5 == "inner" || _local_5 == "knockout" || _local_5 == "hideObject")
               {
                  this._filter[_local_5] = _arg_1[_local_5];
               }
               else
               {
                  addTween(this._filter,_local_5,this._filter[_local_5],_arg_1[_local_5],_local_5);
               }
            }
         }
      }
      
      public function onCompleteTween() : void
      {
         var _local_2:int = 0;
         var _local_1:* = null;
         if(this._remove)
         {
            _local_1 = this._target.filters;
            if(!(_local_1[this._index] is this._type))
            {
               _local_2 = int(_local_1.length);
               while(Boolean(_local_2--))
               {
                  if(_local_1[_local_2] is this._type)
                  {
                     _local_1.splice(_local_2,1);
                     break;
                  }
               }
            }
            else
            {
               _local_1.splice(this._index,1);
            }
            this._target.filters = _local_1;
         }
      }
      
      override public function set changeFactor(_arg_1:Number) : void
      {
         var _local_3:* = null;
         var _local_4:int = int(_tweens.length);
         var _local_2:Array = this._target.filters;
         while(Boolean(_local_4--))
         {
            _local_3 = _tweens[_local_4];
            _local_3.target[_local_3.property] = _local_3.start + _local_3.change * _arg_1;
         }
         if(!(_local_2[this._index] is this._type))
         {
            _local_4 = int(this._index = _local_2.length);
            while(Boolean(_local_4--))
            {
               if(_local_2[_local_4] is this._type)
               {
                  this._index = _local_4;
                  break;
               }
            }
         }
         _local_2[this._index] = this._filter;
         this._target.filters = _local_2;
      }
   }
}

