package character
{
   import character.action.ActionSet;
   import character.action.BaseAction;
   import character.action.ComplexBitmapAction;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import flash.utils.Dictionary;
   import mx.events.PropertyChangeEvent;
   
   public class ComplexBitmapCharacter extends ComplexItem implements ICharacter
   {
      
      protected var _assets:Dictionary;
      
      protected var _actionSet:ActionSet = new ActionSet();
      
      protected var _currentAction:ComplexBitmapAction;
      
      protected var _label:String = "";
      
      protected var _autoStop:Boolean;
      
      private var _rect:Rectangle;
      
      protected var _soundEnabled:Boolean = false;
      
      private var _registerPoint:Point = new Point(0,0);
      
      protected var _bitmapRendItems:Vector.<FrameByFrameItem> = new Vector.<FrameByFrameItem>();
      
      public function ComplexBitmapCharacter(_arg_1:Dictionary, _arg_2:XML = null, _arg_3:String = "", _arg_4:Number = 0, _arg_5:Number = 0, _arg_6:String = "original", _arg_7:<PERSON><PERSON><PERSON> = false)
      {
         this._assets = _arg_1;
         if(<PERSON><PERSON><PERSON>(_arg_2))
         {
            _arg_4 = int(_arg_2.@width);
            _arg_5 = int(_arg_2.@height);
         }
         this._autoStop = _arg_7;
         super(_arg_4,_arg_5,_arg_6,"auto",true);
         _type = CharacterType.COMPLEX_BITMAP_TYPE;
         if(Boolean(_arg_2))
         {
            this.description = _arg_2;
         }
         this._label = _arg_3;
      }
      
      public function get soundEnabled() : Boolean
      {
         return this._soundEnabled;
      }
      
      private function set _164832462soundEnabled(_arg_1:Boolean) : void
      {
         if(this._soundEnabled == _arg_1)
         {
            return;
         }
         this._soundEnabled = _arg_1;
      }
      
      public function set description(_arg_1:XML) : void
      {
         var _local_3:XML = null;
         var _local_4:String = null;
         var _local_5:Array = null;
         var _local_6:XMLList = null;
         var _local_7:Vector.<FrameByFrameItem> = null;
         var _local_8:int = 0;
         var _local_9:ComplexBitmapAction = null;
         var _local_10:XML = null;
         var _local_11:BitmapRendItem = null;
         this._actionSet = new ActionSet();
         var _local_2:XMLList = _arg_1..action;
         this._label = _arg_1.@label;
         if(Boolean(_arg_1.hasOwnProperty("@registerX")))
         {
            this._registerPoint.x = _arg_1.@registerX;
         }
         if(Boolean(_arg_1.hasOwnProperty("@registerY")))
         {
            this._registerPoint.y = _arg_1.@registerY;
         }
         if(Boolean(_arg_1.hasOwnProperty("@rect")))
         {
            _local_4 = String(_arg_1.@rect);
            this._rect = new Rectangle();
            _local_5 = _local_4.split("|");
            this._rect.x = _local_5[0];
            this._rect.y = _local_5[1];
            this._rect.width = _local_5[2];
            this._rect.height = _local_5[3];
         }
         for each(_local_3 in _local_2)
         {
            _local_6 = _local_3.asset;
            _local_7 = new Vector.<FrameByFrameItem>();
            _local_8 = 0;
            while(_local_8 < _local_6.length())
            {
               _local_10 = _local_6[_local_8];
               _local_11 = _local_10.@frames == "" ? new FrameByFrameItem(_local_10.@width,_local_10.@height,this._assets[String(_local_10.@resource)]) : new CrossFrameItem(_local_10.@width,_local_10.@height,this._assets[String(_local_10.@resource)],CharacterUtils.creatFrames(_local_10.@frames));
               FrameByFrameItem(_local_11).sourceName = String(_local_10.@resource);
               _local_11.name = _local_10.@name;
               if(Boolean(_local_10.hasOwnProperty("@x")))
               {
                  _local_11.x = _local_10.@x;
               }
               if(Boolean(_local_10.hasOwnProperty("@y")))
               {
                  _local_11.y = _local_10.@y;
               }
               if(Boolean(_local_10.hasOwnProperty("@points")))
               {
                  FrameByFrameItem(_local_11).moveInfo = CharacterUtils.creatPoints(_local_10.@points);
               }
               _local_7.push(_local_11);
               this._bitmapRendItems.push(_local_11);
               _local_8++;
            }
            _local_9 = new ComplexBitmapAction(_local_7,_local_3.@name,_local_3.@next,int(_local_3.@priority));
            _local_9.sound = _local_3.@sound;
            _local_9.endStop = String(_local_3.@endStop) == "true";
            _local_9.sound = String(_local_3.@sound);
            this._actionSet.addAction(_local_9);
         }
         if(this._actionSet.actions.length > 0)
         {
            this.currentAction = this._actionSet.currentAction as ComplexBitmapAction;
         }
      }
      
      public function getActionFrames(_arg_1:String) : int
      {
         var _local_2:BaseAction = this._actionSet.getAction(_arg_1);
         if(Boolean(_local_2))
         {
            return _local_2.len;
         }
         return 0;
      }
      
      public function get label() : String
      {
         return this._label;
      }
      
      private function set _102727412label(_arg_1:String) : void
      {
         this._label = _arg_1;
      }
      
      public function hasAction(_arg_1:String) : Boolean
      {
         return this._actionSet.getAction(_arg_1) != null;
      }
      
      private function set _1408207997assets(_arg_1:Dictionary) : void
      {
         this._assets = _arg_1;
      }
      
      public function get assets() : Dictionary
      {
         return this._assets;
      }
      
      public function get actions() : Array
      {
         return this._actionSet.actions;
      }
      
      public function addAction(_arg_1:BaseAction) : void
      {
         if(_arg_1 is ComplexBitmapAction)
         {
            this._actionSet.addAction(_arg_1);
            if(this._currentAction == null)
            {
               this.currentAction = _arg_1 as ComplexBitmapAction;
            }
            dispatchEvent(new CharacterEvent(CharacterEvent.ADD_ACTION,_arg_1));
            return;
         }
         throw new Error("ComplexBitmapCharacter\'s action must be ComplexBitmapAction");
      }
      
      public function doAction(_arg_1:String) : void
      {
         var _local_3:FrameByFrameItem = null;
         play();
         var _local_2:ComplexBitmapAction = this._actionSet.getAction(_arg_1) as ComplexBitmapAction;
         if(Boolean(_local_2))
         {
            if(this._currentAction == null)
            {
               this.currentAction = _local_2;
            }
            else if(_local_2.priority >= this._currentAction.priority)
            {
               for each(_local_3 in this._currentAction.assets)
               {
                  _local_3.stop();
                  removeItem(_local_3);
               }
               this._currentAction.reset();
               this.currentAction = _local_2;
            }
         }
      }
      
      protected function set currentAction(_arg_1:ComplexBitmapAction) : void
      {
         var _local_2:FrameByFrameItem = null;
         _arg_1.reset();
         this._currentAction = _arg_1;
         this._autoStop = this._currentAction.endStop;
         for each(_local_2 in this._currentAction.assets)
         {
            _local_2.reset();
            _local_2.play();
            addItem(_local_2);
         }
         if(this._currentAction.sound != "" && this._soundEnabled)
         {
            CharacterSoundManager.instance.play(this._currentAction.sound);
         }
      }
      
      override protected function update() : void
      {
         super.update();
         if(this._currentAction == null)
         {
            return;
         }
         this._currentAction.update();
         if(this._currentAction.isEnd)
         {
            if(this._autoStop)
            {
               stop();
            }
            else
            {
               this.doAction(this._currentAction.nextAction);
            }
         }
      }
      
      public function get registerPoint() : Point
      {
         return this._registerPoint;
      }
      
      public function get rect() : Rectangle
      {
         if(this._rect == null)
         {
            this._rect = new Rectangle(0,0,_itemWidth,_itemHeight);
         }
         return this._rect;
      }
      
      override public function toXml() : XML
      {
         var _local_1:XML = <character></character>;
         _local_1.@type = _type;
         _local_1.@width = _itemWidth;
         _local_1.@height = _itemHeight;
         _local_1.@label = this._label;
         _local_1.@registerX = this._registerPoint.x;
         _local_1.@registerY = this._registerPoint.y;
         _local_1.@rect = [this.rect.x,this.rect.y,this.rect.width,this.rect.height].join("|");
         _local_1.appendChild(this._actionSet.toXml());
         return _local_1;
      }
      
      public function removeAction(_arg_1:String) : void
      {
         var _local_3:FrameByFrameItem = null;
         var _local_2:BaseAction = this._actionSet.getAction(_arg_1);
         if(Boolean(_local_2) && this._currentAction == _local_2)
         {
            for each(_local_3 in this._currentAction.assets)
            {
               _local_3.stop();
               removeItem(_local_3);
            }
            this._currentAction = null;
         }
         this._actionSet.removeAction(_arg_1);
         dispatchEvent(new CharacterEvent(CharacterEvent.REMOVE_ACTION));
      }
      
      override public function dispose() : void
      {
         var _local_1:FrameByFrameItem = null;
         super.dispose();
         for each(_local_1 in this._bitmapRendItems)
         {
            _local_1.dispose();
         }
         this._bitmapRendItems = null;
         this._assets = null;
         this._actionSet.dispose();
         this._actionSet = null;
         this._currentAction = null;
      }
      
      [Bindable(event="propertyChange")]
      public function set assets(_arg_1:Dictionary) : void
      {
         var _local_2:Object = this.assets;
         if(_local_2 !== _arg_1)
         {
            this._1408207997assets = _arg_1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"assets",_local_2,_arg_1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function set soundEnabled(_arg_1:Boolean) : void
      {
         var _local_2:Object = this.soundEnabled;
         if(_local_2 !== _arg_1)
         {
            this._164832462soundEnabled = _arg_1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"soundEnabled",_local_2,_arg_1));
            }
         }
      }
      
      [Bindable(event="propertyChange")]
      public function set label(_arg_1:String) : void
      {
         var _local_2:Object = this.label;
         if(_local_2 !== _arg_1)
         {
            this._102727412label = _arg_1;
            if(this.hasEventListener("propertyChange"))
            {
               this.dispatchEvent(PropertyChangeEvent.createUpdateEvent(this,"label",_local_2,_arg_1));
            }
         }
      }
   }
}

