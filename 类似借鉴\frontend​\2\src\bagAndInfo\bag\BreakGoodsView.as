package bagAndInfo.bag
{
   import bagAndInfo.cell.BagCell;
   import com.pickgliss.events.ComponentEvent;
   import com.pickgliss.events.FrameEvent;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.LayerManager;
   import com.pickgliss.ui.controls.SimpleBitmapButton;
   import com.pickgliss.ui.controls.alert.BaseAlerFrame;
   import com.pickgliss.ui.image.Scale9CornerImage;
   import com.pickgliss.ui.text.FilterFrameText;
   import com.pickgliss.ui.vo.AlertInfo;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.manager.LanguageMgr;
   import ddt.manager.MessageTipManager;
   import ddt.manager.PlayerManager;
   import ddt.manager.SocketManager;
   import ddt.manager.SoundManager;
   import flash.events.Event;
   import flash.events.KeyboardEvent;
   
   public class BreakGoodsView extends BaseAlerFrame
   {
      
      private static const EnterKeyCode:int = 13;
      
      private static const ESCkeyCode:int = 27;
      
      protected var _input:FilterFrameText;
      
      private var _NumString:FilterFrameText;
      
      private var _tipString:FilterFrameText;
      
      private var _inputBG:Scale9CornerImage;
      
      protected var _cell:BagCell;
      
      private var _upBtn:SimpleBitmapButton;
      
      private var _downBtn:SimpleBitmapButton;
      
      public function BreakGoodsView()
      {
         super();
         cancelButtonStyle = "core.simplebt";
         submitButtonStyle = "core.simplebt";
         var _local_1:AlertInfo = new AlertInfo();
         _local_1.title = LanguageMgr.GetTranslation("tank.view.bagII.BreakGoodsView.split");
         info = _local_1;
         this._input = ComponentFactory.Instance.creatComponentByStylename("breakGoodsInput");
         this._input.text = "1";
         this._inputBG = ComponentFactory.Instance.creatComponentByStylename("breakInputbg");
         this._inputBG.x = this._input.x - 1;
         this._inputBG.y = this._input.y - 2;
         addToContent(this._inputBG);
         addToContent(this._input);
         this._NumString = ComponentFactory.Instance.creatComponentByStylename("breakGoodsNumText");
         this._NumString.text = LanguageMgr.GetTranslation("tank.view.bagII.BreakGoodsView.num");
         addToContent(this._NumString);
         this._tipString = ComponentFactory.Instance.creatComponentByStylename("breakGoodsPleasEnterText");
         this._tipString.text = LanguageMgr.GetTranslation("tank.view.bagII.BreakGoodsView.input");
         addToContent(this._tipString);
         submitButtonEnable = false;
         this._upBtn = ComponentFactory.Instance.creatComponentByStylename("breakUpButton");
         addToContent(this._upBtn);
         this._downBtn = ComponentFactory.Instance.creatComponentByStylename("breakDownButton");
         addToContent(this._downBtn);
         this.addEvent();
      }
      
      private function addEvent() : void
      {
         this._input.addEventListener("change",this.__input);
         this._input.addEventListener("keyUp",this.__onInputKeyUp);
         addEventListener("response",this.__responseHandler);
         addEventListener("addedToStage",this.__onToStage);
         this._upBtn.addEventListener("click",this.__onUpBtn);
         this._downBtn.addEventListener("click",this.__onDownBtn);
      }
      
      private function __onUpBtn(_arg_1:Event) : void
      {
         var _local_2:int = int(this._input.text);
         _local_2++;
         this._input.text = String(_local_2);
         this.downBtnEnable();
      }
      
      private function __onDownBtn(_arg_1:Event) : void
      {
         var _local_2:int = int(this._input.text);
         if(_local_2 == 0)
         {
            return;
         }
         _local_2--;
         this._input.text = String(_local_2);
         this.downBtnEnable();
      }
      
      private function __onToStage(_arg_1:Event) : void
      {
      }
      
      private function __onInputKeyUp(_arg_1:KeyboardEvent) : void
      {
         switch(_arg_1.keyCode)
         {
            case 13:
               this.okFun();
               return;
            case 27:
               this.dispose();
         }
      }
      
      private function __getFocus(_arg_1:Event) : void
      {
         this._input.setFocus();
      }
      
      private function removeEvent() : void
      {
         if(Boolean(this._input))
         {
            this._input.removeEventListener("change",this.__input);
            this._input.removeEventListener("keyUp",this.__onInputKeyUp);
         }
         removeEventListener("response",this.__responseHandler);
         removeEventListener("addedToStage",this.__onToStage);
         removeEventListener("click",this.__onUpBtn);
         removeEventListener("click",this.__onDownBtn);
      }
      
      private function __input(_arg_1:Event) : void
      {
         submitButtonEnable = this._input.text != "";
         this.downBtnEnable();
      }
      
      private function downBtnEnable() : void
      {
         if(!this._input.text || this._input.text == "" || int(this._input.text) < 1)
         {
            this._downBtn.enable = false;
         }
         else
         {
            this._downBtn.enable = true;
         }
      }
      
      public function show() : void
      {
         LayerManager.Instance.addToLayer(this,2,true,1);
      }
      
      private function __okClickCall(_arg_1:ComponentEvent) : void
      {
         this.okFun();
      }
      
      private function __responseHandler(_arg_1:FrameEvent) : void
      {
         switch(_arg_1.responseCode)
         {
            case 0:
            case 1:
            case 4:
               this.dispose();
               return;
            case 2:
            case 3:
               this.okFun();
         }
      }
      
      private function getFocus() : void
      {
         if(Boolean(stage))
         {
            stage.focus = this._input;
         }
      }
      
      protected function okFun() : void
      {
         var _local_2:int = 0;
         var _local_5:int = 0;
         var _local_4:int = 0;
         var _local_6:* = null;
         var _local_1:* = null;
         SoundManager.instance.play("008");
         var _local_3:int = int(this._input.text);
         if(_local_3 > 0 && _local_3 < this._cell.itemInfo.Count)
         {
            _local_6 = this._cell.splitItem(_local_3);
            _local_2 = -1;
            _local_1 = PlayerManager.Instance.Self.getBag(_local_6.BagType);
            if(_local_6.BagType == 0)
            {
               _local_5 = 31;
               while(_local_5 < 127)
               {
                  if(_local_1.items[_local_5] == null)
                  {
                     _local_2 = _local_5;
                     break;
                  }
                  _local_5++;
               }
            }
            else if(_local_6.BagType == 1)
            {
               _local_4 = 0;
               while(_local_4 < 96)
               {
                  if(_local_1.items[_local_4] == null)
                  {
                     _local_2 = _local_4;
                     break;
                  }
                  _local_4++;
               }
            }
            if(_local_2 == -1)
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.view.bagII.BreakGoodsView.BreakFail"));
            }
            else
            {
               SocketManager.Instance.out.sendMoveGoods(_local_6.BagType,_local_6.Place,_local_6.BagType,_local_2,_local_6.Count);
            }
            this.dispose();
         }
         else if(_local_3 == 0)
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.view.bagII.BreakGoodsView.wrong2"));
            this._input.text = "";
         }
         else
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.view.bagII.BreakGoodsView.right"));
            this._input.text = "";
         }
      }
      
      override public function dispose() : void
      {
         SoundManager.instance.play("008");
         this.removeEvent();
         ObjectUtils.disposeObject(this._inputBG);
         this._inputBG = null;
         ObjectUtils.disposeObject(this._input);
         this._input = null;
         ObjectUtils.disposeObject(this._NumString);
         this._NumString = null;
         ObjectUtils.disposeObject(this._tipString);
         this._tipString = null;
         this._cell = null;
         if(Boolean(this._upBtn))
         {
            ObjectUtils.disposeObject(this._upBtn);
         }
         this._upBtn = null;
         if(Boolean(this._downBtn))
         {
            ObjectUtils.disposeObject(this._downBtn);
         }
         this._downBtn = null;
         if(Boolean(parent))
         {
            parent.removeChild(this);
         }
         super.dispose();
      }
      
      public function get cell() : BagCell
      {
         return this._cell;
      }
      
      public function set cell(_arg_1:BagCell) : void
      {
         this._cell = _arg_1;
      }
   }
}

