package forcesbattle
{
   import com.pickgliss.ui.LayerManager;
   import com.pickgliss.utils.ClassUtils;
   import ddt.events.PkgEvent;
   import ddt.manager.LanguageMgr;
   import ddt.manager.MessageTipManager;
   import ddt.manager.PlayerManager;
   import ddt.manager.ServerConfigManager;
   import ddt.manager.SocketManager;
   import ddt.utils.AssetModuleLoader;
   import flash.display.Sprite;
   import flash.events.EventDispatcher;
   import forcesbattle.cnf.relic.ForcesRelicCnfAnayzer;
   import forcesbattle.cnf.relic.ForcesRelicExProCnfAnayzer;
   import forcesbattle.cnf.relic.ForcesRelicLevelCnfAnayzer;
   import forcesbattle.cnf.relic.ForcesRelicManualCnfAnayzer;
   import forcesbattle.data.relic.ForcesRelicCnfVo;
   import forcesbattle.data.relic.ForcesRelicManualVo;
   import forcesbattle.data.relic.ForcesRelicVo;
   import forcesbattle.model.ForcesRelicModel;
   import road7th.comm.PackageIn;
   
   public class ForcesRelicManager extends EventDispatcher
   {
      
      private static var _instance:ForcesRelicManager;
      
      private var _model:ForcesRelicModel;
      
      private var _isLoadAllRes:Boolean = false;
      
      private var _isLoadXml:Boolean = false;
      
      private var _isLoadLevelXml:Boolean;
      
      private var _isLoadProXml:Boolean = false;
      
      private var _isLoadManualXml:Boolean = false;
      
      public function ForcesRelicManager()
      {
         super();
      }
      
      public static function get instance() : ForcesRelicManager
      {
         if(_instance == null)
         {
            _instance = new ForcesRelicManager();
         }
         return _instance;
      }
      
      public function setup() : void
      {
         this._model = new ForcesRelicModel();
         this.addEvent();
      }
      
      private function addEvent() : void
      {
         SocketManager.Instance.addEventListener(PkgEvent.format(768,1),this.__onUpdateBaseInfo);
         SocketManager.Instance.addEventListener(PkgEvent.format(768,2),this.__onUpdateRelicInfo);
         SocketManager.Instance.addEventListener(PkgEvent.format(768,3),this.__onUpGradeCallBack);
         SocketManager.Instance.addEventListener(PkgEvent.format(768,4),this.__onUpStageCallBack);
         SocketManager.Instance.addEventListener(PkgEvent.format(768,5),this.__onUpdateManualInfo);
      }
      
      private function __onUpdateBaseInfo(_arg_1:PkgEvent) : void
      {
         var _local_11:int = 0;
         var _local_12:int = 0;
         var _local_2:int = 0;
         var _local_10:int = 0;
         var _local_9:int = 0;
         var _local_3:int = 0;
         var _local_6:PackageIn = _arg_1.pkg;
         var _local_5:int = _local_6.readInt();
         var _local_4:int = _local_6.readInt();
         _local_11 = 0;
         while(_local_11 < _local_4)
         {
            this._model.curRelicHoleArr[_local_11] = 0;
            _local_11++;
         }
         var _local_8:int = _local_6.readInt();
         _local_12 = 0;
         while(_local_12 < _local_8)
         {
            _local_2 = _local_6.readInt();
            this._model.curRelicHoleArr[_local_12] = _local_2;
            _local_12++;
         }
         this._model.relicSuitDic.clear();
         var _local_7:int = _local_6.readInt();
         _local_10 = 0;
         while(_local_10 < _local_7)
         {
            _local_9 = _local_6.readInt();
            _local_3 = _local_6.readInt();
            this._model.relicSuitDic[_local_9] = _local_3;
            _local_10++;
         }
         this._model.curRelicHoleIndex = _local_4;
         this._model.shopScore = _local_5;
         dispatchEvent(new ForcesRelicEvent("updatePlayerInfo"));
      }
      
      private function __onUpdateRelicInfo(_arg_1:PkgEvent) : void
      {
         var _local_2:PackageIn = _arg_1.pkg;
         this._updateRelicInfo(_local_2);
         dispatchEvent(new ForcesRelicEvent("updateRelicView"));
         dispatchEvent(new ForcesRelicEvent("updateZFView"));
      }
      
      private function __onUpGradeCallBack(_arg_1:PkgEvent) : void
      {
         var _local_2:PackageIn = _arg_1.pkg;
         this._updateRelicInfo(_local_2);
         dispatchEvent(new ForcesRelicEvent("updateRelicView"));
      }
      
      private function __onUpStageCallBack(_arg_1:PkgEvent) : void
      {
         var _local_2:PackageIn = _arg_1.pkg;
         this._updateRelicInfo(_local_2);
         dispatchEvent(new ForcesRelicEvent("updateRelicView"));
      }
      
      private function _updateRelicInfo(_arg_1:PackageIn) : void
      {
         var _local_5:int = 0;
         var _local_12:int = 0;
         var _local_3:int = 0;
         var _local_7:int = 0;
         var _local_4:int = 0;
         var _local_9:int = 0;
         var _local_13:int = 0;
         var _local_6:* = null;
         var _local_10:Boolean = _arg_1.readBoolean();
         if(_local_10)
         {
            this._model.relicInfoDic.clear();
            this._model.relicShardDic.clear();
         }
         var _local_8:int = _arg_1.readInt();
         ConsoleLog.write("_local_8:" + _local_8);
         var _local_11:int = _arg_1.readInt();
         ConsoleLog.write("_local_11:" + _local_11);
         var _local_2:int = _arg_1.readInt();
         ConsoleLog.write("_local_2:" + _local_2);
         _local_5 = 0;
         while(_local_5 < _local_2)
         {
            _local_6 = new ForcesRelicVo();
            _local_6.id = _arg_1.readInt();
            ConsoleLog.write("当前循环 _local_6.id:" + _local_6.id);
            _local_6.level = _arg_1.readInt();
            ConsoleLog.write("当前循环 _local_6.level:" + _local_6.level);
            _local_6.stage = _arg_1.readInt();
            ConsoleLog.write("当前循环 _local_6.stage:" + _local_6.stage);
            _local_6.curExp = _arg_1.readInt();
            ConsoleLog.write("当前循环 _local_6.curExp:" + _local_6.curExp);
            _local_6.ShardNum = _arg_1.readInt();
            ConsoleLog.write("当前循环 _local_6.ShardNum:" + _local_6.ShardNum);
            _local_6.proArr = [];
            _local_12 = _arg_1.readInt();
            ConsoleLog.write("当前循环 _local_12:" + _local_12);
            _local_3 = 0;
            while(_local_3 < _local_12)
            {
               _local_7 = _arg_1.readInt();
               ConsoleLog.write("当前内层循环 _local_7:" + _local_7);
               _local_4 = _arg_1.readInt();
               ConsoleLog.write("当前内层循环 _local_4:" + _local_4);
               _local_9 = _arg_1.readInt();
               ConsoleLog.write("当前内层循环 _local_9:" + _local_9);
               _local_13 = _arg_1.readInt();
               ConsoleLog.write("当前内层循环 _local_13:" + _local_13);
               _local_6.proArr.push(_local_4 + "," + _local_9 + "," + _local_13);
               _local_3++;
            }
            this._model.commonShard1 = _local_8;
            ConsoleLog.write("设置 _model.commonShard1 为:" + _local_8);
            this._model.commonShard2 = _local_11;
            ConsoleLog.write("设置 _model.commonShard2 为:" + _local_11);
            this._model.relicInfoDic.add(_local_6.id,_local_6);
            ConsoleLog.write("向 _model.relicInfoDic 添加数据，id:" + _local_6.id);
            this._model.relicShardDic.add(_local_6.id,_local_6.ShardNum);
            ConsoleLog.write("向 _model.relicShardDic 添加数据，id:" + _local_6.id + "，ShardNum:" + _local_6.ShardNum);
            _local_5++;
         }
      }
      
      private function __onUpdateManualInfo(_arg_1:PkgEvent) : void
      {
         var _local_5:int = 0;
         var _local_4:* = null;
         var _local_2:PackageIn = _arg_1.pkg;
         var _local_3:int = _local_2.readInt();
         _local_5 = 0;
         while(_local_5 < _local_3)
         {
            _local_4 = new ForcesRelicManualVo();
            _local_4.type = _local_2.readInt();
            _local_4.exp = _local_2.readInt();
            _local_4.level = _local_2.readInt();
            this._model.manualInfoDic.add(_local_4.type,_local_4);
            _local_5++;
         }
         dispatchEvent(new ForcesRelicEvent("updateManaualView"));
      }
      
      public function getCanUnlockHoleByIndex(_arg_1:int) : Boolean
      {
         var _local_2:Array = ServerConfigManager.instance.RelicOpenEquipPos;
         var _local_3:int = int(_local_2[_arg_1 - 1]);
         return true;
      }
      
      public function getIsUnlockByIndex(_arg_1:int) : Boolean
      {
         return _arg_1 <= this._model.curRelicHoleIndex;
      }
      
      public function putonRelicByID(_arg_1:int) : void
      {
         var _local_7:ForcesRelicVo = this._model.relicInfoDic[_arg_1];
         if(!_local_7)
         {
            return;
         }
         var _local_6:ForcesRelicCnfVo = this._model.relicCnfDic[_arg_1];
         if(!_local_6)
         {
            return;
         }
         if(this._model.curRelicHoleArr.indexOf(_arg_1) >= 0)
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.forceRelic.txt14"));
            return;
         }
         var _local_5:int = -1;
         var _local_4:Array = this.getCurEquipRelcFunTypeNumArr();
         var _local_2:Boolean = this.checkSuitStateByType(_local_6.Type);
         if(_local_2)
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.forceRelic.txt15",3));
            return;
         }
         var _local_3:int = int(this._model.curRelicHoleArr.indexOf(0));
         if(_local_3 < 0 || !this.getIsUnlockByIndex(_local_3 + 1))
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.forceRelic.txt16"));
            return;
         }
         _local_5 = _local_3 + 1;
         SocketManager.Instance.out.sendForcesRelicTryEquipItem(1,_local_5,_arg_1);
      }
      
      public function getCurEquipRelcFunTypeNumArr() : Array
      {
         var _local_5:int = 0;
         var _local_2:int = 0;
         var _local_1:int = 0;
         var _local_4:* = null;
         var _local_3:* = null;
         _local_5 = 0;
         while(_local_5 < this._model.curRelicHoleArr.length)
         {
            _local_4 = this._model.relicInfoDic[this._model.curRelicHoleArr[_local_5]];
            if(_local_4)
            {
               _local_3 = this._model.relicCnfDic[_local_4.id];
               if(_local_3)
               {
                  if(_local_3.Type == 1)
                  {
                     _local_2 += 1;
                  }
                  else if(_local_3.Type == 2)
                  {
                     _local_1 += 1;
                  }
               }
            }
            _local_5++;
         }
         return [_local_2,_local_1];
      }
      
      public function checkSuitStateByType(_arg_1:int) : Boolean
      {
         var _local_3:int = 0;
         var _local_2:Array = this.getCurEquipRelcFunTypeNumArr();
         if(_arg_1 == 1)
         {
            _local_3 = int(_local_2[0]);
         }
         else if(_arg_1 == 2)
         {
            _local_3 = int(_local_2[1]);
         }
         return _local_3 >= 3;
      }
      
      public function showSelfRelicDetailView(_arg_1:int) : void
      {
         var _local_3:ForcesRelicVo = this._model.relicInfoDic[_arg_1];
         if(!_local_3)
         {
            return;
         }
         var _local_2:ForcesRelicCnfVo = this._model.relicCnfDic[_arg_1];
         if(!_local_2)
         {
            return;
         }
         dispatchEvent(new ForcesRelicEvent("updateDetailViewByRelicType",_arg_1));
      }
      
      public function getManualCurProByQua(_arg_1:int, _arg_2:int = -1) : Array
      {
         var _local_9:int = 0;
         var _local_5:int = 0;
         var _local_3:int = 0;
         var _local_6:* = null;
         var _local_4:* = null;
         var _local_10:* = null;
         var _local_7:Array = [[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]];
         var _local_8:int = (this._model.manualInfoDic[0] as ForcesRelicManualVo).level;
         _local_4 = this._model.manualCnfDic[0][_local_8];
         if(_arg_1 == 0)
         {
            _local_9 = 0;
            while(_local_9 < this._model.manualTypeArr.length)
            {
               _local_5 = int(this._model.manualTypeArr[_local_9]);
               if(_local_5 != 0)
               {
                  _local_10 = this._model.manualInfoDic[_local_5];
                  if(!_local_10)
                  {
                     MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.forceRelic.txt23"));
                  }
                  else
                  {
                     _local_6 = this._model.manualCnfDic[_local_5][_local_10.level];
                     if(_local_6)
                     {
                        _local_7[0][0] = _local_6.Attack + (_local_7[0][0] || 0);
                        _local_7[1][0] = _local_6.Defence + (_local_7[1][0] || 0);
                        _local_7[2][0] = _local_6.Agility + (_local_7[2][0] || 0);
                        _local_7[3][0] = _local_6.Luck + (_local_7[3][0] || 0);
                        _local_7[4][0] = _local_6.MagicAttack + (_local_7[4][0] || 0);
                        _local_7[5][0] = _local_6.MagicDefence + (_local_7[5][0] || 0);
                        _local_7[6][0] = _local_6.Damage + (_local_7[6][0] || 0);
                        _local_7[7][0] = _local_6.Blood + (_local_7[7][0] || 0);
                     }
                  }
               }
               _local_9++;
            }
            _local_7[0][1] = _local_4 ? _local_4.Attack : 0;
            _local_7[1][1] = _local_4 ? _local_4.Defence : 0;
            _local_7[2][1] = _local_4 ? _local_4.Agility : 0;
            _local_7[3][1] = _local_4 ? _local_4.Luck : 0;
            _local_7[4][1] = _local_4 ? _local_4.MagicAttack : 0;
            _local_7[5][1] = _local_4 ? _local_4.MagicDefence : 0;
            _local_7[6][1] = _local_4 ? _local_4.Damage : 0;
            _local_7[7][1] = _local_4 ? _local_4.Blood : 0;
         }
         else
         {
            if(_arg_2 < 0)
            {
               _local_10 = this._model.manualInfoDic[_arg_1];
               if(!_local_10)
               {
                  MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.forceRelic.txt23"));
                  return [];
               }
               _local_3 = int(_local_10.level);
            }
            else
            {
               _local_3 = _arg_2;
            }
            _local_6 = this._model.manualCnfDic[_arg_1][_local_3];
            if(_local_6)
            {
               _local_7 = [[_local_6.Attack,_local_4.Attack],[_local_6.Defence,_local_4.Defence],[_local_6.Agility,_local_4.Agility],[_local_6.Luck,_local_4.Luck],[_local_6.MagicAttack,_local_4.MagicAttack],[_local_6.MagicDefence,_local_4.MagicDefence],[_local_6.Damage,_local_4.Damage],[_local_6.Blood,_local_4.Blood]];
            }
         }
         return _local_7;
      }
      
      public function getManualTotalProArrByQua(_arg_1:int) : Array
      {
         var _local_2:int = 0;
         var _local_6:int = 0;
         var _local_3:int = 0;
         var _local_4:* = null;
         var _local_5:Array = [];
         if(_arg_1 == 0)
         {
            _local_6 = 0;
            while(_local_6 < this._model.manualTypeArr.length)
            {
               _local_3 = int(this._model.manualTypeArr[_local_6]);
               if(_local_3 != 0)
               {
                  _local_2 = int(this._model.manualMaxLeveDic[_local_3]);
                  _local_4 = this._model.manualCnfDic[_local_3][_local_2];
                  if(_local_4)
                  {
                     _local_5[0] = _local_4.Attack + (_local_5[0] || 0);
                     _local_5[1] = _local_4.Defence + (_local_5[1] || 0);
                     _local_5[2] = _local_4.Agility + (_local_5[2] || 0);
                     _local_5[3] = _local_4.Luck + (_local_5[3] || 0);
                     _local_5[4] = _local_4.MagicAttack + (_local_5[4] || 0);
                     _local_5[5] = _local_4.MagicDefence + (_local_5[5] || 0);
                     _local_5[6] = _local_4.Damage + (_local_5[6] || 0);
                     _local_5[7] = _local_4.Blood + (_local_5[7] || 0);
                  }
               }
               _local_6++;
            }
         }
         else
         {
            _local_2 = int(this._model.manualMaxLeveDic[_arg_1]);
            _local_4 = this._model.manualCnfDic[_arg_1][_local_2];
            if(_local_4)
            {
               _local_5 = [_local_4.Attack,_local_4.Defence,_local_4.Agility,_local_4.Luck,_local_4.MagicAttack,_local_4.MagicDefence,_local_4.Damage,_local_4.Blood];
            }
         }
         return _local_5;
      }
      
      public function get relicUpgradeNeedLvArr() : Array
      {
         var _local_3:int = 0;
         var _local_1:Array = [];
         var _local_2:Array = ServerConfigManager.instance.RelicAdvanceToUpgrade;
         _local_3 = 0;
         while(_local_3 < _local_2.length)
         {
            _local_1.push((_local_2[_local_3] as String).split(",")[1]);
            _local_3++;
         }
         return _local_1;
      }
      
      public function getIsNeedAdvanceStage(_arg_1:ForcesRelicVo) : Boolean
      {
         var _local_2:int = int(this.relicUpgradeNeedLvArr[_arg_1.stage]);
         if(_arg_1.level >= _local_2 && _arg_1.stage < this.relicUpgradeNeedLvArr.length - 1)
         {
            return true;
         }
         return false;
      }
      
      public function changeColorToString(_arg_1:String) : String
      {
         var _local_2:String = _arg_1.slice(2).toLowerCase();
         return "#" + _local_2;
      }
      
      public function get relicMaxLv() : int
      {
         return this.relicUpgradeNeedLvArr[this.relicUpgradeNeedLvArr.length - 1];
      }
      
      public function setZfProSelectIndex(_arg_1:int) : void
      {
         this._model.curZfProSelectIndex = _arg_1;
         dispatchEvent(new ForcesRelicEvent("updateZFView"));
      }
      
      public function loadAllResForHall() : void
      {
         if(PlayerManager.Instance.Self.Grade < 30)
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.forceRelic.txt41",30));
            return;
         }
         SocketManager.Instance.out.sendPersonalLimitShop(171);
         if(this._isLoadAllRes)
         {
            this.showRelicViewInHall();
            return;
         }
         AssetModuleLoader.addModelLoader("forcesbattle",7);
         AssetModuleLoader.addModelLoader("forcesrelic",5);
         AssetModuleLoader.startCodeLoader(this.showRelicViewInHall);
      }
      
      private function showRelicViewInHall() : void
      {
         this._isLoadAllRes = true;
         var _local_1:Sprite = ClassUtils.CreatInstance("forcesbattle.views.relic.ForcesRelicMainView") as Sprite;
         LayerManager.Instance.addToLayer(_local_1,3,true,1);
      }
      
      public function loadForcesRelicXmlComplete(_arg_1:ForcesRelicCnfAnayzer) : void
      {
         this._isLoadXml = true;
         this._model.relicCnfDic = _arg_1.relicDic;
         this._model.relicCnfDicByFunType = _arg_1.relicDicByFunType;
      }
      
      public function loadForcesRelicLevelXmlComplete(_arg_1:ForcesRelicLevelCnfAnayzer) : void
      {
         this._isLoadLevelXml = true;
         this._model.relicLevelCnfDic = _arg_1.levelDic;
      }
      
      public function loadForcesRelicProXmlComplete(_arg_1:ForcesRelicExProCnfAnayzer) : void
      {
         this._isLoadProXml = true;
         this._model.relicExProCnfDic = _arg_1.relicExProDic;
      }
      
      public function loadForcesRelicManualXmlComplete(_arg_1:ForcesRelicManualCnfAnayzer) : void
      {
         this._isLoadManualXml = true;
         this._model.manualCnfDic = _arg_1.manualDic;
         this._model.manualMaxLeveDic = _arg_1.manualMaxLevelDic;
         this._model.manualTypeArr = _arg_1.typeArr;
      }
      
      public function get model() : ForcesRelicModel
      {
         return this._model;
      }
   }
}

