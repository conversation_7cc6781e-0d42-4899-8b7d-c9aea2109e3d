package bagAndInfo.bag
{
   import bagAndInfo.cell.BagCell;
   import bagAndInfo.cell.DragEffect;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.controls.TextButton;
   import ddt.interfaces.ICell;
   import ddt.interfaces.IDragable;
   import ddt.manager.DragManager;
   import ddt.manager.PlayerManager;
   import ddt.manager.SoundManager;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.filters.ColorMatrixFilter;
   
   public class BreakGoodsBtn extends TextButton implements IDragable
   {
      
      private var _dragTarget:BagCell;
      
      private var _enabel:Boolean;
      
      private var win:BreakGoodsView;
      
      private var myColorMatrix_filter:ColorMatrixFilter;
      
      private var lightingFilter:ColorMatrixFilter;
      
      public function BreakGoodsBtn()
      {
         super();
      }
      
      override protected function init() : void
      {
         super.init();
         buttonMode = true;
         this.initEvent();
      }
      
      private function __mouseClick(_arg_1:MouseEvent) : void
      {
         if(!PlayerManager.Instance.Self.bagLocked)
         {
            this.dragStart(_arg_1.stageX,_arg_1.stageY);
         }
      }
      
      public function dragStart(_arg_1:Number, _arg_2:Number) : void
      {
         DragManager.startDrag(this,this,ComponentFactory.Instance.creatBitmap("bagAndInfo.bag.breakIconAsset"),_arg_1,_arg_2,"move");
      }
      
      public function dragStop(_arg_1:DragEffect) : void
      {
         var _local_2:* = null;
         if(_arg_1.action == "move" && _arg_1.target is ICell)
         {
            _local_2 = _arg_1.target as BagCell;
            if(_local_2)
            {
               if(_local_2.itemInfo.Count > 1 && _local_2.itemInfo.BagType != 11 && _local_2.itemInfo.BagType != 511)
               {
                  this._dragTarget = _local_2;
                  SoundManager.instance.play("008");
                  this._dragTarget = _local_2;
                  SoundManager.instance.play("008");
                  this.win = ComponentFactory.Instance.creatComponentByStylename("breakGoodsView");
                  this.win.cell = _local_2;
                  this.win.show();
               }
            }
         }
      }
      
      private function breakBack() : void
      {
         if(Boolean(this._dragTarget))
         {
         }
         if(Boolean(stage))
         {
            this.dragStart(stage.mouseX,stage.mouseY);
         }
      }
      
      public function getSource() : IDragable
      {
         return this;
      }
      
      public function getDragData() : Object
      {
         return this;
      }
      
      private function removeEvents() : void
      {
         removeEventListener("click",this.__mouseClick);
      }
      
      private function initEvent() : void
      {
         addEventListener("click",this.__mouseClick);
         addEventListener("addedToStage",this.__addToStage);
         addEventListener("removedFromStage",this.__removeFromStage);
      }
      
      override public function dispose() : void
      {
         this.removeEvents();
         if(Boolean(this._dragTarget))
         {
            this._dragTarget.locked = false;
         }
         PlayerManager.Instance.Self.Bag.unLockAll();
         if(this.win != null)
         {
            this.win.dispose();
         }
         this.win = null;
         super.dispose();
      }
      
      private function __addToStage(_arg_1:Event) : void
      {
      }
      
      private function __removeFromStage(_arg_1:Event) : void
      {
      }
      
      private function cancelBack() : void
      {
         if(Boolean(this._dragTarget))
         {
            this._dragTarget.locked = false;
         }
      }
   }
}

