package collectionTask.model
{
   import collectionTask.event.CollectionTaskEvent;
   import collectionTask.vo.PlayerVO;
   import flash.events.EventDispatcher;
   import flash.events.IEventDispatcher;
   import road7th.data.DictionaryData;
   
   public class CollectionTaskModel extends EventDispatcher
   {
      
      private var _players:DictionaryData = new DictionaryData(true);
      
      private var _playerNameVisible:Boolean = true;
      
      private var _playerChatBallVisible:Boolean = true;
      
      private var _playerVisible:Boolean = true;
      
      public function CollectionTaskModel(_arg_1:IEventDispatcher = null)
      {
         super(_arg_1);
      }
      
      public function addPlayer(_arg_1:PlayerVO) : void
      {
         this._players.add(_arg_1.playerInfo.ID,_arg_1);
      }
      
      public function removePlayer(_arg_1:int) : void
      {
         this._players.remove(_arg_1);
      }
      
      public function get playerNameVisible() : Boolean
      {
         return this._playerNameVisible;
      }
      
      public function set playerNameVisible(_arg_1:<PERSON>olean) : void
      {
         this._playerNameVisible = _arg_1;
         dispatchEvent(new CollectionTaskEvent("playerNameVisible"));
      }
      
      public function get playerChatBallVisible() : Boolean
      {
         return this._playerChatBallVisible;
      }
      
      public function set playerChatBallVisible(_arg_1:Boolean) : void
      {
         this._playerChatBallVisible = _arg_1;
         dispatchEvent(new CollectionTaskEvent("playerChatBallVisible"));
      }
      
      public function set playerVisible(_arg_1:Boolean) : void
      {
         this._playerVisible = _arg_1;
         dispatchEvent(new CollectionTaskEvent("playerVisible"));
      }
      
      public function get playerVisible() : Boolean
      {
         return this._playerVisible;
      }
      
      public function getPlayers() : DictionaryData
      {
         return this._players;
      }
   }
}

