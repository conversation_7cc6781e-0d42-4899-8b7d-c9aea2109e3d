package beadSystem
{
   import beadSystem.data.BeadEvent;
   import beadSystem.model.BeadModel;
   import ddt.data.EquipType;
   import ddt.data.goods.InventoryItemInfo;
   import ddt.events.CEvent;
   import ddt.events.PkgEvent;
   import ddt.manager.BeadTemplateManager;
   import ddt.manager.PlayerManager;
   import ddt.manager.SocketManager;
   import ddt.utils.AssetModuleLoader;
   import flash.events.EventDispatcher;
   import road7th.comm.PackageIn;
   
   public class beadSystemManager extends EventDispatcher
   {
      
      private static var _instance:beadSystemManager;
      
      public static const INFO_VIEW:String = "infoview";
      
      public static const MAIN_VIEW:String = "mainView";
      
      public static const INFO_FRAME:String = "infoframe";
      
      public static const CREATE_COMPLETE:String = "createComplete";
      
      private var _isFirstLoadPackage:Boolean = true;
      
      private var cevent:CEvent;
      
      public function beadSystemManager()
      {
         super();
      }
      
      public static function get Instance() : beadSystemManager
      {
         if(_instance == null)
         {
            _instance = new beadSystemManager();
         }
         return _instance;
      }
      
      public function setup() : void
      {
         this.initEvent();
      }
      
      public function initEvent() : void
      {
         SocketManager.Instance.addEventListener(PkgEvent.format(121,3),this.__onOpenPackage);
         SocketManager.Instance.addEventListener(PkgEvent.format(121,6),this.__onOpenHole);
      }
      
      private function __onOpenPackage(_arg_1:PkgEvent) : void
      {
         var _local_3:PackageIn = _arg_1.pkg as PackageIn;
         var _local_2:int = _local_3.readInt();
         dispatchEvent(new BeadEvent("lightButton",_local_2));
         BeadModel.beadRequestBtnIndex = _local_2;
         trace("index" + _local_2);
      }
      
      private function __onOpenHole(_arg_1:PkgEvent) : void
      {
         dispatchEvent(new BeadEvent("openBeadHole",0));
         trace("init Hole info.......");
      }
      
      public function showFrame(_arg_1:String) : void
      {
         this.cevent = new CEvent("openview",{"type":_arg_1});
         AssetModuleLoader.addModelLoader("ddtbead",6);
         AssetModuleLoader.addModelLoader("ddtstore",6);
         AssetModuleLoader.startCodeLoader(this.show);
      }
      
      public function show() : void
      {
         dispatchEvent(this.cevent);
      }
      
      public function getEquipPlace(_arg_1:InventoryItemInfo) : int
      {
         var _local_2:int = 0;
         var _local_3:int = 0;
         if(_arg_1.Property1 == "31" && _arg_1.Property2 == "1")
         {
            return 1;
         }
         if(_arg_1.Property1 == "31" && _arg_1.Property2 == "2")
         {
            if(Boolean(PlayerManager.Instance.Self.BeadBag.getItemAt(2)))
            {
               if(Boolean(PlayerManager.Instance.Self.BeadBag.getItemAt(3)))
               {
                  return 2;
               }
               return 3;
            }
            return 2;
         }
         if(_arg_1.Property1 == "31" && _arg_1.Property2 == "3")
         {
            _local_2 = 5;
            if(PlayerManager.Instance.Self.Grade >= 15)
            {
               _local_2 = 6;
            }
            if(PlayerManager.Instance.Self.Grade >= 18)
            {
               _local_2 = 7;
            }
            if(PlayerManager.Instance.Self.Grade >= 21)
            {
               _local_2 = 8;
            }
            if(PlayerManager.Instance.Self.Grade >= 24)
            {
               _local_2 = 9;
            }
            if(PlayerManager.Instance.Self.Grade >= 27)
            {
               _local_2 = 10;
            }
            if(PlayerManager.Instance.Self.Grade >= 30)
            {
               _local_2 = 11;
            }
            if(PlayerManager.Instance.Self.Grade >= 33)
            {
               _local_2 = 12;
            }
            _local_3 = 4;
            while(_local_3 <= _local_2)
            {
               if(!PlayerManager.Instance.Self.BeadBag.getItemAt(_local_3))
               {
                  return _local_3;
               }
               _local_3++;
            }
            return 4;
         }
         return -1;
      }
      
      public function getBeadNameTextFormatStyle(_arg_1:int) : String
      {
         var _local_2:* = null;
         switch(_arg_1)
         {
            case 1:
            case 2:
            case 7:
            case 8:
            case 13:
            case 14:
               _local_2 = "beadSystem.beadCell.name.tf2";
               break;
            case 3:
            case 4:
            case 9:
            case 10:
            case 15:
            case 16:
               _local_2 = "beadSystem.beadCell.name.tf3";
               break;
            case 5:
            case 6:
            case 11:
            case 12:
            case 17:
               _local_2 = "beadSystem.beadCell.name.tf4";
               break;
            default:
               _local_2 = "beadSystem.beadCell.name.tf1";
         }
         return _local_2;
      }
      
      public function judgeLevel(_arg_1:int, _arg_2:int) : Boolean
      {
         switch(_arg_2)
         {
            case 1:
               if(1 <= _arg_1 && _arg_1 <= 4)
               {
                  return true;
               }
               break;
            case 2:
               if(1 <= _arg_1 && _arg_1 <= 8)
               {
                  return true;
               }
               break;
            case 3:
               if(1 <= _arg_1 && _arg_1 <= 12)
               {
                  return true;
               }
               break;
            case 4:
               if(1 <= _arg_1 && _arg_1 <= 16)
               {
                  return true;
               }
               break;
            case 5:
               if(1 <= _arg_1 && _arg_1 <= 19)
               {
                  return true;
               }
               break;
            case 6:
               return true;
            default:
               return false;
         }
         return false;
      }
      
      public function getBeadMcIndex(_arg_1:int) : int
      {
         var _local_2:int = 0;
         switch(_arg_1)
         {
            case 1:
            case 2:
               _local_2 = 2;
               break;
            case 3:
            case 4:
               _local_2 = 3;
               break;
            case 5:
            case 6:
               _local_2 = 4;
               break;
            case 7:
            case 8:
               _local_2 = 7;
               break;
            case 9:
            case 10:
               _local_2 = 8;
               break;
            case 11:
            case 12:
               _local_2 = 9;
               break;
            case 13:
            case 14:
               _local_2 = 10;
               break;
            case 15:
            case 16:
               _local_2 = 11;
               break;
            case 17:
               _local_2 = 12;
         }
         return _local_2;
      }
      
      public function getBeadName(_arg_1:InventoryItemInfo) : String
      {
         var _local_2:String = "";
         if(!_arg_1 || !EquipType.isBead(int(_arg_1.Property1)))
         {
            return "";
         }
         if(_arg_1.Hole2 > 0)
         {
            _local_2 = BeadTemplateManager.Instance.GetBeadInfobyID(_arg_1.TemplateID).Name + "Lv" + _arg_1.Hole1;
         }
         else
         {
            _local_2 = BeadTemplateManager.Instance.GetBeadInfobyID(_arg_1.TemplateID).Name + "Lv" + BeadTemplateManager.Instance.GetBeadInfobyID(_arg_1.TemplateID).BaseLevel;
         }
         return _local_2;
      }
      
      public function getBeadTotleLevel() : int
      {
         var _local_4:int = 0;
         var _local_1:int = 0;
         var _local_2:int = 0;
         var _local_3:* = null;
         _local_4 = 1;
         while(_local_4 <= 18)
         {
            _local_3 = PlayerManager.Instance.Self.BeadBag.getItemAt(_local_4);
            if(_local_3)
            {
               _local_1 = BeadTemplateManager.Instance.GetBeadInfobyID(_local_3.TemplateID).BaseLevel;
               _local_2 += _local_1;
            }
            _local_4++;
         }
         return _local_2;
      }
   }
}

