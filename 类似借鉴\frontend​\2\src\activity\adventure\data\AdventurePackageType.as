package activity.adventure.data
{
   public class AdventurePackageType
   {
      
      public static const ACTIVITYINFO:int = 1;
      
      public static const GAMELEVELINFO:int = 2;
      
      public static const GAMESTEP:int = 3;
      
      public static const WALK_END:int = 4;
      
      public static const SELECT_EVENT:int = 5;
      
      public static const NEW_EVENT:int = 6;
      
      public static const UPDATE_BOX:int = 7;
      
      public static const SHOP_BUY:int = 10;
      
      public static const BUY_COUNT:int = 11;
      
      public static const OPEN_BOX:int = 8;
      
      public static const UPDATE_COUNT:int = 9;
      
      public static const RESET:int = 12;
      
      public static const RANKINFO:int = 13;
      
      public static const LASTWEAKEXPLORE:int = 14;
      
      public static const LEAVE_SEARCH:int = 15;
      
      public static const FREE_COUNT:int = 16;
      
      public static const CROWDFUNDING:int = 17;
      
      public static const CROWDFUNDING_ADD:int = 18;
      
      public static const CROWDFUNDING_START:int = 19;
      
      public function AdventurePackageType()
      {
         super();
      }
   }
}

