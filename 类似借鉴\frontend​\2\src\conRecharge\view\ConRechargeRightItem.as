package conRecharge.view
{
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.controls.container.VBox;
   import com.pickgliss.ui.core.Disposeable;
   import com.pickgliss.utils.ObjectUtils;
   import conRecharge.ConRechargeManager;
   import flash.display.Sprite;
   
   public class ConRechargeRightItem extends Sprite implements Disposeable
   {
      
      private var _vbox:VBox;
      
      public function ConRechargeRightItem()
      {
         super();
         this.initView();
      }
      
      private function initView() : void
      {
         var _local_2:int = 0;
         var _local_1:* = null;
         this._vbox = ComponentFactory.Instance.creatComponentByStylename("conRecharge.rightItem.vbox");
         addChild(this._vbox);
         _local_2 = 0;
         while(_local_2 < ConRechargeManager.instance.dayGiftbagArray.length)
         {
            _local_1 = new DayItem(ConRechargeManager.instance.dayGiftbagArray[_local_2]);
            this._vbox.addChild(_local_1);
            _local_2++;
         }
      }
      
      public function dispose() : void
      {
         ObjectUtils.disposeObject(this._vbox);
         this._vbox = null;
      }
   }
}

import bagAndInfo.cell.BagCell;
import com.pickgliss.ui.ComponentFactory;
import com.pickgliss.ui.controls.BaseButton;
import com.pickgliss.ui.controls.container.HBox;
import com.pickgliss.ui.core.Component;
import com.pickgliss.utils.ObjectUtils;
import conRecharge.ConRechargeManager;
import ddt.data.goods.InventoryItemInfo;
import ddt.data.goods.ItemTemplateInfo;
import ddt.manager.ItemManager;
import ddt.manager.SocketManager;
import ddt.utils.PositionUtils;
import flash.display.Bitmap;
import flash.display.DisplayObject;
import flash.display.DisplayObjectContainer;
import flash.display.InteractiveObject;
import flash.display.Sprite;
import flash.events.EventDispatcher;
import flash.events.MouseEvent;
import wonderfulActivity.WonderfulActivityManager;
import wonderfulActivity.data.GiftBagInfo;
import wonderfulActivity.data.SendGiftInfo;

class DayItem extends Component
{
   
   private var _bg:Bitmap;
   
   private var _btn:BaseButton;
   
   private var _hbox:HBox;
   
   private var _info:GiftBagInfo;
   
   private var _statusArr:Array;
   
   private var _num:Sprite;
   
   public function DayItem(_arg_1:GiftBagInfo)
   {
      var _local_8:int = 0;
      var _local_2:* = null;
      var _local_7:* = null;
      var _local_6:* = null;
      var _local_3:* = null;
      var _local_5:* = null;
      var _local_4:* = null;
      super();
      this._info = _arg_1;
      this._bg = ComponentFactory.Instance.creatBitmap("asset.conRecharge.rightItem.bg");
      addChild(this._bg);
      this._statusArr = WonderfulActivityManager.Instance.getActivityInitDataById(ConRechargeManager.instance.actId).statusArr;
      this._statusArr.sortOn("statusID",16);
      if(WonderfulActivityManager.Instance.getActivityInitDataById(ConRechargeManager.instance.actId).giftInfoDic[_arg_1.giftbagId].times != 0)
      {
         this._btn = ComponentFactory.Instance.creatComponentByStylename("conRecharge.havaReceived.btn");
         addChild(this._btn);
         this._btn.addEventListener("click",this.clickHandler);
         this._btn.enable = false;
      }
      else if(_arg_1.giftConditionArr[0].conditionValue > this._statusArr[0].statusValue)
      {
         this._btn = ComponentFactory.Instance.creatComponentByStylename("conRecharge.canReceive.btn");
         addChild(this._btn);
         this._btn.addEventListener("click",this.clickHandler);
         this._btn.enable = false;
      }
      else
      {
         this._btn = ComponentFactory.Instance.creatComponentByStylename("conRecharge.canReceive.btn");
         addChild(this._btn);
         this._btn.addEventListener("click",this.clickHandler);
      }
      this._num = ComponentFactory.Instance.creatNumberSprite(_arg_1.giftConditionArr[0].conditionValue,"asset.conRecharge.red");
      addChild(this._num);
      PositionUtils.setPos(this._num,"asset.conRecharge.red.pos");
      this._hbox = ComponentFactory.Instance.creatComponentByStylename("conRecharge.rightItem.hbox");
      addChild(this._hbox);
      _local_8 = 0;
      while(_local_8 < _arg_1.giftRewardArr.length)
      {
         _local_7 = _arg_1.giftRewardArr[_local_8].property.split(",");
         _local_6 = ItemManager.Instance.getTemplateById(_arg_1.giftRewardArr[_local_8].templateId) as ItemTemplateInfo;
         _local_3 = new InventoryItemInfo();
         ObjectUtils.copyProperties(_local_3,_local_6);
         _local_3.StrengthenLevel = _local_7[0];
         _local_3.AttackCompose = _local_7[1];
         _local_3.DefendCompose = _local_7[2];
         _local_3.AgilityCompose = _local_7[3];
         _local_3.LuckCompose = _local_7[4];
         _local_3.MagicAttack = _local_7[6];
         _local_3.MagicDefence = _local_7[7];
         _local_3.ValidDate = _arg_1.giftRewardArr[_local_8].validDate;
         _local_3.IsBinds = _arg_1.giftRewardArr[_local_8].isBind;
         _local_3.Count = _arg_1.giftRewardArr[_local_8].count;
         _local_5 = ComponentFactory.Instance.creatBitmap("asset.conRecharge.goodsBG");
         _local_4 = new BagCell(0,_local_3,false,_local_5);
         this._hbox.addChild(_local_4);
         _local_8++;
      }
   }
   
   private function clickHandler(_arg_1:MouseEvent) : void
   {
      var _local_3:SendGiftInfo = new SendGiftInfo();
      _local_3.activityId = ConRechargeManager.instance.actId;
      _local_3.giftIdArr = [this._info.giftbagId];
      var _local_2:Vector.<SendGiftInfo> = new Vector.<SendGiftInfo>();
      _local_2.push(_local_3);
      SocketManager.Instance.out.sendWonderfulActivityGetReward(_local_2);
      ObjectUtils.disposeObject(this._btn);
      this._btn.removeEventListener("click",this.clickHandler);
      this._btn = ComponentFactory.Instance.creatComponentByStylename("conRecharge.havaReceived.btn");
      addChild(this._btn);
      this._btn.enable = false;
   }
   
   override public function dispose() : void
   {
      super.dispose();
      this._btn.removeEventListener("click",this.clickHandler);
      ObjectUtils.disposeObject(this._bg);
      this._bg = null;
      ObjectUtils.disposeObject(this._btn);
      this._btn = null;
      ObjectUtils.disposeObject(this._hbox);
      this._hbox = null;
      ObjectUtils.disposeObject(this._num);
      this._num = null;
   }
}
