package caveloot
{
   public class CaveLootPackageType
   {
      
      public static const ACTIVITYSTATES:int = 1;
      
      public static const BASEINFO:int = 2;
      
      public static const GETRANK_INFO:int = 3;
      
      public static const DRAW:int = 4;
      
      public static const TAKE_MINEBAGITEM:int = 5;
      
      public static const SELL_MINEBAGITEM:int = 6;
      
      public static const GETPROGRESSAWARD:int = 7;
      
      public static const GETPROGRESSINFO:int = 8;
      
      public static const UPDATESHOPCOIN:int = 9;
      
      public static const UPDATE_RANKINFO:int = 18;
      
      public function CaveLootPackageType()
      {
         super();
      }
   }
}

