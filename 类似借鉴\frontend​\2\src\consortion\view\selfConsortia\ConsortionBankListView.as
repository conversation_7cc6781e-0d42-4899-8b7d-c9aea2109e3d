package consortion.view.selfConsortia
{
   import bagAndInfo.bag.BagListView;
   import bagAndInfo.cell.BagCell;
   import bagAndInfo.cell.CellFactory;
   import com.pickgliss.events.InteractiveEvent;
   import com.pickgliss.utils.DoubleClickManager;
   import ddt.events.CellEvent;
   import flash.events.MouseEvent;
   import flash.utils.Dictionary;
   
   public class ConsortionBankListView extends BagListView
   {
      
      private static var MAX_LINE_NUM:int = 10;
      
      private var _bankLevel:int;
      
      public function ConsortionBankListView(_arg_1:int, _arg_2:int = 0)
      {
         super(_arg_1,MAX_LINE_NUM);
      }
      
      override public function updateBankBag(_arg_1:int) : void
      {
         var _local_5:int = 0;
         var _local_4:int = 0;
         var _local_2:int = 0;
         var _local_3:* = null;
         if(_arg_1 == this._bankLevel)
         {
            return;
         }
         _local_5 = this._bankLevel;
         while(_local_5 < _arg_1)
         {
            _local_4 = 0;
            while(_local_4 < MAX_LINE_NUM)
            {
               _local_2 = _local_5 * MAX_LINE_NUM + _local_4;
               _local_3 = _cells[_local_2] as BagCell;
               _local_3.grayFilters = false;
               _local_3.mouseEnabled = true;
               _local_4++;
            }
            _local_5++;
         }
         this._bankLevel = _arg_1;
      }
      
      override protected function __doubleClickHandler(_arg_1:InteractiveEvent) : void
      {
         if((_arg_1.currentTarget as BagCell).info != null)
         {
            dispatchEvent(new CellEvent("doubleclick",_arg_1.currentTarget));
         }
      }
      
      override protected function __clickHandler(_arg_1:InteractiveEvent) : void
      {
         if(Boolean(_arg_1.currentTarget))
         {
            dispatchEvent(new CellEvent("itemclick",_arg_1.currentTarget,false,false));
         }
      }
      
      private function __resultHandler(_arg_1:MouseEvent) : void
      {
      }
      
      override protected function createCells() : void
      {
         var _local_4:int = 0;
         var _local_3:int = 0;
         var _local_1:int = 0;
         var _local_2:* = null;
         _cells = new Dictionary();
         _local_4 = 0;
         while(_local_4 < MAX_LINE_NUM)
         {
            _local_3 = 0;
            while(_local_3 < MAX_LINE_NUM)
            {
               _local_1 = _local_4 * MAX_LINE_NUM + _local_3;
               _local_2 = CellFactory.instance.createBankCell(_local_1) as BagCell;
               addChild(_local_2);
               _local_2.bagType = _bagType;
               _local_2.addEventListener("interactive_click",this.__clickHandler);
               _local_2.addEventListener("interactive_double_click",this.__doubleClickHandler);
               DoubleClickManager.Instance.enableDoubleClick(_local_2);
               _local_2.addEventListener("lockChanged",__cellChanged);
               _cells[_local_2.place] = _local_2;
               if(this._bankLevel <= _local_4)
               {
                  _local_2.grayFilters = true;
                  _local_2.mouseEnabled = false;
               }
               _local_3++;
            }
            _local_4++;
         }
      }
      
      override public function checkBankCell() : int
      {
         var _local_4:int = 0;
         var _local_3:int = 0;
         var _local_1:int = 0;
         var _local_2:* = null;
         if(this._bankLevel == 0)
         {
            return 1;
         }
         _local_4 = 0;
         while(_local_4 < this._bankLevel)
         {
            _local_3 = 0;
            while(_local_3 < MAX_LINE_NUM)
            {
               _local_1 = _local_4 * MAX_LINE_NUM + _local_3;
               _local_2 = _cells[_local_1] as BagCell;
               if(!_local_2.info)
               {
                  return 0;
               }
               _local_3++;
            }
            _local_4++;
         }
         if(this._bankLevel == MAX_LINE_NUM)
         {
            return 2;
         }
         return 3;
      }
      
      override public function dispose() : void
      {
         var _local_1:BagCell = null;
         for each(_local_1 in _cells)
         {
            _local_1.removeEventListener("interactive_click",this.__clickHandler);
            _local_1.removeEventListener("interactive_double_click",this.__doubleClickHandler);
            _local_1.removeEventListener("lockChanged",__cellChanged);
         }
         super.dispose();
         if(Boolean(this.parent))
         {
            this.parent.removeChild(this);
         }
      }
   }
}

