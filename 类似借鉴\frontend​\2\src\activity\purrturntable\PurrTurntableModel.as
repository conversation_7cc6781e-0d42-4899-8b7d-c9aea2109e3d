package activity.purrturntable
{
   import road7th.data.DictionaryData;
   
   public class PurrTurntableModel
   {
      
      public var purrmoneyCountInt:int;
      
      public var chipCountInt:int;
      
      public var limitCountInt:int;
      
      public var openCountInt:int;
      
      public var rankArr:Array;
      
      public var myRankInt:int;
      
      public var showTempDic:DictionaryData;
      
      public var DataTypeArr:Array;
      
      public var rewardTempDic:DictionaryData;
      
      public var showArr:Array;
      
      public var startDateStr:String;
      
      public var endDateStr:String;
      
      public var showDateStr:String;
      
      public var playinfoTypeInt:int;
      
      public var playinfoLevelInt:int;
      
      public var playinfoFreeCoinInt:int = 0;
      
      public var playinfoSubscriptInt:int;
      
      public var playinfoScoreInt:int;
      
      public var playinfoClaimedDic:DictionaryData;
      
      public var romandTypeInt:int = -1;
      
      public var romandLevelInt:int = -1;
      
      public var NiuniumoneyRestrictInt:int;
      
      public var AddStateInt:int;
      
      public var isdrawBool:Boolean;
      
      public var issuccessfulBool:Boolean;
      
      public var recoverByteInt:int;
      
      public var rankawardStr:String;
      
      public var Params1Str:String;
      
      public var Params2Str:String;
      
      public var Params5Str:String;
      
      public var FreeAddTipBool:Boolean = false;
      
      public var TypeDic:DictionaryData;
      
      public function PurrTurntableModel()
      {
         super();
         this.isdrawBool = false;
      }
      
      public function RankAward() : Array
      {
         return this.rankawardStr.split("|");
      }
      
      public function getParams1() : Array
      {
         return this.Params1Str.split("|");
      }
      
      public function getParams2() : Array
      {
         return this.Params2Str.split(",");
      }
      
      public function setPlayinfoClaimed(_arg_1:String) : void
      {
         var _local_3:int = 0;
         this.playinfoClaimedDic = new DictionaryData();
         var _local_2:Array = _arg_1.split(",");
         while(_local_3 < _local_2.length)
         {
            this.playinfoClaimedDic.add(_local_2[_local_3],true);
            _local_3++;
         }
      }
   }
}

