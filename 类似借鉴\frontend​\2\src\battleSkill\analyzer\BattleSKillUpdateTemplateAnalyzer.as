package battleSkill.analyzer
{
   import battleSkill.info.BattleSkillUpdateInfo;
   import battleSkill.info.BattleSkillUpdateMaterialInfo;
   import com.pickgliss.loader.DataAnalyzer;
   import com.pickgliss.utils.ObjectUtils;
   
   public class BattleSKillUpdateTemplateAnalyzer extends DataAnalyzer
   {
      
      private var _list:Vector.<BattleSkillUpdateInfo>;
      
      public function BattleSKillUpdateTemplateAnalyzer(_arg_1:Function)
      {
         super(_arg_1);
      }
      
      override public function analyze(_arg_1:*) : void
      {
         var _local_3:* = undefined;
         var _local_8:int = 0;
         var _local_7:int = 0;
         var _local_9:* = null;
         var _local_2:* = null;
         var _local_6:* = null;
         var _local_4:* = null;
         var _local_5:XML = new XML(_arg_1);
         this._list = new Vector.<BattleSkillUpdateInfo>();
         if(_local_5.@value == "true")
         {
            _local_6 = _local_5..Skill;
            _local_8 = 0;
            while(_local_8 < _local_6.length())
            {
               _local_9 = new BattleSkillUpdateInfo();
               _local_3 = new Vector.<BattleSkillUpdateMaterialInfo>();
               _local_9.SkillID = _local_6[_local_8].@SkillID;
               _local_4 = _local_6[_local_8]..SkillDetail;
               _local_7 = 0;
               while(_local_7 < _local_4.length())
               {
                  _local_2 = new BattleSkillUpdateMaterialInfo();
                  ObjectUtils.copyPorpertiesByXML(_local_2,_local_4[_local_7]);
                  _local_3.push(_local_2);
                  _local_9.UpdateMaterialInfo = _local_3;
                  _local_7++;
               }
               this._list.push(_local_9);
               _local_8++;
            }
            onAnalyzeComplete();
         }
         else
         {
            message = _local_5.@message;
            onAnalyzeError();
         }
      }
      
      public function get list() : Vector.<BattleSkillUpdateInfo>
      {
         return this._list;
      }
   }
}

