package com.pickgliss.ui.controls
{
   import com.greensock.TweenLite;
   import com.pickgliss.events.InteractiveEvent;
   import com.pickgliss.events.ListItemEvent;
   import com.pickgliss.geom.InnerRectangle;
   import com.pickgliss.toplevel.StageReferance;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.ComponentSetting;
   import com.pickgliss.ui.core.Component;
   import com.pickgliss.utils.ClassUtils;
   import com.pickgliss.utils.DisplayUtils;
   import com.pickgliss.utils.ObjectUtils;
   import flash.display.DisplayObject;
   import flash.display.Shape;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import flash.text.TextField;
   
   [Event(name="stateChange",type="com.pickgliss.events.InteractiveEvent")]
   public class ComboBox extends Component
   {
      
      public static const P_button:String = "button";
      
      public static const P_defaultShowState:String = "currentShowState";
      
      public static const P_listInnerRect:String = "listInnerRect";
      
      public static const P_listPanel:String = "listPanel";
      
      public static const P_textField:String = "textField";
      
      public static const P_textInnerRect:String = "textInnerRect";
      
      protected static const COMBOX_HIDE_STATE:int = 0;
      
      protected static const COMBOX_SHOW_STATE:int = 1;
      
      public static const P_snapItemHeight:String = "snapItemHeight";
      
      public static var HIDE:int = 0;
      
      public static var SHOW:int = 1;
      
      protected var _button:BaseButton;
      
      protected var _buttonStyle:String;
      
      protected var _comboboxZeroPos:Point;
      
      protected var _currentSelectedCellValue:*;
      
      protected var _currentSelectedIndex:int = -1;
      
      protected var _currentSelectedItem:*;
      
      protected var _defaultShowState:int = 0;
      
      protected var _listInnerRect:InnerRectangle;
      
      protected var _listInnerRectString:String;
      
      protected var _listPanel:ListPanel;
      
      protected var _listPanelStyle:String;
      
      protected var _maskExtends:int = 100;
      
      protected var _maskShape:Shape;
      
      protected var _selctedPropName:String;
      
      protected var _state:int;
      
      protected var _textField:TextField;
      
      protected var _textInnerRect:InnerRectangle;
      
      protected var _textRectString:String = "textRectString";
      
      protected var _textStyle:String;
      
      protected var _tweenY:int;
      
      protected var _maxHeight:int = 540;
      
      protected var _easeType:int = 1;
      
      private var mGrayLayer:Sprite;
      
      protected var _snapItemHeight:Boolean;
      
      public function ComboBox()
      {
         super();
      }
      
      public function set button(_arg_1:BaseButton) : void
      {
         if(this._button == _arg_1)
         {
            return;
         }
         ObjectUtils.disposeObject(this._button);
         this._button = _arg_1;
         onPropertiesChanged("button");
      }
      
      public function get button() : BaseButton
      {
         return this._button;
      }
      
      public function set buttonStyle(_arg_1:String) : void
      {
         if(this._buttonStyle == _arg_1)
         {
            return;
         }
         this._buttonStyle = _arg_1;
         this.button = ComponentFactory.Instance.creat(this._buttonStyle);
      }
      
      public function get currentSelectedCellValue() : *
      {
         return this._currentSelectedCellValue;
      }
      
      public function get currentSelectedIndex() : int
      {
         return this._currentSelectedIndex;
      }
      
      public function set currentSelectedIndex(_arg_1:int) : void
      {
         this._listPanel.list.currentSelectedIndex = _arg_1;
      }
      
      public function get currentSelectedItem() : *
      {
         return this._currentSelectedItem;
      }
      
      public function set defaultShowState(_arg_1:int) : void
      {
         if(this._defaultShowState == _arg_1)
         {
            return;
         }
         this._defaultShowState = _arg_1;
         onPropertiesChanged("currentShowState");
      }
      
      override public function dispose() : void
      {
         if(Boolean(this._listPanel) && Boolean(this._listPanel.list))
         {
            this._listPanel.list.removeStateListener(this.updateListSize);
         }
         StageReferance.stage.removeEventListener("click",this.__onStageClick);
         StageReferance.stage.removeEventListener("mouseDown",this.__onStageDown);
         removeEventListener("addedToStage",this.__onAddToStage);
         if(Boolean(this._listPanel) && Boolean(this._listPanel.list))
         {
            this._listPanel.list.removeEventListener("listItemClick",this.__onItemChanged);
         }
         ObjectUtils.disposeObject(this._listPanel);
         this._listPanel = null;
         ObjectUtils.disposeObject(this._button);
         this._button = null;
         ObjectUtils.disposeObject(this._textField);
         this._textField = null;
         ObjectUtils.disposeObject(this._maskShape);
         this._maskShape = null;
         this._listInnerRect = null;
         super.dispose();
      }
      
      public function doHide() : void
      {
         if(this._state == HIDE)
         {
            return;
         }
         if(this._listPanel.vectorListModel == null)
         {
            return;
         }
         if(this._listPanel.vectorListModel.getSize() == 0)
         {
            return;
         }
         this._defaultShowState = 0;
         TweenLite.killTweensOf(this._listPanel);
         TweenLite.to(this._listPanel,ComponentSetting.COMBOBOX_HIDE_TIME,{
            "y":this._comboboxZeroPos.y - this._listPanel.height,
            "ease":ComponentSetting.COMBOBOX_HIDE_EASE_FUNCTION,
            "onComplete":this.onHideComplete
         });
         this._state = HIDE;
      }
      
      public function doShow() : void
      {
         if(this._state == SHOW)
         {
            return;
         }
         if(this._listPanel.vectorListModel == null)
         {
            return;
         }
         if(this._listPanel.vectorListModel.getSize() == 0)
         {
            return;
         }
         this.onPosChanged();
         this._defaultShowState = 1;
         if(Boolean(this._listPanel))
         {
            ComponentSetting.COMBOX_LIST_LAYER.addChild(this._listPanel.asDisplayObject());
         }
         ComponentSetting.COMBOX_LIST_LAYER.addChild(this._maskShape);
         TweenLite.killTweensOf(this._listPanel);
         TweenLite.to(this._listPanel,ComponentSetting.COMBOBOX_SHOW_TIME,{
            "y":this._tweenY,
            "ease":ComponentSetting.COMBOBOX_SHOW_EASE_FUNCTION
         });
         this._state = SHOW;
      }
      
      public function set listInnerRect(_arg_1:InnerRectangle) : void
      {
         if(this._listInnerRect != null && this._listInnerRect.equals(_arg_1))
         {
            return;
         }
         this._listInnerRect = _arg_1;
         onPropertiesChanged("listInnerRect");
      }
      
      public function set listInnerRectString(_arg_1:String) : void
      {
         if(this._listInnerRectString == _arg_1)
         {
            return;
         }
         this._listInnerRectString = _arg_1;
         this.listInnerRect = ClassUtils.CreatInstance("com.pickgliss.geom.InnerRectangle",ComponentFactory.parasArgs(this._listInnerRectString));
      }
      
      public function get listPanel() : ListPanel
      {
         return this._listPanel;
      }
      
      public function set listPanel(_arg_1:ListPanel) : void
      {
         if(this._listPanel == _arg_1)
         {
            return;
         }
         if(Boolean(this._listPanel))
         {
            this._listPanel.list.removeEventListener("listItemClick",this.__onItemChanged);
         }
         ObjectUtils.disposeObject(this._listPanel);
         this._listPanel = _arg_1;
         this._listPanel.list.addEventListener("listItemClick",this.__onItemChanged);
         onPropertiesChanged("listPanel");
      }
      
      public function set listPanelStyle(_arg_1:String) : void
      {
         if(this._listPanelStyle == _arg_1)
         {
            return;
         }
         this._listPanelStyle = _arg_1;
         this.listPanel = ComponentFactory.Instance.creat(this._listPanelStyle);
      }
      
      public function set selctedPropName(_arg_1:String) : void
      {
         if(this._selctedPropName == _arg_1)
         {
            return;
         }
         this._selctedPropName = _arg_1;
      }
      
      public function get textField() : TextField
      {
         return this._textField;
      }
      
      public function set textField(_arg_1:TextField) : void
      {
         if(this._textField == _arg_1)
         {
            return;
         }
         this._textField = _arg_1;
         onPropertiesChanged("textField");
      }
      
      public function set textInnerRect(_arg_1:InnerRectangle) : void
      {
         if(this._textInnerRect != null && this._textInnerRect.equals(_arg_1))
         {
            return;
         }
         this._textInnerRect = _arg_1;
         onPropertiesChanged("textInnerRect");
      }
      
      public function set textInnerRectString(_arg_1:String) : void
      {
         if(this._textRectString == _arg_1)
         {
            return;
         }
         this._textRectString = _arg_1;
         this.textInnerRect = ClassUtils.CreatInstance("com.pickgliss.geom.InnerRectangle",ComponentFactory.parasArgs(this._textRectString));
      }
      
      public function set textStyle(_arg_1:String) : void
      {
         if(this._textStyle == _arg_1)
         {
            return;
         }
         this._textStyle = _arg_1;
         this.textField = ComponentFactory.Instance.creat(this._textStyle);
      }
      
      public function set enable(_arg_1:Boolean) : void
      {
         this._button.enable = _arg_1;
         if(!_arg_1)
         {
            this.mGrayLayer = new Sprite();
            this.mGrayLayer.width = 500;
            this.mGrayLayer.height = 500;
            this.mGrayLayer.alpha = 0.5;
            addChild(this.mGrayLayer);
         }
         else if(Boolean(this.mGrayLayer) && Boolean(this.mGrayLayer.parent))
         {
            this.mGrayLayer.parent.removeChild(this.mGrayLayer);
         }
      }
      
      public function get enable() : Boolean
      {
         return this._button.enable;
      }
      
      protected function __onItemChanged(_arg_1:ListItemEvent) : void
      {
         this._currentSelectedItem = _arg_1.cell;
         this._currentSelectedCellValue = _arg_1.cellValue;
         this._currentSelectedIndex = _arg_1.index;
         if(this._selctedPropName != null)
         {
            this._textField.text = _arg_1.cell[this._selctedPropName];
         }
         dispatchEvent(new InteractiveEvent("stateChange"));
      }
      
      override protected function addChildren() : void
      {
         super.addChildren();
         if(Boolean(this._button))
         {
            addChild(this._button);
         }
         if(Boolean(this._textField))
         {
            addChild(this._textField);
         }
      }
      
      override protected function init() : void
      {
         this._maskShape = new Shape();
         addEventListener("addedToStage",this.__onAddToStage);
         StageReferance.stage.addEventListener("click",this.__onStageClick);
         StageReferance.stage.addEventListener("mouseDown",this.__onStageDown);
         super.init();
      }
      
      protected function __onStageClick(_arg_1:MouseEvent) : void
      {
         var _local_2:DisplayObject = _arg_1.target as DisplayObject;
         if(!DisplayUtils.isTargetOrContain(_local_2,this) && !DisplayUtils.isTargetOrContain(_local_2,this._listPanel))
         {
            return;
         }
         if(DisplayUtils.isTargetOrContain(_local_2,this._button) || DisplayUtils.isTargetOrContain(_local_2,this._listPanel.list))
         {
            if(this._state == HIDE)
            {
               this.doShow();
            }
            else
            {
               this.doHide();
            }
         }
      }
      
      protected function __onStageDown(_arg_1:MouseEvent) : void
      {
         var _local_2:DisplayObject = _arg_1.target as DisplayObject;
         if(DisplayUtils.isTargetOrContain(_local_2,this._listPanel) || DisplayUtils.isTargetOrContain(_local_2,this))
         {
            return;
         }
         this.doHide();
      }
      
      protected function onHideComplete() : void
      {
         if(Boolean(this._listPanel) && Boolean(this._listPanel.parent))
         {
            this._listPanel.parent.removeChild(this._listPanel.asDisplayObject());
         }
         if(Boolean(this._maskShape) && Boolean(this._maskShape.parent))
         {
            this._maskShape.parent.removeChild(this._maskShape);
         }
      }
      
      override protected function onPosChanged() : void
      {
         this._comboboxZeroPos = DisplayUtils.getPointFromObject(new Point(0,0),this,ComponentSetting.COMBOX_LIST_LAYER);
         if(this._comboboxZeroPos.y + this._listInnerRect.para2 + this._listInnerRect.para4 > this._maxHeight)
         {
            this._tweenY = this._comboboxZeroPos.y - this._listInnerRect.para4;
            this._easeType = 2;
         }
         else
         {
            this._tweenY = this._comboboxZeroPos.y + this._listInnerRect.para2;
            this._easeType = 1;
         }
         this.updateListPos();
         this.updateMask();
      }
      
      override protected function onProppertiesUpdate() : void
      {
         super.onProppertiesUpdate();
         if(Boolean(_changedPropeties["listInnerRect"]) || Boolean(_changedPropeties["height"]) || Boolean(_changedPropeties["width"]) || Boolean(_changedPropeties["currentShowState"]) || Boolean(_changedPropeties["listPanel"]))
         {
            this.onPosChanged();
            this.updateListSize();
            if(Boolean(this._listPanel))
            {
               this._listPanel.list.addStateListener(this.updateListSize);
            }
         }
         if(Boolean(_changedPropeties["textInnerRect"]) || Boolean(_changedPropeties["height"]) || Boolean(_changedPropeties["width"]))
         {
            DisplayUtils.layoutDisplayWithInnerRect(this._textField,this._textInnerRect,_width,_height);
         }
         if(Boolean(_changedPropeties["height"]) || Boolean(_changedPropeties["width"]))
         {
            this._button.beginChanges();
            this._button.width = _width;
            this._button.height = _height;
            this._button.commitChanges();
         }
      }
      
      protected function updateListPos() : void
      {
         if(this._listInnerRect == null || this._listPanel == null)
         {
            return;
         }
         var _local_1:Rectangle = this._listInnerRect.getInnerRect(_width,_height);
         this._listPanel.x = this._comboboxZeroPos.x + _local_1.x;
         this._listPanel.y = this._comboboxZeroPos.y + _local_1.y;
         if(this._defaultShowState == 0)
         {
            if(this._easeType == 1)
            {
               this._listPanel.y = this._comboboxZeroPos.y - this._listPanel.height;
            }
            else
            {
               this._listPanel.y = this._comboboxZeroPos.y;
            }
         }
         else if(this._defaultShowState == 1)
         {
            this._listPanel.y = this._comboboxZeroPos.y + this._listInnerRect.para2;
         }
      }
      
      protected function updateListSize(_arg_1:InteractiveEvent = null) : void
      {
         if(this._listPanel == null)
         {
            return;
         }
         var _local_2:Rectangle = this._listInnerRect.getInnerRect(_width,_height);
         if(this._snapItemHeight)
         {
            this._listPanel.height = this._listPanel.list.getViewSize().height + this._listPanel.getShowHScrollbarExtendHeight();
         }
         else
         {
            this._listPanel.height = _local_2.height;
         }
         this._listPanel.width = _local_2.width;
         this._maskShape = DisplayUtils.drawRectShape(this._listPanel.width + 2 * this._maskExtends,this._listPanel.height + this._maskExtends * 2,this._maskShape);
         this.updateMask();
      }
      
      protected function updateMask() : void
      {
         if(!this._listPanel)
         {
            return;
         }
         this._listPanel.mask = this._maskShape;
         this._maskShape.x = this._comboboxZeroPos.x - this._maskExtends;
         this._maskShape.y = this._easeType == 1 ? this._comboboxZeroPos.y + _height : this._comboboxZeroPos.y - this._maskShape.height;
      }
      
      public function set snapItemHeight(_arg_1:Boolean) : void
      {
         if(this._snapItemHeight == _arg_1)
         {
            return;
         }
         this._snapItemHeight = _arg_1;
         onPropertiesChanged("snapItemHeight");
      }
      
      protected function __onAddToStage(_arg_1:Event) : void
      {
         this.onPosChanged();
      }
   }
}

