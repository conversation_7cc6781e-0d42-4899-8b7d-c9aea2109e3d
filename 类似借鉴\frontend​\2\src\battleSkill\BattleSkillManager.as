package battleSkill
{
   import battleSkill.analyzer.BattleSKillUpdateTemplateAnalyzer;
   import battleSkill.analyzer.BattleSkillSkillTemplateAnalyzer;
   import battleSkill.event.BattleSkillEvent;
   import battleSkill.info.BattleSkillSkillInfo;
   import battleSkill.info.BattleSkillUpdateInfo;
   import ddt.CoreManager;
   import ddt.events.PkgEvent;
   import ddt.loader.LoaderCreate;
   import ddt.manager.GameInSocketOut;
   import ddt.manager.ServerConfigManager;
   import ddt.manager.SocketManager;
   import ddt.utils.HelperDataModuleLoad;
   import ddt.utils.HelperUIModuleLoad;
   import flash.events.IEventDispatcher;
   import flash.utils.Dictionary;
   import horse.HorseManager;
   import horse.data.HorseSkillVo;
   import road7th.comm.PackageIn;
   import road7th.data.DictionaryData;
   
   public class BattleSkillManager extends CoreManager
   {
      
      private static var _manager:BattleSkillManager;
      
      private var _skillTemplateList:Vector.<BattleSkillSkillInfo>;
      
      private var _skillUpdateTempList:Vector.<BattleSkillUpdateInfo>;
      
      private var _initiativeSkillList:Array = null;
      
      private var _passiveSkillList:Array = null;
      
      private var _activatedSkillList:Array;
      
      private var _bringSkillList:Dictionary;
      
      public var curUpSkillId:int;
      
      public function BattleSkillManager(_arg_1:IEventDispatcher = null)
      {
         super(_arg_1);
      }
      
      public static function get instance() : BattleSkillManager
      {
         if(_manager == null)
         {
            _manager = new BattleSkillManager();
         }
         return _manager;
      }
      
      override protected function start() : void
      {
         this.loadresource();
      }
      
      private function loadresource() : void
      {
         new HelperUIModuleLoad().loadUIModule(["battleSkill"],(function():*
         {
            var moduleLoaded:* = function():void
            {
               loadTempleResource((function():*
               {
                  var loaded:* = function():void
                  {
                     dispatchEvent(new BattleSkillEvent(BattleSkillEvent.OPEN_SKILL_VIEW,null));
                  };
                  return loaded;
               })());
            };
            return moduleLoaded;
         })());
      }
      
      public function setup() : void
      {
         this._initiativeSkillList = [];
         this._passiveSkillList = [];
         this.clearSkillCacheData();
         this.initEvent();
      }
      
      public function loadTempleResource(_arg_1:Function) : void
      {
         var callBack:* = _arg_1;
         if(this._skillTemplateList != null && this._skillUpdateTempList != null)
         {
            callBack();
            return;
         }
         new HelperDataModuleLoad().loadDataModule([LoaderCreate.Instance.createBattleSkillUpdateTemplate],(function():*
         {
            var loaded:* = function():void
            {
               skillSort();
               callBack();
            };
            return loaded;
         })());
      }
      
      private function initEvent() : void
      {
         SocketManager.Instance.addEventListener(PkgEvent.format(132,8),this.battleSkillInfo_Handler);
         SocketManager.Instance.addEventListener(PkgEvent.format(132,6),this.updateBattleSkill_Handler);
         SocketManager.Instance.addEventListener(PkgEvent.format(132,7),this.bringBattleSkill_Handler);
      }
      
      private function battleSkillInfo_Handler(_arg_1:PkgEvent) : void
      {
         var _local_5:int = 0;
         var _local_3:int = 0;
         var _local_6:int = 0;
         this.clearSkillCacheData();
         var _local_4:PackageIn = _arg_1.pkg;
         var _local_2:int = _local_4.readInt();
         _local_6 = 0;
         while(_local_6 < _local_2)
         {
            _local_5 = _local_4.readInt();
            _local_3 = _local_4.readInt();
            this._activatedSkillList.push(_local_5);
            if(_local_3 > 0)
            {
               this._bringSkillList[_local_3] = _local_5;
            }
            _local_6++;
         }
         dispatchEvent(new BattleSkillEvent(BattleSkillEvent.BATTLESKILL_INFO));
      }
      
      private function updateBattleSkill_Handler(_arg_1:PkgEvent) : void
      {
         var _local_3:String = null;
         var _local_4:PackageIn = _arg_1.pkg;
         var _local_5:int = _local_4.readInt();
         var _local_2:int = int(this._activatedSkillList.indexOf(_local_5));
         if(_local_2 != -1)
         {
            this._activatedSkillList.splice(_local_2,1);
         }
         this._activatedSkillList.push(_local_5);
         for(_local_3 in this._bringSkillList)
         {
            if(this._bringSkillList[_local_3] == this.curUpSkillId)
            {
               this._bringSkillList[_local_3] = _local_5;
               break;
            }
         }
         dispatchEvent(new BattleSkillEvent(BattleSkillEvent.UPDATE_SKILL,_local_5));
      }
      
      private function bringBattleSkill_Handler(_arg_1:PkgEvent) : void
      {
         var _local_3:String = null;
         var _local_4:PackageIn = _arg_1.pkg;
         var _local_2:Array = [];
         var _local_5:int = _local_4.readInt();
         var _local_6:int = _local_4.readInt();
         _local_2.push(_local_5);
         _local_2.push(_local_6);
         if(_local_6 == 0)
         {
            for(_local_3 in this._bringSkillList)
            {
               if(this._bringSkillList[_local_3] == _local_5)
               {
                  this._bringSkillList[_local_3] = 0;
                  break;
               }
            }
         }
         else
         {
            this._bringSkillList[_local_6] = _local_5;
         }
         dispatchEvent(new BattleSkillEvent(BattleSkillEvent.BRIGHT_SKILL,_local_2));
      }
      
      public function isEquipFull(_arg_1:int) : int
      {
         var _local_3:int = 0;
         var _local_2:int = this.isInitiaveSkill(_arg_1) ? 1 : 4;
         var _local_4:int = _local_2 + 2;
         while(_local_2 <= _local_4)
         {
            if(this._bringSkillList[_local_2] <= 0)
            {
               _local_3 = _local_2;
               break;
            }
            _local_2++;
         }
         return _local_3;
      }
      
      public function isSkillHasEquip(_arg_1:int) : Boolean
      {
         var _local_2:String = null;
         for(_local_2 in this._bringSkillList)
         {
            if(this._bringSkillList[_local_2] == _arg_1)
            {
               return true;
            }
         }
         return false;
      }
      
      public function getBringSkillList() : Dictionary
      {
         return this._bringSkillList;
      }
      
      public function get curUseSkillList() : DictionaryData
      {
         var _local_2:int = 0;
         var _local_1:DictionaryData = new DictionaryData();
         if(Boolean(this._bringSkillList))
         {
            _local_2 = 1;
            while(_local_2 <= 3)
            {
               _local_1[_local_2] = this._bringSkillList[_local_2];
               _local_2++;
            }
         }
         return _local_1;
      }
      
      public function getActivatedSkillArr() : Array
      {
         return this._activatedSkillList;
      }
      
      public function getBattleSKillInfoBySkillID(_arg_1:int) : BattleSkillSkillInfo
      {
         var _local_3:BattleSkillSkillInfo = null;
         var _local_2:Array = this._initiativeSkillList.concat(this._passiveSkillList);
         for each(_local_3 in _local_2)
         {
            if(_local_3.SkillID == _arg_1)
            {
               return _local_3;
            }
         }
         return null;
      }
      
      public function isActivateBySkillID(_arg_1:int) : Boolean
      {
         return this._activatedSkillList.indexOf(_arg_1) != -1;
      }
      
      public function getActivateSkillInfoByType(_arg_1:int) : BattleSkillSkillInfo
      {
         var _local_2:int = 0;
         var _local_3:* = null;
         _local_2 = 0;
         while(_local_2 < this._activatedSkillList.length)
         {
            _local_3 = this.getBattleSKillInfoBySkillID(this._activatedSkillList[_local_2]);
            if(_local_3.Type == _arg_1)
            {
               return _local_3;
            }
            _local_2++;
         }
         return null;
      }
      
      public function getInitiativeSkillList() : Array
      {
         if(this._initiativeSkillList == null || this._initiativeSkillList.length <= 0)
         {
            this.skillSort();
         }
         return this._initiativeSkillList;
      }
      
      public function getPassiveSkillList() : Array
      {
         if(this._passiveSkillList == null || this._passiveSkillList.length <= 0)
         {
            this.skillSort();
         }
         return this._passiveSkillList;
      }
      
      public function loadSkillTemplateList(_arg_1:BattleSkillSkillTemplateAnalyzer) : void
      {
         this._skillTemplateList = _arg_1.list;
         this.skillSort();
      }
      
      public function loadSkillUpdateTemplateList(_arg_1:BattleSKillUpdateTemplateAnalyzer) : void
      {
         this._skillUpdateTempList = _arg_1.list;
      }
      
      public function getUpMaterialArr(_arg_1:int) : BattleSkillUpdateInfo
      {
         var _local_3:BattleSkillUpdateInfo = null;
         var _local_2:BattleSkillUpdateInfo = null;
         for each(_local_2 in this._skillUpdateTempList)
         {
            if(_local_2.SkillID == _arg_1)
            {
               _local_3 = _local_2;
               break;
            }
         }
         return _local_3;
      }
      
      public function getNextlevelSkillInfo(_arg_1:int) : BattleSkillSkillInfo
      {
         var _local_2:BattleSkillSkillInfo = null;
         var _local_3:BattleSkillSkillInfo = null;
         var _local_4:BattleSkillSkillInfo = this.getBattleSKillInfoBySkillID(_arg_1);
         for each(_local_3 in this._skillTemplateList)
         {
            if(_local_4.NextID == _local_3.SkillID)
            {
               _local_2 = _local_3;
            }
         }
         return _local_2;
      }
      
      private function skillSort() : void
      {
         var _local_2:BattleSkillSkillInfo = null;
         if(this._skillTemplateList == null)
         {
            return;
         }
         if(Boolean(this._initiativeSkillList) && this._initiativeSkillList.length > 0)
         {
            this._initiativeSkillList = [];
         }
         if(Boolean(this._passiveSkillList) && this._passiveSkillList.length > 0)
         {
            this._passiveSkillList = [];
         }
         var _local_1:Array = ServerConfigManager.instance.getBattleSkillDefaultActivate();
         for each(_local_2 in this._skillTemplateList)
         {
            if(this.isInitiaveSkill(_local_2.SkillID))
            {
               if(_local_1.indexOf(_local_2.SkillID.toString()) == -1)
               {
                  this._initiativeSkillList.push(_local_2);
               }
               else
               {
                  this._initiativeSkillList.unshift(_local_2);
               }
            }
            else if(this.isPassivitySkill(_local_2.SkillID))
            {
               this._passiveSkillList.push(_local_2);
            }
         }
      }
      
      private function clearSkillCacheData() : void
      {
         var _local_1:int = 0;
         this._activatedSkillList = [];
         this._bringSkillList = new Dictionary();
         _local_1 = 1;
         while(_local_1 <= 6)
         {
            this._bringSkillList[_local_1] = 0;
            _local_1++;
         }
      }
      
      private function isInitiaveSkill(_arg_1:int) : Boolean
      {
         var _local_2:HorseSkillVo = HorseManager.instance.getHorseSkillInfoById(_arg_1);
         return Boolean(_local_2) && _local_2.Probability == -1;
      }
      
      private function isPassivitySkill(_arg_1:int) : Boolean
      {
         var _local_2:HorseSkillVo = HorseManager.instance.getHorseSkillInfoById(_arg_1);
         return Boolean(_local_2) && _local_2.Probability == 10000;
      }
      
      public function sendUpSkill(_arg_1:int) : void
      {
         this.curUpSkillId = _arg_1;
         GameInSocketOut.sendUpdateBattleSkill(_arg_1);
      }
   }
}

