package bagAndInfo.bag
{
   import com.pickgliss.events.FrameEvent;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.LayerManager;
   import com.pickgliss.ui.controls.SimpleBitmapButton;
   import com.pickgliss.ui.controls.alert.BaseAlerFrame;
   import com.pickgliss.ui.text.FilterFrameText;
   import com.pickgliss.ui.vo.AlertInfo;
   import ddt.data.EquipType;
   import ddt.data.goods.InventoryItemInfo;
   import ddt.manager.LanguageMgr;
   import ddt.manager.ServerConfigManager;
   import ddt.manager.SocketManager;
   import ddt.manager.SoundManager;
   import flash.display.Bitmap;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import road7th.data.DictionaryData;
   import uigeneral.rewardSelect.RewardSelectBox;
   
   public class OpenBatchView extends BaseAlerFrame
   {
      
      private var _item:InventoryItemInfo;
      
      private var _txt:FilterFrameText;
      
      private var _inputBg:Bitmap;
      
      private var _inputText:FilterFrameText;
      
      private var _maxBtn:SimpleBitmapButton;
      
      public function OpenBatchView()
      {
         super();
         this.initView();
         this.initEvent();
      }
      
      public function set item(_arg_1:InventoryItemInfo) : void
      {
         this._item = _arg_1;
      }
      
      private function initView() : void
      {
         cancelButtonStyle = "core.simplebt";
         submitButtonStyle = "core.simplebt";
         var _local_1:AlertInfo = new AlertInfo(LanguageMgr.GetTranslation("ddt.bag.item.openBatch.titleStr"),LanguageMgr.GetTranslation("ok"),LanguageMgr.GetTranslation("cancel"));
         _local_1.moveEnable = false;
         _local_1.autoDispose = false;
         _local_1.sound = "008";
         info = _local_1;
         this._txt = ComponentFactory.Instance.creatComponentByStylename("openBatchView.promptTxt");
         this._txt.text = LanguageMgr.GetTranslation("ddt.bag.item.openBatch.promptStr");
         this._inputBg = ComponentFactory.Instance.creatBitmap("bagAndInfo.openBatchView.inputBg");
         this._inputText = ComponentFactory.Instance.creatComponentByStylename("openBatchView.inputTxt");
         this._inputText.text = "1";
         this._maxBtn = ComponentFactory.Instance.creatComponentByStylename("openBatchView.maxBtn");
         addToContent(this._txt);
         addToContent(this._inputBg);
         addToContent(this._inputText);
         addToContent(this._maxBtn);
      }
      
      private function initEvent() : void
      {
         addEventListener("response",this.responseHandler,false,0,true);
         this._maxBtn.addEventListener("click",this.changeMaxHandler,false,0,true);
         this._inputText.addEventListener("change",this.inputTextChangeHandler,false,0,true);
      }
      
      private function changeMaxHandler(_arg_1:MouseEvent) : void
      {
         SoundManager.instance.play("008");
         if(Boolean(this._item))
         {
            this._inputText.text = String(this._item.Count > this.getOpenMaxCount() ? this.getOpenMaxCount() : this._item.Count);
         }
      }
      
      private function inputTextChangeHandler(_arg_1:Event) : void
      {
         var _local_2:int = 0;
         var _local_4:int = 0;
         var _local_3:int = 0;
         if(Boolean(this._item))
         {
            _local_2 = int(this._inputText.text);
            _local_4 = 99;
            if(_local_2 > 99)
            {
               _local_4 = this.getOpenMaxCount();
            }
            _local_3 = this._item.Count > _local_4 ? _local_4 : this._item.Count;
            if(_local_2 > _local_3)
            {
               this._inputText.text = _local_3.toString();
            }
            if(_local_2 < 1)
            {
               this._inputText.text = "1";
            }
         }
      }
      
      private function getOpenMaxCount() : int
      {
         var _local_1:DictionaryData = ServerConfigManager.instance.batchOpenConfig;
         var _local_2:* = 999999;
         if(_local_1.hasKey(this._item.TemplateID))
         {
            _local_2 = _local_1[this._item.TemplateID];
         }
         return _local_2;
      }
      
      private function responseHandler(_arg_1:FrameEvent) : void
      {
         var _local_2:* = undefined;
         switch(_arg_1.responseCode)
         {
            case 0:
            case 1:
            case 4:
               this.dispose();
               return;
            case 2:
            case 3:
               if(Boolean(this._item))
               {
                  if(this._item.CategoryID == 11 && int(this._item.Property1) == 66)
                  {
                     _local_2 = new RewardSelectBox(this._item,int(this._inputText.text));
                     LayerManager.Instance.addToLayer(_local_2,3,true,1);
                  }
                  if(this._item.TemplateID == 11998 || this._item.TemplateID == 11997 || this._item.TemplateID == 11901 || this._item.TemplateID == 11902 || this._item.TemplateID == 11903 || this._item.TemplateID == 11904 || this._item.TemplateID == 11905 || this._item.TemplateID == 12535 || this._item.TemplateID == 11956 || this._item.TemplateID == 11955 || EquipType.isFireworks(this._item) || this._item.TemplateID == 12746)
                  {
                     SocketManager.Instance.out.sendUseCard(this._item.BagType,this._item.Place,[this._item.TemplateID],this._item.PayType,false,true,int(this._inputText.text));
                  }
                  else if(this._item.TemplateID == 112108 || this._item.TemplateID == 112150 || this._item.TemplateID == 1120538 || this._item.TemplateID == 1120539)
                  {
                     SocketManager.Instance.out.sendOpenRandomBox(this._item.Place,int(this._inputText.text));
                  }
                  else if(this._item.TemplateID == 11961 || this._item.TemplateID == 11965 || this._item.TemplateID == 11967)
                  {
                     SocketManager.Instance.out.sendOpenNationWord(this._item.BagType,this._item.Place,int(this._inputText.text));
                  }
                  else if(this._item.CategoryID == 18)
                  {
                     SocketManager.Instance.out.sendOpenCardBox(this._item.Place,int(this._inputText.text),this._item.BagType);
                  }
                  else if(this._item.CategoryID == 66)
                  {
                     SocketManager.Instance.out.sendOpenSpecialCardBox(this._item.Place,int(this._inputText.text),this._item.BagType);
                  }
                  else if(this._item.TemplateID == 1120412 || this._item.TemplateID == 1120413 || this._item.TemplateID == 1120414 || this._item.TemplateID == 1120433 || this._item.TemplateID == 1120434)
                  {
                     SocketManager.Instance.out.sendChangeSex(this._item.BagType,this._item.Place,int(this._inputText.text));
                  }
                  else if(this._item.CategoryID == 68)
                  {
                     SocketManager.Instance.out.sendOpenAmuletBox(this._item.BagType,this._item.Place,int(this._inputText.text));
                  }
                  else if(EquipType.GOURD_EXP_BOTTLE.indexOf(this._item.TemplateID) >= 0)
                  {
                     SocketManager.Instance.out.sendUseCard(this._item.BagType,this._item.Place,[this._item.TemplateID],this._item.PayType,false,true,int(this._inputText.text));
                  }
                  else
                  {
                     SocketManager.Instance.out.sendItemOpenUp(this._item.BagType,this._item.Place,int(this._inputText.text));
                  }
               }
               this.dispose();
         }
      }
      
      private function removeEvent() : void
      {
         removeEventListener("response",this.responseHandler);
         this._maxBtn.removeEventListener("click",this.changeMaxHandler);
         this._inputText.removeEventListener("change",this.inputTextChangeHandler);
      }
      
      override public function dispose() : void
      {
         super.dispose();
         this._item = null;
         this._txt = null;
         this._inputBg = null;
         this._inputText = null;
         this._maxBtn = null;
      }
   }
}

