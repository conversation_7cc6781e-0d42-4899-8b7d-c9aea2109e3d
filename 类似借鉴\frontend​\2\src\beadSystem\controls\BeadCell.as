package beadSystem.controls
{
   import bagAndInfo.bag.BagView;
   import bagAndInfo.cell.BagCell;
   import bagAndInfo.cell.DragEffect;
   import baglocked.BaglockedManager;
   import beadSystem.beadSystemManager;
   import beadSystem.data.BeadEvent;
   import beadSystem.model.BeadModel;
   import com.pickgliss.events.FrameEvent;
   import com.pickgliss.ui.AlertManager;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.LayerManager;
   import com.pickgliss.ui.controls.alert.BaseAlerFrame;
   import com.pickgliss.ui.text.FilterFrameText;
   import com.pickgliss.utils.ClassUtils;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.data.goods.InventoryItemInfo;
   import ddt.data.goods.ItemTemplateInfo;
   import ddt.events.CEvent;
   import ddt.events.CellEvent;
   import ddt.manager.BeadTemplateManager;
   import ddt.manager.DragManager;
   import ddt.manager.LanguageMgr;
   import ddt.manager.MessageTipManager;
   import ddt.manager.PlayerManager;
   import ddt.manager.ServerConfigManager;
   import ddt.manager.SocketManager;
   import ddt.manager.SoundManager;
   import ddt.view.tips.GoodTipInfo;
   import flash.display.Bitmap;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.utils.getQualifiedClassName;
   import store.view.embed.EmbedStoneCell;
   import store.view.embed.EmbedUpLevelCell;
   
   public class BeadCell extends BagCell
   {
      
      private var _lockIcon:Bitmap;
      
      private var _nameTxt:FilterFrameText;
      
      private var _beadFeedMC:MovieClip;
      
      private var _beadInfo:InventoryItemInfo;
      
      private var _itemInfo:InventoryItemInfo;
      
      private var beadFeedBtn:BeadFeedButton;
      
      public function BeadCell(_arg_1:int, _arg_2:ItemTemplateInfo = null, _arg_3:Boolean = true, _arg_4:Boolean = true, _arg_5:DisplayObject = null)
      {
         super(_arg_1,_arg_2,_arg_3,Boolean(_arg_5) ? _arg_5 : ComponentFactory.Instance.creatBitmap("bagAndInfo.cell.bagCellBgAsset"));
      }
      
      public function get beadPlace() : int
      {
         return _place;
      }
      
      override public function dragDrop(_arg_1:DragEffect) : void
      {
         var _local_4:* = null;
         var _local_3:* = null;
         var _local_2:String = getQualifiedClassName(_arg_1.source);
         if(_arg_1.source is EmbedUpLevelCell)
         {
            _arg_1.action = "none";
            DragManager.acceptDrag(this);
            if(Boolean(this.itemInfo) && this.itemInfo.Hole1 == 21)
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.beadSystem.mostHightLevel"));
               return;
            }
            if(Boolean(this.itemInfo) && this.itemInfo.Hole1 == 1)
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.beadSystem.lvOneCanntUpgrade"));
            }
            if(Boolean(this.itemInfo) && this.itemInfo.TemplateID >= 1210008000)
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("高级宝珠不可升级"));
            }
            SocketManager.Instance.out.sendBeadEquip(31,this.beadPlace);
         }
         else if(_arg_1.data is InventoryItemInfo && !(_arg_1.source is BeadAdvanceCell) && _local_2 != "beadSystem.views::BeadAdvanceInfoCell")
         {
            _local_4 = _arg_1.data as InventoryItemInfo;
            if(_arg_1.source is BeadCell)
            {
               SocketManager.Instance.out.sendBeadEquip(_local_4.Place,this.beadPlace);
               DragManager.acceptDrag(this);
               return;
            }
            if(PlayerManager.Instance.Self.bagLocked)
            {
               BaglockedManager.Instance.show();
               return;
            }
            this._beadInfo = _local_4;
            _arg_1.action = "none";
            DragManager.acceptDrag(this);
            if(this.itemInfo && !this.itemInfo.IsBinds && _arg_1.source != BeadCell)
            {
               _local_3 = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("tips"),LanguageMgr.GetTranslation("ddt.beadSystem.useBindBead"),LanguageMgr.GetTranslation("ok"),LanguageMgr.GetTranslation("cancel"),false,true,false,2);
               _local_3.addEventListener("response",this.__onBindRespones1);
            }
            else
            {
               SocketManager.Instance.out.sendBeadEquip(_local_4.Place,this.beadPlace);
            }
         }
         else if(_arg_1.source is BeadLockButton)
         {
            DragManager.acceptDrag(this);
         }
         else if(_arg_1.source is BeadFeedButton)
         {
            DragManager.acceptDrag(this);
         }
      }
      
      protected function __onBindRespones1(_arg_1:FrameEvent) : void
      {
         switch(_arg_1.responseCode)
         {
            case 0:
            case 1:
            case 4:
               break;
            case 2:
            case 3:
               if(this._beadInfo.Property2 == this.info.Property2)
               {
                  SocketManager.Instance.out.sendBeadEquip(this._beadInfo.Place,this.beadPlace);
               }
               else
               {
                  MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.view.store.matte.notType"));
               }
         }
         _arg_1.currentTarget.removeEventListener("response",this.__onBindRespones);
         ObjectUtils.disposeObject(_arg_1.currentTarget);
      }
      
      public function set itemInfo(_arg_1:InventoryItemInfo) : void
      {
         this._itemInfo = _arg_1;
      }
      
      override public function get itemInfo() : InventoryItemInfo
      {
         return this._itemInfo;
      }
      
      override public function dragStop(_arg_1:DragEffect) : void
      {
         SoundManager.instance.play("008");
         dispatchEvent(new CellEvent("dragStop",null,true));
         if(PlayerManager.Instance.Self.bagLocked)
         {
            BaglockedManager.Instance.show();
            this.locked = false;
            this.dragShowPicTxt();
            return;
         }
         if(_arg_1.action == "move")
         {
            this.locked = false;
            this.dragShowPicTxt();
         }
         if(_arg_1.action == "move" && !_arg_1.target)
         {
            _arg_1.action = "none";
            if(!(_arg_1.target is EmbedStoneCell) || !(_arg_1.target is EmbedUpLevelCell))
            {
               if(!_arg_1.target)
               {
                  SocketManager.Instance.out.reclaimGoods(21,this._itemInfo.Place,1);
               }
            }
            this.locked = false;
         }
         this.dragShowPicTxt();
         super.dragStop(_arg_1);
      }
      
      override public function set locked(_arg_1:Boolean) : void
      {
         super.locked = _arg_1;
         if(_arg_1)
         {
            if(Boolean(_cellMouseOverFormer))
            {
               _cellMouseOverFormer.visible = true;
            }
         }
         else if(Boolean(_cellMouseOverFormer))
         {
            _cellMouseOverFormer.visible = false;
         }
      }
      
      public function FeedBead() : void
      {
         if(!this.itemInfo.IsUsed)
         {
            if(BeadModel.beadCanUpgrade && Boolean(this.info))
            {
               if(PlayerManager.Instance.Self.embedUpLevelCell.itemInfo.Hole1 == 21)
               {
                  MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.beadSystem.mostHightLevel"));
                  return;
               }
               if(this._itemInfo.Hole1 >= 22 || BeadTemplateManager.Instance.GetBeadInfobyID(this._itemInfo.TemplateID).Type3 > 0)
               {
                  beadSystemManager.Instance.addEventListener("createComplete",this.__onCreateComplete);
                  beadSystemManager.Instance.showFrame("infoframe");
                  return;
               }
               this.boxPrompts();
            }
            else
            {
               MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.beadSystem.tipNoFeedBead"));
            }
         }
         else
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.beadSystem.tipLocked"));
         }
      }
      
      private function __onCreateComplete(_arg_1:CEvent) : void
      {
         var _local_3:* = undefined;
         var _local_2:* = null;
         beadSystemManager.Instance.removeEventListener("createComplete",this.__onCreateComplete);
         if(_arg_1.data.type == "infoframe")
         {
            _local_2 = _arg_1.data.spr;
            _local_3 = _local_2;
            _local_3["setBeadName"](this.tipData["beadName"]);
            LayerManager.Instance.addToLayer(_local_2,1,true,1);
            _local_2["textInput"].setFocus();
            _local_2.addEventListener("response",this.__onConfigResponse);
         }
      }
      
      private function insteadString(_arg_1:String, _arg_2:String) : String
      {
         return _arg_1.slice(_arg_1.lastIndexOf(_arg_2) + 1,_arg_1.length);
      }
      
      private function boxPrompts() : void
      {
         var _local_3:* = null;
         var _local_2:* = null;
         var _local_1:* = null;
         if(Boolean(this.itemInfo))
         {
            if(this.itemInfo.IsBinds && !BeadModel.isBeadCellIsBind)
            {
               _local_3 = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("tips"),LanguageMgr.GetTranslation("ddt.beadSystem.useBindBead"),LanguageMgr.GetTranslation("ok"),LanguageMgr.GetTranslation("cancel"),false,true,false,2);
               _local_3.addEventListener("response",this.__onBindRespones);
            }
            else
            {
               _local_2 = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("tips"),LanguageMgr.GetTranslation("ddt.beadSystem.FeedBeadConfirm"),LanguageMgr.GetTranslation("ok"),LanguageMgr.GetTranslation("cancel"),false,true,false,2);
               _local_1 = ComponentFactory.Instance.creatComponentByStylename("beadSystem.feedBeadShowExpTextOneFeed");
               _local_1.htmlText = LanguageMgr.GetTranslation("ddt.beadSystem.feedBeadGetExp",this._itemInfo.Hole2);
               _local_2.height = 200;
               _local_2.addChild(_local_1);
               _local_2.addEventListener("response",this.__onFeedResponse);
            }
         }
      }
      
      protected function __onConfigResponse(_arg_1:FrameEvent) : void
      {
         var _local_2:BaseAlerFrame = _arg_1.currentTarget as BaseAlerFrame;
         SoundManager.instance.playButtonSound();
         switch(_arg_1.responseCode)
         {
            case 2:
            case 3:
               if(_local_2["textInput"].text == "YES" || _local_2["textInput"].text == "yes")
               {
                  this.boxPrompts();
                  _local_2.removeEventListener("response",this.__onFeedResponse);
                  ObjectUtils.disposeObject(_local_2);
               }
               else
               {
                  MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("ddt.beadSystem.feedBeadPromptInfo"));
               }
               return;
            default:
               _local_2.removeEventListener("response",this.__onFeedResponse);
               ObjectUtils.disposeObject(_local_2);
               return;
         }
      }
      
      protected function __onBindRespones(_arg_1:FrameEvent) : void
      {
         var _local_3:* = null;
         var _local_2:* = null;
         SoundManager.instance.playButtonSound();
         switch(_arg_1.responseCode)
         {
            case 0:
            case 4:
               break;
            case 3:
               _local_3 = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("tips"),LanguageMgr.GetTranslation("ddt.beadSystem.FeedBeadConfirm"),LanguageMgr.GetTranslation("ok"),LanguageMgr.GetTranslation("cancel"),false,true,false,2);
               _local_2 = ComponentFactory.Instance.creatComponentByStylename("beadSystem.feedBeadShowExpTextOneFeed");
               _local_2.htmlText = LanguageMgr.GetTranslation("ddt.beadSystem.feedBeadGetExp",this.itemInfo.Hole2);
               _local_3.addChild(_local_2);
               _local_3.addEventListener("response",this.__onFeedResponse);
         }
         _arg_1.currentTarget.removeEventListener("response",this.__onBindRespones);
         ObjectUtils.disposeObject(_arg_1.currentTarget);
      }
      
      protected function __onFeedResponse(_arg_1:FrameEvent) : void
      {
         (parent.parent as BagView).beadFeedBtn.dragAgain();
         SoundManager.instance.play("008");
         switch(_arg_1.responseCode)
         {
            case 2:
            case 3:
               if(!this._beadFeedMC)
               {
                  this._beadFeedMC = ClassUtils.CreatInstance("beadSystem.feed.MC");
                  this._beadFeedMC.gotoAndPlay(1);
                  this._beadFeedMC.x = -10;
                  this._beadFeedMC.y = 130;
                  this._beadFeedMC.addEventListener("startFeedBead",this.__onFeedStart);
                  this._beadFeedMC.addEventListener("feedComplete",this.__onFeedComplete);
                  addChild(this._beadFeedMC);
                  break;
               }
         }
         _arg_1.currentTarget.removeEventListener("response",this.__onFeedResponse);
         ObjectUtils.disposeObject(_arg_1.currentTarget);
      }
      
      private function __onFeedComplete(_arg_1:Event) : void
      {
         this._beadFeedMC.removeEventListener("startFeedBead",this.__onFeedStart);
         this._beadFeedMC.removeEventListener("feedComplete",this.__onFeedComplete);
         this._beadFeedMC.stop();
         ObjectUtils.disposeObject(this._beadFeedMC);
         this._beadFeedMC = null;
      }
      
      private function __onFeedStart(_arg_1:Event) : void
      {
         var _local_2:Array = [];
         _local_2.push(this._place);
         SocketManager.Instance.out.sendBeadUpgrade(_local_2);
         if(this.itemInfo.Hole2 + BeadModel.upgradeCellInfo.Hole2 >= ServerConfigManager.instance.getBeadUpgradeExp()[BeadModel.upgradeCellInfo.Hole1 + 1])
         {
            beadSystemManager.Instance.dispatchEvent(new BeadEvent("playUpgradeMC"));
         }
      }
      
      public function LockBead() : Boolean
      {
         var _local_1:* = undefined;
         if(!this.itemInfo)
         {
            return false;
         }
         if(!this.itemInfo.IsUsed)
         {
            if(Boolean(this._lockIcon))
            {
               this._lockIcon.visible = true;
               setChildIndex(this._lockIcon,numChildren - 1);
            }
            else
            {
               this._lockIcon = ComponentFactory.Instance.creatBitmap("asset.beadSystem.beadInset.lockIcon1");
               _local_1 = 0.7;
               this._lockIcon.scaleY = _local_1;
               this._lockIcon.scaleX = _local_1;
               this.addChild(this._lockIcon);
               setChildIndex(this._lockIcon,numChildren - 1);
            }
            SocketManager.Instance.out.sendBeadLock(this._place);
         }
         else
         {
            if(Boolean(this._lockIcon))
            {
               this._lockIcon.visible = false;
            }
            SocketManager.Instance.out.sendBeadLock(this._place);
            this.itemInfo.IsUsed = false;
         }
         return true;
      }
      
      private function onStack2(_arg_1:FrameEvent) : void
      {
         SoundManager.instance.play("008");
         var _local_2:BaseAlerFrame = _arg_1.target as BaseAlerFrame;
         _local_2.removeEventListener("response",this.onStack2);
         _local_2.dispose();
      }
      
      override public function set info(_arg_1:ItemTemplateInfo) : void
      {
         var _local_2:int = 0;
         var _local_3:* = undefined;
         if(Boolean(_info))
         {
            _tipData = null;
            this.locked = false;
            if(Boolean(this._nameTxt))
            {
               this._nameTxt.htmlText = "";
               this._nameTxt.visible = false;
            }
         }
         super.info = _arg_1;
         if(Boolean(_arg_1))
         {
            if(!this._nameTxt)
            {
               this._nameTxt = ComponentFactory.Instance.creatComponentByStylename("beadSystem.beadCell.name");
               this._nameTxt.mouseEnabled = false;
               addChild(this._nameTxt);
            }
            this._nameTxt.text = BeadTemplateManager.Instance.GetBeadInfobyID(_arg_1.TemplateID).Name;
            this._nameTxt.visible = true;
            this.setChildIndex(this._nameTxt,this.numChildren - 1);
            tipStyle = "core.GoodsTip";
            _tipData = new GoodTipInfo();
            GoodTipInfo(_tipData).itemInfo = _info;
            if(this.itemInfo.Hole2 > 0)
            {
               GoodTipInfo(_tipData).exp = this.itemInfo.Hole2;
               GoodTipInfo(_tipData).upExp = ServerConfigManager.instance.getBeadUpgradeExp()[this.itemInfo.Hole1 + 1];
               GoodTipInfo(_tipData).beadName = this.itemInfo.Name + "-" + BeadTemplateManager.Instance.GetBeadInfobyID(_arg_1.TemplateID).Name + "Lv" + this.itemInfo.Hole1;
            }
            else
            {
               GoodTipInfo(_tipData).exp = ServerConfigManager.instance.getBeadUpgradeExp()[BeadTemplateManager.Instance.GetBeadInfobyID(this.itemInfo.TemplateID).BaseLevel];
               GoodTipInfo(_tipData).upExp = ServerConfigManager.instance.getBeadUpgradeExp()[BeadTemplateManager.Instance.GetBeadInfobyID(this.itemInfo.TemplateID).BaseLevel + 1];
               GoodTipInfo(_tipData).beadName = this.itemInfo.Name + "-" + BeadTemplateManager.Instance.GetBeadInfobyID(_arg_1.TemplateID).Name + "Lv" + BeadTemplateManager.Instance.GetBeadInfobyID(this.itemInfo.TemplateID).BaseLevel;
            }
            if(this.itemInfo.IsUsed)
            {
               if(Boolean(this._lockIcon))
               {
                  this._lockIcon.visible = true;
                  setChildIndex(this._lockIcon,numChildren - 1);
               }
               else
               {
                  this._lockIcon = ComponentFactory.Instance.creatBitmap("asset.beadSystem.beadInset.lockIcon1");
                  _local_3 = 0.7;
                  this._lockIcon.scaleY = _local_3;
                  this._lockIcon.scaleX = _local_3;
                  this.addChild(this._lockIcon);
               }
            }
            else if(Boolean(this._lockIcon))
            {
               this._lockIcon.visible = false;
            }
            if(this.beadPlace >= 32 && this.beadPlace <= 81)
            {
               _local_2 = 1;
            }
            else if(this.beadPlace >= 82 && this.beadPlace <= 131)
            {
               _local_2 = 2;
            }
            else if(this.beadPlace >= 132 && this.beadPlace <= 181)
            {
               _local_2 = 3;
            }
            dispatchEvent(new BeadEvent("beadCellChanged",_local_2));
         }
         else
         {
            if(Boolean(this._lockIcon))
            {
               this._lockIcon.visible = false;
            }
            if(Boolean(this._nameTxt))
            {
               this._nameTxt.visible = false;
            }
         }
      }
      
      override public function dragStart() : void
      {
         if(_info && !locked && stage && _allowDrag)
         {
            if(DragManager.startDrag(this,_info,createDragImg(),stage.mouseX + 10,stage.mouseY + 10,"move"))
            {
               this.locked = true;
               this.dragHidePicTxt();
               if(Boolean(_info) && _pic.numChildren > 0)
               {
                  dispatchEvent(new CellEvent("dragStart",this.info,true));
               }
            }
         }
      }
      
      private function dragHidePicTxt() : void
      {
         this._nameTxt.visible = false;
         if(Boolean(this._lockIcon))
         {
            this._lockIcon.visible = false;
         }
      }
      
      private function dragShowPicTxt() : void
      {
         this._nameTxt.visible = true;
         if(this.itemInfo.IsUsed && Boolean(this._lockIcon))
         {
            this._lockIcon.visible = true;
         }
      }
      
      override protected function initTip() : void
      {
         tipDirctions = "7,6,2,1,5,4,0,3,6";
         tipGapV = 0;
         tipGapH = 0;
      }
      
      override public function dispose() : void
      {
         if(Boolean(this._nameTxt))
         {
            ObjectUtils.disposeObject(this._nameTxt);
         }
         this._nameTxt = null;
         if(Boolean(this._lockIcon))
         {
            ObjectUtils.disposeObject(this._lockIcon);
         }
         this._lockIcon = null;
         if(Boolean(this._beadFeedMC))
         {
            ObjectUtils.disposeObject(this._beadFeedMC);
         }
         this._beadFeedMC = null;
         super.dispose();
      }
   }
}

