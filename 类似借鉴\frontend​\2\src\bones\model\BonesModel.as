package bones.model
{
   import road7th.data.DictionaryData;
   
   public class BonesModel
   {
      
      public static const BONES:String = "bones";
      
      public static const BONES_GAME:String = "gamebones";
      
      private var _parasBones:DictionaryData;
      
      private var _boneSets:DictionaryData;
      
      private var _dynamicBoneSets:DictionaryData;
      
      public function BonesModel()
      {
         super();
         this._boneSets = new DictionaryData();
         this._parasBones = new DictionaryData();
         this._dynamicBoneSets = new DictionaryData();
      }
      
      public function getBonesStyle(_arg_1:String) : BoneVo
      {
         return this._boneSets[_arg_1] || this._dynamicBoneSets[_arg_1];
      }
      
      public function hasLoadingBones(_arg_1:String) : Bo<PERSON>an
      {
         return this._parasBones.hasKey(_arg_1);
      }
      
      public function parasBonesStyle(_arg_1:XMLList, _arg_2:String) : void
      {
         var _local_6:int = 0;
         var _local_4:* = null;
         var _local_5:* = null;
         if(this.hasLoadingBones(_arg_2))
         {
            trace("该bones xml已经解析过了 type:" + _arg_2 + " ,请勿重复加载解析!!!!!!!!");
            return;
         }
         this._parasBones.add(_arg_2,true);
         var _local_3:int = int(_arg_1.length());
         _local_6 = 0;
         while(_local_6 < _local_3)
         {
            _local_4 = _arg_1[_local_6] as XML;
            _local_5 = new BoneVo();
            _local_5.styleName = _local_4.@styleName;
            _local_5.atlasName = _local_4.@atlasName;
            _local_5.path = _local_4.@path;
            if(Boolean(_local_4.hasOwnProperty("@loadType")))
            {
               _local_5.loadType = int(_local_4.@loadType);
            }
            if(Boolean(_local_4.hasOwnProperty("@boneType")))
            {
               _local_5.boneType = _local_4.@boneType;
            }
            if(Boolean(_local_4.hasOwnProperty("@ext")))
            {
               _local_5.ext = _local_4.@ext;
            }
            if(this._boneSets.hasKey(_local_5.styleName))
            {
               trace("bone name " + _local_5.styleName + "重名, 请注意检查!!!!!!!!!!!!!!!!!!!!");
            }
            else
            {
               this._boneSets.add(_local_5.styleName,_local_5);
            }
            _local_6++;
         }
      }
      
      public function getBoneVoListByAtlasName(_arg_1:String) : Array
      {
         var _local_5:int = 0;
         var _local_4:int = 0;
         var _local_2:Array = [];
         var _local_3:int = this._boneSets.length;
         _local_5 = 0;
         while(_local_5 < _local_3)
         {
            if(_arg_1 == this._boneSets.list[_local_5].atlasName)
            {
               _local_2.push(this._boneSets.list[_local_5]);
            }
            _local_5++;
         }
         if(_local_2.length == 0)
         {
            _local_3 = this._dynamicBoneSets.length;
            _local_4 = 0;
            while(_local_4 < _local_3)
            {
               if(_arg_1 == this._dynamicBoneSets.list[_local_4].atlasName)
               {
                  _local_2.push(this._dynamicBoneSets.list[_local_4]);
               }
               _local_4++;
            }
         }
         return _local_2;
      }
      
      public function addBoneVo(_arg_1:BoneVo) : void
      {
         this._dynamicBoneSets.add(_arg_1.styleName,_arg_1);
      }
      
      public function addBoneVoByStyle(_arg_1:String, _arg_2:String, _arg_3:int = 0, _arg_4:int = 2, _arg_5:String = "none") : void
      {
         var _local_6:BoneVo = new BoneVo();
         var _local_7:* = _arg_1;
         _local_6.atlasName = _local_7;
         _local_6.styleName = _local_7;
         _local_6.path = _arg_2;
         _local_6.loadType = _arg_3;
         _local_6.useType = _arg_4;
         _local_6.boneType = _arg_5;
         this.addBoneVo(_local_6);
      }
   }
}

