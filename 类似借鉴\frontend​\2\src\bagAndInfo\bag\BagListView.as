package bagAndInfo.bag
{
   import bagAndInfo.cell.BagCell;
   import bagAndInfo.cell.BaseCell;
   import bagAndInfo.cell.CellFactory;
   import com.pickgliss.events.InteractiveEvent;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.controls.container.SimpleTileList;
   import com.pickgliss.utils.DoubleClickManager;
   import ddt.data.BagInfo;
   import ddt.data.goods.InventoryItemInfo;
   import ddt.events.BagEvent;
   import ddt.events.CellEvent;
   import ddt.manager.SoundManager;
   import flash.display.Bitmap;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.utils.Dictionary;
   
   public class BagListView extends SimpleTileList
   {
      
      public static const BAG_CAPABILITY:int = 49;
      
      private var _allBagData:BagInfo;
      
      protected var _cellNum:int;
      
      protected var _bagdata:BagInfo;
      
      protected var _bagType:int;
      
      protected var _page:int;
      
      protected var _cells:Dictionary;
      
      protected var _cellMouseOverBg:Bitmap;
      
      protected var _cellVec:Array;
      
      private var _isSetFoodData:Boolean;
      
      private var _currentBagType:int;
      
      public function BagListView(_arg_1:int, _arg_2:int = 7, _arg_3:int = 49)
      {
         this._cellNum = _arg_3;
         this._bagType = _arg_1;
         super(_arg_2);
         _hSpace = _vSpace = 0;
         this._cellVec = [];
         this.createCells();
      }
      
      protected function createCells() : void
      {
         var _local_2:int = 0;
         var _local_1:* = null;
         this._cells = new Dictionary();
         this._cellMouseOverBg = ComponentFactory.Instance.creatBitmap("bagAndInfo.cell.bagCellOverBgAsset");
         _local_2 = 0;
         while(_local_2 < this._cellNum)
         {
            _local_1 = BagCell(CellFactory.instance.createBagCell(_local_2));
            _local_1.mouseOverEffBoolean = false;
            addChild(_local_1);
            _local_1.bagType = this._bagType;
            _local_1.addEventListener("interactive_click",this.__clickHandler);
            _local_1.addEventListener("mouseOver",this._cellOverEff);
            _local_1.addEventListener("mouseOut",this._cellOutEff);
            _local_1.addEventListener("interactive_double_click",this.__doubleClickHandler);
            DoubleClickManager.Instance.enableDoubleClick(_local_1);
            _local_1.addEventListener("lockChanged",this.__cellChanged);
            this._cells[_local_1.place] = _local_1;
            this._cellVec.push(_local_1);
            _local_2++;
         }
      }
      
      protected function __doubleClickHandler(_arg_1:InteractiveEvent) : void
      {
         if((_arg_1.currentTarget as BagCell).info != null)
         {
            SoundManager.instance.play("008");
            dispatchEvent(new CellEvent("doubleclick",_arg_1.currentTarget));
         }
      }
      
      protected function __cellChanged(_arg_1:Event) : void
      {
         dispatchEvent(new Event("change"));
      }
      
      protected function __clickHandler(_arg_1:InteractiveEvent) : void
      {
         if((_arg_1.currentTarget as BagCell).info != null)
         {
            dispatchEvent(new CellEvent("itemclick",_arg_1.currentTarget,false,false,_arg_1.ctrlKey));
         }
      }
      
      protected function _cellOverEff(_arg_1:MouseEvent) : void
      {
         BagCell(_arg_1.currentTarget).onParentMouseOver(this._cellMouseOverBg);
      }
      
      protected function _cellOutEff(_arg_1:MouseEvent) : void
      {
         BagCell(_arg_1.currentTarget).onParentMouseOut();
      }
      
      public function setCellInfo(_arg_1:int, _arg_2:InventoryItemInfo) : void
      {
         if(!this._cells[String(_arg_1)])
         {
            return;
         }
         if(_arg_2 == null)
         {
            this._cells[String(_arg_1)].info = null;
            return;
         }
         if(_arg_2.Count == 0)
         {
            this._cells[String(_arg_1)].info = null;
         }
         else
         {
            this._cells[String(_arg_1)].info = _arg_2;
         }
      }
      
      protected function clearDataCells() : void
      {
         var _local_1:BagCell = null;
         for each(_local_1 in this._cells)
         {
            _local_1.info = null;
         }
      }
      
      public function set currentBagType(_arg_1:int) : void
      {
         this._currentBagType = _arg_1;
      }
      
      public function setData(_arg_1:BagInfo) : void
      {
         var _local_3:String = null;
         this._isSetFoodData = false;
         if(_arg_1 == null)
         {
            return;
         }
         if(this._bagdata == _arg_1 && _arg_1.BagType != 0 && _arg_1.BagType != 1)
         {
            return;
         }
         if(this._bagdata != null)
         {
            this._bagdata.removeEventListener("update",this.__updateGoods);
         }
         this.clearDataCells();
         this._bagdata = _arg_1;
         var _local_2:Array = [];
         for(_local_3 in this._bagdata.items)
         {
            if(this._cells[_local_3] != null)
            {
               if(this._currentBagType == 5)
               {
                  if(this._bagdata.items[_local_3].CategoryID == 50 || this._bagdata.items[_local_3].CategoryID == 51 || this._bagdata.items[_local_3].CategoryID == 52)
                  {
                     this._bagdata.items[_local_3].isMoveSpace = true;
                     this._cells[_local_3].info = this._bagdata.items[_local_3];
                     _local_2.push(this._cells[_local_3]);
                  }
               }
               else
               {
                  this._bagdata.items[_local_3].isMoveSpace = true;
                  this._cells[_local_3].info = this._bagdata.items[_local_3];
               }
            }
         }
         this._bagdata.addEventListener("update",this.__updateGoods);
         if(this._currentBagType == 5)
         {
            this._cellsSort(_local_2);
         }
      }
      
      private function sortItems() : void
      {
         var _local_3:String = null;
         var _local_1:* = null;
         var _local_2:Array = [];
         for(_local_3 in this._bagdata.items)
         {
            _local_1 = this._bagdata.items[_local_3];
            if(this._cells[_local_3] != null && _local_1)
            {
               if(_local_1.CategoryID == 50 || _local_1.CategoryID == 51 || _local_1.CategoryID == 52)
               {
                  BaseCell(this._cells[_local_3]).info = _local_1;
                  _local_2.push(this._cells[_local_3]);
               }
            }
         }
         this._cellsSort(_local_2);
      }
      
      private function _cellsSort(_arg_1:Array) : void
      {
         var _local_6:int = 0;
         var _local_4:Number = NaN;
         var _local_5:Number = NaN;
         var _local_3:int = 0;
         var _local_2:* = null;
         if(_arg_1.length <= 0)
         {
            return;
         }
         _local_6 = 0;
         while(_local_6 < _arg_1.length)
         {
            _local_4 = Number(_arg_1[_local_6].x);
            _local_5 = Number(_arg_1[_local_6].y);
            _local_3 = int(this._cellVec.indexOf(_arg_1[_local_6]));
            _local_2 = this._cellVec[_local_6];
            _arg_1[_local_6].x = _local_2.x;
            _arg_1[_local_6].y = _local_2.y;
            _local_2.x = _local_4;
            _local_2.y = _local_5;
            this._cellVec[_local_6] = _arg_1[_local_6];
            this._cellVec[_local_3] = _local_2;
            _local_6++;
         }
      }
      
      protected function __updateFoodGoods(_arg_1:BagEvent) : void
      {
         var _local_3:int = 0;
         var _local_7:InventoryItemInfo = null;
         var _local_6:String = null;
         var _local_5:* = null;
         var _local_4:* = null;
         var _local_2:* = null;
         if(!this._bagdata)
         {
            return;
         }
         var _local_8:Dictionary = _arg_1.changedSlots;
         for each(_local_7 in _local_8)
         {
            _local_3 = -1;
            _local_5 = null;
            for(_local_6 in this._bagdata.items)
            {
               _local_4 = this._bagdata.items[_local_6] as InventoryItemInfo;
               if(_local_7.ItemID == _local_4.ItemID)
               {
                  _local_5 = _local_7;
                  _local_3 = int(_local_6);
                  break;
               }
            }
            if(_local_3 != -1)
            {
               _local_2 = this._bagdata.getItemAt(_local_3);
               if(_local_2)
               {
                  _local_2.Count = _local_5.Count;
                  if(Boolean(this._cells[String(_local_3)].info))
                  {
                     this.setCellInfo(_local_3,null);
                  }
                  else
                  {
                     this.setCellInfo(_local_3,_local_2);
                  }
               }
               else
               {
                  this.setCellInfo(_local_3,null);
               }
               dispatchEvent(new Event("change"));
            }
         }
      }
      
      protected function __updateGoods(_arg_1:BagEvent) : void
      {
         var _local_3:InventoryItemInfo = null;
         var _local_4:* = null;
         var _local_2:* = null;
         if(this._isSetFoodData)
         {
            this.__updateFoodGoods(_arg_1);
         }
         else
         {
            _local_4 = _arg_1.changedSlots;
            for each(_local_3 in _local_4)
            {
               _local_2 = this._bagdata.getItemAt(_local_3.Place);
               if(_local_2)
               {
                  if(this._currentBagType == 5)
                  {
                     if(_local_2.CategoryID != 50 && _local_2.CategoryID != 51 && _local_2.CategoryID != 52)
                     {
                        this.setCellInfo(_local_3.Place,null);
                        continue;
                     }
                  }
                  this.setCellInfo(_local_2.Place,_local_2);
               }
               else
               {
                  this.setCellInfo(_local_3.Place,null);
               }
               dispatchEvent(new Event("change"));
            }
         }
         if(this._currentBagType == 5)
         {
            this.sortItems();
         }
      }
      
      override public function dispose() : void
      {
         var _local_1:BagCell = null;
         if(this._bagdata != null)
         {
            this._bagdata.removeEventListener("update",this.__updateGoods);
            this._bagdata = null;
         }
         for each(_local_1 in this._cells)
         {
            _local_1.removeEventListener("interactive_click",this.__clickHandler);
            _local_1.removeEventListener("lockChanged",this.__cellChanged);
            _local_1.removeEventListener("mouseOver",this._cellOverEff);
            _local_1.removeEventListener("mouseOut",this._cellOutEff);
            _local_1.removeEventListener("interactive_double_click",this.__doubleClickHandler);
            DoubleClickManager.Instance.disableDoubleClick(_local_1);
            _local_1.dispose();
         }
         this._cells = null;
         this._cellVec = null;
         if(Boolean(this._cellMouseOverBg))
         {
            if(Boolean(this._cellMouseOverBg.parent))
            {
               this._cellMouseOverBg.parent.removeChild(this._cellMouseOverBg);
            }
            this._cellMouseOverBg.bitmapData.dispose();
         }
         this._cellMouseOverBg = null;
         super.dispose();
      }
      
      public function get cells() : Dictionary
      {
         return this._cells;
      }
      
      public function updateBankBag(_arg_1:int) : void
      {
      }
      
      public function checkBankCell() : int
      {
         return 0;
      }
   }
}

