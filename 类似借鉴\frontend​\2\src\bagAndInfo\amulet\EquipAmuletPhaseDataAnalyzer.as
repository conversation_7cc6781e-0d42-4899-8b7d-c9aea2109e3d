package bagAndInfo.amulet
{
   import bagAndInfo.amulet.vo.EquipAmuletPhaseVo;
   import com.pickgliss.loader.DataAnalyzer;
   import com.pickgliss.utils.ObjectUtils;
   import road7th.data.DictionaryData;
   
   public class EquipAmuletPhaseDataAnalyzer extends DataAnalyzer
   {
      
      private var _data:DictionaryData;
      
      public function EquipAmuletPhaseDataAnalyzer(_arg_1:Function)
      {
         super(_arg_1);
      }
      
      override public function analyze(_arg_1:*) : void
      {
         var _local_5:int = 0;
         var _local_4:* = null;
         var _local_2:* = null;
         this._data = new DictionaryData();
         var _local_3:XML = new XML(_arg_1);
         if(_local_3.@value == "true")
         {
            _local_4 = _local_3..Item;
            _local_5 = 0;
            while(_local_5 < _local_4.length())
            {
               _local_2 = new EquipAmuletPhaseVo();
               ObjectUtils.copyPorpertiesByXML(_local_2,_local_4[_local_5]);
               _local_2.grade = _local_4[_local_5].@AmuletLevel;
               this._data.add(_local_2.Phase,_local_2);
               _local_5++;
            }
            onAnalyzeComplete();
         }
         else
         {
            message = _local_3.@message;
            onAnalyzeError();
         }
         this._data = null;
      }
      
      public function get data() : DictionaryData
      {
         return this._data;
      }
   }
}

