package com.pickgliss.loader
{
   import com.pickgliss.toplevel.StageReferance;
   import flash.events.Event;
   import flash.utils.Timer;
   
   public class DataAnalyzer
   {
      
      protected var _onCompleteCall:Function;
      
      public var message:String;
      
      public var analyzeCompleteCall:Function;
      
      public var analyzeErrorCall:Function;
      
      protected var _analyzeCount:Timer;
      
      protected var _index:int;
      
      protected var _len:int;
      
      protected var __dataList:*;
      
      protected var _maxParseNum:int = 200;
      
      private var _maxParseByteNum:int = 1000;
      
      public function DataAnalyzer(_arg_1:Function)
      {
         super();
         this._onCompleteCall = _arg_1;
      }
      
      public function analyze(_arg_1:*) : void
      {
      }
      
      public function analyzeWait(_arg_1:*) : void
      {
         if(this.__dataList)
         {
            this._len = this.__dataList is XMLList ? int(this.__dataList.length()) : int(this.__dataList.length);
            this._index = 0;
            StageReferance.stage.addEventListener("enterFrame",this.partExceute);
         }
      }
      
      public function analyzeByteWait(_arg_1:int, _arg_2:int = 1000) : void
      {
         this._maxParseByteNum = _arg_2;
         this._len = _arg_1;
         if(Boolean(this._len))
         {
            this._index = 0;
            StageReferance.stage.addEventListener("enterFrame",this.partByteExceute);
            return;
         }
         this.onAnalyzeComplete();
      }
      
      protected function partByteExceute(_arg_1:Event) : void
      {
         var _local_2:int = 0;
         _local_2 = 0;
         while(_local_2 < this._maxParseByteNum)
         {
            if(this._index >= this._len)
            {
               this.onAnalyzeComplete();
               return;
            }
            this.analyzeItem(this._index);
            ++this._index;
            _local_2++;
         }
      }
      
      protected function partExceute(_arg_1:Event) : void
      {
         var _local_2:int = 0;
         _local_2 = 0;
         while(_local_2 < this._maxParseNum)
         {
            if(this._index >= this._len)
            {
               this.onAnalyzeComplete();
               return;
            }
            this.analyzeItem(this.__dataList[this._index]);
            ++this._index;
            _local_2++;
         }
      }
      
      protected function analyzeItem(_arg_1:*) : void
      {
      }
      
      protected function onAnalyzeComplete() : void
      {
         if(this._onCompleteCall != null)
         {
            this._onCompleteCall(this);
         }
         if(this.analyzeCompleteCall != null)
         {
            this.analyzeCompleteCall();
         }
         this._onCompleteCall = null;
         this.analyzeCompleteCall = null;
         StageReferance.stage.removeEventListener("enterFrame",this.partExceute);
         StageReferance.stage.removeEventListener("enterFrame",this.partByteExceute);
         this.__dataList = null;
      }
      
      protected function onAnalyzeError() : void
      {
         if(this.analyzeErrorCall != null)
         {
            this.analyzeErrorCall();
         }
         StageReferance.stage.removeEventListener("enterFrame",this.partExceute);
         this.__dataList = null;
      }
   }
}

