package AvatarCollection.data
{
   import com.pickgliss.loader.DataAnalyzer;
   import road7th.data.DictionaryData;
   
   public class AvatarCollPetTalentSuitDataAnalyze extends DataAnalyzer
   {
      
      private var _suitDic:DictionaryData;
      
      public function AvatarCollPetTalentSuitDataAnalyze(_arg_1:Function)
      {
         super(_arg_1);
      }
      
      override public function analyze(_arg_1:*) : void
      {
         var index:int = 0;
         var listInfo:* = undefined;
         var _local_1:AvatarCollPetTalentSuitVo = null;
         var _local_3:String = null;
         var _local_2:XML = new XML(_arg_1);
         this._suitDic = new DictionaryData();
         if(_local_2.@value == "true")
         {
            listInfo = _local_2..Item;
            index = 0;
            while(index < listInfo.length())
            {
               _local_1 = new AvatarCollPetTalentSuitVo();
               _local_1.GroupID = listInfo[index].@GroupID;
               _local_1.Name = listInfo[index].@Name;
               _local_3 = listInfo[index].@SuitElement;
               _local_1.SuitElement = _local_3.split(",");
               _local_1.Skill1 = listInfo[index].@Skill1;
               _local_1.Skill2 = listInfo[index].@Skill2;
               _local_1.Count1 = listInfo[index].@Count1;
               _local_1.Count2 = listInfo[index].@Count2;
               this._suitDic.add(_local_1.GroupID,_local_1);
               index++;
            }
            onAnalyzeComplete();
         }
         else
         {
            message = _local_2.@message;
            onAnalyzeError();
         }
      }
      
      public function get suitDic() : DictionaryData
      {
         return this._suitDic;
      }
   }
}

