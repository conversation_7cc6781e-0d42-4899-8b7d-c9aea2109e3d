package com.pickgliss.ui.controls
{
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.text.FilterFrameText;
   import flash.display.Sprite;
   
   public class FTextButton extends Sprite
   {
      
      private var _btn:SimpleBitmapButton;
      
      private var _txt:FilterFrameText;
      
      public var id:int;
      
      public function FTextButton(_arg_1:String, _arg_2:String)
      {
         super();
         this._btn = ComponentFactory.Instance.creatComponentByStylename(_arg_1);
         addChild(this._btn);
         this._txt = ComponentFactory.Instance.creatComponentByStylename(_arg_2);
         this._txt.text = "前去充值";
         this._txt.selectable = false;
         this._txt.width = this._btn.width;
         this._txt.height = this._btn.height;
         addChild(this._txt);
      }
      
      public function setTxt(_arg_1:String) : void
      {
         this._txt.text = _arg_1;
      }
   }
}

