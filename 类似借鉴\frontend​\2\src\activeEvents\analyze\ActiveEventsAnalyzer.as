package activeEvents.analyze
{
   import activeEvents.data.ActiveEventsInfo;
   import com.pickgliss.loader.DataAnalyzer;
   import com.pickgliss.utils.ObjectUtils;
   
   public class ActiveEventsAnalyzer extends DataAnalyzer
   {
      
      private var _list:Array;
      
      private var _xml:XML;
      
      public function ActiveEventsAnalyzer(_arg_1:Function)
      {
         super(_arg_1);
      }
      
      override public function analyze(_arg_1:*) : void
      {
         var _local_4:int = 0;
         var _local_3:* = null;
         this._xml = new XML(_arg_1);
         this._list = [];
         var _local_2:XMLList = this._xml..Item;
         if(this._xml.@value == "true")
         {
            _local_4 = 0;
            while(_local_4 < _local_2.length())
            {
               _local_3 = new ActiveEventsInfo();
               ObjectUtils.copyPorpertiesByXML(_local_3,_local_2[_local_4]);
               this._list.push(_local_3);
               _local_4++;
            }
            onAnalyzeComplete();
         }
         else
         {
            message = this._xml.@message;
            onAnalyzeError();
            onAnalyzeComplete();
         }
      }
      
      public function get list() : Array
      {
         return this._list.slice(0);
      }
   }
}

