package cityBattle.view
{
   import baglocked.BaglockedManager;
   import com.pickgliss.events.FrameEvent;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.controls.Frame;
   import com.pickgliss.ui.controls.SelectedButtonGroup;
   import com.pickgliss.ui.controls.TextButton;
   import com.pickgliss.ui.controls.container.VBox;
   import com.pickgliss.ui.text.FilterFrameText;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.manager.LanguageMgr;
   import ddt.manager.LeavePageManager;
   import ddt.manager.PlayerManager;
   import ddt.manager.ServerConfigManager;
   import ddt.manager.SocketManager;
   import ddt.manager.SoundManager;
   import flash.events.MouseEvent;
   
   public class ContentionInspireFrame extends Frame
   {
      
      private var _inspireInfo:FilterFrameText;
      
      private var _vBox:VBox;
      
      private var _itemGroup:SelectedButtonGroup;
      
      private var _submitButton:TextButton;
      
      private var _moneyArray:Array = [];
      
      public function ContentionInspireFrame()
      {
         super();
         this.initView();
         this.initEvent();
      }
      
      private function initView() : void
      {
         var _local_4:int = 0;
         var _local_2:* = null;
         var _local_3:* = null;
         titleText = LanguageMgr.GetTranslation("ddt.cityBattle.inspireTitle");
         this._inspireInfo = ComponentFactory.Instance.creatComponentByStylename("contention.inspireInfo.txt");
         addToContent(this._inspireInfo);
         this._inspireInfo.text = LanguageMgr.GetTranslation("ddt.cityBattle.inspireInfo");
         this._vBox = ComponentFactory.Instance.creatComponentByStylename("cityBattle.inspireFrame.vBox");
         addToContent(this._vBox);
         this._itemGroup = new SelectedButtonGroup();
         var _local_1:Array = ["","","(9折)","(4折)","(2.8折)"];
         _local_4 = 1;
         while(_local_4 <= ServerConfigManager.instance.cityOccupationAddPrice.length)
         {
            _local_2 = ServerConfigManager.instance.cityOccupationAddPrice[_local_4 - 1].split(",");
            _local_3 = ComponentFactory.Instance.creatComponentByStylename("contention.inspireSelected.btn");
            _local_3.field.htmlText = LanguageMgr.GetTranslation("ddt.cityBattle.inspireSelected.info",_local_2[0],_local_2[1],_local_1[_local_4]);
            this._moneyArray.push(_local_2[1]);
            this._itemGroup.addSelectItem(_local_3);
            this._vBox.addChild(_local_3);
            _local_4++;
         }
         this._itemGroup.selectIndex = 0;
         this._submitButton = ComponentFactory.Instance.creatComponentByStylename("contention.inspireEnter");
         this._submitButton.text = LanguageMgr.GetTranslation("ddt.cityBattle.inspireSubmit");
         addToContent(this._submitButton);
      }
      
      private function initEvent() : void
      {
         this._submitButton.addEventListener("click",this.__buy);
         addEventListener("response",this._responseHandle);
      }
      
      private function removeEvent() : void
      {
         removeEventListener("response",this._responseHandle);
         this._submitButton.removeEventListener("click",this.__buy);
      }
      
      protected function _responseHandle(_arg_1:FrameEvent) : void
      {
         SoundManager.instance.play("008");
         switch(_arg_1.responseCode)
         {
            case 0:
            case 1:
            case 4:
               this.dispose();
         }
      }
      
      private function __buy(_arg_1:MouseEvent) : void
      {
         if(PlayerManager.Instance.Self.bagLocked)
         {
            BaglockedManager.Instance.show();
            return;
         }
         if(PlayerManager.Instance.Self.Money < this._moneyArray[this._itemGroup.selectIndex])
         {
            LeavePageManager.showFillFrame();
            return;
         }
         SocketManager.Instance.out.inspire(this._itemGroup.selectIndex + 1);
      }
      
      override public function dispose() : void
      {
         this.removeEvent();
         ObjectUtils.disposeObject(this._itemGroup);
         this._itemGroup = null;
         ObjectUtils.disposeObject(this._inspireInfo);
         this._inspireInfo = null;
         ObjectUtils.disposeObject(this._vBox);
         this._vBox = null;
         ObjectUtils.disposeObject(this._submitButton);
         this._submitButton = null;
         super.dispose();
      }
   }
}

