using System;
using System.Collections.Generic;
using System.Linq;
using Bussiness;
using EntityDatabase.PlayerModels;
using EntityDatabase.ServerModels;
using Game.Base.Packets;
using Game.Server.ForceRelic;
using Game.Server.GameObjects;
using Game.Server.Packets.Client;
using SqlDataProvider.Data;

namespace Game.Server.PairBox
{
	// Token: 0x02000C43 RID: 3139
	[PacketHandler(768, "圣物")]
	public class ForceRelicHandler : IPacketHandler
	{
		// Token: 0x0600583C RID: 22588 RVA: 0x0023EB34 File Offset: 0x0023CD34
		public int HandlePacket(GameClient client, GSPacketIn packet)
		{
			switch (packet.ReadByte())
			{
			case 1:
				this.SendBaseInfo(client.Player);
				break;
			case 2:
				ForceRelicHandler.SendRelicInfo(client.Player);
				break;
			case 3:
			{
				int num = packet.ReadInt();
				int num2 = packet.ReadInt();
				int num3 = packet.ReadInt();
				this.UpdateRelicLevel(client.Player, num, num2, num3);
				break;
			}
			case 4:
			{
				int num4 = packet.ReadInt();
				int num5 = packet.ReadInt();
				int num6 = packet.ReadInt();
				this.UpdateRelicStage(client.Player, num4, num6, num5);
				break;
			}
			case 5:
				this.SendManualInfo(client.Player);
				break;
			case 6:
			{
				int num7 = packet.ReadInt();
				int num8 = packet.ReadInt();
				int num9 = packet.ReadInt();
				this.updateRelicProArr(client.Player, num7, num8, num9);
				break;
			}
			case 7:
			{
				int num10 = packet.ReadInt();
				int num11 = packet.ReadInt();
				this.updateRelicManualInfo(client.Player, num10, num11);
				break;
			}
			case 8:
			{
				int num12 = packet.ReadInt();
				int num13 = packet.ReadInt();
				int num14 = packet.ReadInt();
				this.UpdateRelicEquipInfo(client.Player, num12, num13, num14);
				break;
			}
			}
			return 1;
		}

		// Token: 0x0600583D RID: 22589 RVA: 0x0023EC90 File Offset: 0x0023CE90
		private void updateRelicManualInfo(GamePlayer player, int type, int level)
		{
			string[] array = player.PlayerCharacter.RelicInfo.ManualInfo.Split(new char[] { '|' });
			string[] array2 = array[type].Split(new char[] { ',' });
			TS_Relic_DegreeTemplate degreeInfo = ForceRelicMgr.GetDegreeInfo(type, level);
			TS_Relic_DegreeTemplate degreeInfo2 = ForceRelicMgr.GetDegreeInfo(type, level - 1);
			bool flag = level > 1;
			int num;
			if (flag)
			{
				int exp = degreeInfo2.Exp;
				int exp2 = degreeInfo.Exp;
				num = exp2 - exp;
			}
			else
			{
				num = degreeInfo.Exp;
			}
			bool flag2 = int.Parse(array2[1]) < num;
			if (flag2)
			{
				player.SendMessage("经验值不足");
			}
			else
			{
				array2[2] = (int.Parse(array[type].Split(new char[] { ',' })[2]) + 1).ToString();
				array2[1] = (int.Parse(array[type].Split(new char[] { ',' })[1]) - num).ToString();
				array[type] = string.Join(",", array2);
				player.PlayerCharacter.RelicInfo.ManualInfo = string.Join("|", array);
				this.SendManualInfo(player);
			}
		}

		// Token: 0x0600583E RID: 22590 RVA: 0x0023EDC0 File Offset: 0x0023CFC0
		private void updateRelicProArr(GamePlayer player, int relicID, int index, int type)
		{
			Random random = new Random();
			Sys_Users_RelicEquipInfo sys_Users_RelicEquipInfo = player.PlayerCharacter.RelicEquipInfo.FirstOrDefault((Sys_Users_RelicEquipInfo p) => p.RelicID == relicID);
			TS_Relic_ItemTemplate relicTemplateByRelicID = ForceRelicMgr.GetRelicTemplateByRelicID(relicID);
			Sys_Users_RelicInfo relicInfo = player.PlayerCharacter.RelicInfo;
			Sys_Users_RelicEquipInfo sys_Users_RelicEquipInfo2 = player.PlayerCharacter.RelicEquipInfo.FirstOrDefault((Sys_Users_RelicEquipInfo p) => p.RelicID == relicID);
			bool flag = relicTemplateByRelicID == null || sys_Users_RelicEquipInfo2 == null;
			if (flag)
			{
				player.SendMessage("数据异常");
			}
			else
			{
				bool flag2 = sys_Users_RelicEquipInfo == null;
				if (!flag2)
				{
					TS_Relic_AdvanceTemplate advanceInfo = ForceRelicMgr.GetAdvanceInfo(relicID, sys_Users_RelicEquipInfo.Stage);
					bool flag3 = advanceInfo == null;
					if (!flag3)
					{
						string text = GameProperties.RelicBuffSubstatPrice.Split(new char[] { '|' })[relicTemplateByRelicID.Quality - 1].Split(new char[] { ',' })[1];
						bool flag4 = text == null;
						if (!flag4)
						{
							bool flag5 = sys_Users_RelicEquipInfo.ShardNum >= int.Parse(text);
							if (flag5)
							{
								sys_Users_RelicEquipInfo.ShardNum -= int.Parse(text);
							}
							else
							{
								bool flag6 = relicTemplateByRelicID.Quality <= 3;
								if (flag6)
								{
									bool flag7 = relicInfo.RelicNorMalCount <= int.Parse(text);
									if (flag7)
									{
										player.SendMessage("普通碎片不足");
										return;
									}
									player.PlayerCharacter.RelicInfo.RelicNorMalCount -= int.Parse(text);
								}
								else
								{
									bool flag8 = relicInfo.RelicRareCount <= int.Parse(text);
									if (flag8)
									{
										player.SendMessage("稀有碎片不足");
										return;
									}
									player.PlayerCharacter.RelicInfo.RelicRareCount -= int.Parse(text);
								}
							}
							string text2 = advanceInfo.Skill.Split(new char[] { ',' })[1].Split(new char[] { '-' })[1];
							string[] array = sys_Users_RelicEquipInfo.ProArr.Split(new char[] { '|' });
							string text3 = random.Next(Convert.ToInt32(array[index - 1].Split(new char[] { ',' })[1]), Convert.ToInt32(text2) + 1).ToString();
							array[index - 1] = string.Concat(new string[]
							{
								array[index - 1].Split(new char[] { ',' })[0],
								",",
								text3,
								",",
								0.ToString()
							});
							sys_Users_RelicEquipInfo.ProArr = string.Join("|", array);
							ForceRelicHandler.SendRelicInfo(player);
						}
					}
				}
			}
		}

		// Token: 0x0600583F RID: 22591 RVA: 0x0023F098 File Offset: 0x0023D298
		private void UpdateRelicStage(GamePlayer player, int relicID, int type, int level)
		{
			TS_Relic_AdvanceTemplate advanceInfo = ForceRelicMgr.GetAdvanceInfo(relicID, level);
			Sys_Users_RelicEquipInfo sys_Users_RelicEquipInfo = player.PlayerCharacter.RelicEquipInfo.FirstOrDefault((Sys_Users_RelicEquipInfo p) => p.RelicID == relicID);
			Sys_Users_RelicInfo relicInfo = player.PlayerCharacter.RelicInfo;
			TS_Relic_ItemTemplate relicTemplateByRelicID = ForceRelicMgr.GetRelicTemplateByRelicID(relicID);
			bool flag = advanceInfo == null;
			if (flag)
			{
				player.SendMessage("数据异常");
			}
			else
			{
				bool flag2 = relicTemplateByRelicID == null;
				if (flag2)
				{
					player.SendMessage("数据异常");
				}
				else
				{
					bool flag3 = sys_Users_RelicEquipInfo == null;
					if (flag3)
					{
						player.SendMessage("无此道具");
					}
					else
					{
						string text = advanceInfo.Skill.Split(new char[] { ',' })[0];
						string[] array = advanceInfo.Skill.Split(new char[] { ',' })[1].Split(new char[] { '-' });
						bool flag4 = text == null || array == null;
						if (flag4)
						{
							player.SendMessage("数据异常");
						}
						else
						{
							string[] array2 = advanceInfo.ItemCost.Split(new char[] { '|' });
							bool flag5 = type == int.Parse(array2[0].Split(new char[] { ',' })[0]);
							if (flag5)
							{
								bool flag6 = sys_Users_RelicEquipInfo.ShardNum <= int.Parse(array2[0].Split(new char[] { ',' })[1]);
								if (flag6)
								{
									player.SendMessage("该装备碎片不足");
									return;
								}
								sys_Users_RelicEquipInfo.ShardNum -= int.Parse(array2[0].Split(new char[] { ',' })[1]);
							}
							else
							{
								bool flag7 = type == int.Parse(array2[1].Split(new char[] { ',' })[0]);
								if (!flag7)
								{
									return;
								}
								bool flag8 = type == 2;
								if (flag8)
								{
									bool flag9 = relicInfo.RelicNorMalCount <= int.Parse(array2[1].Split(new char[] { ',' })[1]);
									if (flag9)
									{
										player.SendMessage("普通碎片不足");
										return;
									}
									player.PlayerCharacter.RelicInfo.RelicNorMalCount -= int.Parse(array2[1].Split(new char[] { ',' })[1]);
								}
								bool flag10 = type == 3;
								if (flag10)
								{
									bool flag11 = relicInfo.RelicRareCount <= int.Parse(array2[1].Split(new char[] { ',' })[1]);
									if (flag11)
									{
										player.SendMessage("稀有碎片不足");
										return;
									}
									player.PlayerCharacter.RelicInfo.RelicRareCount -= int.Parse(array2[1].Split(new char[] { ',' })[1]);
								}
							}
							Random random = new Random();
							int num = random.Next(int.Parse(array[0]), int.Parse(array[1]));
							Sys_Users_RelicEquipInfo sys_Users_RelicEquipInfo2 = sys_Users_RelicEquipInfo;
							int stage = sys_Users_RelicEquipInfo2.Stage;
							sys_Users_RelicEquipInfo2.Stage = stage + 1;
							sys_Users_RelicEquipInfo.Exp = 0;
							sys_Users_RelicEquipInfo.Level = 0;
							string[] array3 = sys_Users_RelicEquipInfo.ProArr.Split(new char[] { '|' });
							array3[sys_Users_RelicEquipInfo.Stage - 1] = string.Concat(new string[]
							{
								text,
								",",
								num.ToString(),
								",",
								0.ToString()
							});
							sys_Users_RelicEquipInfo.ProArr = string.Join("|", array3);
							ForceRelicHandler.SendRelicInfo(player);
						}
					}
				}
			}
		}

		// Token: 0x06005840 RID: 22592 RVA: 0x0023F444 File Offset: 0x0023D644
		private void UpdateRelicLevel(GamePlayer player, int relicID, int templateID, int count)
		{
			Sys_Users_RelicEquipInfo sys_Users_RelicEquipInfo = player.PlayerCharacter.RelicEquipInfo.FirstOrDefault((Sys_Users_RelicEquipInfo p) => p.RelicID == relicID);
			ItemInfo itemByTemplateID = player.GetItemByTemplateID(templateID);
			int num = int.Parse(GameProperties.RelicUpgradeItem.Split(new char[] { ',' })[1]);
			bool flag = itemByTemplateID == null;
			if (flag)
			{
				player.SendMessage("材料不存在");
			}
			else
			{
				TS_Relic_UpgradeTemplate upgradeInfo = ForceRelicMgr.GetUpgradeInfo(relicID, sys_Users_RelicEquipInfo.Level + 1);
				bool flag2 = upgradeInfo == null;
				if (flag2)
				{
					player.SendMessage("数据异常");
				}
				else
				{
					int num2 = (upgradeInfo.NeedExp - sys_Users_RelicEquipInfo.Exp) / num;
					bool flag3 = itemByTemplateID.Count < count;
					if (flag3)
					{
						player.SendMessage("材料不足");
					}
					else
					{
						bool flag4 = num2 > count;
						if (flag4)
						{
							sys_Users_RelicEquipInfo.Exp += count * 50;
						}
						else
						{
							Sys_Users_RelicEquipInfo sys_Users_RelicEquipInfo2 = sys_Users_RelicEquipInfo;
							int level = sys_Users_RelicEquipInfo2.Level;
							sys_Users_RelicEquipInfo2.Level = level + 1;
							sys_Users_RelicEquipInfo.Exp = 0;
						}
						player.RemoveTemplate(templateID, count);
						ForceRelicHandler.SendRelicInfo(player);
					}
				}
			}
		}

		// Token: 0x06005841 RID: 22593 RVA: 0x0023F574 File Offset: 0x0023D774
		private void UpdateRelicEquipInfo(GamePlayer player, int state, int index, int relicID)
		{
			int num = 0;
			int num2 = 0;
			Sys_Users_RelicEquipInfo sys_Users_RelicEquipInfo = player.PlayerCharacter.RelicEquipInfo.FirstOrDefault((Sys_Users_RelicEquipInfo p) => p.RelicID == relicID);
			bool flag = sys_Users_RelicEquipInfo == null;
			if (!flag)
			{
				string[] array = player.PlayerCharacter.RelicInfo.RelicHoleArr.Split(new char[] { ',' });
				bool flag2 = state == 1;
				if (flag2)
				{
					array[index - 1] = relicID.ToString();
				}
				else
				{
					array[index - 1] = "0";
				}
				foreach (string text in array)
				{
					TS_Relic_ItemTemplate relicTemplateByRelicID = ForceRelicMgr.GetRelicTemplateByRelicID(int.Parse(text));
					bool flag3 = relicTemplateByRelicID != null;
					if (flag3)
					{
						bool flag4 = relicTemplateByRelicID.Type == 1;
						if (flag4)
						{
							num++;
						}
						else
						{
							num2++;
						}
					}
				}
				string[] array3 = player.PlayerCharacter.RelicInfo.RelicSuitArr.Split(new char[] { '|' });
				bool flag5 = num >= 3;
				if (flag5)
				{
					array3[0] = "1,30";
				}
				else
				{
					array3[0] = "0,0";
				}
				bool flag6 = num2 >= 3;
				if (flag6)
				{
					array3[1] = "2,30";
				}
				else
				{
					array3[1] = "0,0";
				}
				player.PlayerCharacter.RelicInfo.RelicHoleArr = "";
				player.PlayerCharacter.RelicInfo.RelicHoleArr = string.Join(",", array);
				player.PlayerCharacter.RelicInfo.RelicSuitArr = string.Join("|", array3);
				player.EquipBag.UpdatePlayerProperties();
				this.SendBaseInfo(player);
			}
		}

		// Token: 0x06005842 RID: 22594 RVA: 0x0023F73C File Offset: 0x0023D93C
		public void SendBaseInfo(GamePlayer player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(768);
			gspacketIn.WriteByte(1);
			gspacketIn.WriteInt(player.PlayerCharacter.RelicInfo.RelicScore);
			gspacketIn.WriteInt(player.PlayerCharacter.RelicInfo.RelicHoleIndex);
			string[] array = (from s in player.PlayerCharacter.RelicInfo.RelicHoleArr.Split(new char[] { ',' })
				where !string.IsNullOrEmpty(s)
				select s).ToArray<string>();
			gspacketIn.WriteInt(array.Length);
			foreach (string text in array)
			{
				gspacketIn.WriteInt(int.Parse(text));
			}
			string[] array3 = (from s in player.PlayerCharacter.RelicInfo.RelicSuitArr.Split(new char[] { '|' })
				where !string.IsNullOrEmpty(s)
				select s).ToArray<string>();
			gspacketIn.WriteInt(array3.Length);
			foreach (string text2 in array3)
			{
				gspacketIn.WriteInt(int.Parse(text2.Split(new char[] { ',' })[0]));
				gspacketIn.WriteInt(int.Parse(text2.Split(new char[] { ',' })[1]));
			}
			player.SendTCP(gspacketIn);
		}

		// Token: 0x06005843 RID: 22595 RVA: 0x0023F8C8 File Offset: 0x0023DAC8
		public static void SendRelicInfo(GamePlayer player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(768);
			gspacketIn.WriteByte(2);
			gspacketIn.WriteBoolean(true);
			gspacketIn.WriteInt(player.PlayerCharacter.RelicInfo.RelicNorMalCount);
			gspacketIn.WriteInt(player.PlayerCharacter.RelicInfo.RelicRareCount);
			List<Sys_Users_RelicEquipInfo> relicEquipInfo = player.PlayerCharacter.RelicEquipInfo;
			bool flag = relicEquipInfo == null;
			if (!flag)
			{
				gspacketIn.WriteInt(relicEquipInfo.Count);
				foreach (Sys_Users_RelicEquipInfo sys_Users_RelicEquipInfo in relicEquipInfo)
				{
					gspacketIn.WriteInt(sys_Users_RelicEquipInfo.RelicID);
					gspacketIn.WriteInt(sys_Users_RelicEquipInfo.Level);
					gspacketIn.WriteInt(sys_Users_RelicEquipInfo.Stage);
					gspacketIn.WriteInt(sys_Users_RelicEquipInfo.Exp);
					gspacketIn.WriteInt(sys_Users_RelicEquipInfo.ShardNum);
					string proArr = sys_Users_RelicEquipInfo.ProArr;
					List<string> list = (from s in proArr.Split(new char[] { '|' })
						where !string.IsNullOrEmpty(s)
						select s).ToList<string>();
					gspacketIn.WriteInt(list.Count);
					foreach (string text in list)
					{
						gspacketIn.WriteInt(0);
						gspacketIn.WriteInt(int.Parse(text.Split(new char[] { ',' })[0]));
						gspacketIn.WriteInt(int.Parse(text.Split(new char[] { ',' })[1]));
						gspacketIn.WriteInt(int.Parse(text.Split(new char[] { ',' })[2]));
					}
				}
				player.SendTCP(gspacketIn);
			}
		}

		// Token: 0x06005844 RID: 22596 RVA: 0x0023FAEC File Offset: 0x0023DCEC
		public void SendManualInfo(GamePlayer player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(768);
			gspacketIn.WriteByte(5);
			string manualInfo = player.PlayerCharacter.RelicInfo.ManualInfo;
			bool flag = manualInfo == null;
			if (!flag)
			{
				string[] array = (from s in manualInfo.Split(new char[] { '|' })
					where !string.IsNullOrEmpty(s)
					select s).ToArray<string>();
				gspacketIn.WriteInt(array.Length);
				foreach (string text in array)
				{
					gspacketIn.WriteInt(int.Parse(text.Split(new char[] { ',' })[0]));
					gspacketIn.WriteInt(int.Parse(text.Split(new char[] { ',' })[1]));
					gspacketIn.WriteInt(int.Parse(text.Split(new char[] { ',' })[2]));
				}
				player.SendTCP(gspacketIn);
			}
		}
	}
}
