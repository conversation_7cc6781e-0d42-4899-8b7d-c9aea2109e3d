package bagAndInfo
{
   import bagAndInfo.amulet.EquipAmuletManager;
   import bagAndInfo.info.PlayerInfoViewControl;
   import ddt.data.EquipType;
   import ddt.data.Experience;
   import ddt.data.goods.InventoryItemInfo;
   import ddt.data.player.PlayerInfo;
   import ddt.manager.PlayerManager;
   import ddt.utils.StaticFormula;
   import explorerManual.data.model.PlayerManualProInfo;
   import store.equipGhost.EquipGhostManager;
   import store.equipGhost.data.GhostPropertyData;
   
   public class PlayerProValueAddManager
   {
      
      public function PlayerProValueAddManager()
      {
         super();
      }
      
      public static function equipAddProValue(_arg_1:String, _arg_2:PlayerInfo) : int
      {
         var _local_5:int = 0;
         var _local_4:int = 0;
         var _local_3:* = null;
         _local_5 = 0;
         while(_local_5 < 19)
         {
            _local_3 = _arg_2.Bag.getItemAt(_local_5);
            if(_local_3 && Boolean(_local_3.hasOwnProperty(_arg_1)))
            {
               _local_4 += _local_3[_arg_1];
            }
            if(_local_3 && Boolean(_local_3.hasOwnProperty(_arg_1 + "Compose")))
            {
               _local_4 += _local_3[_arg_1 + "Compose"];
            }
            if(_local_3 && EquipType.isArm(_local_3) && _arg_1 == "AddDamage")
            {
               _local_4 += strengthenAddDamageProValue(_local_3) + getGhostPropertyData(_local_3);
            }
            if(_local_3 && (EquipType.isHead(_local_3) || EquipType.isCloth(_local_3)) && _arg_1 == "Arm")
            {
               _local_4 += strengthenAddDamageProValue(_local_3) + getGhostPropertyData(_local_3);
            }
            _local_5++;
         }
         return _local_4;
      }
      
      private static function getGhostPropertyData(_arg_1:InventoryItemInfo) : int
      {
         var _local_5:GhostPropertyData = null;
         var _local_3:InventoryItemInfo = _arg_1 as InventoryItemInfo;
         if(_local_3 == null)
         {
            return 0;
         }
         var _local_4:PlayerInfo = PlayerInfoViewControl.currentPlayer || PlayerManager.Instance.Self;
         var _local_6:* = _local_4.ID != PlayerManager.Instance.Self.ID;
         var _local_2:Boolean = _local_3.BagType == 0 && _local_3.Place <= 30;
         if(_local_6)
         {
            _local_5 = _local_2 ? EquipGhostManager.getInstance().getPorpertyData(_local_3,_local_4) : null;
         }
         else
         {
            _local_5 = _local_3.fromBag && (_local_2 || EquipGhostManager.getInstance().isEquipGhosting()) ? EquipGhostManager.getInstance().getPorpertyData(_local_3,_local_4) : null;
         }
         return Boolean(_local_5) ? int(_local_5.mainProperty) : 0;
      }
      
      public static function strengthenAddDamageProValue(_arg_1:InventoryItemInfo) : int
      {
         var _local_2:int = _arg_1.isGold ? _arg_1.StrengthenLevel + 1 : _arg_1.StrengthenLevel;
         return StaticFormula.getHertAddition(int(_arg_1.Property7),_local_2);
      }
      
      public static function equipAddHPValue(_arg_1:PlayerInfo) : int
      {
         var _local_4:int = 0;
         var _local_6:int = 0;
         var _local_5:int = 0;
         var _local_3:InventoryItemInfo = _arg_1.Bag.getItemAt(18);
         if(Boolean(_local_3) && _local_3.CanUse)
         {
            _local_6 = EquipAmuletManager.Instance.getAmuletHpByGrade(_local_3.StrengthenLevel);
            _local_4 += _local_6;
         }
         var _local_2:InventoryItemInfo = _arg_1.Bag.getItemAt(12);
         if(Boolean(_local_2))
         {
            _local_5 = int(int(Experience.getBasicHP(_arg_1.Grade) * int(_local_2.Property1) / 100));
            _local_4 += _local_5;
         }
         return _local_4;
      }
      
      public static function manualAddProValue(_arg_1:String, _arg_2:PlayerInfo) : int
      {
         var _local_3:int = 0;
         var _local_4:PlayerManualProInfo = _arg_2.manualProInfo;
         if(Boolean(_local_4) && Boolean(_local_4.hasOwnProperty(_arg_1)))
         {
            _local_3 = int(_local_4[_arg_1]);
         }
         return _local_3;
      }
   }
}

