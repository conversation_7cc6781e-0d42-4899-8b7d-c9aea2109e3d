package collectionTask.model
{
   import collectionTask.vo.CollectionRobertVo;
   import com.pickgliss.loader.DataAnalyzer;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.manager.ItemManager;
   
   public class CollectionTaskAnalyzer extends DataAnalyzer
   {
      
      private var _collectionTaskInfoList:Vector.<CollectionRobertVo>;
      
      public function CollectionTaskAnalyzer(_arg_1:Function)
      {
         super(_arg_1);
      }
      
      override public function analyze(_arg_1:*) : void
      {
         var _local_5:int = 0;
         var _local_4:* = null;
         var _local_2:* = null;
         var _local_3:XML = new XML(_arg_1);
         this._collectionTaskInfoList = new Vector.<CollectionRobertVo>();
         if(_local_3.@value == "true")
         {
            _local_4 = _local_3..item;
            _local_5 = 0;
            while(_local_5 < _local_4.length())
            {
               _local_2 = new CollectionRobertVo();
               ObjectUtils.copyPorpertiesByXML(_local_2,_local_4[_local_5]);
               _local_2.Style = "," + _local_2.Glass + "|" + ItemManager.Instance.getTemplateById(_local_2.Glass).Pic + "," + _local_2.Hair + "|" + ItemManager.Instance.getTemplateById(_local_2.Hair).Pic + "," + _local_2.Eye + "|" + ItemManager.Instance.getTemplateById(_local_2.Eye).Pic + "," + "," + _local_2.Face + "|" + ItemManager.Instance.getTemplateById(_local_2.Face).Pic + ",,,,,";
               this._collectionTaskInfoList.push(_local_2);
               _local_5++;
            }
            onAnalyzeComplete();
         }
         else
         {
            message = _local_3.@message;
            onAnalyzeError();
            onAnalyzeError();
         }
      }
      
      public function get collectionTaskInfoList() : Vector.<CollectionRobertVo>
      {
         return this._collectionTaskInfoList;
      }
   }
}

