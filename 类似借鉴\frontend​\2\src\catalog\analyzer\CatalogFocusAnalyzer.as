package catalog.analyzer
{
   import catalog.data.CatalogConditionData;
   import com.pickgliss.loader.DataAnalyzer;
   import com.pickgliss.utils.ObjectUtils;
   import road7th.data.DictionaryData;
   
   public class CatalogFocusAnalyzer extends DataAnalyzer
   {
      
      private var _data:DictionaryData;
      
      public function CatalogFocusAnalyzer(param1:Function = null)
      {
         super(param1);
      }
      
      override public function analyze(param1:*) : void
      {
         var _loc7_:XML = null;
         var _loc2_:int = 0;
         var _loc3_:XMLList = null;
         var _loc4_:int = 0;
         var _loc5_:int = 0;
         var _loc6_:CatalogConditionData = null;
         this._data = new DictionaryData();
         _loc7_ = new XML(param1);
         if(_loc7_.@value == "true")
         {
            _loc2_ = int(_loc7_.Item.length());
            _loc3_ = _loc7_.Item;
            _loc5_ = 0;
            while(_loc5_ < _loc2_)
            {
               _loc6_ = new CatalogConditionData();
               ObjectUtils.copyPorpertiesByXML(_loc6_,_loc3_[_loc5_]);
               _loc4_ = _loc6_.FocusId - 2001;
               this._data[_loc4_] = _loc6_;
               _loc5_++;
            }
            onAnalyzeComplete();
         }
         else
         {
            message = _loc7_.@message;
            onAnalyzeError();
            onAnalyzeComplete();
         }
      }
      
      public function get data() : DictionaryData
      {
         return this._data;
      }
   }
}

