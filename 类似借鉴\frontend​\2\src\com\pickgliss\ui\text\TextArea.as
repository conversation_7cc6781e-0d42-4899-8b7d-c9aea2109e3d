package com.pickgliss.ui.text
{
   import com.pickgliss.geom.IntPoint;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.controls.DisplayObjectViewport;
   import com.pickgliss.ui.controls.ScrollPanel;
   import com.pickgliss.utils.DisplayUtils;
   import com.pickgliss.utils.ObjectUtils;
   import flash.events.Event;
   import flash.events.KeyboardEvent;
   import flash.events.MouseEvent;
   import flash.geom.Rectangle;
   import flash.ui.Mouse;
   
   public class TextArea extends ScrollPanel
   {
      
      public static const P_textField:String = "textField";
      
      protected var _currentTextHeight:int = 0;
      
      protected var _enable:Boolean = true;
      
      protected var _textField:FilterFrameText;
      
      protected var _textStyle:String;
      
      public function TextArea()
      {
         super();
         _viewSource.addEventListener("click",this.__onTextAreaClick);
         _viewSource.addEventListener("mouseOver",this.__onTextAreaOver);
         _viewSource.addEventListener("mouseOut",this.__onTextAreaOut);
      }
      
      override public function dispose() : void
      {
         Mouse.cursor = "auto";
         _viewSource.removeEventListener("click",this.__onTextAreaClick);
         _viewSource.removeEventListener("mouseOver",this.__onTextAreaOver);
         _viewSource.removeEventListener("mouseOut",this.__onTextAreaOut);
         this._textField.removeEventListener("keyDown",this.__onTextKeyDown);
         this._textField.removeEventListener("change",this.__onTextChanged);
         ObjectUtils.disposeObject(this._textField);
         this._textField = null;
         super.dispose();
      }
      
      public function get editable() : Boolean
      {
         return this._textField.type == "input";
      }
      
      public function set editable(_arg_1:Boolean) : void
      {
         if(_arg_1)
         {
            this._textField.type = "input";
            _viewSource.addEventListener("mouseOver",this.__onTextAreaOver);
            _viewSource.addEventListener("mouseOut",this.__onTextAreaOut);
         }
         else
         {
            this._textField.type = "dynamic";
            _viewSource.removeEventListener("mouseOver",this.__onTextAreaOver);
            _viewSource.removeEventListener("mouseOut",this.__onTextAreaOut);
         }
      }
      
      public function get enable() : Boolean
      {
         return this._enable;
      }
      
      public function set enable(_arg_1:Boolean) : void
      {
         this._textField.mouseEnabled = this._enable;
         if(this._enable)
         {
            _viewSource.addEventListener("mouseOver",this.__onTextAreaOver);
            _viewSource.addEventListener("mouseOut",this.__onTextAreaOut);
         }
         else
         {
            _viewSource.removeEventListener("mouseOver",this.__onTextAreaOver);
            _viewSource.removeEventListener("mouseOut",this.__onTextAreaOut);
         }
      }
      
      public function get maxChars() : int
      {
         return this._textField.maxChars;
      }
      
      public function set maxChars(_arg_1:int) : void
      {
         this._textField.maxChars = _arg_1;
      }
      
      public function get text() : String
      {
         return this._textField.text;
      }
      
      public function set text(_arg_1:String) : void
      {
         this._textField.text = _arg_1;
         DisplayObjectViewport(_viewSource).invalidateView();
      }
      
      public function set htmlText(_arg_1:String) : void
      {
         this._textField.htmlText = _arg_1;
         DisplayObjectViewport(_viewSource).invalidateView();
      }
      
      public function get htmlText() : String
      {
         return this._textField.htmlText;
      }
      
      public function get textField() : FilterFrameText
      {
         return this._textField;
      }
      
      public function set textField(_arg_1:FilterFrameText) : void
      {
         if(this._textField == _arg_1)
         {
            return;
         }
         if(Boolean(this._textField))
         {
            this._textField.removeEventListener("change",this.__onTextChanged);
            this._textField.removeEventListener("keyDown",this.__onTextKeyDown);
            ObjectUtils.disposeObject(this._textField);
         }
         this._textField = _arg_1;
         this._textField.multiline = true;
         this._textField.mouseWheelEnabled = false;
         this._textField.addEventListener("keyDown",this.__onTextKeyDown);
         this._textField.addEventListener("change",this.__onTextChanged);
         onPropertiesChanged("textField");
      }
      
      public function set textStyle(_arg_1:String) : void
      {
         if(this._textStyle == _arg_1)
         {
            return;
         }
         this._textStyle = _arg_1;
         this.textField = ComponentFactory.Instance.creat(this._textStyle);
      }
      
      override protected function layoutComponent() : void
      {
         var _local_1:Rectangle = null;
         super.layoutComponent();
         _local_1 = _viewportInnerRect.getInnerRect(_width,_height);
         this._textField.x = _local_1.x;
         this._textField.y = _local_1.y;
         this._textField.width = _viewSource.width;
      }
      
      override protected function onProppertiesUpdate() : void
      {
         super.onProppertiesUpdate();
         if(Boolean(_changedPropeties["textField"]))
         {
            DisplayObjectViewport(_viewSource).setView(this._textField);
         }
      }
      
      private function __onTextAreaClick(_arg_1:MouseEvent) : void
      {
         this._textField.setFocus();
      }
      
      private function __onTextAreaOut(_arg_1:MouseEvent) : void
      {
         Mouse.cursor = "auto";
      }
      
      private function __onTextAreaOver(_arg_1:MouseEvent) : void
      {
         Mouse.cursor = "ibeam";
      }
      
      private function __onTextChanged(_arg_1:Event) : void
      {
         this.upScrollArea();
      }
      
      private function __onTextKeyDown(_arg_1:KeyboardEvent) : void
      {
         if(_arg_1.keyCode == 13)
         {
            _arg_1.stopPropagation();
         }
         else if(_arg_1.keyCode == 38)
         {
            this.upScrollArea();
         }
         else if(_arg_1.keyCode == 40)
         {
            this.upScrollArea();
         }
         else if(_arg_1.keyCode == 46)
         {
            this.upScrollArea();
         }
      }
      
      public function upScrollArea() : void
      {
         var _local_1:Number = NaN;
         var _local_5:Number = NaN;
         var _local_3:Number = NaN;
         var _local_4:Number = NaN;
         var _local_2:Number = NaN;
         var _local_6:Number = NaN;
         var _local_7:Number = NaN;
         var _local_8:* = null;
         DisplayObjectViewport(_viewSource).invalidateView();
         if(this._textField.caretIndex <= 0)
         {
            viewPort.viewPosition = new IntPoint(0,0);
         }
         else
         {
            _local_1 = DisplayUtils.getTextFieldLineHeight(this._textField);
            _local_8 = viewPort.viewPosition;
            _local_5 = DisplayUtils.getTextFieldCareLinePosX(this._textField);
            _local_3 = DisplayUtils.getTextFieldCareLinePosY(this._textField);
            _local_4 = _local_5 - _local_8.x;
            _local_2 = _local_3 + _local_1 - _local_8.y;
            DisplayObjectViewport(_viewSource).invalidateView();
            _local_6 = Number(_local_8.x);
            _local_7 = Number(_local_8.y);
            if(_local_4 < 0)
            {
               _local_6 = _local_5;
            }
            else if(_local_4 > viewPort.getExtentSize().width)
            {
               _local_6 = _local_5 + viewPort.getExtentSize().width;
            }
            if(_local_2 < _local_1)
            {
               _local_7 = _local_3;
            }
            else if(_local_2 > viewPort.getExtentSize().height)
            {
               _local_7 = _local_3 + viewPort.getExtentSize().height;
            }
            if(_local_6 > 0 || _local_7 > 0)
            {
               viewPort.viewPosition = new IntPoint(_local_6,_local_7);
            }
         }
      }
   }
}

