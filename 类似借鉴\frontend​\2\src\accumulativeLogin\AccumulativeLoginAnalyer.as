package accumulativeLogin
{
   import accumulativeLogin.data.AccumulativeLoginRewardData;
   import com.pickgliss.loader.DataAnalyzer;
   import com.pickgliss.utils.ObjectUtils;
   import flash.utils.Dictionary;
   
   public class AccumulativeLoginAnalyer extends DataAnalyzer
   {
      
      private var _accumulativeloginDataDic:Dictionary;
      
      public function AccumulativeLoginAnalyer(_arg_1:Function)
      {
         super(_arg_1);
         this._accumulativeloginDataDic = new Dictionary();
      }
      
      override public function analyze(_arg_1:*) : void
      {
         var _local_7:int = 0;
         var _local_3:AccumulativeLoginRewardData = null;
         var _local_6:* = null;
         var _local_2:* = null;
         var _local_4:* = null;
         var _local_5:XML = new XML(_arg_1);
         if(_local_5.@value == "true")
         {
            _local_6 = _local_5..Item;
            _local_2 = [];
            _local_7 = 0;
            while(_local_7 < _local_6.length())
            {
               _local_4 = new AccumulativeLoginRewardData();
               ObjectUtils.copyPorpertiesByXML(_local_4,_local_6[_local_7]);
               _local_2.push(_local_4);
               _local_7++;
            }
            for each(_local_3 in _local_2)
            {
               if(!this._accumulativeloginDataDic[_local_3.Count])
               {
                  this._accumulativeloginDataDic[_local_3.Count] = [];
               }
               this._accumulativeloginDataDic[_local_3.Count].push(_local_3);
            }
            onAnalyzeComplete();
         }
         else
         {
            message = _local_5.@message;
            onAnalyzeError();
            onAnalyzeError();
         }
      }
      
      public function get accumulativeloginDataDic() : Dictionary
      {
         return this._accumulativeloginDataDic;
      }
   }
}

