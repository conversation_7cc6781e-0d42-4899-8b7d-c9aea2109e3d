package consortiaRoseFlower
{
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.controls.SimpleBitmapButton;
   import com.pickgliss.ui.core.Disposeable;
   import com.pickgliss.ui.text.FilterFrameText;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.events.CEvent;
   import ddt.manager.LanguageMgr;
   import flash.display.Bitmap;
   import flash.display.Sprite;
   import flash.events.KeyboardEvent;
   import flash.events.MouseEvent;
   import org.aswing.KeyboardManager;
   
   public class ConsortiaRoseDetailView extends Sprite implements Disposeable
   {
      
      private var _bg:Bitmap;
      
      private var _close:SimpleBitmapButton;
      
      private var _detailTxt:FilterFrameText;
      
      public function ConsortiaRoseDetailView(_arg_1:Boolean, _arg_2:String, _arg_3:String)
      {
         super();
         this._bg = ComponentFactory.Instance.creatBitmap("ast.rose.bg");
         addChild(this._bg);
         if(_arg_1)
         {
            this._close = ComponentFactory.Instance.creat("rose.closeBtn");
            addChild(this._close);
            this._close.addEventListener("click",this.onCloseClick);
            KeyboardManager.getInstance().addEventListener("keyDown",this.onKeyDown);
         }
         this._detailTxt = ComponentFactory.Instance.creat("rose.detailTxt");
         this._detailTxt.text = LanguageMgr.GetTranslation("consortia.roseFlower.Tip",_arg_2,_arg_3);
         addChild(this._detailTxt);
      }
      
      protected function onKeyDown(_arg_1:KeyboardEvent) : void
      {
         if(_arg_1.keyCode == 27)
         {
            this.closeRose();
         }
      }
      
      protected function onCloseClick(_arg_1:MouseEvent) : void
      {
         this.closeRose();
      }
      
      private function closeRose() : void
      {
         ObjectUtils.disposeObject(this);
         dispatchEvent(new CEvent("close_rose"));
      }
      
      public function dispose() : void
      {
         if(this._close != null)
         {
            this._close.removeEventListener("click",this.onCloseClick);
            ObjectUtils.disposeObject(this._close);
            this._close = null;
         }
         ObjectUtils.disposeObject(this._bg);
         this._bg = null;
         KeyboardManager.getInstance().removeEventListener("keyDown",this.onKeyDown);
         ObjectUtils.disposeObject(this._detailTxt);
         this._detailTxt = null;
      }
   }
}

