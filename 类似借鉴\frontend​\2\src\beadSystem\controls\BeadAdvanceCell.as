package beadSystem.controls
{
   import bagAndInfo.cell.DragEffect;
   import ddt.data.goods.InventoryItemInfo;
   import ddt.data.goods.ItemTemplateInfo;
   import ddt.manager.DragManager;
   
   public class BeadAdvanceCell extends BeadCell
   {
      
      public function BeadAdvanceCell(_arg_1:int, _arg_2:ItemTemplateInfo = null, _arg_3:<PERSON><PERSON><PERSON> = true, _arg_4:<PERSON>olean = true)
      {
         super(_arg_1,_arg_2,_arg_3,_arg_4);
      }
      
      override public function dragStart() : void
      {
         if(_info && !locked && stage && _allowDrag)
         {
            DragManager.startDrag(this,_info,createDragImg(),stage.mouseX,stage.mouseY,"move");
         }
      }
      
      override public function dragDrop(_arg_1:DragEffect) : void
      {
         var _local_2:* = null;
         if(_arg_1.data is InventoryItemInfo && this.info == null)
         {
            _arg_1.action = "none";
            _local_2 = _arg_1.data;
            this.itemInfo = _local_2;
            this.info = _local_2;
            DragManager.acceptDrag(this);
         }
      }
      
      override public function dragStop(_arg_1:DragEffect) : void
      {
         if(_arg_1.action == "none")
         {
            locked = false;
         }
         if(_arg_1.action == "none" && _arg_1.target != null)
         {
            this.itemInfo = null;
            this.info = null;
         }
      }
   }
}

