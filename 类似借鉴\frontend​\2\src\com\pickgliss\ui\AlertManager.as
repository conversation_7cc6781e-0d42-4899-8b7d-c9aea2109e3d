package com.pickgliss.ui
{
   import com.pickgliss.action.AlertAction;
   import com.pickgliss.events.ComponentEvent;
   import com.pickgliss.manager.CacheSysManager;
   import com.pickgliss.toplevel.StageReferance;
   import com.pickgliss.ui.controls.alert.BaseAlerFrame;
   import com.pickgliss.ui.vo.AlertInfo;
   import com.pickgliss.utils.ObjectUtils;
   import com.pickgliss.utils.StringUtils;
   import flash.events.Event;
   
   public final class AlertManager
   {
      
      private static var _instance:AlertManager;
      
      public static const NOSELECTBTN:int = 0;
      
      public static const SELECTBTN:int = 1;
      
      public static var DEFAULT_CONFIRM_LABEL:String = "確 定";
      
      private var _layerType:int;
      
      private var _simpleAlertInfo:AlertInfo;
      
      public function AlertManager()
      {
         super();
      }
      
      public static function get Instance() : AlertManager
      {
         if(_instance == null)
         {
            _instance = new AlertManager();
         }
         return _instance;
      }
      
      public function set layerType(_arg_1:int) : void
      {
         this._layerType = _arg_1;
      }
      
      public function alert(_arg_1:String, _arg_2:AlertInfo, _arg_3:int = 0, _arg_4:String = null) : BaseAlerFrame
      {
         var _local_5:BaseAlerFrame = ComponentFactory.Instance.creat(_arg_1);
         _local_5.addEventListener("propertiesChanged",this.__onAlertSizeChanged);
         _local_5.addEventListener("removedFromStage",this.__onAlertRemoved);
         _local_5.info = _arg_2;
         if(Boolean(_arg_4) && CacheSysManager.isLock(_arg_4))
         {
            CacheSysManager.getInstance().cache(_arg_4,new AlertAction(_local_5,this._layerType,_arg_3));
         }
         else
         {
            LayerManager.Instance.addToLayer(_local_5,this._layerType,_local_5.info.frameCenter,_arg_3);
            StageReferance.stage.focus = _local_5;
         }
         return _local_5;
      }
      
      private function __onAlertRemoved(_arg_1:Event) : void
      {
         var _local_2:BaseAlerFrame = _arg_1.currentTarget as BaseAlerFrame;
         _local_2.removeEventListener("propertiesChanged",this.__onAlertSizeChanged);
         _local_2.removeEventListener("removedFromStage",this.__onAlertRemoved);
      }
      
      private function __onAlertSizeChanged(_arg_1:ComponentEvent) : void
      {
         var _local_2:BaseAlerFrame = _arg_1.currentTarget as BaseAlerFrame;
         if(_local_2.info.frameCenter)
         {
            _local_2.x = (StageReferance.stageWidth - _local_2.width) / 2;
            _local_2.y = (StageReferance.stageHeight - _local_2.height) / 2;
         }
      }
      
      public function setup(_arg_1:int, _arg_2:AlertInfo) : void
      {
         this._simpleAlertInfo = _arg_2;
         this._layerType = _arg_1;
      }
      
      public function simpleAlert(_arg_1:String, _arg_2:String, _arg_3:String = "", _arg_4:String = "", _arg_5:Boolean = false, _arg_6:Boolean = false, _arg_7:Boolean = false, _arg_8:int = 0, _arg_9:String = null, _arg_10:String = "SimpleAlert", _arg_11:int = 30, _arg_12:Boolean = true, _arg_13:int = 0, _arg_14:int = 0) : BaseAlerFrame
      {
         var _local_15:AlertInfo = null;
         if(StringUtils.isEmpty(_arg_3))
         {
            _arg_3 = DEFAULT_CONFIRM_LABEL;
         }
         _local_15 = new AlertInfo();
         ObjectUtils.copyProperties(_local_15,this._simpleAlertInfo);
         _local_15.sound = this._simpleAlertInfo.sound;
         _local_15.data = _arg_2;
         _local_15.autoDispose = _arg_5;
         _local_15.title = _arg_1;
         _local_15.submitLabel = _arg_3;
         _local_15.cancelLabel = _arg_4;
         _local_15.enableHtml = _arg_6;
         _local_15.mutiline = _arg_7;
         _local_15.buttonGape = _arg_11;
         _local_15.autoButtonGape = _arg_12;
         _local_15.type = _arg_13;
         _local_15.selectBtnY = _arg_14;
         if(StringUtils.isEmpty(_arg_4))
         {
            _local_15.showCancel = false;
         }
         return this.alert(_arg_10,_local_15,_arg_8,_arg_9);
      }
   }
}

