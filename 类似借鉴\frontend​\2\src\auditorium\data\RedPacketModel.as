package auditorium.data
{
   import flash.globalization.DateTimeFormatter;
   import road7th.utils.DateUtils;
   
   public class RedPacketModel
   {
      
      private var _createID:int;
      
      private var _redPacketID:int;
      
      private var _redType:int;
      
      private var _isEffective:Boolean;
      
      private var _desc:String;
      
      private var _remain:int;
      
      public function RedPacketModel()
      {
         super();
      }
      
      public function get remain() : int
      {
         return this._remain;
      }
      
      public function set remain(_arg_1:int) : void
      {
         this._remain = _arg_1;
      }
      
      public function get surplusTime() : String
      {
         var _local_1:* = null;
         if(this._remain > 0)
         {
            _local_1 = this.formatDate(this._remain);
            return _local_1[2] + ":" + _local_1[3];
         }
         return "00:00";
      }
      
      private function formatDate(_arg_1:int) : Array
      {
         var _local_7:DateTimeFormatter = new DateTimeFormatter("HH:NN:SS");
         var _local_3:Date = new Date(null,null,null,null,null,_arg_1);
         var _local_10:String = _local_7.format(_local_3);
         var _local_5:String = _local_10.split(" ")[1];
         var _local_9:Array = _local_5.split(":");
         if(_local_9.length < 3)
         {
            _local_9 = DateUtils.dateTimeRemainArr(_arg_1);
         }
         else
         {
            _local_9.unshift("0");
         }
         var _local_2:int = int(_local_9[0]);
         var _local_4:int = int(_local_9[1]);
         var _local_6:int = int(_local_9[2]);
         var _local_8:int = int(_local_9[3]);
         return _local_9;
      }
      
      public function get desc() : String
      {
         return this._desc;
      }
      
      public function set desc(_arg_1:String) : void
      {
         this._desc = _arg_1;
      }
      
      public function get isEffective() : Boolean
      {
         return this._isEffective;
      }
      
      public function set isEffective(_arg_1:Boolean) : void
      {
         this._isEffective = _arg_1;
      }
      
      public function get redPacketID() : int
      {
         return this._redPacketID;
      }
      
      public function set redPacketID(_arg_1:int) : void
      {
         this._redPacketID = _arg_1;
      }
      
      public function get createID() : int
      {
         return this._createID;
      }
      
      public function set createID(_arg_1:int) : void
      {
         this._createID = _arg_1;
      }
      
      public function get redType() : int
      {
         return this._redType;
      }
      
      public function set redType(_arg_1:int) : void
      {
         this._redType = _arg_1;
      }
   }
}

