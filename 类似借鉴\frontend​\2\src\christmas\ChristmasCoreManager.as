package christmas
{
   import christmas.event.ChrismasEvent;
   import christmas.info.ChristmasSystemItemsInfo;
   import christmas.model.ChristmasModel;
   import christmas.player.PlayerVO;
   import com.pickgliss.manager.CacheSysManager;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.controls.BaseButton;
   import com.pickgliss.ui.core.Component;
   import ddt.CoreManager;
   import ddt.data.BagInfo;
   import ddt.data.goods.ShopItemInfo;
   import ddt.data.player.SelfInfo;
   import ddt.events.CrazyTankSocketEvent;
   import ddt.events.PkgEvent;
   import ddt.manager.ChatManager;
   import ddt.manager.GameInSocketOut;
   import ddt.manager.LanguageMgr;
   import ddt.manager.MessageTipManager;
   import ddt.manager.PlayerManager;
   import ddt.manager.SocketManager;
   import ddt.manager.StateManager;
   import flash.events.Event;
   import hallIcon.HallIconManager;
   import road7th.comm.PackageIn;
   import room.RoomManager;
   import room.model.RoomInfo;
   
   public class ChristmasCoreManager extends CoreManager
   {
      
      public static var isTimeOver:Boolean;
      
      private static var _instance:ChristmasCoreManager;
      
      public static var isFrameChristmas:Boolean;
      
      public static var isToRoom:Boolean;
      
      public static var isComeRoom:Boolean;
      
      public static var CHRISTMAS_BOSS_INFO:String = "christmasBossInfo";
      
      public static var CHRISTMAS_BOSS_RANk:String = "christmasBossRank";
      
      public static var CHRISTMAS_BUFF_CHANGE:String = "christmasBuffChange";
      
      public static var CHRISTMAS_MAKE_INFO:String = "christmasMakeInfo";
      
      public static var CHRISTMAS_BOSS_HP:String = "christmasBossHp";
      
      private var _self:SelfInfo;
      
      private var _model:ChristmasModel;
      
      private var _christmasResourceId:String;
      
      private var _currentPVE_ID:int;
      
      private var _mapPath:String;
      
      private var _appearPos:Array = [];
      
      private var _christmasInfo:ChristmasSystemItemsInfo;
      
      private var _outFun:Function;
      
      public var _goods:ShopItemInfo;
      
      public var isReConnect:Boolean = false;
      
      public var loadUiModuleComplete:Boolean = false;
      
      private var _pkg:PackageIn;
      
      public function ChristmasCoreManager(_arg_1:PrivateClass)
      {
         super();
      }
      
      public static function get instance() : ChristmasCoreManager
      {
         if(ChristmasCoreManager._instance == null)
         {
            ChristmasCoreManager._instance = new ChristmasCoreManager(new PrivateClass());
         }
         return ChristmasCoreManager._instance;
      }
      
      public function setup() : void
      {
         this._model = new ChristmasModel();
         this._self = new SelfInfo();
         this._christmasInfo = new ChristmasSystemItemsInfo();
         this._christmasInfo.myPlayerVO = new PlayerVO();
         SocketManager.Instance.addEventListener("christmas_system",this.pkgHandler);
         SocketManager.Instance.addEventListener(PkgEvent.format(653,1),this.getBossInfo);
         SocketManager.Instance.addEventListener(PkgEvent.format(653,4),this.getRankInfo);
         SocketManager.Instance.addEventListener(PkgEvent.format(653,3),this.buyBuffHandler);
         SocketManager.Instance.addEventListener(PkgEvent.format(653,7),this.__remainHandler);
         SocketManager.Instance.addEventListener(PkgEvent.format(653,8),this.__makeInfoHandler);
         SocketManager.Instance.addEventListener(PkgEvent.format(653,9),this.__isCanEnterBossHandler);
         SocketManager.Instance.addEventListener(PkgEvent.format(653,10),this.__christmasBossBloodHandler);
      }
      
      override protected function start() : void
      {
         dispatchEvent(new ChrismasEvent("xmas_click_christmas_icon"));
      }
      
      private function showChristmas() : void
      {
         dispatchEvent(new ChrismasEvent("xmas_show",this._pkg));
         this._pkg = null;
      }
      
      private function pkgHandler(_arg_1:CrazyTankSocketEvent) : void
      {
         var _local_3:CrazyTankSocketEvent = null;
         var _local_4:PackageIn = _arg_1.pkg;
         var _local_2:int = _arg_1.cmd;
         switch(_local_2)
         {
            case 16:
               this.openOrclose(_local_4);
               break;
            case 17:
               this._pkg = _local_4;
               this.showChristmas();
               break;
            case 27:
               _local_3 = new CrazyTankSocketEvent("getpackstoplayer",_local_4);
               break;
            case 20:
               _local_3 = new CrazyTankSocketEvent("player_statue",_local_4);
               break;
            case 21:
               _local_3 = new CrazyTankSocketEvent("christmas_move",_local_4);
               break;
            case 18:
               _local_3 = new CrazyTankSocketEvent("addplayer_room",_local_4);
               break;
            case 19:
               _local_3 = new CrazyTankSocketEvent("christmas_exit",_local_4);
               break;
            case 22:
               _local_3 = new CrazyTankSocketEvent("christmas_monster",_local_4);
         }
         if(Boolean(_local_3))
         {
            dispatchEvent(_local_3);
         }
      }
      
      private function __christmasBossBloodHandler(_arg_1:PkgEvent) : void
      {
         var _local_3:PackageIn = _arg_1.pkg;
         var _local_2:int = _local_3.readInt();
         this.model.bossMaxHp = _local_3.readLong();
         this.model.bossCurHp = _local_3.readLong();
         this.dispatchEvent(new Event(CHRISTMAS_BOSS_HP));
      }
      
      private function __isCanEnterBossHandler(_arg_1:PkgEvent) : void
      {
         var _local_3:PackageIn = _arg_1.pkg;
         this.model.bossResureTime = _local_3.readDate();
         var _local_2:Boolean = _local_3.readBoolean();
         if(_local_2)
         {
            this.setupFightEvent();
            SocketManager.Instance.out.createUserGuide(72);
         }
      }
      
      private function __makeInfoHandler(_arg_1:PkgEvent) : void
      {
         var _local_2:PackageIn = _arg_1.pkg;
         this.model.todayCount = _local_2.readInt();
         this.model.count = _local_2.readInt();
         this.model.rewardStr = _local_2.readUTF();
         this.model.rewardRemainStr = _local_2.readUTF();
         this.dispatchEvent(new Event(CHRISTMAS_MAKE_INFO));
      }
      
      private function __remainHandler(_arg_1:PkgEvent) : void
      {
         var _local_2:PackageIn = _arg_1.pkg;
         this.model.remainPlayCount = _local_2.readInt();
         this.dispatchEvent(new Event(CHRISTMAS_BOSS_INFO));
      }
      
      private function buyBuffHandler(_arg_1:PkgEvent) : void
      {
         var _local_2:PackageIn = _arg_1.pkg;
         this.model.bossActiveId = _local_2.readInt();
         this.model.buffCount = _local_2.readInt();
         this.dispatchEvent(new Event(CHRISTMAS_BUFF_CHANGE));
      }
      
      private function getBossInfo(_arg_1:PkgEvent) : void
      {
         var _local_2:PackageIn = _arg_1.pkg;
         this.model.bossActiveId = _local_2.readInt();
         this.model.buffCount = _local_2.readInt();
         this.model.remainPlayCount = _local_2.readInt();
         this.model.bossStartTime = _local_2.readDate();
         this.model.bossEndTime = _local_2.readDate();
         this.model.bossState = _local_2.readInt();
         this.model.bossRemainOpenTime = _local_2.readInt();
         this.model.bossResureTime = _local_2.readDate();
         this.dispatchEvent(new Event(CHRISTMAS_BOSS_INFO));
      }
      
      private function getRankInfo(_arg_1:PkgEvent) : void
      {
         var _local_9:int = 0;
         var _local_3:* = undefined;
         var _local_7:int = 0;
         var _local_6:* = null;
         var _local_8:* = null;
         var _local_5:PackageIn = _arg_1.pkg;
         this.model.bossActiveId = _local_5.readInt();
         this.model.myDamage = _local_5.readLong();
         this.model.myRank = _local_5.readInt();
         this.model.allDamage = _local_5.readLong();
         while(Boolean(this.model.bossRankArr.length))
         {
            this.model.bossRankArr.shift();
         }
         var _local_4:int = _local_5.readInt();
         _local_9 = 0;
         while(_local_9 < _local_4)
         {
            _local_6 = {};
            _local_6.areaID = _local_5.readInt();
            _local_6.userID = _local_5.readInt();
            _local_6.nickName = _local_5.readUTF();
            _local_6.damage = _local_5.readInt();
            _local_6.rank = _local_5.readInt();
            this.model.bossRankArr.push(_local_6);
            _local_9++;
         }
         this.dispatchEvent(new Event(CHRISTMAS_BOSS_RANk));
         var _local_2:Boolean = _local_5.readBoolean();
         if(_local_2 && !CacheSysManager.isLock("alertInFight"))
         {
            _local_3 = ComponentFactory.Instance.creat("christmasWorldboss.ranking.frame");
            _local_3.addPersonRanking(this.model.bossRankArr);
            _local_3.show();
            _local_8 = LanguageMgr.GetTranslation("tank.christmas.worldBossChatTxt") + ":";
            _local_7 = 0;
            while(_local_7 < this.model.bossRankArr.length)
            {
               _local_8 += this.model.bossRankArr[_local_7].nickName;
               _local_8 += _local_7 == this.model.bossRankArr.length - 1 ? "" : ",";
               _local_7++;
            }
            ChatManager.Instance.sysChatYellow(_local_8);
         }
      }
      
      public function setupFightEvent() : void
      {
         RoomManager.Instance.removeEventListener("gameRoomCreate",this.__gameStart);
         RoomManager.Instance.addEventListener("gameRoomCreate",this.__gameStart);
      }
      
      private function __gameStart(_arg_1:CrazyTankSocketEvent) : void
      {
         RoomManager.Instance.removeEventListener("gameRoomCreate",this.__gameStart);
         var _local_2:RoomInfo = RoomManager.Instance.current;
         if(_local_2.type == 72)
         {
            dispatchEvent(new ChrismasEvent("boss_game_start"));
         }
         else
         {
            dispatchEvent(new ChrismasEvent("xmas_game_start"));
         }
      }
      
      public function playingSnowmanEnter() : void
      {
         dispatchEvent(new ChrismasEvent("xmas_playing_snowman"));
      }
      
      public function reConnect() : void
      {
         dispatchEvent(new ChrismasEvent("xmas_reconnect"));
      }
      
      public function reConnectChristmasFunc() : void
      {
         dispatchEvent(new ChrismasEvent("xmas_reconnect_christmas"));
      }
      
      private function reConnectLoadUiComplete() : void
      {
         this.loadUiModuleComplete = true;
         SocketManager.Instance.out.enterChristmasRoomIsTrue();
      }
      
      private function openOrclose(_arg_1:PackageIn) : void
      {
         var _local_7:int = 0;
         var _local_6:int = 0;
         var _local_5:int = 0;
         var _local_3:* = null;
         var _local_4:* = null;
         var _local_2:* = null;
         this._model.isOpen = _arg_1.readBoolean();
         if(this._model.isOpen)
         {
            this._model.beginTime = _arg_1.readDate();
            this._model.endTime = _arg_1.readDate();
            this._model.packsLen = _arg_1.readInt();
            _local_3 = [];
            _local_7 = 0;
            while(_local_7 < this._model.packsLen)
            {
               _local_4 = new ChristmasSystemItemsInfo();
               _local_4.conditionCount = _arg_1.readInt();
               _local_6 = _arg_1.readInt();
               _local_5 = 0;
               while(_local_5 < _local_6)
               {
                  _local_2 = {};
                  _local_2.tempid = _arg_1.readInt();
                  _local_2.count = _arg_1.readInt();
                  _local_4.TemplateArr.push(_local_2);
                  _local_5++;
               }
               _local_3.push(_local_4);
               _local_7++;
            }
            _local_3.sortOn("conditionCount",16);
            this._model.myGiftData = _local_3;
         }
         this.checkIcon();
      }
      
      public function getBagSnowPacksCount() : int
      {
         var _local_3:SelfInfo = PlayerManager.Instance.Self;
         var _local_1:BagInfo = _local_3.getBag(1);
         return _local_1.getItemCountByTemplateId(12778);
      }
      
      public function initHall() : void
      {
         this.checkIcon();
      }
      
      public function checkIcon() : void
      {
         if(this._model.isOpen)
         {
            HallIconManager.instance.updateSwitchHandler("christmas",true);
         }
         else
         {
            HallIconManager.instance.updateSwitchHandler("christmas",false);
            if(StateManager.currentStateType == "christmas" || StateManager.currentStateType == "christmasroom")
            {
               StateManager.setState("main");
            }
         }
      }
      
      public function openView() : void
      {
         if(PlayerManager.Instance.Self.Grade < 10)
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("christmas.Icon.NoEnter",10));
            return;
         }
         if(StateManager.currentStateType == "main")
         {
            show();
         }
      }
      
      public function get christmasInfo() : ChristmasSystemItemsInfo
      {
         return this._christmasInfo;
      }
      
      public function getCount() : int
      {
         var _local_3:SelfInfo = PlayerManager.Instance.Self;
         var _local_1:BagInfo = _local_3.getBag(1);
         return _local_1.getItemCountByTemplateId(12778);
      }
      
      public function checkMoney(_arg_1:Boolean) : Boolean
      {
         if(_arg_1)
         {
            if(PlayerManager.Instance.Self.BandMoney < this._goods.AValue1)
            {
               return false;
            }
         }
         else if(PlayerManager.Instance.Self.Money < this._goods.AValue1)
         {
            return false;
         }
         return true;
      }
      
      public function returnComponentBnt(_arg_1:BaseButton, _arg_2:String) : Component
      {
         var _local_3:Component = new Component();
         _local_3.tipData = _arg_2;
         _local_3.tipDirctions = "0,1,2";
         _local_3.tipStyle = "ddt.view.tips.OneLineTip";
         _local_3.tipGapH = 20;
         _local_3.width = _arg_1.width;
         _local_3.x = _arg_1.x;
         _local_3.y = _arg_1.y;
         _arg_1.x = 0;
         _arg_1.y = 0;
         _local_3.addChild(_arg_1);
         return _local_3;
      }
      
      public function exitGame() : void
      {
         GameInSocketOut.sendGamePlayerExit();
      }
      
      public function get model() : ChristmasModel
      {
         return this._model;
      }
      
      public function get mapPath() : String
      {
         return this._mapPath;
      }
      
      public function set mapPath(_arg_1:String) : void
      {
         this._mapPath = _arg_1;
      }
   }
}

class PrivateClass
{
   
   public function PrivateClass()
   {
      super();
   }
}
