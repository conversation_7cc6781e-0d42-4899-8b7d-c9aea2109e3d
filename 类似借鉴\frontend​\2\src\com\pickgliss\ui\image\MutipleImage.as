package com.pickgliss.ui.image
{
   import com.pickgliss.geom.InnerRectangle;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.utils.ClassUtils;
   import com.pickgliss.utils.ObjectUtils;
   import flash.display.DisplayObject;
   import flash.display.Sprite;
   
   public class MutipleImage extends Image
   {
      
      public static const P_imageRect:String = "imagesRect";
      
      private var _imageLinks:Array;
      
      private var _imageRectString:String;
      
      private var _images:Vector.<DisplayObject>;
      
      private var _imagesRect:Vector.<InnerRectangle>;
      
      public function MutipleImage()
      {
         super();
      }
      
      override public function dispose() : void
      {
         var _local_1:int = 0;
         if(<PERSON><PERSON><PERSON>(this._images))
         {
            _local_1 = 0;
            while(_local_1 < this._images.length)
            {
               ObjectUtils.disposeObject(this._images[_local_1]);
               _local_1++;
            }
         }
         this._images = null;
         this._imagesRect = null;
         super.dispose();
      }
      
      public function set imageRectString(_arg_1:String) : void
      {
         var _local_2:int = 0;
         if(this._imageRectString == _arg_1)
         {
            return;
         }
         this._imagesRect = new Vector.<InnerRectangle>();
         this._imageRectString = _arg_1;
         var _local_3:Array = ComponentFactory.parasArgs(this._imageRectString);
         _local_2 = 0;
         while(_local_2 < _local_3.length)
         {
            if(_local_3[_local_2] == "")
            {
               this._imagesRect.push(null);
            }
            else
            {
               this._imagesRect.push(ClassUtils.CreatInstance("com.pickgliss.geom.InnerRectangle",_local_3[_local_2].split("|")));
            }
            _local_2++;
         }
         onPropertiesChanged("imagesRect");
      }
      
      override protected function addChildren() : void
      {
         var _local_1:int = 0;
         super.addChildren();
         if(this._images == null)
         {
            return;
         }
         _local_1 = 0;
         while(_local_1 < this._images.length)
         {
            Sprite(_display).addChild(this._images[_local_1]);
            _local_1++;
         }
      }
      
      override protected function init() : void
      {
         _display = new Sprite();
         super.init();
      }
      
      override protected function resetDisplay() : void
      {
         this._imageLinks = ComponentFactory.parasArgs(_resourceLink);
         this.removeImages();
         this.creatImages();
      }
      
      override protected function updateSize() : void
      {
         var _local_2:int = 0;
         var _local_1:* = null;
         if(this._images == null)
         {
            return;
         }
         if(Boolean(_changedPropeties["width"]) || Boolean(_changedPropeties["height"]) || Boolean(_changedPropeties["imagesRect"]))
         {
            _local_2 = 0;
            while(_local_2 < this._images.length)
            {
               if(Boolean(this._imagesRect) && Boolean(this._imagesRect[_local_2]))
               {
                  _local_1 = this._imagesRect[_local_2].getInnerRect(_width,_height);
                  this._images[_local_2].x = _local_1.x;
                  this._images[_local_2].y = _local_1.y;
                  this._images[_local_2].height = _local_1.height;
                  this._images[_local_2].width = _local_1.width;
                  _width = Math.max(_width,this._images[_local_2].width);
                  _height = Math.max(_height,this._images[_local_2].height);
               }
               else
               {
                  this._images[_local_2].width = _width;
                  this._images[_local_2].height = _height;
               }
               _local_2++;
            }
         }
      }
      
      private function creatImages() : void
      {
         var _local_3:int = 0;
         var _local_1:* = null;
         var _local_2:* = null;
         this._images = new Vector.<DisplayObject>();
         _local_3 = 0;
         while(_local_3 < this._imageLinks.length)
         {
            _local_2 = this._imageLinks[_local_3].split("|");
            _local_1 = ComponentFactory.Instance.creat(_local_2[0]);
            this._images.push(_local_1);
            _local_3++;
         }
      }
      
      private function removeImages() : void
      {
         var _local_1:int = 0;
         if(this._images == null)
         {
            return;
         }
         _local_1 = 0;
         while(_local_1 < this._images.length)
         {
            ObjectUtils.disposeObject(this._images[_local_1]);
            _local_1++;
         }
      }
   }
}

