package com.pickgliss.loader
{
   import com.pickgliss.events.LoaderResourceEvent;
   import flash.events.EventDispatcher;
   import flash.external.ExternalInterface;
   import flash.system.fscommand;
   
   public class LoadInterfaceManager
   {
      
      private static var _eventDispatcher:EventDispatcher = new EventDispatcher();
      
      public function LoadInterfaceManager()
      {
         super();
      }
      
      [Event(name="checkComplete",type="com.pickgliss.loader.LoadInterfaceEvent")]
      [Event(name="deleteComplete",type="com.pickgliss.loader.LoadInterfaceEvent")]
      [Event(name="flashGotoAndPlay",type="com.pickgliss.loader.LoadInterfaceEvent")]
      [Event(name="setSound",type="com.pickgliss.loader.LoadInterfaceEvent")]
      public static function get eventDispatcher() : EventDispatcher
      {
         return _eventDispatcher;
      }
      
      public static function initAppInterface() : void
      {
         if(ExternalInterface.available)
         {
            if(LoadResourceManager.Instance.isMicroClient)
            {
               ExternalInterface.addCallback("setLoginType",LoadResourceManager.Instance.setLoginType);
               ExternalInterface.addCallback("checkComplete",__checkHandler);
               ExternalInterface.addCallback("deleteComplete",__deleteHandler);
               ExternalInterface.addCallback("flashGotoAndPlay",__flashGotoAndPlayHandler);
               ExternalInterface.addCallback("setSound",__setSoundHandler);
            }
         }
         LoadResourceManager.Instance.dispatchEvent(new LoaderResourceEvent("init complete"));
      }
      
      private static function __checkHandler(... _args) : void
      {
         _eventDispatcher.dispatchEvent(new LoadInterfaceEvent("checkComplete",_args));
      }
      
      private static function __deleteHandler(... _args) : void
      {
         _eventDispatcher.dispatchEvent(new LoadInterfaceEvent("deleteComplete",_args));
      }
      
      private static function __flashGotoAndPlayHandler(... _args) : void
      {
         _eventDispatcher.dispatchEvent(new LoadInterfaceEvent("flashGotoAndPlay",_args));
      }
      
      private static function __setSoundHandler(... _args) : void
      {
         _eventDispatcher.dispatchEvent(new LoadInterfaceEvent("setSound",_args));
      }
      
      public static function setVersion(_arg_1:int) : void
      {
         if(ExternalInterface.available)
         {
            if(LoadResourceManager.Instance.isMicroClient)
            {
               fscommand("setVersion",_arg_1.toString());
            }
         }
      }
      
      public static function checkResource(_arg_1:int, _arg_2:String, _arg_3:String, _arg_4:Boolean = false) : void
      {
         if(ExternalInterface.available)
         {
            if(LoadResourceManager.Instance.isMicroClient)
            {
               fscommand("checkResource",[_arg_1,_arg_2,_arg_3,_arg_4].join("|"));
            }
         }
      }
      
      public static function deleteResource(_arg_1:String) : void
      {
         if(ExternalInterface.available)
         {
            if(LoadResourceManager.Instance.isMicroClient)
            {
               fscommand("deleteResource",_arg_1);
            }
         }
      }
      
      public static function traceMsg(_arg_1:String) : void
      {
         trace(_arg_1);
         if(ExternalInterface.available)
         {
            if(LoadResourceManager.Instance.isMicroClient)
            {
               fscommand("printTest",_arg_1);
            }
         }
      }
      
      public static function alertAndRestart(_arg_1:String) : void
      {
         trace(_arg_1);
         if(ExternalInterface.available)
         {
            if(LoadResourceManager.Instance.isMicroClient)
            {
               traceMsg("alertAndRestart:" + _arg_1);
               fscommand("alertAndRestart",_arg_1);
            }
         }
      }
      
      public static function setDailyTask(_arg_1:String) : void
      {
         trace("setDailyTask:" + _arg_1);
         if(ExternalInterface.available)
         {
            if(LoadResourceManager.Instance.isMicroClient)
            {
               fscommand("setDailyTask",_arg_1);
            }
         }
      }
      
      public static function setDailyActivity(_arg_1:String) : void
      {
         trace("setDailyActivity:" + _arg_1);
         if(ExternalInterface.available)
         {
            if(LoadResourceManager.Instance.isMicroClient)
            {
               fscommand("setDailyActivity",_arg_1);
            }
         }
      }
      
      public static function setFatigue(_arg_1:String) : void
      {
         trace("setFatigue:" + _arg_1);
         if(ExternalInterface.available)
         {
            if(LoadResourceManager.Instance.isMicroClient)
            {
               traceMsg("setFatigue:" + _arg_1);
               fscommand("setFatigue",_arg_1);
            }
         }
      }
      
      public static function setSound(_arg_1:String) : void
      {
         trace("setSound:" + _arg_1);
         if(ExternalInterface.available)
         {
            if(LoadResourceManager.Instance.isMicroClient)
            {
               traceMsg("setSound:" + _arg_1);
               fscommand("setSound",_arg_1);
            }
         }
      }
   }
}

