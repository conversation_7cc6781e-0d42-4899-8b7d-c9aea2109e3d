package register
{
   import com.pickgliss.utils.ClassUtils;
   import flash.media.Sound;
   
   public class RegisterSoundManager
   {
      
      private static var _instance:RegisterSoundManager;
      
      private var _buttonAudio:Sound;
      
      public function RegisterSoundManager()
      {
         super();
      }
      
      public static function get instance() : RegisterSoundManager
      {
         if(_instance == null)
         {
            _instance = new RegisterSoundManager();
         }
         return _instance;
      }
      
      public function setup() : void
      {
         _buttonAudio = ClassUtils.CreatInstance("asset.train.buttonSound") as Sound;
      }
      
      public function playButton() : void
      {
         if(_buttonAudio)
         {
            _buttonAudio.play();
         }
      }
      
      public function clear() : void
      {
         if(_buttonAudio)
         {
            _buttonAudio.close();
            _buttonAudio = null;
         }
      }
   }
}

