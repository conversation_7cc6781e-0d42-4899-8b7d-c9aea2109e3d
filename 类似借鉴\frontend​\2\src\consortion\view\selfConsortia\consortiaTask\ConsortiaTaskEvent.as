package consortion.view.selfConsortia.consortiaTask
{
   import flash.events.Event;
   
   public class ConsortiaTaskEvent extends Event
   {
      
      public static const GETCONSORTIATASKINFO:String = "getConsortiaTaskInfo";
      
      public static const DELAY_TASK_TIME:String = "Consortia_Delay_Task_Time";
      
      public var value:int;
      
      public function ConsortiaTaskEvent(_arg_1:String, _arg_2:<PERSON><PERSON><PERSON> = false, _arg_3:<PERSON><PERSON><PERSON> = false)
      {
         super(_arg_1,_arg_2,_arg_3);
      }
   }
}

