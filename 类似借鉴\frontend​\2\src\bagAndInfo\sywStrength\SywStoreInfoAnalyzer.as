package bagAndInfo.sywStrength
{
   import com.pickgliss.loader.DataAnalyzer;
   import com.pickgliss.utils.ObjectUtils;
   import road7th.data.DictionaryData;
   
   public class SywStoreInfoAnalyzer extends DataAnalyzer
   {
      
      private var _sywStoreInfoData:DictionaryData;
      
      public function SywStoreInfoAnalyzer(param1:Function)
      {
         super(param1);
      }
      
      override public function analyze(param1:*) : void
      {
         var _loc2_:XMLList = null;
         var _loc3_:int = 0;
         var _loc4_:SywStoreInfoVo = null;
         var _loc5_:XML = new XML(param1);
         this._sywStoreInfoData = new DictionaryData();
         if(_loc5_.@value == "true")
         {
            _loc2_ = _loc5_..Item;
            _loc3_ = 0;
            while(_loc3_ < _loc2_.length())
            {
               _loc4_ = new SywStoreInfoVo();
               ObjectUtils.copyPorpertiesByXML(_loc4_,_loc2_[_loc3_]);
               this._sywStoreInfoData.add(_loc4_.StrengthLevel,_loc4_);
               _loc3_++;
            }
            onAnalyzeComplete();
         }
         else
         {
            message = _loc5_.@message;
            onAnalyzeError();
         }
      }
      
      public function get SywStoreInfoData() : DictionaryData
      {
         return this._sywStoreInfoData;
      }
   }
}

