package com.pickgliss.ui
{
   import com.pickgliss.utils.ClassUtils;
   import com.pickgliss.utils.ObjectUtils;
   import com.pickgliss.utils.StringUtils;
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.display.Sprite;
   import flash.events.TimerEvent;
   import flash.system.ApplicationDomain;
   import flash.utils.Dictionary;
   import flash.utils.Timer;
   
   public final class ComponentFactory
   {
      
      private static var _instance:ComponentFactory;
      
      private static var COMPONENT_COUNTER:int = 1;
      
      private var _allComponents:Dictionary;
      
      private var _model:ComponentModel;
      
      private var _temp:Dictionary;
      
      private var _nameList:Array;
      
      private var _index:int;
      
      public function ComponentFactory(_arg_1:ComponentFactoryEnforcer)
      {
         super();
         this._model = new ComponentModel();
         this._temp = new Dictionary();
         this._nameList = [];
         this._allComponents = new Dictionary();
         ClassUtils.uiSourceDomain = ApplicationDomain.currentDomain;
      }
      
      public static function get Instance() : ComponentFactory
      {
         if(_instance == null)
         {
            _instance = new ComponentFactory(new ComponentFactoryEnforcer());
         }
         return _instance;
      }
      
      public static function parasArgs(_arg_1:String) : Array
      {
         var _local_3:int = 0;
         var _local_2:Array = _arg_1.split(",");
         _local_3 = 0;
         while(_local_3 < _local_2.length)
         {
            StringUtils.trim(_local_2[_local_3]);
            _local_3++;
         }
         return _local_2;
      }
      
      public function creat(_arg_1:String, _arg_2:Array = null) : *
      {
         var _local_3:* = undefined;
         if(Boolean(this._model.getComonentStyle(_arg_1)))
         {
            _local_3 = this.creatComponentByStylename(_arg_1,_arg_2);
         }
         else if(Boolean(this._model.getBitmapSet(_arg_1)) || ClassUtils.classIsBitmapData(_arg_1))
         {
            _local_3 = this.creatBitmap(_arg_1);
         }
         else if(Boolean(this._model.getCustomObjectStyle(_arg_1)))
         {
            _local_3 = this.creatCustomObject(_arg_1,_arg_2);
         }
         else
         {
            _local_3 = ClassUtils.CreatInstance(_arg_1,_arg_2);
         }
         return _local_3;
      }
      
      public function creatBitmap(_arg_1:String) : Bitmap
      {
         var _local_2:* = null;
         var _local_3:* = null;
         var _local_4:XML = this._model.getBitmapSet(_arg_1);
         if(_local_4 == null)
         {
            if(!ClassUtils.uiSourceDomain.hasDefinition(_arg_1))
            {
               throw new Error("Bitmap:" + _arg_1 + " is Not Found!",888);
            }
            _local_2 = ClassUtils.CreatInstance(_arg_1,[0,0]);
            _local_3 = new Bitmap(_local_2);
            this._model.addBitmapSet(_arg_1,new XML("<bitmapData resourceLink=\'" + _arg_1 + "\' width=\'" + _local_3.width + "\' height=\'" + _local_3.height + "\' />"));
         }
         else
         {
            if(_local_4.name() == ComponentSetting.BITMAPDATA_TAG_NAME)
            {
               _local_2 = this.creatBitmapData(_arg_1);
               _local_3 = new Bitmap(_local_2,"auto",String(_local_4.@smoothing) == "true");
            }
            else
            {
               _local_3 = ClassUtils.CreatInstance(_arg_1);
            }
            ObjectUtils.copyPorpertiesByXML(_local_3,_local_4);
         }
         return _local_3;
      }
      
      public function creatNumberSprite(_arg_1:int, _arg_2:String, _arg_3:int = 0) : Sprite
      {
         var _local_6:int = 0;
         var _local_4:* = null;
         var _local_7:Sprite = new Sprite();
         var _local_5:String = String(_arg_1);
         _local_6 = 0;
         while(_local_6 < _local_5.length)
         {
            _local_4 = this.creatBitmap(_arg_2 + _local_5.substr(_local_6,1));
            _local_7.addChild(_local_4);
            _local_4.x = (_local_4.width + _arg_3) * _local_6;
            _local_6++;
         }
         return _local_7;
      }
      
      public function creatBitmapData(_arg_1:String) : BitmapData
      {
         var _local_2:* = null;
         var _local_3:XML = this._model.getBitmapSet(_arg_1);
         if(_local_3 == null)
         {
            return ClassUtils.CreatInstance(_arg_1,[0,0]);
         }
         if(_local_3.name() == ComponentSetting.BITMAPDATA_TAG_NAME)
         {
            _local_2 = ClassUtils.CreatInstance(_arg_1,[int(_local_3.@width),int(_local_3.@height)]);
         }
         else
         {
            _local_2 = ClassUtils.CreatInstance(_arg_1)["btimapData"];
         }
         return _local_2;
      }
      
      public function creatComponentByStylename(_arg_1:String, _arg_2:Array = null) : *
      {
         var _local_3:XML = this.getComponentStyle(_arg_1);
         var _local_5:String = _local_3.@classname;
         var _local_4:* = ClassUtils.CreatInstance(_local_5,_arg_2);
         _local_4.id = this.componentID;
         this._allComponents[_local_4.id] = _local_4;
         if(ClassUtils.classIsComponent(_local_5))
         {
            _local_4.beginChanges();
            ObjectUtils.copyPorpertiesByXML(_local_4,_local_3);
            _local_4.commitChanges();
         }
         else
         {
            ObjectUtils.copyPorpertiesByXML(_local_4,_local_3);
         }
         _local_4["stylename"] = _arg_1;
         return _local_4;
      }
      
      private function getComponentStyle(_arg_1:String) : XML
      {
         var _local_3:* = null;
         var _local_2:XML = this._model.getComonentStyle(_arg_1);
         while(Boolean(_local_2.hasOwnProperty("@parentStyle")))
         {
            _local_3 = this._model.getComonentStyle(String(_local_2.@parentStyle));
            delete _local_2.@parentStyle;
            ObjectUtils.combineXML(_local_2,_local_3);
         }
         return _local_2;
      }
      
      public function getCustomStyle(_arg_1:String) : XML
      {
         var _local_3:* = null;
         var _local_2:XML = this._model.getCustomObjectStyle(_arg_1);
         if(_local_2 == null)
         {
            return null;
         }
         while(Boolean(_local_2) && Boolean(_local_2.hasOwnProperty("@parentStyle")))
         {
            _local_3 = this._model.getCustomObjectStyle(String(_local_2.@parentStyle));
            delete _local_2.@parentStyle;
            ObjectUtils.combineXML(_local_2,_local_3);
         }
         return _local_2;
      }
      
      public function creatCustomObject(_arg_1:String, _arg_2:Array = null) : *
      {
         var _local_3:XML = this.getCustomStyle(_arg_1);
         var _local_4:String = _local_3.@classname;
         var _local_5:* = ClassUtils.CreatInstance(_local_4,_arg_2);
         ObjectUtils.copyPorpertiesByXML(_local_5,_local_3);
         return _local_5;
      }
      
      public function getComponentByID(_arg_1:int) : *
      {
         return this._allComponents[_arg_1];
      }
      
      public function checkAllComponentDispose(_arg_1:Array) : void
      {
         var _local_7:int = 0;
         var _local_2:XML = null;
         var _local_4:* = undefined;
         var _local_6:Dictionary = this._model.allComponentStyle;
         var _local_3:int = int(_arg_1.length);
         var _local_5:int = 1;
         _local_7 = 0;
         while(_local_7 < _arg_1.length)
         {
            for each(_local_2 in _local_6)
            {
               if(_local_2.@componentModule != null && _local_2.@componentModule == _arg_1[_local_7])
               {
                  for each(_local_4 in this._allComponents)
                  {
                     if(_local_4 && _local_4.stylename == _local_2.@stylename)
                     {
                        trace(_local_5.toString() + ". " + String(_local_2.@stylename) + " =================> 可能未释放...请注意!");
                        _local_5++;
                     }
                  }
               }
            }
            _local_7++;
         }
      }
      
      public function removeComponent(_arg_1:int) : void
      {
         delete this._allComponents[_arg_1];
      }
      
      public function creatFrameFilters(_arg_1:String) : Array
      {
         var _local_4:int = 0;
         var _local_2:Array = parasArgs(_arg_1);
         var _local_3:Array = [];
         _local_4 = 0;
         while(_local_4 < _local_2.length)
         {
            if(_local_2[_local_4] == "null")
            {
               _local_3.push(null);
            }
            else
            {
               _local_3.push(this.creatFilters(_local_2[_local_4]));
            }
            _local_4++;
         }
         return _local_3;
      }
      
      public function creatFilters(_arg_1:String) : Array
      {
         var _local_4:int = 0;
         var _local_3:Array = _arg_1.split("|");
         var _local_2:Array = [];
         _local_4 = 0;
         while(_local_4 < _local_3.length)
         {
            _local_2.push(ComponentFactory.Instance.model.getSet(_local_3[_local_4]));
            _local_4++;
         }
         return _local_2;
      }
      
      public function get model() : ComponentModel
      {
         return this._model;
      }
      
      public function setup(_arg_1:XML) : void
      {
         var _local_2:String = String(_arg_1.@name);
         this._nameList.push(_local_2);
         this._temp[_local_2] = _arg_1;
      }
      
      public function waitAnalysis() : void
      {
         var _local_1:Timer = new Timer(100);
         _local_1.addEventListener("timer",this.__onAnalysisTimer);
         _local_1.start();
      }
      
      protected function __onAnalysisTimer(_arg_1:TimerEvent) : void
      {
         var _local_3:int = 0;
         var _local_2:* = null;
         _local_3 = 0;
         while(_local_3 < 40)
         {
            if(this._index >= this._nameList.length)
            {
               _local_2 = _arg_1.currentTarget as Timer;
               _local_2.stop();
               _local_2.removeEventListener("timer",this.__onAnalysisTimer);
               return;
            }
            this.analysisXML(this._nameList[this._index]);
            ++this._index;
            _local_3++;
         }
      }
      
      public function analysisXML(_arg_1:String) : void
      {
         var _local_2:XML = this._temp[_arg_1] as XML;
         if(Boolean(_local_2))
         {
            this._model.addComponentSet(_local_2);
            delete this._temp[_arg_1];
         }
      }
      
      public function get componentID() : int
      {
         return COMPONENT_COUNTER++;
      }
   }
}

class ComponentFactoryEnforcer
{
   
   public function ComponentFactoryEnforcer()
   {
      super();
   }
}
