package activity.signin.analyzer
{
   import activity.signin.data.SignInRewardInfo;
   import com.pickgliss.loader.DataAnalyzer;
   import com.pickgliss.utils.ObjectUtils;
   import road7th.data.DictionaryData;
   
   public class SignInRewardTemplateAnalyzer extends DataAnalyzer
   {
      
      private var _data:DictionaryData;
      
      private var _maxID:int;
      
      public function SignInRewardTemplateAnalyzer(_arg_1:Function)
      {
         super(_arg_1);
      }
      
      override public function analyze(_arg_1:*) : void
      {
         var _local_5:int = 0;
         var _local_3:* = null;
         var _local_4:* = null;
         var _local_2:XML = new XML(_arg_1);
         this._data = new DictionaryData();
         if(_local_2.@value == "true")
         {
            _local_3 = _local_2..Item;
            _local_5 = 0;
            while(_local_5 < _local_3.length())
            {
               _local_4 = new SignInRewardInfo();
               ObjectUtils.copyPorpertiesByXML(_local_4,_local_3[_local_5]);
               _local_4.isBind = String(_local_3[_local_5].@IsBind) == "1";
               this._maxID = this._maxID > _local_4.ID ? this._maxID : int(_local_4.ID);
               this._data.add(_local_4.ID,_local_4);
               _local_5++;
            }
            onAnalyzeComplete();
         }
         else
         {
            message = _local_2.@message;
            onAnalyzeError();
            onAnalyzeComplete();
         }
         this._data = null;
      }
      
      public function get data() : DictionaryData
      {
         return this._data;
      }
      
      public function get maxID() : int
      {
         return this._maxID;
      }
   }
}

