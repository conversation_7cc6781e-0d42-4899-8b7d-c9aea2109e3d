package consortion.view.selfConsortia.consortiaTask
{
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.LayerManager;
   import ddt.events.PkgEvent;
   import ddt.manager.LanguageMgr;
   import ddt.manager.MessageTipManager;
   import ddt.manager.PlayerManager;
   import ddt.manager.SocketManager;
   import flash.events.EventDispatcher;
   import flash.events.IEventDispatcher;
   import road7th.comm.PackageIn;
   
   public class ConsortiaTaskModel extends EventDispatcher
   {
      
      public static const RELEASE_TASK:int = 0;
      
      public static const RESET_TASK:int = 1;
      
      public static const SUMBIT_TASK:int = 2;
      
      public static const GET_TASKINFO:int = 3;
      
      public static const UPDATE_TASKINFO:int = 4;
      
      public static const SUCCESS_FAIL:int = 5;
      
      public static const DELAY_TIME:int = 6;
      
      public var taskInfo:ConsortiaTaskInfo;
      
      public var isHaveTask_noRelease:Boolean = false;
      
      public var lockNum:int = 0;
      
      public function ConsortiaTaskModel(_arg_1:IEventDispatcher = null)
      {
         super(_arg_1);
         this.initEvents();
      }
      
      private function initEvents() : void
      {
         SocketManager.Instance.addEventListener(PkgEvent.format(129,22),this.__releaseTaskCallBack);
      }
      
      private function __releaseTaskCallBack(_arg_1:PkgEvent) : void
      {
         var _local_4:Boolean = false;
         var _local_9:Boolean = false;
         var _local_5:int = 0;
         var _local_22:int = 0;
         var _local_18:int = 0;
         var _local_21:int = 0;
         var _local_12:int = 0;
         var _local_3:int = 0;
         var _local_2:int = 0;
         var _local_13:int = 0;
         var _local_15:int = 0;
         var _local_7:int = 0;
         var _local_10:int = 0;
         var _local_17:int = 0;
         var _local_16:int = 0;
         var _local_20:* = null;
         var _local_19:* = null;
         var _local_14:* = null;
         var _local_8:PackageIn = _arg_1.pkg as PackageIn;
         var _local_11:int = _local_8.readByte();
         if(_local_11 == 2)
         {
            _local_4 = _local_8.readBoolean();
            if(!_local_4)
            {
               this.taskInfo = null;
            }
         }
         else if(_local_11 == 5)
         {
            _local_9 = _local_8.readBoolean();
            this.taskInfo = null;
         }
         else if(_local_11 == 4)
         {
            _local_5 = _local_8.readInt();
            _local_22 = _local_8.readInt();
            _local_18 = _local_8.readInt();
            if(this.taskInfo != null)
            {
               this.taskInfo.updateItemData(_local_5,_local_22,_local_18);
            }
         }
         else if(_local_11 == 0 || _local_11 == 1)
         {
            _local_21 = _local_8.readInt();
            this.taskInfo = new ConsortiaTaskInfo();
            _local_12 = 0;
            while(_local_12 < _local_21)
            {
               _local_3 = _local_8.readInt();
               _local_20 = _local_8.readUTF();
               this.taskInfo.addItemData(_local_3,_local_20);
               _local_12++;
            }
         }
         else
         {
            if(_local_11 == 6)
            {
               _local_19 = new ConsortiaTaskEvent("Consortia_Delay_Task_Time");
               _local_19.value = _local_8.readInt();
               dispatchEvent(_local_19);
               PlayerManager.Instance.Self.consortiaInfo.Riches = _local_8.readInt();
               return;
            }
            _local_2 = _local_8.readInt();
            if(_local_2 > 0)
            {
               this.taskInfo = new ConsortiaTaskInfo();
               _local_13 = 0;
               while(_local_13 < _local_2)
               {
                  _local_15 = _local_8.readInt();
                  _local_7 = _local_8.readInt();
                  _local_14 = _local_8.readUTF();
                  _local_10 = _local_8.readInt();
                  _local_17 = _local_8.readInt();
                  _local_16 = _local_8.readInt();
                  this.taskInfo.addItemData(_local_15,_local_14,_local_7,_local_10,_local_17,_local_16);
                  _local_13++;
               }
               this.taskInfo.sortItem();
               this.taskInfo.exp = _local_8.readInt();
               this.taskInfo.offer = _local_8.readInt();
               this.taskInfo.contribution = _local_8.readInt();
               this.taskInfo.riches = _local_8.readInt();
               this.taskInfo.buffID = _local_8.readInt();
               this.taskInfo.beginTime = _local_8.readDate();
               this.taskInfo.time = _local_8.readInt();
               this.taskInfo.level = _local_8.readInt();
            }
            else if(_local_2 == -1)
            {
               this.taskInfo = null;
               this.isHaveTask_noRelease = true;
            }
            else
            {
               this.taskInfo = null;
            }
         }
         var _local_6:ConsortiaTaskEvent = new ConsortiaTaskEvent("getConsortiaTaskInfo");
         _local_6.value = _local_11;
         dispatchEvent(_local_6);
      }
      
      public function showReleaseFrame() : void
      {
         if(this.taskInfo != null)
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("consortia.task.released"));
            return;
         }
         if(this.isHaveTask_noRelease)
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("consortia.task.havetaskNoRelease"));
         }
         var _local_1:ConsortiaReleaseTaskFrame = ComponentFactory.Instance.creatComponentByStylename("ConsortiaReleaseTaskFrame");
         LayerManager.Instance.addToLayer(_local_1,3,true,1);
      }
   }
}

