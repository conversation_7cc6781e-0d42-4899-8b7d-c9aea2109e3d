package beadSystem.tips
{
   import beadSystem.views.BeadUpgradeTipView;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.data.goods.InventoryItemInfo;
   import ddt.data.goods.ItemTemplateInfo;
   import ddt.manager.BeadTemplateManager;
   import ddt.view.tips.GoodTip;
   import ddt.view.tips.GoodTipInfo;
   
   public class BeadUpgradeTip extends GoodTip
   {
      
      private var _upgradeBeadTip:BeadUpgradeTipView;
      
      public function BeadUpgradeTip()
      {
         super();
      }
      
      override protected function init() : void
      {
         super.init();
      }
      
      override protected function addChildren() : void
      {
         super.addChildren();
      }
      
      override public function set tipData(_arg_1:Object) : void
      {
         super.tipData = _arg_1;
         this.beadUpgradeTip(_arg_1 as GoodTipInfo);
      }
      
      override public function showTip(_arg_1:ItemTemplateInfo, _arg_2:Boolean = false) : void
      {
         super.showTip(_arg_1,_arg_2);
      }
      
      private function beadUpgradeTip(_arg_1:GoodTipInfo) : void
      {
         var _local_3:InventoryItemInfo = null;
         var _local_2:GoodTipInfo = null;
         var _local_4:InventoryItemInfo = null;
         if(Boolean(_arg_1))
         {
            _local_4 = _arg_1.itemInfo as InventoryItemInfo;
         }
         if(Boolean(_local_4) && _local_4.Hole1 < 21)
         {
            _local_2 = new GoodTipInfo();
            _local_3 = new InventoryItemInfo();
            ObjectUtils.copyProperties(_local_3,_local_4);
            _local_3.Hole1 += 1;
            _local_3.TemplateID = BeadTemplateManager.Instance.GetBeadTemplateIDByLv(_local_3.Hole1,_local_4.TemplateID);
            _local_2.itemInfo = _local_3;
            _local_2.beadName = _local_3.Name + "-" + BeadTemplateManager.Instance.GetBeadInfobyID(_local_3.TemplateID).Name + "Lv" + _local_3.Hole1;
            if(!this._upgradeBeadTip)
            {
               this._upgradeBeadTip = new BeadUpgradeTipView();
            }
            this._upgradeBeadTip.x = _tipbackgound.x + _tipbackgound.width + 35;
            if(!this.contains(this._upgradeBeadTip))
            {
               addChild(this._upgradeBeadTip);
            }
            this._upgradeBeadTip.tipData = _local_2;
         }
         else
         {
            if(Boolean(this._upgradeBeadTip))
            {
               ObjectUtils.disposeObject(this._upgradeBeadTip);
            }
            this._upgradeBeadTip = null;
         }
      }
      
      override public function dispose() : void
      {
         super.dispose();
         if(Boolean(this._upgradeBeadTip))
         {
            ObjectUtils.disposeObject(this._upgradeBeadTip);
         }
         this._upgradeBeadTip = null;
         if(Boolean(parent))
         {
            parent.removeChild(this);
         }
      }
   }
}

