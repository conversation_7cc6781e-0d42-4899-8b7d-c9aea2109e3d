package com.greensock
{
   import com.greensock.core.PropTween;
   import com.greensock.core.SimpleTimeline;
   import com.greensock.core.TweenCore;
   import flash.display.Shape;
   import flash.events.Event;
   import flash.utils.Dictionary;
   import flash.utils.getTimer;
   
   public class TweenLite extends TweenCore
   {
      
      public static var onPluginEvent:Function;
      
      public static var overwriteManager:Object;
      
      public static var rootFrame:Number;
      
      public static var rootTimeline:SimpleTimeline;
      
      public static var rootFramesTimeline:SimpleTimeline;
      
      public static const version:Number = 11.62;
      
      public static var plugins:Object = {};
      
      public static var fastEaseLookup:Dictionary = new Dictionary(false);
      
      public static var killDelayedCallsTo:Function = TweenLite.killTweensOf;
      
      public static var defaultEase:Function = TweenLite.easeOut;
      
      public static var masterList:Dictionary = new Dictionary(false);
      
      private static var _shape:Shape = new Shape();
      
      protected static var _reservedProps:Object = {
         "ease":1,
         "delay":1,
         "overwrite":1,
         "onComplete":1,
         "onCompleteParams":1,
         "useFrames":1,
         "runBackwards":1,
         "startAt":1,
         "onUpdate":1,
         "onUpdateParams":1,
         "onStart":1,
         "onStartParams":1,
         "onInit":1,
         "onInitParams":1,
         "onReverseComplete":1,
         "onReverseCompleteParams":1,
         "onRepeat":1,
         "onRepeatParams":1,
         "proxiedEase":1,
         "easeParams":1,
         "yoyo":1,
         "onCompleteListener":1,
         "onUpdateListener":1,
         "onStartListener":1,
         "onReverseCompleteListener":1,
         "onRepeatListener":1,
         "orientToBezier":1,
         "timeScale":1,
         "immediateRender":1,
         "repeat":1,
         "repeatDelay":1,
         "timeline":1,
         "data":1,
         "paused":1
      };
      
      public var target:Object;
      
      public var propTweenLookup:Object;
      
      public var ratio:Number = 0;
      
      public var cachedPT1:PropTween;
      
      protected var _ease:Function;
      
      protected var _overwrite:int;
      
      protected var _overwrittenProps:Object;
      
      protected var _hasPlugins:Boolean;
      
      protected var _notifyPluginsOfEnabled:Boolean;
      
      public function TweenLite(_arg_1:Object, _arg_2:Number, _arg_3:Object)
      {
         var _local_4:TweenLite = null;
         super(_arg_2,_arg_3);
         if(_arg_1 == null)
         {
            throw new Error("Cannot tween a null object.");
         }
         this.target = _arg_1;
         if(this.target is TweenCore && Boolean(this.vars.timeScale))
         {
            this.cachedTimeScale = 1;
         }
         this.propTweenLookup = {};
         this._ease = defaultEase;
         this._overwrite = _arg_3.overwrite <= -1 || !overwriteManager.enabled && _arg_3.overwrite > 1 ? int(overwriteManager.mode) : int(_arg_3.overwrite);
         var _local_5:Array = masterList[_arg_1];
         if(!_local_5)
         {
            masterList[_arg_1] = [this];
         }
         else if(this._overwrite == 1)
         {
            for each(_local_4 in _local_5)
            {
               if(!_local_4.gc)
               {
                  _local_4.setEnabled(false,false);
               }
            }
            masterList[_arg_1] = [this];
         }
         else
         {
            _local_5[_local_5.length] = this;
         }
         if(this.active || Boolean(this.vars.immediateRender))
         {
            this.renderTime(0,false,true);
         }
      }
      
      public static function initClass() : void
      {
         rootFrame = 0;
         rootTimeline = new SimpleTimeline(null);
         rootFramesTimeline = new SimpleTimeline(null);
         rootTimeline.cachedStartTime = getTimer() * 0.001;
         rootFramesTimeline.cachedStartTime = rootFrame;
         rootTimeline.autoRemoveChildren = true;
         rootFramesTimeline.autoRemoveChildren = true;
         _shape.addEventListener("enterFrame",updateAll,false,0,true);
         if(overwriteManager == null)
         {
            overwriteManager = {
               "mode":1,
               "enabled":false
            };
         }
      }
      
      public static function to(_arg_1:Object, _arg_2:Number, _arg_3:Object) : TweenLite
      {
         return new TweenLite(_arg_1,_arg_2,_arg_3);
      }
      
      public static function from(_arg_1:Object, _arg_2:Number, _arg_3:Object) : TweenLite
      {
         _arg_3.runBackwards = true;
         if(!("immediateRender" in _arg_3))
         {
            _arg_3.immediateRender = true;
         }
         return new TweenLite(_arg_1,_arg_2,_arg_3);
      }
      
      public static function delayedCall(_arg_1:Number, _arg_2:Function, _arg_3:Array = null, _arg_4:Boolean = false) : TweenLite
      {
         return new TweenLite(_arg_2,0,{
            "delay":_arg_1,
            "onComplete":_arg_2,
            "onCompleteParams":_arg_3,
            "immediateRender":false,
            "useFrames":_arg_4,
            "overwrite":0
         });
      }
      
      protected static function updateAll(_arg_1:Event = null) : void
      {
         var _local_5:int = 0;
         var _local_3:* = null;
         var _local_2:* = null;
         var _local_4:* = null;
         rootTimeline.renderTime((getTimer() * 0.001 - rootTimeline.cachedStartTime) * rootTimeline.cachedTimeScale,false,false);
         rootFrame += 1;
         rootFramesTimeline.renderTime((rootFrame - rootFramesTimeline.cachedStartTime) * rootFramesTimeline.cachedTimeScale,false,false);
         if(!(rootFrame % 60))
         {
            _local_4 = masterList;
            for(_local_3 in _local_4)
            {
               _local_2 = _local_4[_local_3];
               _local_5 = int(_local_2.length);
               while(--_local_5 > -1)
               {
                  if(TweenLite(_local_2[_local_5]).gc)
                  {
                     _local_2.splice(_local_5,1);
                  }
               }
               if(_local_2.length == 0)
               {
                  delete _local_4[_local_3];
               }
            }
         }
      }
      
      public static function killTweensOf(_arg_1:Object, _arg_2:Boolean = false, _arg_3:Object = null) : void
      {
         var _local_6:int = 0;
         var _local_5:* = null;
         var _local_4:* = null;
         if(_arg_1 in masterList)
         {
            _local_5 = masterList[_arg_1];
            _local_6 = int(_local_5.length);
            while(--_local_6 > -1)
            {
               _local_4 = _local_5[_local_6];
               if(!_local_4.gc)
               {
                  if(_arg_2)
                  {
                     _local_4.complete(false,false);
                  }
                  if(_arg_3 != null)
                  {
                     _local_4.killVars(_arg_3);
                  }
                  if(_arg_3 == null || _local_4.cachedPT1 == null && _local_4.initted)
                  {
                     _local_4.setEnabled(false,false);
                  }
               }
            }
            if(_arg_3 == null)
            {
               delete masterList[_arg_1];
            }
         }
      }
      
      protected static function easeOut(_arg_1:Number, _arg_2:Number, _arg_3:Number, _arg_4:Number) : Number
      {
         _arg_1 = 1 - _arg_1 / _arg_4;
         return 1 - _arg_1 * _arg_1;
      }
      
      protected function init() : void
      {
         var _local_6:int = 0;
         var _local_5:* = undefined;
         var _local_1:Boolean = false;
         var _local_4:* = null;
         var _local_2:* = null;
         var _local_3:* = null;
         if(Boolean(this.vars.onInit))
         {
            this.vars.onInit.apply(null,this.vars.onInitParams);
         }
         if(typeof this.vars.ease == "function")
         {
            this._ease = this.vars.ease;
         }
         if(Boolean(this.vars.easeParams))
         {
            this.vars.proxiedEase = this._ease;
            this._ease = this.easeProxy;
         }
         this.cachedPT1 = null;
         this.propTweenLookup = {};
         for(_local_2 in this.vars)
         {
            if(!(_local_2 in _reservedProps && !(_local_2 == "timeScale" && this.target is TweenCore)))
            {
               if(_local_2 in plugins && Boolean((_local_5 = new (plugins[_local_2] as Class)()).onInitTween(this.target,this.vars[_local_2],this)))
               {
                  this.cachedPT1 = new PropTween(_local_5,"changeFactor",0,1,_local_5.overwriteProps.length == 1 ? _local_5.overwriteProps[0] : "_MULTIPLE_",true,this.cachedPT1);
                  if(this.cachedPT1.name == "_MULTIPLE_")
                  {
                     _local_6 = int(_local_5.overwriteProps.length);
                     while(--_local_6 > -1)
                     {
                        this.propTweenLookup[_local_5.overwriteProps[_local_6]] = this.cachedPT1;
                     }
                  }
                  else
                  {
                     this.propTweenLookup[this.cachedPT1.name] = this.cachedPT1;
                  }
                  if(Boolean(_local_5.priority))
                  {
                     this.cachedPT1.priority = _local_5.priority;
                     _local_1 = true;
                  }
                  if(Boolean(_local_5.onDisable) || Boolean(_local_5.onEnable))
                  {
                     this._notifyPluginsOfEnabled = true;
                  }
                  this._hasPlugins = true;
               }
               else
               {
                  this.cachedPT1 = new PropTween(this.target,_local_2,this.target[_local_2],typeof this.vars[_local_2] == "number" ? this.vars[_local_2] - this.target[_local_2] : Number(this.vars[_local_2]),_local_2,false,this.cachedPT1);
                  this.propTweenLookup[_local_2] = this.cachedPT1;
               }
            }
         }
         if(_local_1)
         {
            onPluginEvent("onInitAllProps",this);
         }
         if(Boolean(this.vars.runBackwards))
         {
            _local_3 = this.cachedPT1;
            while(_local_3)
            {
               _local_3.start += _local_3.change;
               _local_3.change = -_local_3.change;
               _local_3 = _local_3.nextNode;
            }
         }
         _hasUpdate = this.vars.onUpdate != null;
         if(Boolean(this._overwrittenProps))
         {
            this.killVars(this._overwrittenProps);
            if(this.cachedPT1 == null)
            {
               this.setEnabled(false,false);
            }
         }
         if(this._overwrite > 1 && this.cachedPT1 && (_local_4 = masterList[this.target]) && _local_4.length > 1)
         {
            if(Boolean(overwriteManager.manageOverwrites(this,this.propTweenLookup,_local_4,this._overwrite)))
            {
               this.init();
            }
         }
         this.initted = true;
      }
      
      override public function renderTime(_arg_1:Number, _arg_2:Boolean = false, _arg_3:Boolean = false) : void
      {
         var _local_4:Boolean = false;
         var _local_7:* = undefined;
         var _local_6:Number = this.cachedTime;
         if(_arg_1 >= this.cachedDuration)
         {
            _local_7 = this.cachedDuration;
            this.cachedTime = _local_7;
            this.cachedTotalTime = _local_7;
            this.ratio = 1;
            _local_4 = true;
            if(this.cachedDuration == 0)
            {
               if((_arg_1 == 0 || _rawPrevTime < 0) && _rawPrevTime != _arg_1)
               {
                  _arg_3 = true;
               }
               _rawPrevTime = _arg_1;
            }
         }
         else if(_arg_1 <= 0)
         {
            _local_7 = 0;
            this.ratio = _local_7;
            this.cachedTime = _local_7;
            this.cachedTotalTime = _local_7;
            if(_arg_1 < 0)
            {
               this.active = false;
               if(this.cachedDuration == 0)
               {
                  if(_rawPrevTime >= 0)
                  {
                     _arg_3 = true;
                     _local_4 = true;
                  }
                  _rawPrevTime = _arg_1;
               }
            }
            if(this.cachedReversed && _local_6 != 0)
            {
               _local_4 = true;
            }
         }
         else
         {
            _local_7 = _arg_1;
            this.cachedTime = _local_7;
            this.cachedTotalTime = _local_7;
            this.ratio = this._ease(_arg_1,0,1,this.cachedDuration);
         }
         if(this.cachedTime == _local_6 && !_arg_3)
         {
            return;
         }
         if(!this.initted)
         {
            this.init();
            if(!_local_4 && Boolean(this.cachedTime))
            {
               this.ratio = this._ease(this.cachedTime,0,1,this.cachedDuration);
            }
         }
         if(!this.active && !this.cachedPaused)
         {
            this.active = true;
         }
         if(_local_6 == 0 && this.vars.onStart && (this.cachedTime != 0 || this.cachedDuration == 0) && !_arg_2)
         {
            this.vars.onStart.apply(null,this.vars.onStartParams);
         }
         var _local_5:PropTween = this.cachedPT1;
         while(Boolean(_local_5))
         {
            _local_5.target[_local_5.property] = _local_5.start + this.ratio * _local_5.change;
            _local_5 = _local_5.nextNode;
         }
         if(_hasUpdate && !_arg_2)
         {
            this.vars.onUpdate.apply(null,this.vars.onUpdateParams);
         }
         if(_local_4 && !this.gc)
         {
            if(this._hasPlugins && Boolean(this.cachedPT1))
            {
               onPluginEvent("onComplete",this);
            }
            complete(true,_arg_2);
         }
      }
      
      public function killVars(_arg_1:Object, _arg_2:Boolean = true) : Boolean
      {
         var _local_5:Boolean = false;
         var _local_4:* = null;
         var _local_3:* = null;
         if(this._overwrittenProps == null)
         {
            this._overwrittenProps = {};
         }
         for(_local_3 in _arg_1)
         {
            if(_local_3 in this.propTweenLookup)
            {
               _local_4 = this.propTweenLookup[_local_3];
               if(Boolean(_local_4.isPlugin) && _local_4.name == "_MULTIPLE_")
               {
                  _local_4.target.killProps(_arg_1);
                  if(_local_4.target.overwriteProps.length == 0)
                  {
                     _local_4.name = "";
                  }
                  if(_local_3 != _local_4.target.propName || _local_4.name == "")
                  {
                     delete this.propTweenLookup[_local_3];
                  }
               }
               if(_local_4.name != "_MULTIPLE_")
               {
                  if(Boolean(_local_4.nextNode))
                  {
                     _local_4.nextNode.prevNode = _local_4.prevNode;
                  }
                  if(Boolean(_local_4.prevNode))
                  {
                     _local_4.prevNode.nextNode = _local_4.nextNode;
                  }
                  else if(this.cachedPT1 == _local_4)
                  {
                     this.cachedPT1 = _local_4.nextNode;
                  }
                  if(Boolean(_local_4.isPlugin) && Boolean(_local_4.target.onDisable))
                  {
                     _local_4.target.onDisable();
                     if(Boolean(_local_4.target.activeDisable))
                     {
                        _local_5 = true;
                     }
                  }
                  delete this.propTweenLookup[_local_3];
               }
            }
            if(_arg_2 && _arg_1 != this._overwrittenProps)
            {
               this._overwrittenProps[_local_3] = 1;
            }
         }
         return _local_5;
      }
      
      override public function invalidate() : void
      {
         if(this._notifyPluginsOfEnabled && Boolean(this.cachedPT1))
         {
            onPluginEvent("onDisable",this);
         }
         this.cachedPT1 = null;
         this._overwrittenProps = null;
         var _local_1:* = this._notifyPluginsOfEnabled = false;
         this.active = _local_1;
         this.initted = _local_1;
         _hasUpdate = _local_1;
         this.propTweenLookup = {};
      }
      
      override public function setEnabled(_arg_1:Boolean, _arg_2:Boolean = false) : Boolean
      {
         var _local_3:* = null;
         if(_arg_1)
         {
            _local_3 = TweenLite.masterList[this.target];
            if(!_local_3)
            {
               TweenLite.masterList[this.target] = [this];
            }
            else
            {
               _local_3[_local_3.length] = this;
            }
         }
         super.setEnabled(_arg_1,_arg_2);
         if(this._notifyPluginsOfEnabled && Boolean(this.cachedPT1))
         {
            return onPluginEvent(_arg_1 ? "onEnable" : "onDisable",this);
         }
         return false;
      }
      
      protected function easeProxy(_arg_1:Number, _arg_2:Number, _arg_3:Number, _arg_4:Number) : Number
      {
         return this.vars.proxiedEase.apply(null,arguments.concat(this.vars.easeParams));
      }
   }
}

