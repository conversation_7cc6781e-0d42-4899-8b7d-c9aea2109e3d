using System;
using System.Collections.Generic;
using System.Linq;
using EntityDatabase.PlayerModels;
using EntityDatabase.ServerModels;
using Game.Server.GameObjects;

namespace Game.Server.ForceRelic
{
	// Token: 0x02000C34 RID: 3124
	public static class ForceRelicMgr
	{
		// Token: 0x0600581A RID: 22554 RVA: 0x0023DB94 File Offset: 0x0023BD94
		public static bool Init()
		{
			bool flag;
			try
			{
				ForceRelicMgr.advanceTemplate = ForceRelicMgr.sd.TS_Relic_AdvanceTemplate.ToList<TS_Relic_AdvanceTemplate>();
				ForceRelicMgr.degreeTemplate = ForceRelicMgr.sd.TS_Relic_DegreeTemplate.ToList<TS_Relic_DegreeTemplate>();
				ForceRelicMgr.itemTemplate = ForceRelicMgr.sd.TS_Relic_ItemTemplate.ToList<TS_Relic_ItemTemplate>();
				ForceRelicMgr.upgradeTemplate = ForceRelicMgr.sd.TS_Relic_UpgradeTemplate.ToList<TS_Relic_UpgradeTemplate>();
				flag = true;
			}
			catch (Exception ex)
			{
				Console.WriteLine(ex.Message);
				flag = false;
			}
			return flag;
		}

		// Token: 0x0600581B RID: 22555 RVA: 0x0023DC1C File Offset: 0x0023BE1C
		public static TS_Relic_ItemTemplate GetRelicTemplateByRelicID(int RelicID)
		{
			return ForceRelicMgr.itemTemplate.FirstOrDefault((TS_Relic_ItemTemplate p) => p.RelicID == RelicID);
		}

		// Token: 0x0600581C RID: 22556 RVA: 0x0023DC54 File Offset: 0x0023BE54
		public static TS_Relic_AdvanceTemplate GetAdvanceInfo(int RelicID, int Level)
		{
			return ForceRelicMgr.advanceTemplate.FirstOrDefault((TS_Relic_AdvanceTemplate p) => p.RelicID == RelicID && p.Level == Level);
		}

		// Token: 0x0600581D RID: 22557 RVA: 0x0023DC90 File Offset: 0x0023BE90
		public static TS_Relic_DegreeTemplate GetDegreeInfo(int Quality, int Level)
		{
			return ForceRelicMgr.degreeTemplate.FirstOrDefault((TS_Relic_DegreeTemplate p) => p.Quality == Quality && p.Level == Level);
		}

		// Token: 0x0600581E RID: 22558 RVA: 0x0023DCCC File Offset: 0x0023BECC
		public static TS_Relic_UpgradeTemplate GetUpgradeInfo(int RelicID, int Level)
		{
			return ForceRelicMgr.upgradeTemplate.FirstOrDefault((TS_Relic_UpgradeTemplate p) => p.RelicID == RelicID && p.Level == Level);
		}

		// Token: 0x0600581F RID: 22559 RVA: 0x0023DD08 File Offset: 0x0023BF08
		public static void GetRelicTempTypeByBase(GamePlayer player, ref int att, ref int def, ref int agi, ref int luck, ref int hp, ref int magicAtt, ref int magicDef)
		{
			string relicHoleArr = player.PlayerCharacter.RelicInfo.RelicHoleArr;
			bool flag = relicHoleArr != null;
			if (flag)
			{
				string[] array = relicHoleArr.Split(new char[] { ',' });
				string[] array2 = array;
				for (int i = 0; i < array2.Length; i++)
				{
					string text = array2[i];
					int id = int.Parse(text);
					bool flag2 = id == 0;
					if (flag2)
					{
						break;
					}
					TS_Relic_ItemTemplate relicTemplateByRelicID = ForceRelicMgr.GetRelicTemplateByRelicID(id);
					bool flag3 = relicTemplateByRelicID != null;
					if (flag3)
					{
						att += relicTemplateByRelicID.CommonProperty;
						def += relicTemplateByRelicID.CommonProperty;
						agi += relicTemplateByRelicID.CommonProperty;
						luck += relicTemplateByRelicID.CommonProperty;
					}
					Sys_Users_RelicEquipInfo sys_Users_RelicEquipInfo = player.PlayerCharacter.RelicEquipInfo.FirstOrDefault((Sys_Users_RelicEquipInfo p) => p.RelicID == id && p.UserID == player.PlayerId);
					bool flag4 = sys_Users_RelicEquipInfo == null;
					if (flag4)
					{
						break;
					}
					string[] array3 = sys_Users_RelicEquipInfo.ProArr.Split(new char[] { '|' });
					foreach (string text2 in array3)
					{
						int num = int.Parse(text2.Split(new char[] { ',' })[0]);
						int num2 = int.Parse(text2.Split(new char[] { ',' })[1]);
						switch (num)
						{
						case 1:
							att += num2;
							break;
						case 2:
							def += num2;
							break;
						case 3:
							agi += num2;
							break;
						case 4:
							luck += num2;
							break;
						case 5:
							magicAtt += num2;
							break;
						case 6:
							magicDef += num2;
							break;
						case 9:
							hp += num2;
							break;
						}
					}
					TS_Relic_UpgradeTemplate upgradeInfo = ForceRelicMgr.GetUpgradeInfo(id, sys_Users_RelicEquipInfo.Level);
					bool flag5 = upgradeInfo == null;
					if (flag5)
					{
						return;
					}
					switch (upgradeInfo.Type)
					{
					case 1:
						att += upgradeInfo.Data;
						break;
					case 2:
						def += upgradeInfo.Data;
						break;
					case 3:
						agi += upgradeInfo.Data;
						break;
					case 4:
						luck += upgradeInfo.Data;
						break;
					case 5:
						magicAtt += upgradeInfo.Data;
						break;
					case 6:
						magicDef += upgradeInfo.Data;
						break;
					case 9:
						hp += upgradeInfo.Data;
						break;
					}
				}
			}
			string[] array5 = player.PlayerCharacter.RelicInfo.ManualInfo.Split(new char[] { '|' });
			foreach (string text3 in array5)
			{
				int num3 = int.Parse(text3.Split(new char[] { ',' })[0]);
				int num4 = int.Parse(text3.Split(new char[] { ',' })[2]);
				TS_Relic_DegreeTemplate degreeInfo = ForceRelicMgr.GetDegreeInfo(num3, num4);
				bool flag6 = degreeInfo != null;
				if (flag6)
				{
					att += degreeInfo.Attack;
					def += degreeInfo.Attack;
					agi += degreeInfo.Attack;
					luck += degreeInfo.Attack;
					hp += degreeInfo.Attack;
					magicAtt += degreeInfo.Attack;
					magicDef += degreeInfo.Attack;
				}
			}
		}

		// Token: 0x06005820 RID: 22560 RVA: 0x0023E0F4 File Offset: 0x0023C2F4
		public static void GetRelicTempTypeByDamage(GamePlayer player, ref int Damage)
		{
			string relicHoleArr = player.PlayerCharacter.RelicInfo.RelicHoleArr;
			bool flag = relicHoleArr != null;
			if (flag)
			{
				string[] array = relicHoleArr.Split(new char[] { ',' });
				string[] array2 = array;
				for (int i = 0; i < array2.Length; i++)
				{
					string text = array2[i];
					int id = int.Parse(text);
					bool flag2 = id == 0;
					if (flag2)
					{
						break;
					}
					Sys_Users_RelicEquipInfo sys_Users_RelicEquipInfo = player.PlayerCharacter.RelicEquipInfo.FirstOrDefault((Sys_Users_RelicEquipInfo p) => p.RelicID == id && p.UserID == player.PlayerId);
					bool flag3 = sys_Users_RelicEquipInfo == null;
					if (flag3)
					{
						break;
					}
					string[] array3 = sys_Users_RelicEquipInfo.ProArr.Split(new char[] { '|' });
					foreach (string text2 in array3)
					{
						int num = int.Parse(text2.Split(new char[] { ',' })[0]);
						int num2 = int.Parse(text2.Split(new char[] { ',' })[1]);
						int num3 = num;
						int num4 = num3;
						if (num4 == 8)
						{
							Damage += num2;
						}
					}
					TS_Relic_UpgradeTemplate upgradeInfo = ForceRelicMgr.GetUpgradeInfo(id, sys_Users_RelicEquipInfo.Level);
					bool flag4 = upgradeInfo == null;
					if (flag4)
					{
						return;
					}
					int type = upgradeInfo.Type;
					int num5 = type;
					if (num5 == 8)
					{
						Damage += upgradeInfo.Data;
					}
				}
			}
			string[] array5 = player.PlayerCharacter.RelicInfo.ManualInfo.Split(new char[] { '|' });
			foreach (string text3 in array5)
			{
				int num6 = int.Parse(text3.Split(new char[] { ',' })[0]);
				int num7 = int.Parse(text3.Split(new char[] { ',' })[2]);
				TS_Relic_DegreeTemplate degreeInfo = ForceRelicMgr.GetDegreeInfo(num6, num7);
				bool flag5 = degreeInfo != null;
				if (flag5)
				{
					Damage += degreeInfo.Damage;
				}
			}
		}

		// Token: 0x06005821 RID: 22561 RVA: 0x0023E34C File Offset: 0x0023C54C
		public static void GetRelicTempTypeByGuard(GamePlayer player, ref int Guard)
		{
			string relicHoleArr = player.PlayerCharacter.RelicInfo.RelicHoleArr;
			bool flag = relicHoleArr != null;
			if (flag)
			{
				string[] array = relicHoleArr.Split(new char[] { ',' });
				string[] array2 = array;
				for (int i = 0; i < array2.Length; i++)
				{
					string text = array2[i];
					int id = int.Parse(text);
					bool flag2 = id == 0;
					if (flag2)
					{
						break;
					}
					Sys_Users_RelicEquipInfo sys_Users_RelicEquipInfo = player.PlayerCharacter.RelicEquipInfo.FirstOrDefault((Sys_Users_RelicEquipInfo p) => p.RelicID == id && p.UserID == player.PlayerId);
					bool flag3 = sys_Users_RelicEquipInfo == null;
					if (flag3)
					{
						break;
					}
					string[] array3 = sys_Users_RelicEquipInfo.ProArr.Split(new char[] { '|' });
					foreach (string text2 in array3)
					{
						int num = int.Parse(text2.Split(new char[] { ',' })[0]);
						int num2 = int.Parse(text2.Split(new char[] { ',' })[1]);
						int num3 = num;
						int num4 = num3;
						if (num4 == 7)
						{
							Guard += num2;
						}
					}
					TS_Relic_UpgradeTemplate upgradeInfo = ForceRelicMgr.GetUpgradeInfo(id, sys_Users_RelicEquipInfo.Level);
					bool flag4 = upgradeInfo == null;
					if (flag4)
					{
						break;
					}
					int type = upgradeInfo.Type;
					int num5 = type;
					if (num5 == 7)
					{
						Guard += upgradeInfo.Data;
					}
				}
			}
		}

		// Token: 0x06005822 RID: 22562 RVA: 0x0023E500 File Offset: 0x0023C700
		public static void GetRelicTempTypeByElement(GamePlayer player, ref int fireAtt, ref int fireDef, ref int windAtt, ref int windDef, ref int landAtt, ref int landDef, ref int waterAtt, ref int waterDef, ref int guangAtt, ref int guangDef, ref int anAtt, ref int anDef)
		{
			string relicHoleArr = player.PlayerCharacter.RelicInfo.RelicHoleArr;
			bool flag = relicHoleArr != null;
			if (flag)
			{
				string[] array = relicHoleArr.Split(new char[] { ',' });
				string[] array2 = array;
				for (int i = 0; i < array2.Length; i++)
				{
					string text = array2[i];
					int id = int.Parse(text);
					bool flag2 = id == 0;
					if (flag2)
					{
						break;
					}
					Sys_Users_RelicEquipInfo sys_Users_RelicEquipInfo = player.PlayerCharacter.RelicEquipInfo.FirstOrDefault((Sys_Users_RelicEquipInfo p) => p.RelicID == id && p.UserID == player.PlayerId);
					bool flag3 = sys_Users_RelicEquipInfo == null;
					if (flag3)
					{
						break;
					}
					string[] array3 = sys_Users_RelicEquipInfo.ProArr.Split(new char[] { '|' });
					foreach (string text2 in array3)
					{
						int num = int.Parse(text2.Split(new char[] { ',' })[0]);
						int num2 = int.Parse(text2.Split(new char[] { ',' })[1]);
						switch (num)
						{
						case 22:
							fireAtt += num2;
							break;
						case 23:
							fireDef += num2;
							break;
						case 24:
							windAtt += num2;
							break;
						case 25:
							windDef += num2;
							break;
						case 26:
							landAtt += num2;
							break;
						case 27:
							landDef += num2;
							break;
						case 28:
							waterAtt += num2;
							break;
						case 29:
							waterDef += num2;
							break;
						case 30:
							guangAtt += num2;
							break;
						case 31:
							guangDef += num2;
							break;
						case 32:
							anAtt += num2;
							break;
						case 33:
							anDef += num2;
							break;
						}
					}
					TS_Relic_UpgradeTemplate upgradeInfo = ForceRelicMgr.GetUpgradeInfo(id, sys_Users_RelicEquipInfo.Level);
					bool flag4 = upgradeInfo == null;
					if (flag4)
					{
						break;
					}
					switch (upgradeInfo.Type)
					{
					case 22:
						fireAtt += upgradeInfo.Data;
						break;
					case 23:
						fireDef += upgradeInfo.Data;
						break;
					case 24:
						windAtt += upgradeInfo.Data;
						break;
					case 25:
						windDef += upgradeInfo.Data;
						break;
					case 26:
						landAtt += upgradeInfo.Data;
						break;
					case 27:
						landDef += upgradeInfo.Data;
						break;
					case 28:
						waterAtt += upgradeInfo.Data;
						break;
					case 29:
						waterDef += upgradeInfo.Data;
						break;
					case 30:
						guangAtt += upgradeInfo.Data;
						break;
					case 31:
						guangDef += upgradeInfo.Data;
						break;
					case 32:
						anAtt += upgradeInfo.Data;
						break;
					case 33:
						anDef += upgradeInfo.Data;
						break;
					}
				}
			}
		}

		// Token: 0x06005823 RID: 22563 RVA: 0x0023E854 File Offset: 0x0023CA54
		public static void GetRelicTempTypeBySecond(GamePlayer player, ref int Crit, ref int Tenacity, ref int SunderArmor, ref int AvoidInjury, ref int Kill, ref int WillFight, ref int ViolenceInjury, ref int Guard, ref int Speed)
		{
			string relicHoleArr = player.PlayerCharacter.RelicInfo.RelicHoleArr;
			bool flag = relicHoleArr != null;
			if (flag)
			{
				string[] array = relicHoleArr.Split(new char[] { ',' });
				string[] array2 = array;
				for (int i = 0; i < array2.Length; i++)
				{
					string text = array2[i];
					int id = int.Parse(text);
					bool flag2 = id == 0;
					if (flag2)
					{
						break;
					}
					Sys_Users_RelicEquipInfo sys_Users_RelicEquipInfo = player.PlayerCharacter.RelicEquipInfo.FirstOrDefault((Sys_Users_RelicEquipInfo p) => p.RelicID == id && p.UserID == player.PlayerId);
					bool flag3 = sys_Users_RelicEquipInfo == null;
					if (flag3)
					{
						break;
					}
					string[] array3 = sys_Users_RelicEquipInfo.ProArr.Split(new char[] { '|' });
					foreach (string text2 in array3)
					{
						int num = int.Parse(text2.Split(new char[] { ',' })[0]);
						int num2 = int.Parse(text2.Split(new char[] { ',' })[1]);
						switch (num)
						{
						case 10:
							Crit += num2;
							break;
						case 11:
							Tenacity += num2;
							break;
						case 12:
							SunderArmor += num2;
							break;
						case 13:
							AvoidInjury += num2;
							break;
						case 14:
							Kill += num2;
							break;
						case 15:
							WillFight += num2;
							break;
						case 16:
							ViolenceInjury += num2;
							break;
						case 17:
							Guard += num2;
							break;
						case 18:
							Speed += num2;
							break;
						}
					}
					TS_Relic_UpgradeTemplate upgradeInfo = ForceRelicMgr.GetUpgradeInfo(id, sys_Users_RelicEquipInfo.Level);
					bool flag4 = upgradeInfo == null;
					if (flag4)
					{
						break;
					}
					switch (upgradeInfo.Type)
					{
					case 10:
						Crit += upgradeInfo.Data;
						break;
					case 11:
						Tenacity += upgradeInfo.Data;
						break;
					case 12:
						SunderArmor += upgradeInfo.Data;
						break;
					case 13:
						AvoidInjury += upgradeInfo.Data;
						break;
					case 14:
						Kill += upgradeInfo.Data;
						break;
					case 15:
						WillFight += upgradeInfo.Data;
						break;
					case 16:
						ViolenceInjury += upgradeInfo.Data;
						break;
					case 17:
						Guard += upgradeInfo.Data;
						break;
					case 18:
						Speed += upgradeInfo.Data;
						break;
					}
				}
			}
		}

		// Token: 0x04002D1F RID: 11551
		private static ServerData sd = new ServerData();

		// Token: 0x04002D20 RID: 11552
		private static List<TS_Relic_AdvanceTemplate> advanceTemplate = new List<TS_Relic_AdvanceTemplate>();

		// Token: 0x04002D21 RID: 11553
		private static List<TS_Relic_DegreeTemplate> degreeTemplate = new List<TS_Relic_DegreeTemplate>();

		// Token: 0x04002D22 RID: 11554
		private static List<TS_Relic_ItemTemplate> itemTemplate = new List<TS_Relic_ItemTemplate>();

		// Token: 0x04002D23 RID: 11555
		private static List<TS_Relic_UpgradeTemplate> upgradeTemplate = new List<TS_Relic_UpgradeTemplate>();

		// Token: 0x04002D24 RID: 11556
		private static PlayerData pd = new PlayerData();
	}
}
