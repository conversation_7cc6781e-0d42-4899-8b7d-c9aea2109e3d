package com.greensock.easing
{
   public class Back
   {
      
      public function Back()
      {
         super();
      }
      
      public static function easeIn(_arg_1:Number, _arg_2:Number, _arg_3:Number, _arg_4:Number, _arg_5:Number = 1.70158) : Number
      {
         _arg_1 /= _arg_4;
         return _arg_3 * _arg_1 * _arg_1 * ((_arg_5 + 1) * _arg_1 - _arg_5) + _arg_2;
      }
      
      public static function easeOut(_arg_1:Number, _arg_2:Number, _arg_3:Number, _arg_4:Number, _arg_5:Number = 1.70158) : Number
      {
         _arg_1 = _arg_1 / _arg_4 - 1;
         return _arg_3 * (_arg_1 * _arg_1 * ((_arg_5 + 1) * _arg_1 + _arg_5) + 1) + _arg_2;
      }
      
      public static function easeInOut(_arg_1:Number, _arg_2:Number, _arg_3:Number, _arg_4:Number, _arg_5:Number = 1.70158) : Number
      {
         _arg_1 /= _arg_4 * 0.5;
         if(_arg_1 < 1)
         {
            _arg_5 *= 1.525;
            return _arg_3 * 0.5 * (_arg_1 * _arg_1 * ((_arg_5 + 1) * _arg_1 - _arg_5)) + _arg_2;
         }
         _arg_1 -= 2;
         _arg_5 *= 1.525;
         return _arg_3 / 2 * (_arg_1 * _arg_1 * ((_arg_5 + 1) * _arg_1 + _arg_5) + 2) + _arg_2;
      }
   }
}

