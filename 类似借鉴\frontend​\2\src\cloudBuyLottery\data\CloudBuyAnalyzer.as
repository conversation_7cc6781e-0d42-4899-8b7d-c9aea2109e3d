package cloudBuyLottery.data
{
   import com.pickgliss.loader.DataAnalyzer;
   import com.pickgliss.utils.ObjectUtils;
   
   public class CloudBuyAnalyzer extends DataAnalyzer
   {
      
      public var dataArr:Array;
      
      public function CloudBuyAnalyzer(_arg_1:Function)
      {
         super(_arg_1);
      }
      
      override public function analyze(_arg_1:*) : void
      {
         var _local_5:int = 0;
         var _local_4:* = null;
         var _local_2:* = null;
         var _local_3:XML = new XML(_arg_1);
         if(_local_3.@value == "true")
         {
            _local_4 = _local_3..ItemLog;
            this.dataArr = [];
            _local_5 = 0;
            while(_local_5 < _local_4.length())
            {
               _local_2 = new CloudBuyLogInfo();
               ObjectUtils.copyPorpertiesByXML(_local_2,_local_4[_local_5]);
               this.dataArr.push(_local_2);
               _local_5++;
            }
            onAnalyzeComplete();
         }
         else
         {
            message = _local_3.@message;
            onAnalyzeError();
            onAnalyzeComplete();
         }
      }
   }
}

