package cityBattle.view
{
   import cityBattle.CityBattleManager;
   import com.pickgliss.events.FrameEvent;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.controls.BaseButton;
   import com.pickgliss.ui.controls.Frame;
   import com.pickgliss.ui.controls.SelectedButton;
   import com.pickgliss.ui.controls.SelectedButtonGroup;
   import com.pickgliss.ui.controls.container.HBox;
   import com.pickgliss.ui.text.FilterFrameText;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.manager.LanguageMgr;
   import ddt.manager.ServerConfigManager;
   import ddt.manager.SocketManager;
   import ddt.manager.SoundManager;
   import ddt.utils.HelpFrameUtils;
   import flash.events.MouseEvent;
   
   public class CityBattleMainFrame extends Frame
   {
      
      private var _castellanTerritorialBtn:SelectedButton;
      
      private var _territorialContentionBtn:SelectedButton;
      
      private var _territorialWelfareBtn:SelectedButton;
      
      private var _hBox:HBox;
      
      private var _btnGroup:SelectedButtonGroup;
      
      private var _view:*;
      
      private var _btnHelp:BaseButton;
      
      private var _redTxt:FilterFrameText;
      
      private var _blueTxt:FilterFrameText;
      
      private var _timeTxt:FilterFrameText;
      
      private var _currentIndex:int;
      
      public var changeBtn:Boolean;
      
      public function CityBattleMainFrame()
      {
         super();
         this.initView();
         this.addEvent();
      }
      
      private function initView() : void
      {
         var _local_1:int = 0;
         this._hBox = ComponentFactory.Instance.creatComponentByStylename("cityBattle.view.hBox");
         addToContent(this._hBox);
         this._castellanTerritorialBtn = ComponentFactory.Instance.creatComponentByStylename("cityBattle.castellanTerritorial.btn");
         this._territorialContentionBtn = ComponentFactory.Instance.creatComponentByStylename("cityBattle.territorialContention.btn");
         this._territorialWelfareBtn = ComponentFactory.Instance.creatComponentByStylename("cityBattle.territorialWelfare.btn");
         this._btnGroup = new SelectedButtonGroup();
         this._hBox.addChild(this._castellanTerritorialBtn);
         this._hBox.addChild(this._territorialContentionBtn);
         this._hBox.addChild(this._territorialWelfareBtn);
         this._btnGroup.addSelectItem(this._castellanTerritorialBtn);
         this._btnGroup.addSelectItem(this._territorialContentionBtn);
         this._btnGroup.addSelectItem(this._territorialWelfareBtn);
         this._btnGroup.selectIndex = _local_1;
         this._currentIndex = _local_1;
         this.changeView(1);
         this._blueTxt = ComponentFactory.Instance.creatComponentByStylename("cityBattle.blueSide.txt");
         addToContent(this._blueTxt);
         this._redTxt = ComponentFactory.Instance.creatComponentByStylename("cityBattle.redSide.txt");
         addToContent(this._redTxt);
         this._timeTxt = ComponentFactory.Instance.creatComponentByStylename("cityBattle.activityTime.txt");
         addToContent(this._timeTxt);
         this._timeTxt.text = LanguageMgr.GetTranslation("ddt.cityBattle.activity.time",ServerConfigManager.instance.cityOccupationStartDate,ServerConfigManager.instance.cityOccupationEndDate);
         if(CityBattleManager.instance.mySide == 1)
         {
            this._blueTxt.text = LanguageMgr.GetTranslation("ddt.cityBattle.mySide.blue");
         }
         else
         {
            this._redTxt.text = LanguageMgr.GetTranslation("ddt.cityBattle.mySide.red");
         }
         this._btnHelp = HelpFrameUtils.Instance.simpleHelpButton(this,"core.helpButtonSmall",{
            "x":629,
            "y":5
         },LanguageMgr.GetTranslation("ddt.cityBattle.help.title"),"asset.cityBattle.view.help",408,488);
      }
      
      private function removeEvent() : void
      {
         removeEventListener("response",this._responseHandle);
         this._castellanTerritorialBtn.removeEventListener("click",this.__changeHandler);
         this._territorialContentionBtn.removeEventListener("click",this.__changeHandler);
         this._territorialWelfareBtn.removeEventListener("click",this.__changeHandler);
      }
      
      private function addEvent() : void
      {
         addEventListener("response",this._responseHandle);
         this._castellanTerritorialBtn.addEventListener("click",this.__changeHandler);
         this._territorialContentionBtn.addEventListener("click",this.__changeHandler);
         this._territorialWelfareBtn.addEventListener("click",this.__changeHandler);
      }
      
      protected function __changeHandler(_arg_1:MouseEvent) : void
      {
         if(this._btnGroup.selectIndex == this._currentIndex)
         {
            return;
         }
         SoundManager.instance.play("008");
         switch(_arg_1.currentTarget)
         {
            case this._castellanTerritorialBtn:
               this.changeView(1);
               break;
            case this._territorialContentionBtn:
               if(CityBattleManager.instance.contentionFirstData)
               {
                  this.changeView(2);
               }
               else
               {
                  SocketManager.Instance.out.cityBattleRank();
               }
               break;
            case this._territorialWelfareBtn:
               this.changeBtn = true;
               if(CityBattleManager.instance.now > 0 && CityBattleManager.instance.now < 8)
               {
                  SocketManager.Instance.out.cityBattleExchange(CityBattleManager.instance.now);
               }
               else
               {
                  SocketManager.Instance.out.cityBattleExchange(7);
               }
         }
         this._currentIndex = this._btnGroup.selectIndex;
      }
      
      public function changeView(_arg_1:int) : void
      {
         ObjectUtils.disposeObject(this._view);
         this._view = null;
         switch(_arg_1)
         {
            case 1:
               this._view = new CastellanView();
               break;
            case 2:
               this._view = new ContentionView();
               break;
            case 3:
               this._view = new WelfareView();
         }
         if(this._view)
         {
            this._view.y = -10;
            addToContent(this._view);
         }
      }
      
      protected function _responseHandle(_arg_1:FrameEvent) : void
      {
         SoundManager.instance.play("008");
         switch(_arg_1.responseCode)
         {
            case 4:
               return;
            case 1:
               this.dispose();
               return;
            case 0:
               this.dispose();
         }
      }
      
      override public function dispose() : void
      {
         this.removeEvent();
         ObjectUtils.disposeObject(this._castellanTerritorialBtn);
         this._castellanTerritorialBtn = null;
         ObjectUtils.disposeObject(this._territorialContentionBtn);
         this._territorialContentionBtn = null;
         ObjectUtils.disposeObject(this._territorialWelfareBtn);
         this._territorialWelfareBtn = null;
         ObjectUtils.disposeObject(this._btnHelp);
         this._btnHelp = null;
         ObjectUtils.disposeObject(this._blueTxt);
         this._blueTxt = null;
         ObjectUtils.disposeObject(this._redTxt);
         this._redTxt = null;
         ObjectUtils.disposeObject(this._timeTxt);
         this._timeTxt = null;
         CityBattleManager.instance.contentionFirstData = false;
         super.dispose();
      }
   }
}

