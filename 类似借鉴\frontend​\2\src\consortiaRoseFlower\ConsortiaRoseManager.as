package consortiaRoseFlower
{
   import com.greensock.TweenLite;
   import com.greensock.TweenMax;
   import com.pickgliss.toplevel.StageReferance;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.events.PkgEvent;
   import ddt.manager.ChatManager;
   import ddt.manager.LanguageMgr;
   import ddt.manager.MessageTipManager;
   import ddt.manager.PlayerManager;
   import ddt.manager.SocketManager;
   import ddt.manager.StateManager;
   import ddt.utils.HelperUIModuleLoad;
   import ddt.utils.PositionUtils;
   import flash.events.Event;
   import flash.events.EventDispatcher;
   import road7th.comm.PackageIn;
   import times.utils.timerManager.TimerJuggler;
   import times.utils.timerManager.TimerManager;
   
   public class ConsortiaRoseManager extends EventDispatcher
   {
      
      private static var instance:ConsortiaRoseManager;
      
      public static const CLOSE_ROSE:String = "close_rose";
      
      private var _roseView:ConsortiaRoseView;
      
      private var _timer:TimerJuggler;
      
      private var _showTime:Number = 30000;
      
      private var _delaySeconds:Number = 3;
      
      private var _playList:Array = [];
      
      private var _isPlaying:Boolean = false;
      
      private var _detailView:ConsortiaRoseDetailView;
      
      public function ConsortiaRoseManager(_arg_1:inner)
      {
         super();
      }
      
      public static function getInstance() : ConsortiaRoseManager
      {
         if(!instance)
         {
            instance = new ConsortiaRoseManager(new inner());
         }
         return instance;
      }
      
      public function setup() : void
      {
         SocketManager.Instance.addEventListener(PkgEvent.format(320),this.onConsortiaRose);
      }
      
      public function onConsortiaRose(_arg_1:PkgEvent) : void
      {
         var _local_3:Number = NaN;
         var _local_7:Boolean = false;
         var _local_9:* = null;
         var _local_5:PackageIn = _arg_1.pkg;
         var _local_8:int = _local_5.readInt();
         var _local_2:String = _local_5.readUTF();
         var _local_6:int = _local_5.readInt();
         var _local_4:String = PlayerManager.Instance.Self.ConsortiaName;
         if(this.isInBattle())
         {
            _local_3 = this._showTime / 1000;
            _local_7 = true;
         }
         else
         {
            _local_3 = 3;
            _local_7 = false;
         }
         if(_local_2 == PlayerManager.Instance.Self.NickName)
         {
            _local_9 = LanguageMgr.GetTranslation("consortia.roseFlower.myRoseExp",_local_6);
            MessageTipManager.getInstance().show(_local_9,0,false,3);
            ChatManager.Instance.sysChatLinkYellow(_local_9);
         }
         else
         {
            _local_9 = LanguageMgr.GetTranslation("consortia.roseFlower.Exp",_local_2,_local_6);
            ChatManager.Instance.sysChatLinkYellow(_local_9);
         }
         this._playList.push([_local_7,_local_4,_local_2]);
         this.playRose();
      }
      
      public function playRose() : void
      {
         var _local_1:* = null;
         if(this._playList.length > 0 && this._isPlaying == false)
         {
            this._isPlaying = true;
            _local_1 = this._playList.shift();
            this.show(_local_1[0],_local_1[1],_local_1[2]);
         }
      }
      
      private function isInBattle() : Boolean
      {
         switch(StateManager.currentStateType)
         {
            case "fightLabGameView":
            case "matchRoom":
            case "challengeRoom":
            case "dungeonRoom":
            case "fighting":
               return true;
            default:
               return false;
         }
      }
      
      public function show(_arg_1:Boolean, _arg_2:String, _arg_3:String) : void
      {
         var $isInBattle:* = undefined;
         var consortiaName:* = undefined;
         var nickName:* = undefined;
         $isInBattle = undefined;
         consortiaName = undefined;
         nickName = undefined;
         $isInBattle = _arg_1;
         consortiaName = _arg_2;
         nickName = _arg_3;
         var onLoaded:* = function():void
         {
            _roseView = new ConsortiaRoseView();
            StageReferance.stage.addChild(_roseView);
            _roseView.startFly();
            _timer = TimerManager.getInstance().addTimerJuggler(_showTime,1,false);
            _timer.addEventListener("timerComplete",onTimerComplete);
            _timer.start();
            _detailView = new ConsortiaRoseDetailView($isInBattle,consortiaName,nickName);
            PositionUtils.setPos(_detailView,"rose.ViewPos");
            _detailView.addEventListener("close_rose",onDetailClose);
            StageReferance.stage.addChild(_detailView);
            if(!$isInBattle)
            {
               TweenLite.delayedCall(_delaySeconds,exitDetail);
            }
         };
         new HelperUIModuleLoad().loadUIModule(["consortiaRose"],onLoaded);
      }
      
      private function exitDetail() : void
      {
         if(this._detailView == null)
         {
            return;
         }
         ObjectUtils.disposeObject(this._detailView);
         this._detailView = null;
      }
      
      private function onDetailViewAutoClose() : void
      {
         this.exitDetail();
         this._isPlaying = false;
         this.playRose();
      }
      
      protected function onDetailClose(_arg_1:Event) : void
      {
         this.onTimerComplete(null);
      }
      
      protected function onTimerComplete(_arg_1:Event) : void
      {
         var e:* = _arg_1;
         var onHide:* = function():void
         {
            _roseView.parent && StageReferance.stage.removeChild(_roseView);
            _roseView.stopFly();
            _roseView = null;
            onDetailViewAutoClose();
         };
         this._timer.stop();
         this._timer.removeEventListener("timerComplete",this.onTimerComplete);
         TimerManager.getInstance().removeJugglerByTimer(this._timer);
         this._timer = null;
         TweenMax.to(this._roseView,1,{
            "alpha":0,
            "onComplete":onHide
         });
      }
   }
}

class inner
{
   
   public function inner()
   {
      super();
   }
}
