package com.pickgliss.ui.controls
{
   import com.pickgliss.ui.controls.list.IDropListTarget;
   import com.pickgliss.ui.text.FilterFrameText;
   import flash.display.DisplayObject;
   
   public class SimpleDropListTarget extends FilterFrameText implements IDropListTarget
   {
      
      public function SimpleDropListTarget()
      {
         super();
      }
      
      public function setValue(_arg_1:*) : void
      {
         text = _arg_1;
      }
      
      public function setCursor(_arg_1:int) : void
      {
         setSelection(_arg_1,_arg_1);
      }
      
      public function getValueLength() : int
      {
         return text.length;
      }
      
      public function asDisplayObject() : DisplayObject
      {
         return this;
      }
   }
}

