package bible
{
   import bagAndInfo.BagAndInfoManager;
   import bible.data.BibleGetBackInfo;
   import bible.data.BibleModel;
   import bible.data.BibleTempAnalyzer;
   import braveDoor.BraveDoorManager;
   import com.pickgliss.ui.LayerManager;
   import com.pickgliss.utils.ClassUtils;
   import ddt.bagStore.BagStore;
   import ddt.data.quest.QuestInfo;
   import ddt.events.PkgEvent;
   import ddt.loader.LoaderCreate;
   import ddt.manager.LanguageMgr;
   import ddt.manager.PlayerManager;
   import ddt.manager.SocketManager;
   import ddt.utils.AssetModuleLoader;
   import ddtBuried.BuriedManager;
   import elf.ElfManager;
   import explorerManual.ExplorerManualManager;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.EventDispatcher;
   import guardCore.GuardCoreManager;
   import hall.HallStateGotoManager;
   import hallIcon.HallIconManager;
   import horse.HorseManager;
   import petsSystem.PetsManager;
   import quest.TaskManager;
   import road7th.comm.PackageIn;
   import vip.VipController;
   import worldboss.WorldBossManager;
   
   public class BibleManager extends EventDispatcher
   {
      
      private static var _ins:BibleManager;
      
      public static var BACKINFO_CHANGE:String = "backinfo_back";
      
      public static var color:Array = [15677951,16770048,16770048,8386048];
      
      public static var stoke:Array = [3473457,5317386,5317386,211968];
      
      private var _model:BibleModel;
      
      private var _clickOpenView:Boolean = false;
      
      public function BibleManager()
      {
         super();
         this._model = new BibleModel();
      }
      
      public static function get ins() : BibleManager
      {
         if(!_ins)
         {
            _ins = new BibleManager();
         }
         return _ins;
      }
      
      public function get model() : BibleModel
      {
         return this._model;
      }
      
      public function set model(_arg_1:BibleModel) : void
      {
         this._model = _arg_1;
      }
      
      public function showIcon() : void
      {
         if(PlayerManager.Instance.Self.Grade >= 15)
         {
            HallIconManager.instance.updateSwitchHandler("Bible",false);
         }
      }
      
      public function setup() : void
      {
         SocketManager.Instance.addEventListener(PkgEvent.format(652),this.__getActiveData);
         SocketManager.Instance.addEventListener(PkgEvent.format(147),this.__getBackData);
      }
      
      private function __getActiveData(_arg_1:PkgEvent) : void
      {
         var _local_10:int = 0;
         var _local_8:int = 0;
         var _local_2:int = 0;
         var _local_3:int = 0;
         var _local_7:int = 0;
         var _local_9:* = null;
         var _local_4:* = null;
         var _local_5:PackageIn = _arg_1.pkg;
         this._model.cardLevel = _local_5.readInt();
         this._model.exploreLevel = _local_5.readInt();
         this._model.initData();
         var _local_6:int = _local_5.readInt();
         _local_10 = 0;
         while(_local_10 < _local_6)
         {
            _local_8 = _local_5.readInt();
            _local_2 = _local_5.readInt();
            _local_3 = _local_5.readInt();
            _local_7 = 0;
            while(_local_7 < this._model.todayTodoDic[_local_2].length)
            {
               _local_9 = this._model.todayTodoDic[_local_2][_local_7];
               if(_local_9.ItemType == _local_3)
               {
                  _local_9.param1 = _local_5.readUTF();
                  _local_9.param2 = _local_5.readUTF();
                  break;
               }
               _local_7++;
            }
            if(_local_3 == 7)
            {
               _local_4 = [];
               _local_4 = this._returnList0Arr(TaskManager.instance.getAvailableQuests(2).list);
               _local_9.param1 = _local_4.length;
            }
            if(Boolean(_local_9.isOpenLevel) && Boolean(_local_9.isOpenTime) && _local_9.param1 == 0)
            {
               _local_9.Weight = 0;
            }
            _local_10++;
         }
         this._model.sort();
         this.showFrame();
      }
      
      private function __getBackData(_arg_1:PkgEvent) : void
      {
         var _local_10:int = 0;
         var _local_8:int = 0;
         var _local_7:int = 0;
         var _local_2:int = 0;
         var _local_9:* = null;
         var _local_5:* = null;
         var _local_4:* = null;
         var _local_3:PackageIn = _arg_1.pkg;
         this._model.getBackArr = [];
         var _local_6:int = _local_3.readInt();
         _local_10 = 0;
         while(_local_10 < _local_6)
         {
            _local_9 = new BibleGetBackInfo();
            _local_9.id = _local_3.readInt();
            _local_9.state = _local_3.readInt();
            _local_9.money = _local_3.readInt();
            _local_8 = _local_3.readInt();
            _local_7 = 0;
            while(_local_7 < _local_8)
            {
               _local_5 = {};
               _local_5.itemId = _local_3.readInt();
               _local_5.itemCount = _local_3.readInt();
               _local_9.itemArr.push(_local_5);
               _local_7++;
            }
            _local_2 = _local_3.readInt();
            _local_7 = 0;
            while(_local_7 < _local_8)
            {
               _local_4 = {};
               _local_4.itemId = _local_3.readInt();
               _local_4.itemCount = _local_3.readInt();
               _local_9.freeItemArr.push(_local_4);
               _local_7++;
            }
            this._model.getBackArr.push(_local_9);
            _local_10++;
         }
         this.dispatchEvent(new Event(BACKINFO_CHANGE));
      }
      
      private function _returnList0Arr(_arg_1:Array) : Array
      {
         var _local_3:int = 0;
         var _local_2:Array = [];
         _local_3 = 0;
         while(_local_3 < _arg_1.length)
         {
            _local_2[_local_3] = [];
            _local_2[_local_3][0] = QuestInfo(_arg_1[_local_3]).Title;
            if(QuestInfo(_arg_1[_local_3]).RepeatMax > 50)
            {
               _local_2[_local_3][1] = LanguageMgr.GetTranslation("ddt.exitPrompt.alotofTask");
            }
            else if(QuestInfo(_arg_1[_local_3]).RepeatMax == 1)
            {
               _local_2[_local_3][1] = "0/" + QuestInfo(_arg_1[_local_3]).RepeatMax;
            }
            else
            {
               _local_2[_local_3][1] = String(QuestInfo(_arg_1[_local_3]).RepeatMax - QuestInfo(_arg_1[_local_3]).data.repeatLeft) + "/" + QuestInfo(_arg_1[_local_3]).RepeatMax;
            }
            _local_3++;
         }
         return _local_2;
      }
      
      public function loadRes() : void
      {
         AssetModuleLoader.addRequestLoader(LoaderCreate.Instance.creatTexpExpLoader());
         AssetModuleLoader.addRequestLoader(LoaderCreate.Instance.creatBibleLoader());
         AssetModuleLoader.addModelLoader("bible",5);
         AssetModuleLoader.startCodeLoader(this.getData);
      }
      
      private function getData() : void
      {
         SocketManager.Instance.out.getHomeTempleLevel();
         SocketManager.Instance.out.sendBibleData();
      }
      
      private function showFrame() : void
      {
         if(!PlayerManager.Instance.Self.isNewOnceFinish(163) && Boolean(TaskManager.instance.getQuestDataByID(1564)))
         {
            SocketManager.Instance.out.sendQuestCheck(1564,1,0);
            SocketManager.Instance.out.syncWeakStep(163);
         }
         this._clickOpenView = true;
         this._model.isHide = true;
         var _local_1:Sprite = ClassUtils.CreatInstance("bible.view.BibleMain");
         LayerManager.Instance.addToLayer(_local_1,3,true,1);
         _local_1.x = 56;
         _local_1.y = 54;
      }
      
      public function analyzerIntroductionTemplate(_arg_1:BibleTempAnalyzer) : void
      {
         this._model.tempInfoArr = _arg_1.infoArr;
      }
      
      public function gotoSystemById(_arg_1:int, _arg_2:int) : void
      {
         if(_arg_1 == 1)
         {
            switch(_arg_2)
            {
               case 1:
                  BagAndInfoManager.Instance.showBagAndInfo(2);
                  break;
               case 2:
                  BagAndInfoManager.Instance.showBagAndInfo(4);
                  break;
               case 3:
                  BagAndInfoManager.Instance.showBagAndInfo(5);
                  break;
               case 4:
                  BagAndInfoManager.Instance.showBagAndInfo(6);
                  break;
               case 5:
                  BagAndInfoManager.Instance.showBagAndInfo(7);
                  break;
               case 6:
                  BagAndInfoManager.Instance.showBagAndInfo(8);
                  break;
               case 7:
                  BagAndInfoManager.Instance.showBagAndInfo(9);
                  break;
               case 8:
                  BagAndInfoManager.Instance.showBagAndInfo(11);
                  break;
               case 9:
                  GuardCoreManager.instance.show();
                  break;
               case 10:
                  ExplorerManualManager.instance.show();
                  break;
               case 11:
                  BagAndInfoManager.Instance.showBagAndInfo(0);
                  break;
               case 12:
                  BagStore.instance.openStore();
                  break;
               case 13:
                  BagStore.instance.openStore("bag_store",1);
                  break;
               case 14:
                  BagStore.instance.openStore("bag_store",2);
                  break;
               case 15:
                  BagStore.instance.openStore("forge_store",1);
                  break;
               case 16:
                  BagStore.instance.openStore("forge_store",4);
                  break;
               case 17:
                  BagStore.instance.openStore("fine_store",0);
                  break;
               case 18:
                  BagStore.instance.openStore("fine_store",1);
                  break;
               case 19:
                  BagStore.instance.openStore("fine_store",3);
                  break;
               case 20:
                  BagStore.instance.openStore("fine_store",2);
                  break;
               case 21:
                  BagStore.instance.openStore("fine_store",4);
                  break;
               case 22:
                  BagStore.instance.openStore("forge_store",3);
                  break;
               case 23:
                  BagStore.instance.openStore("forge_store",0);
                  break;
               case 24:
                  PetsManager.instance.show(2);
                  break;
               case 25:
                  PetsManager.instance.show(1);
                  break;
               case 26:
                  PetsManager.instance.show(3);
                  break;
               case 27:
                  PetsManager.instance.show(0,3);
                  break;
               case 28:
                  PetsManager.instance.show(0,1);
                  break;
               case 29:
                  HorseManager.instance.show();
                  break;
               case 30:
                  HorseManager.instance.isCanSelected = true;
                  HorseManager.instance.selectedType = 1;
                  HorseManager.instance.show();
                  break;
               case 31:
                  HorseManager.instance.isCanSelected = true;
                  HorseManager.instance.selectedType = 2;
                  HorseManager.instance.show();
                  break;
               case 32:
                  HorseManager.instance.isCanSelected = true;
                  HorseManager.instance.selectedType = 3;
                  HorseManager.instance.show();
                  break;
               case 33:
                  BagStore.instance.openStore("emblem",0);
                  break;
               case 34:
                  ElfManager.instance.show();
            }
         }
         else
         {
            switch(_arg_2)
            {
               case 1:
               case 2:
               case 3:
                  WorldBossManager.Instance.show();
                  return;
               case 9:
                  HallStateGotoManager.instance.gotoReverseGame();
                  return;
               case 4:
                  BraveDoorManager.instance.openView_Handler();
                  return;
               case 5:
                  HallStateGotoManager.instance.gotoPetFameView();
                  return;
               case 6:
                  BuriedManager.Instance.enter();
                  return;
               case 7:
                  HallStateGotoManager.instance.gotoTaskView();
                  return;
               case 8:
                  HallStateGotoManager.instance.gotoFlyHappy();
                  return;
               case 10:
                  HallStateGotoManager.instance.gotoAdventure();
                  return;
               case 12:
                  HallStateGotoManager.instance.gotoDungeonView();
                  return;
               case 13:
                  HallStateGotoManager.instance.gotoLabyrinthView();
                  return;
               case 14:
                  HallStateGotoManager.instance.gotoCryptBossView();
                  return;
               case 15:
                  HallStateGotoManager.instance.gotoBattleRoomView();
                  return;
               case 16:
                  HallStateGotoManager.instance.gotoLianSaiView();
                  return;
               case 17:
                  HallStateGotoManager.instance.gotoTeamBattleView();
                  return;
               case 18:
                  BagAndInfoManager.Instance.showBagAndInfo(6);
                  return;
               case 19:
                  HallStateGotoManager.instance.gotoConsortia();
                  return;
               case 20:
                  BagAndInfoManager.Instance.showBagAndInfo(11);
                  return;
               case 21:
                  HallStateGotoManager.instance.gotoPetFameView();
                  return;
               case 22:
                  VipController.instance.show();
                  return;
               case 25:
                  SocketManager.Instance.out.sendInitHandler();
                  return;
            }
         }
      }
   }
}

