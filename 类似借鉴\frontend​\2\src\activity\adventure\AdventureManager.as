package activity.adventure
{
   import activity.adventure.analyzer.AdventureEventListAnalyzer;
   import activity.adventure.analyzer.AdventureShopListAnalyzer;
   import activity.adventure.data.AdventureBoxInfo;
   import activity.adventure.data.AdventureEvent;
   import activity.adventure.data.AdventureExploreInfo;
   import activity.adventure.data.AdventureGXRankInfo;
   import activity.adventure.data.AdventureModel;
   import com.pickgliss.ui.LayerManager;
   import com.pickgliss.utils.ClassUtils;
   import ddt.events.PkgEvent;
   import ddt.loader.LoaderCreate;
   import ddt.manager.LanguageMgr;
   import ddt.manager.MessageTipManager;
   import ddt.manager.PlayerManager;
   import ddt.manager.SocketManager;
   import ddt.manager.StateManager;
   import ddt.manager.TimeManager;
   import ddt.utils.AssetModuleLoader;
   import flash.display.Sprite;
   import flash.events.EventDispatcher;
   import hallIcon.HallIconManager;
   import road7th.comm.PackageIn;
   
   public class AdventureManager extends EventDispatcher
   {
      
      private static var _ins:AdventureManager;
      
      private var _model:AdventureModel;
      
      public function AdventureManager()
      {
         super();
         this._model = new AdventureModel();
      }
      
      public static function get ins() : AdventureManager
      {
         if(!_ins)
         {
            _ins = new AdventureManager();
         }
         return _ins;
      }
      
      public function setup() : void
      {
         SocketManager.Instance.addEventListener(PkgEvent.format(642,1),this.__getActivityInfo);
         SocketManager.Instance.addEventListener(PkgEvent.format(642,2),this.__getGameInfo);
         SocketManager.Instance.addEventListener(PkgEvent.format(642,3),this.__gameSetpUpdate);
         SocketManager.Instance.addEventListener(PkgEvent.format(642,6),this.__newEventHandler);
         SocketManager.Instance.addEventListener(PkgEvent.format(642,5),this.__gameSelectedHandler);
         SocketManager.Instance.addEventListener(PkgEvent.format(642,7),this.__updateBox);
         SocketManager.Instance.addEventListener(PkgEvent.format(642,10),this.__updateShop);
         SocketManager.Instance.addEventListener(PkgEvent.format(642,9),this.__updateCount);
         SocketManager.Instance.addEventListener(PkgEvent.format(642,12),this.__reset);
         SocketManager.Instance.addEventListener(PkgEvent.format(642,13),this.__rankInfo);
         SocketManager.Instance.addEventListener(PkgEvent.format(642,14),this.__lastWeakInfo);
      }
      
      public function get model() : AdventureModel
      {
         return this._model;
      }
      
      public function set model(_arg_1:AdventureModel) : void
      {
         this._model = _arg_1;
      }
      
      public function loadRes() : void
      {
         AssetModuleLoader.addRequestLoader(LoaderCreate.Instance.creatTreasureEventListLoader());
         AssetModuleLoader.addRequestLoader(LoaderCreate.Instance.creatAdventureShopLoader());
         AssetModuleLoader.addModelLoader("adventure",7);
         AssetModuleLoader.startCodeLoader(this.showFrame);
         SocketManager.Instance.out.sendAdventureLastWeak();
      }
      
      public function analyerAdventureEventList(_arg_1:AdventureEventListAnalyzer) : void
      {
         this._model.eventList = _arg_1.eventList;
      }
      
      public function analyerAdventureShopList(_arg_1:AdventureShopListAnalyzer) : void
      {
         this._model.shopList = _arg_1.shopList;
      }
      
      private function __getActivityInfo(_arg_1:PkgEvent) : void
      {
         var _local_4:int = 0;
         var _local_2:* = null;
         var _local_3:PackageIn = _arg_1.pkg;
         this._model.openLevel = _local_3.readInt();
         this._model.resetCount = _local_3.extend2;
         while(Boolean(this._model.thisWeakExploreArr.length))
         {
            this._model.thisWeakExploreArr.shift();
         }
         _local_4 = 0;
         while(_local_4 < this._model.openLevel)
         {
            _local_2 = new AdventureExploreInfo();
            _local_2.type = _local_4;
            _local_2.bossPercent = _local_3.readInt();
            _local_2.count = _local_3.readInt();
            this._model.thisWeakExploreArr.push(_local_2);
            _local_4++;
         }
         this.loadRes();
      }
      
      private function __updateCount(_arg_1:PkgEvent) : void
      {
         var _local_2:PackageIn = _arg_1.pkg;
         this._model.turnCount = _local_2.readInt();
         this._model.hasBuyCount = _local_2.extend2;
         this.dispatchEvent(new AdventureEvent(AdventureEvent.UPDATE_INFO));
      }
      
      private function __getGameInfo(_arg_1:PkgEvent) : void
      {
         var _local_6:int = 0;
         var _local_5:* = null;
         var _local_4:PackageIn = _arg_1.pkg;
         this._model.curLevel = _local_4.readInt();
         this._model.gridStep = _local_4.readInt();
         var _local_2:String = _local_4.readUTF();
         var _local_3:Array = _local_2.split(",");
         this._model.clearEvent();
         _local_6 = 0;
         while(_local_6 < _local_3.length)
         {
            _local_5 = this._model.getEventItemConfig(_local_3[_local_6]);
            this._model.gateEventList.push(_local_5);
            _local_6++;
         }
         this._model.totalStep = this._model.gateEventList.length - 1;
         StateManager.setState("adventure");
      }
      
      private function __gameSetpUpdate(_arg_1:PkgEvent) : void
      {
         var _local_2:PackageIn = _arg_1.pkg;
         var _local_3:int = _local_2.extend2;
         this._model.gridStep = _local_2.extend1;
         this._model.turnCount = _local_2.readInt();
         this.dispatchEvent(new AdventureEvent(AdventureEvent.PLAY_DICE,{"step":_local_3}));
      }
      
      private function __gameSelectedHandler(_arg_1:PkgEvent) : void
      {
         var _local_4:PackageIn = _arg_1.pkg;
         var _local_2:Boolean = _local_4.readBoolean();
         var _local_3:String = _local_4.readUTF();
         MessageTipManager.getInstance().show(_local_3,0,true,1);
         this._model.isLockAction = false;
      }
      
      private function __updateShop(_arg_1:PkgEvent) : void
      {
         var _local_4:* = null;
         var _local_3:PackageIn = _arg_1.pkg;
         var _local_2:int = _local_3.extend2;
         this.dispatchEvent(new AdventureEvent(AdventureEvent.SHOP_BUY,{"tempId":_local_2}));
         _local_4 = LanguageMgr.GetTranslation("tank.equipAmulet.buyStiveComplete");
         MessageTipManager.getInstance().show(_local_4,0,true,1);
      }
      
      private function __updateBox(_arg_1:PkgEvent) : void
      {
         var _local_6:int = 0;
         var _local_2:int = 0;
         var _local_5:* = null;
         var _local_4:PackageIn = _arg_1.pkg;
         var _local_7:int = _local_4.readInt();
         this._model.clearBox();
         var _local_3:Date = TimeManager.Instance.Now();
         _local_6 = 0;
         while(_local_6 < _local_7)
         {
            _local_5 = new AdventureBoxInfo();
            _local_5.ID = _local_4.readLong();
            _local_2 = _local_4.readInt();
            _local_5.remainTime = _local_2 - _local_3.time / 1000;
            _local_5.boxStr = _local_4.readUTF();
            this._model.treasureBoxList.push(_local_5);
            _local_6++;
         }
         this.dispatchEvent(new AdventureEvent(AdventureEvent.UPDATE_BOX));
      }
      
      private function __reset(_arg_1:PkgEvent) : void
      {
         var _local_6:* = null;
         var _local_4:* = null;
         var _local_5:PackageIn = _arg_1.pkg;
         var _local_3:int = _local_5.extend2;
         var _local_2:Boolean = _local_5.readBoolean();
         if(_local_2)
         {
            _local_6 = LanguageMgr.GetTranslation("tank.adventure.resetSuccess");
            MessageTipManager.getInstance().show(_local_6,0,true,1);
            _local_4 = this._model.thisWeakExploreArr[_local_3 - 1];
            _local_4.bossPercent = 100;
            this.dispatchEvent(new AdventureEvent(AdventureEvent.RESET,{"type":_local_3}));
         }
      }
      
      private function __rankInfo(_arg_1:PkgEvent) : void
      {
         var _local_7:int = 0;
         var _local_6:* = null;
         var _local_3:* = null;
         var _local_4:PackageIn = _arg_1.pkg;
         var _local_5:int = _local_4.extend2;
         var _local_2:Array = this._model.rankArr[_local_5 - 1];
         while(Boolean(_local_2.length))
         {
            _local_2.shift();
         }
         var _local_8:int = _local_4.readInt();
         _local_7 = 0;
         while(_local_7 < _local_8)
         {
            _local_6 = new AdventureGXRankInfo();
            _local_6.name = _local_4.readUTF();
            _local_6.percent = _local_4.readInt();
            _local_6.rank = _local_7 + 1;
            if(_local_6.name == PlayerManager.Instance.Self.NickName)
            {
               _local_3 = _local_6;
            }
            else
            {
               _local_2.push(_local_6);
            }
            _local_7++;
         }
         if(_local_3)
         {
            _local_2.splice(0,0,_local_3);
         }
         this.dispatchEvent(new AdventureEvent(AdventureEvent.UPDATE_RANK));
      }
      
      private function __lastWeakInfo(_arg_1:PkgEvent) : void
      {
         var _local_5:int = 0;
         var _local_3:* = null;
         var _local_4:PackageIn = _arg_1.pkg;
         while(Boolean(this._model.lastWeakExploreArr.length))
         {
            this._model.lastWeakExploreArr.shift();
         }
         var _local_2:int = _local_4.readInt();
         _local_5 = 0;
         while(_local_5 < _local_2)
         {
            _local_3 = new AdventureExploreInfo();
            _local_3.type = _local_4.readInt() - 1;
            _local_3.count = _local_4.readInt();
            this._model.lastWeakExploreArr.push(_local_3);
            _local_5++;
         }
      }
      
      private function __newEventHandler(_arg_1:PkgEvent) : void
      {
         var _local_2:PackageIn = _arg_1.pkg;
         var _local_3:int = _local_2.extend2;
         this.dispatchEvent(new AdventureEvent(AdventureEvent.NEW_EVENT,{"type":_local_3}));
      }
      
      private function showFrame() : void
      {
         var _local_1:Sprite = ClassUtils.CreatInstance("activity.adventure.view.AdventureActivityMain");
         _local_1.width = 500;
         _local_1.height = 300;
         LayerManager.Instance.addToLayer(_local_1,3,true,1);
      }
      
      public function showIcon() : void
      {
         HallIconManager.instance.updateSwitchHandler("adventure",false);
      }
   }
}

