package activity.boguAdventure.model
{
   public class BoguAdventureType
   {
      
      public static const ACTIVITY_OPEN:int = 1;
      
      public static const ENTER_BOGUADVENTURE:int = 90;
      
      public static const UPDATE_CELL:int = 91;
      
      public static const REVIVE_GAME:int = 92;
      
      public static const ACQUIRE_AWARD:int = 93;
      
      public static const FREE_RESET:int = 99;
      
      public static const UPDATE_RANK:int = 100;
      
      public static const OUT_BOGUADVENTURE:int = 94;
      
      public function BoguAdventureType()
      {
         super();
      }
   }
}

