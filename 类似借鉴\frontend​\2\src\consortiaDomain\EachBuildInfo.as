package consortiaDomain
{
   public class EachBuildInfo
   {
      
      public var Id:int;
      
      public var Repair:int;
      
      public var Blood:int;
      
      public var State:int;
      
      public var repairPlayerNum:int;
      
      public var lowHpWarnArr:Array;
      
      public function EachBuildInfo()
      {
         var _local_1:int = 0;
         super();
         this.lowHpWarnArr = [];
         _local_1 = 0;
         while(_local_1 < ConsortiaDomainManager.BUILD_LOW_HP_WARN_ARR.length)
         {
            this.lowHpWarnArr.push(false);
            _local_1++;
         }
      }
   }
}

