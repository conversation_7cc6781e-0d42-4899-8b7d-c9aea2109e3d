package cardCollectAward.data
{
   import road7th.data.DictionaryData;
   
   public class CardCollectAwardInfo
   {
      
      private var _title:String;
      
      private var _desc:String;
      
      private var _minLv:int;
      
      private var _quertions:String;
      
      private var _beginTime:Date;
      
      private var _endTime:Date;
      
      private var _qq:String;
      
      private var _phone:String;
      
      private var _awardData:DictionaryData;
      
      public function CardCollectAwardInfo()
      {
         super();
         this._awardData = new DictionaryData();
      }
      
      public function get awardData() : DictionaryData
      {
         return this._awardData;
      }
      
      public function get phone() : String
      {
         return this._phone;
      }
      
      public function set phone(_arg_1:String) : void
      {
         this._phone = _arg_1;
      }
      
      public function get qq() : String
      {
         return this._qq;
      }
      
      public function set qq(_arg_1:String) : void
      {
         this._qq = _arg_1;
      }
      
      public function get endTime() : Date
      {
         return this._endTime;
      }
      
      public function set endTime(_arg_1:Date) : void
      {
         this._endTime = _arg_1;
      }
      
      public function get beginTime() : Date
      {
         return this._beginTime;
      }
      
      public function set beginTime(_arg_1:Date) : void
      {
         this._beginTime = _arg_1;
      }
      
      public function get quertions() : String
      {
         return this._quertions;
      }
      
      public function set quertions(_arg_1:String) : void
      {
         var _local_7:int = 0;
         var _local_6:* = null;
         var _local_2:* = null;
         var _local_4:* = null;
         var _local_5:* = null;
         var _local_3:* = null;
         this._quertions = _arg_1;
         if(this._quertions != null)
         {
            _local_6 = this._quertions.split("|");
            _local_2 = [];
            _local_7 = 0;
            while(_local_7 < _local_6.length)
            {
               _local_2 = (_local_6[_local_7] as String).split(",");
               if(this._awardData != null && _local_2[0] != null)
               {
                  _local_4 = new AwardItem();
                  _local_4.title = _local_2[0];
                  if(_local_2[1] != null)
                  {
                     _local_3 = (_local_2[1] as String).split(";");
                     while(_local_3 != null && _local_3.length > 0)
                     {
                        _local_5 = new ItemInfo();
                        _local_5.name = _local_3.shift();
                        _local_4.addItem(_local_5);
                     }
                  }
                  this._awardData[_local_7] = _local_4;
               }
               _local_7++;
            }
         }
      }
      
      public function getQuertionByIndex(_arg_1:int) : AwardItem
      {
         if(this._awardData != null && this._awardData.hasKey(_arg_1))
         {
            return this._awardData[_arg_1];
         }
         return null;
      }
      
      public function get minLv() : int
      {
         return this._minLv;
      }
      
      public function set minLv(_arg_1:int) : void
      {
         this._minLv = _arg_1;
      }
      
      public function get desc() : String
      {
         return this._desc;
      }
      
      public function set desc(_arg_1:String) : void
      {
         this._desc = _arg_1;
      }
      
      public function get title() : String
      {
         return this._title;
      }
      
      public function set title(_arg_1:String) : void
      {
         this._title = _arg_1;
      }
   }
}

