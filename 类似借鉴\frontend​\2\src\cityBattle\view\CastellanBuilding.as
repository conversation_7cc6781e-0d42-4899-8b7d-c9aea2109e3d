package cityBattle.view
{
   import cityBattle.CityBattleManager;
   import cityBattle.data.CastellanInfo;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.core.Disposeable;
   import com.pickgliss.utils.ClassUtils;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.manager.LanguageMgr;
   import ddt.utils.PositionUtils;
   import ddt.view.character.CharactoryFactory;
   import ddt.view.character.RoomCharacter;
   import ddt.view.tips.OneLineTip;
   import flash.display.Bitmap;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class CastellanBuilding extends Sprite implements Disposeable
   {
      
      private var building:MovieClip;
      
      private var control:MovieClip;
      
      private var _character:<PERSON><PERSON>haracter;
      
      private var _tip:OneLineTip;
      
      private var _cityName:Bitmap;
      
      public function CastellanBuilding(_arg_1:int)
      {
         var _local_2:CastellanInfo = null;
         super();
         _local_2 = CityBattleManager.instance.castellanList[_arg_1 - 1];
         this.building = ClassUtils.CreatInstance("asset.cityBattle.building" + String(_arg_1));
         addChild(this.building);
         if(_arg_1 == CityBattleManager.instance.now)
         {
            this.building.gotoAndStop(2);
         }
         else
         {
            this.building.gotoAndStop(1);
         }
         this.control = ClassUtils.CreatInstance("asset.cityBattle.control");
         addChild(this.control);
         PositionUtils.setPos(this.control,"castellan.control" + String(_arg_1) + "Pos");
         this.control.gotoAndStop(_local_2.side + 1);
         this._cityName = ComponentFactory.Instance.creatBitmap("asset.cityBattle.buildingName" + String(_arg_1));
         addChild(this._cityName);
         if(Boolean(_local_2.winner))
         {
            this._character = CharactoryFactory.createCharacter(_local_2.winner,"room") as RoomCharacter;
            this._character.showGun = false;
            this._character.show(false,-1);
            addChild(this._character);
            PositionUtils.setPos(this._character,"castellan.character" + String(_arg_1) + "Pos");
            this._character.mouseEnabled = true;
            this._character.addEventListener("mouseOver",this.overHandler);
            this._character.addEventListener("mouseOut",this.outHandler);
            this._tip = new OneLineTip();
            this._tip.tipData = LanguageMgr.GetTranslation("ddt.cityBattle.winnerInfo.tips",_local_2.winner.NickName,_local_2.winner.zoneName,_local_2.winner.Grade);
            this._tip.x = this._character.x;
            this._tip.visible = false;
            addChild(this._tip);
         }
      }
      
      private function overHandler(_arg_1:MouseEvent) : void
      {
         this._tip.visible = true;
      }
      
      private function outHandler(_arg_1:MouseEvent) : void
      {
         this._tip.visible = false;
      }
      
      public function dispose() : void
      {
         if(Boolean(this._character))
         {
            this._character.removeEventListener("mouseOver",this.overHandler);
            this._character.removeEventListener("mouseOut",this.outHandler);
         }
         ObjectUtils.disposeObject(this._tip);
         this._tip = null;
         ObjectUtils.disposeObject(this.building);
         this.building = null;
         ObjectUtils.disposeObject(this._cityName);
         this._cityName = null;
         ObjectUtils.disposeObject(this.control);
         this.control = null;
         ObjectUtils.disposeObject(this._character);
         this._character = null;
      }
   }
}

