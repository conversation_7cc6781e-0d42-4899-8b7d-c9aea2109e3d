package consortion.view.selfConsortia.consortiaTask
{
   import baglocked.BaglockedManager;
   import com.pickgliss.events.FrameEvent;
   import com.pickgliss.ui.AlertManager;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.controls.alert.BaseAlerFrame;
   import com.pickgliss.ui.image.MovieImage;
   import com.pickgliss.ui.image.Scale9CornerImage;
   import com.pickgliss.ui.vo.AlertInfo;
   import com.pickgliss.utils.ObjectUtils;
   import consortion.ConsortionModelManager;
   import ddt.manager.LanguageMgr;
   import ddt.manager.PlayerManager;
   import ddt.manager.ServerConfigManager;
   import ddt.manager.SocketManager;
   import ddt.manager.SoundManager;
   import ddt.utils.PositionUtils;
   
   public class ConsortiaReleaseTaskFrame extends BaseAlerFrame
   {
      
      private var _arr:Array = [3,3,5,5,8,8,10,10,12,12];
      
      private var _releaseContentTextScale9BG:Scale9CornerImage;
      
      private var _content:MovieImage;
      
      private var _levelView:ConsortiaTaskLevelView;
      
      private var _selectedLevelRecord:int;
      
      public function ConsortiaReleaseTaskFrame()
      {
         super();
         this.initView();
         this.initEvents();
      }
      
      private function initView() : void
      {
         var _local_1:AlertInfo = new AlertInfo();
         _local_1.submitLabel = LanguageMgr.GetTranslation("consortia.task.releaseTable");
         _local_1.title = LanguageMgr.GetTranslation("consortia.task.releaseTable.title");
         _local_1.showCancel = false;
         info = _local_1;
         this._releaseContentTextScale9BG = ComponentFactory.Instance.creatComponentByStylename("consortion.releaseContentTextScale9BG");
         this._content = ComponentFactory.Instance.creatComponentByStylename("conortion.releaseContentText");
         addToContent(this._releaseContentTextScale9BG);
         addToContent(this._content);
         this._levelView = new ConsortiaTaskLevelView();
         PositionUtils.setPos(this._levelView,"consortiaTask.levelViewPos");
         addToContent(this._levelView);
      }
      
      private function initEvents() : void
      {
         addEventListener("response",this.__response);
      }
      
      private function removeEvents() : void
      {
         removeEventListener("response",this.__response);
      }
      
      private function __response(_arg_1:FrameEvent) : void
      {
         SoundManager.instance.play("008");
         if(_arg_1.responseCode == 3 || _arg_1.responseCode == 2)
         {
            if(ConsortionModelManager.Instance.TaskModel.isHaveTask_noRelease)
            {
               ConsortionModelManager.Instance.TaskModel.isHaveTask_noRelease = false;
               ObjectUtils.disposeObject(this);
            }
            else
            {
               this.__okClick();
               ObjectUtils.disposeObject(this);
            }
         }
         else
         {
            ObjectUtils.disposeObject(this);
         }
      }
      
      private function __okClick() : void
      {
         if(PlayerManager.Instance.Self.bagLocked)
         {
            BaglockedManager.Instance.show();
            return;
         }
         this._selectedLevelRecord = this._levelView.selectedLevel;
         var _local_1:BaseAlerFrame = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("consortia.task.okTable"),LanguageMgr.GetTranslation("consortia.task.OKContent",ServerConfigManager.instance.MissionRiches[this._selectedLevelRecord * 2 - 1]),LanguageMgr.GetTranslation("ok"),LanguageMgr.GetTranslation("cancel"),false,true,true,2);
         _local_1.moveEnable = false;
         _local_1.addEventListener("response",this._responseII);
      }
      
      private function _responseII(_arg_1:FrameEvent) : void
      {
         (_arg_1.currentTarget as BaseAlerFrame).removeEventListener("response",this._responseII);
         if(_arg_1.responseCode == 2 || _arg_1.responseCode == 3)
         {
            if(PlayerManager.Instance.Self.consortiaInfo.Riches < ServerConfigManager.instance.MissionRiches[this._selectedLevelRecord * 2 - 1])
            {
               this.__openRichesTip();
            }
            else
            {
               SocketManager.Instance.out.sendReleaseConsortiaTask(0,true,this._selectedLevelRecord * 2 - 1);
               SocketManager.Instance.out.sendReleaseConsortiaTask(2);
               ObjectUtils.disposeObject(this);
            }
         }
         ObjectUtils.disposeObject(_arg_1.currentTarget as BaseAlerFrame);
      }
      
      private function __openRichesTip() : void
      {
         var _local_1:BaseAlerFrame = AlertManager.Instance.simpleAlert(LanguageMgr.GetTranslation("AlertDialog.Info"),LanguageMgr.GetTranslation("ddt.consortion.skillItem.click.enough1"),LanguageMgr.GetTranslation("ok"),LanguageMgr.GetTranslation("cancel"),false,false,false,1);
         _local_1.addEventListener("response",this.__noEnoughHandler);
      }
      
      private function __noEnoughHandler(_arg_1:FrameEvent) : void
      {
         var _local_2:BaseAlerFrame = null;
         SoundManager.instance.play("008");
         switch(_arg_1.responseCode)
         {
            case 2:
            case 3:
               ConsortionModelManager.Instance.alertTaxFrame();
         }
         _local_2 = _arg_1.currentTarget as BaseAlerFrame;
         _local_2.removeEventListener("response",this.__noEnoughHandler);
         _local_2.dispose();
         _local_2 = null;
      }
      
      override public function dispose() : void
      {
         this.removeEvents();
         this._releaseContentTextScale9BG = null;
         this._content = null;
         super.dispose();
         ObjectUtils.disposeAllChildren(this);
         this._levelView = null;
         if(Boolean(parent))
         {
            parent.removeChild(this);
         }
      }
   }
}

