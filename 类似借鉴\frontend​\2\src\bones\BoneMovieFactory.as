package bones
{
   import bones.display.BoneMovieFastStarling;
   import bones.display.BoneMovieFlash;
   import bones.display.BoneMovieStarling;
   import bones.display.IBoneMovie;
   import bones.loader.BonesLoaderManager;
   import bones.model.BoneVo;
   import bones.model.BonesModel;
   import dragonBones.Armature;
   import dragonBones.animation.WorldClock;
   import dragonBones.factories.BaseFactory;
   import dragonBones.factories.NativeFactory;
   import dragonBones.factories.StarlingFactory;
   import dragonBones.fast.FastArmature;
   import dragonBones.objects.AnimationData;
   import dragonBones.objects.ArmatureData;
   import dragonBones.objects.Frame;
   import road7th.DDTAssetManager;
   import road7th.data.DictionaryData;
   import starling.core.Starling;
   
   public class BoneMovieFactory
   {
      
      private static var _instance:BoneMovieFactory;
      
      private var _starlingFactory:StarlingFactory;
      
      private var _flashFactory:NativeFactory;
      
      private var _model:BonesModel;
      
      public function BoneMovieFactory(_arg_1:BoneMovieFactoryEnforcer)
      {
         super();
         this._model = new BonesModel();
      }
      
      public static function get instance() : BoneMovieFactory
      {
         if(_instance == null)
         {
            _instance = new BoneMovieFactory(new BoneMovieFactoryEnforcer());
         }
         return _instance;
      }
      
      public function setup() : void
      {
         this._starlingFactory = new StarlingFactory();
         this._flashFactory = new NativeFactory();
         this._flashFactory.fillBitmapSmooth = true;
         Starling.juggler.add(WorldClock.clock);
      }
      
      public function reset() : void
      {
         Starling.juggler.remove(WorldClock.clock);
         if(Boolean(this._starlingFactory))
         {
            this._starlingFactory.dispose(true);
         }
         if(Boolean(this._flashFactory))
         {
            this._flashFactory.dispose(true);
         }
         BonesLoaderManager.instance.clear();
         this.setup();
      }
      
      public function creatBoneMovie(_arg_1:String, _arg_2:int = 0, _arg_3:String = "default", _arg_4:Object = null) : *
      {
         var _local_6:* = null;
         var _local_5:* = null;
         var _local_7:* = null;
         var _local_4:* = null;
         if(_arg_1 == null || _arg_1 == "" || !this._model.getBonesStyle(_arg_1))
         {
            throw new Error("未找到\'" + _arg_1 + "\'的配置，请检查!");
         }
         if(_arg_2 == 0)
         {
            _local_6 = new BoneMovieStarling(_arg_1);
            (_local_6 as BoneMovieStarling).touchable = false;
            _local_7 = this._starlingFactory;
         }
         else
         {
            if(_arg_2 != 1)
            {
               throw new Error("骨骼动画创建失败.指定类型异常");
            }
            _local_6 = new BoneMovieFlash(_arg_1);
            (_local_6 as BoneMovieFlash).mouseEnabled = false;
            (_local_6 as BoneMovieFlash).mouseChildren = false;
            _local_7 = this._flashFactory;
         }
         if(_local_7.getTextureAtlas(_arg_1) == null)
         {
            _local_6.loadWait();
            _arg_4 ||= _arg_2;
            BonesLoaderManager.instance.startLoader(_arg_1,int(_arg_4),_arg_3);
         }
         else
         {
            _local_4 = this.getArmature(_arg_1,_local_7);
            _local_6.setArmature(_local_4,_local_4.armatureData);
         }
         return _local_6;
      }
      
      public function analysisFrameArgs(_arg_1:IBoneMovie) : DictionaryData
      {
         var _local_8:int = 0;
         var _local_10:int = 0;
         var _local_6:AnimationData = null;
         var _local_2:Frame = null;
         var _local_4:* = null;
         var _local_3:* = null;
         var _local_7:* = null;
         var _local_9:DictionaryData = new DictionaryData();
         var _local_5:ArmatureData = _arg_1.armature.armatureData;
         if(Boolean(_local_5.animationDataList) && _local_5.animationDataList.length > 0)
         {
            for each(_local_6 in _local_5.animationDataList)
            {
               for each(_local_2 in _local_6.frameList)
               {
                  if(Boolean(_local_2.event))
                  {
                     _local_8 = int(_local_2.event.indexOf("_"));
                     if(_local_8 > 0)
                     {
                        _local_4 = _local_2.event.slice(0,_local_8);
                        if(_local_4 == "args")
                        {
                           _local_3 = _local_2.event.slice(_local_8 + 1,_local_2.event.length).split("|");
                           _local_10 = 0;
                           while(_local_10 < _local_3.length)
                           {
                              _local_7 = _local_3[_local_10].split(":");
                              _local_9.add(_local_7[0],_local_7[1]);
                              _local_10++;
                           }
                        }
                     }
                  }
               }
            }
         }
         return _local_9;
      }
      
      public function getArmature(_arg_1:String, _arg_2:BaseFactory) : Armature
      {
         var _local_4:BoneVo = this._model.getBonesStyle(_arg_1);
         return _arg_2.buildArmature(_arg_1,_arg_1,_local_4.atlasName);
      }
      
      public function creatBoneMovieFast(_arg_1:String, _arg_2:String = "default") : BoneMovieFastStarling
      {
         var _local_4:* = null;
         var _local_3:* = null;
         if(_arg_1 == null || _arg_1 == "" || !this._model.getBonesStyle(_arg_1))
         {
            throw new Error("未找到\'" + _arg_1 + "\'的配置，请检查!");
         }
         var _local_5:BoneMovieFastStarling = new BoneMovieFastStarling(_arg_1);
         _local_5.touchable = false;
         if(this._starlingFactory.getTextureAtlas(_arg_1) == null)
         {
            _local_5.loadWait();
            BonesLoaderManager.instance.startLoader(_arg_1,0,_arg_2);
         }
         else
         {
            _local_3 = this.getFastArmature(_arg_1,this._starlingFactory);
            _local_5.setArmature(_local_3,_local_3.armatureData);
         }
         return _local_5;
      }
      
      public function getFastArmature(_arg_1:String, _arg_2:BaseFactory) : FastArmature
      {
         var _local_4:BoneVo = this._model.getBonesStyle(_arg_1);
         var _local_3:FastArmature = _arg_2.buildFastArmature(_arg_1,_arg_1,_local_4.atlasName);
         _local_3.enableAnimationCache(25);
         return _local_3;
      }
      
      public function checkTextureAtlas(_arg_1:String, _arg_2:int) : Boolean
      {
         if(_arg_2 == 0)
         {
            return Boolean(DDTAssetManager.instance.getTextureAtlas(_arg_1));
         }
         if(_arg_2 == 1)
         {
            return Boolean(DDTAssetManager.instance.getBitmapDataAtlas(_arg_1));
         }
         return Boolean(DDTAssetManager.instance.getTextureAtlas(_arg_1) && DDTAssetManager.instance.getBitmapDataAtlas(_arg_1));
      }
      
      public function get flashFactory() : NativeFactory
      {
         return this._flashFactory;
      }
      
      public function get starlingFactory() : StarlingFactory
      {
         return this._starlingFactory;
      }
      
      public function hasBoneMovie(_arg_1:String) : Boolean
      {
         var _local_2:* = null;
         var _local_3:BoneVo = this._model.getBonesStyle(_arg_1);
         if(_arg_1 == null || _arg_1 == "" || _local_3 == null)
         {
            throw new Error("未找到\'" + _arg_1 + "\'的配置，请检查!");
         }
         if(this.checkTextureAtlas(_local_3.atlasName,_local_3.useType) == false)
         {
            return false;
         }
         if(DDTAssetManager.instance.getSkeletonData(_local_3.styleName) == null)
         {
            return false;
         }
         return true;
      }
      
      public function get model() : BonesModel
      {
         return this._model;
      }
   }
}

class BoneMovieFactoryEnforcer
{
   
   public function BoneMovieFactoryEnforcer()
   {
      super();
   }
}
