package cityBattle.view
{
   import com.pickgliss.events.FrameEvent;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.controls.Frame;
   import com.pickgliss.ui.controls.TextButton;
   import com.pickgliss.ui.image.ScaleBitmapImage;
   import com.pickgliss.ui.text.FilterFrameText;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.manager.LanguageMgr;
   import ddt.manager.SoundManager;
   import ddt.utils.Helpers;
   import ddt.view.bossbox.AwardsGoodsList;
   import flash.events.MouseEvent;
   
   public class WinnerPrizeView extends Frame
   {
      
      private var _infoTxt:FilterFrameText;
      
      private var GoodsBG:ScaleBitmapImage;
      
      private var list:AwardsGoodsList;
      
      private var _goodsList:Array;
      
      private var _button:TextButton;
      
      public function WinnerPrizeView()
      {
         super();
         this.initView();
      }
      
      private function initView() : void
      {
         titleText = LanguageMgr.GetTranslation("ddt.cityBattle.winnerPrize");
         this._infoTxt = ComponentFactory.Instance.creatComponentByStylename("welfare.winnerPrizeInfo.txt");
         addChild(this._infoTxt);
         this.GoodsBG = ComponentFactory.Instance.creat("welfare.prizeGoodsBg");
         addToContent(this.GoodsBG);
         this._button = ComponentFactory.Instance.creat("welfare.okButton");
         this._button.text = LanguageMgr.GetTranslation("ok");
         addToContent(this._button);
         this._button.addEventListener("click",this._click);
         addEventListener("response",this.__frameEventHandler);
      }
      
      private function _click(_arg_1:MouseEvent) : void
      {
         this.dispose();
      }
      
      public function set goodsList(_arg_1:Array) : void
      {
         this._goodsList = _arg_1;
         this.list = ComponentFactory.Instance.creatCustomObject("welfare.winnerPrize.goodsList");
         this.list.show(this._goodsList);
         addChild(this.list);
      }
      
      private function __frameEventHandler(_arg_1:FrameEvent) : void
      {
         switch(_arg_1.responseCode)
         {
            case 0:
            case 1:
               SoundManager.instance.play("008");
               this.dispose();
         }
      }
      
      public function get goodsList() : Array
      {
         return Helpers.clone(this._goodsList) as Array;
      }
      
      override public function dispose() : void
      {
         super.dispose();
         removeEventListener("response",this.__frameEventHandler);
         this._button.removeEventListener("click",this._click);
         ObjectUtils.disposeObject(this._infoTxt);
         this._infoTxt = null;
         ObjectUtils.disposeObject(this.GoodsBG);
         this.GoodsBG = null;
         ObjectUtils.disposeObject(this._button);
         this._button = null;
         if(Boolean(this.list))
         {
            ObjectUtils.disposeObject(this.list);
         }
         this.list = null;
         super.dispose();
      }
   }
}

