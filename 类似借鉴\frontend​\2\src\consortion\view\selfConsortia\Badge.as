package consortion.view.selfConsortia
{
   import com.pickgliss.loader.BitmapLoader;
   import com.pickgliss.loader.LoadResourceManager;
   import com.pickgliss.loader.LoaderEvent;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.LayerManager;
   import com.pickgliss.ui.ShowTipManager;
   import com.pickgliss.ui.core.Disposeable;
   import com.pickgliss.ui.core.ITipedDisplay;
   import com.pickgliss.utils.ObjectUtils;
   import consortion.data.BadgeInfo;
   import ddt.manager.BadgeInfoManager;
   import ddt.manager.PathManager;
   import flash.display.Bitmap;
   import flash.display.DisplayObject;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class Badge extends Sprite implements Disposeable, ITipedDisplay
   {
      
      public static const LARGE:String = "large";
      
      public static const NORMAL:String = "normal";
      
      public static const SMALL:String = "small";
      
      private static const LARGE_SIZE:int = 78;
      
      private static const NORMAL_SIZE:int = 48;
      
      private static const SMALL_SIZE:int = 28;
      
      private var _size:String = "large";
      
      private var _badgeID:int = -1;
      
      private var _buyDate:Date;
      
      private var _badge:Bitmap;
      
      private var _loader:BitmapLoader;
      
      private var _clickEnale:Boolean = false;
      
      private var _tipInfo:Object;
      
      private var _tipDirctions:String;
      
      private var _tipGapH:int;
      
      private var _tipGapV:int;
      
      private var _tipStyle:String = "consortion.view.selfConsortia.BadgeTip";
      
      private var _showTip:Boolean;
      
      public function Badge(_arg_1:String = "small")
      {
         var _local_2:int = 0;
         super();
         this._size = _arg_1;
         graphics.beginFill(16777215,0);
         if(this._size == "large")
         {
            _local_2 = 78;
         }
         else if(this._size == "normal")
         {
            _local_2 = 48;
         }
         else if(this._size == "small")
         {
            _local_2 = 28;
         }
         graphics.drawRect(0,0,_local_2,_local_2);
         graphics.endFill();
         this._tipGapV = 5;
         this._tipGapH = 5;
         this._tipDirctions = "7,6,5";
         if(this._size == "small")
         {
            this._tipStyle = "ddt.view.tips.OneLineTip";
         }
         else
         {
            this._tipStyle = "consortion.view.selfConsortia.BadgeTip";
         }
      }
      
      public function get showTip() : Boolean
      {
         return this._showTip;
      }
      
      public function set showTip(_arg_1:Boolean) : void
      {
         this._showTip = _arg_1;
         if(this._showTip)
         {
            ShowTipManager.Instance.addTip(this);
         }
         else
         {
            ShowTipManager.Instance.removeTip(this);
         }
      }
      
      public function get clickEnale() : Boolean
      {
         return this._clickEnale;
      }
      
      public function set clickEnale(_arg_1:Boolean) : void
      {
         if(_arg_1 == this._clickEnale)
         {
            return;
         }
         this._clickEnale = _arg_1;
         if(this._clickEnale)
         {
            addEventListener("click",this.onClick);
         }
         else
         {
            removeEventListener("click",this.onClick);
         }
      }
      
      private function onClick(_arg_1:MouseEvent) : void
      {
         var _local_2:BadgeShopFrame = ComponentFactory.Instance.creatComponentByStylename("consortion.badgeShopFrame");
         LayerManager.Instance.addToLayer(_local_2,3,true);
      }
      
      public function get buyDate() : Date
      {
         return this._buyDate;
      }
      
      public function set buyDate(_arg_1:Date) : void
      {
         this._buyDate = _arg_1;
      }
      
      public function get badgeID() : int
      {
         return this._badgeID;
      }
      
      public function set badgeID(_arg_1:int) : void
      {
         if(_arg_1 == this._badgeID)
         {
            return;
         }
         this._badgeID = _arg_1;
         this.getTipInfo();
         this.updateView();
      }
      
      private function getTipInfo() : void
      {
         this._tipInfo = {};
         var _local_1:BadgeInfo = BadgeInfoManager.instance.getBadgeInfoByID(this._badgeID);
         if(Boolean(_local_1))
         {
            this._tipInfo.name = _local_1.BadgeName;
            this._tipInfo.LimitLevel = _local_1.LimitLevel;
            this._tipInfo.ValidDate = _local_1.ValidDate;
            if(Boolean(this._buyDate))
            {
               this._tipInfo.buyDate = this._buyDate;
            }
         }
      }
      
      private function updateView() : void
      {
         this.removeBadge();
         this._loader = LoadResourceManager.Instance.createLoader(PathManager.solveBadgePath(this._badgeID),0);
         this._loader.addEventListener("complete",this.onComplete);
         this._loader.addEventListener("loadError",this.onError);
         LoadResourceManager.Instance.startLoad(this._loader);
      }
      
      private function removeBadge() : void
      {
         if(Boolean(this._badge))
         {
            if(Boolean(this._badge.parent))
            {
               this._badge.parent.removeChild(this._badge);
            }
            this._badge.bitmapData.dispose();
            this._badge = null;
         }
      }
      
      private function onComplete(_arg_1:LoaderEvent) : void
      {
         var _local_2:* = undefined;
         this._loader.removeEventListener("complete",this.onComplete);
         this._loader.removeEventListener("loadError",this.onError);
         if(this._loader.isSuccess)
         {
            this._badge = this._loader.content as Bitmap;
            this._badge.smoothing = true;
            if(this._size == "large")
            {
               _local_2 = 78;
               this._badge.height = _local_2;
               this._badge.width = _local_2;
            }
            else if(this._size == "normal")
            {
               _local_2 = 48;
               this._badge.height = _local_2;
               this._badge.width = _local_2;
            }
            else
            {
               _local_2 = 28;
               this._badge.height = _local_2;
               this._badge.width = _local_2;
            }
            addChild(this._badge);
         }
      }
      
      private function onError(_arg_1:LoaderEvent) : void
      {
         this._loader.removeEventListener("complete",this.onComplete);
         this._loader.removeEventListener("loadError",this.onError);
      }
      
      public function asDisplayObject() : DisplayObject
      {
         return this;
      }
      
      public function get tipData() : Object
      {
         return this._tipInfo;
      }
      
      public function set tipData(_arg_1:Object) : void
      {
         this._tipInfo = _arg_1;
      }
      
      public function get tipDirctions() : String
      {
         return this._tipDirctions;
      }
      
      public function set tipDirctions(_arg_1:String) : void
      {
         this._tipDirctions = _arg_1;
      }
      
      public function get tipGapH() : int
      {
         return this._tipGapH;
      }
      
      public function set tipGapH(_arg_1:int) : void
      {
         this._tipGapH = _arg_1;
      }
      
      public function get tipGapV() : int
      {
         return this._tipGapV;
      }
      
      public function set tipGapV(_arg_1:int) : void
      {
         this._tipGapV = _arg_1;
      }
      
      public function get tipStyle() : String
      {
         return this._tipStyle;
      }
      
      public function set tipStyle(_arg_1:String) : void
      {
         this._tipStyle = _arg_1;
      }
      
      public function dispose() : void
      {
         ShowTipManager.Instance.removeTip(this);
         ObjectUtils.disposeObject(this._badge);
         this._badge = null;
         if(Boolean(this._loader))
         {
            this._loader.removeEventListener("complete",this.onComplete);
            this._loader.removeEventListener("loadError",this.onError);
         }
         this._loader = null;
         this._tipInfo = null;
         if(Boolean(parent))
         {
            parent.removeChild(this);
         }
      }
   }
}

