package accumulativeLogin.view
{
   import accumulativeLogin.AccumulativeManager;
   import accumulativeLogin.data.AccumulativeLoginRewardData;
   import bagAndInfo.cell.BagCell;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.controls.SimpleBitmapButton;
   import com.pickgliss.ui.controls.container.HBox;
   import com.pickgliss.ui.text.FilterFrameText;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.data.goods.InventoryItemInfo;
   import ddt.manager.ItemManager;
   import ddt.manager.LanguageMgr;
   import ddt.manager.MessageTipManager;
   import ddt.manager.PlayerManager;
   import ddt.manager.SocketManager;
   import ddt.utils.PositionUtils;
   import flash.display.Bitmap;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.filters.ColorMatrixFilter;
   import flash.utils.Dictionary;
   
   public class AccumulativeLoginView extends Sprite
   {
      
      private var _back:Bitmap;
      
      private var _progressBarBack:Bitmap;
      
      private var _progressBar:Bitmap;
      
      private var _progressBarItemArr:Array;
      
      private var _clickSpriteArr:Array;
      
      private var _progressCompleteItem:MovieClip;
      
      private var _dayTxtArr:Array;
      
      private var _loginDayTxt:FilterFrameText;
      
      private var _loginDayNum:int;
      
      private var _awardDayNum:int;
      
      private var _hBox:HBox;
      
      private var _dataDic:Dictionary;
      
      private var _selectedDay:int;
      
      private var _selectedFiveWeaponId:int;
      
      private var _dayGiftPackDic:Dictionary;
      
      private var _fiveWeaponArr:Array;
      
      private var _bagCellBgArr:Array;
      
      private var _filter:ColorMatrixFilter;
      
      private var _movieStringArr:Array = ["wonderfulactivity.login.gun","wonderfulactivity.login.axe","wonderfulactivity.login.chick","wonderfulactivity.login.boomerang"];
      
      private var _movieVector:Vector.<AccumulativeMovieSprite>;
      
      private var _getButton:SimpleBitmapButton;
      
      public function AccumulativeLoginView()
      {
         super();
         this._movieVector = new Vector.<AccumulativeMovieSprite>();
         this._progressBarItemArr = [];
         this._clickSpriteArr = [];
         this._dayTxtArr = [];
         this._dayGiftPackDic = new Dictionary();
         this._bagCellBgArr = [];
         this._fiveWeaponArr = [];
      }
      
      public function setState(_arg_1:int, _arg_2:int) : void
      {
      }
      
      public function init() : void
      {
         this.createFilter();
         this.initEvent();
         this.initView();
         this.initData();
         this.initViewWithData();
         this.selectedDay = this._loginDayNum;
      }
      
      private function createFilter() : void
      {
         var _local_1:Array = [];
         _local_1 = _local_1.concat([0.3086,0.6094,0.082,0,0]);
         _local_1 = _local_1.concat([0.3086,0.6094,0.082,0,0]);
         _local_1 = _local_1.concat([0.3086,0.6094,0.082,0,0]);
         _local_1 = _local_1.concat([0,0,0,1,0]);
         this._filter = new ColorMatrixFilter(_local_1);
      }
      
      public function initEvent() : void
      {
         AccumulativeManager.instance.addEventListener("accumulativeLoginAwardRefresh",this.__refreshAward);
      }
      
      protected function __refreshAward(_arg_1:Event) : void
      {
         this._loginDayNum = PlayerManager.Instance.Self.accumulativeLoginDays > 7 ? 7 : PlayerManager.Instance.Self.accumulativeLoginDays;
         this._awardDayNum = PlayerManager.Instance.Self.accumulativeAwardDays;
         this.checkMovieCanClick();
         if(this._awardDayNum >= this._loginDayNum)
         {
            this._getButton.enable = false;
         }
         else
         {
            this._getButton.enable = true;
         }
         this.selectedDay = this._selectedDay;
      }
      
      private function initView() : void
      {
         var _local_5:int = 0;
         var _local_6:int = 0;
         var _local_2:int = 0;
         var _local_8:int = 0;
         var _local_4:* = null;
         var _local_3:* = null;
         var _local_1:* = null;
         var _local_7:* = null;
         this._back = ComponentFactory.Instance.creat("wonderfulactivity.login.back");
         addChild(this._back);
         this._loginDayTxt = ComponentFactory.Instance.creatComponentByStylename("wonderfulactivity.accumulativeLogin.dayTxt");
         addChild(this._loginDayTxt);
         _local_5 = 1;
         while(_local_5 < 8)
         {
            _local_4 = ComponentFactory.Instance.creatComponentByStylename("wonderfulactivity.accumulativeLogin.dayTxt");
            addChild(_local_4);
            _local_4.text = "" + _local_5;
            _local_4.x = _local_5 == 7 ? 700 : 334 + 62 * (_local_5 - 1);
            _local_4.y = 150;
            this._dayTxtArr.push(_local_4);
            _local_5++;
         }
         this._progressBarBack = ComponentFactory.Instance.creat("wonderfulactivity.login.barback");
         addChild(this._progressBarBack);
         this._progressBar = ComponentFactory.Instance.creat("wonderfulactivity.login.bar");
         addChild(this._progressBar);
         _local_6 = 0;
         while(_local_6 < 6)
         {
            _local_3 = ComponentFactory.Instance.creat("wonderfulactivity.login.barItem");
            _local_3.x = 334 + 62 * _local_6;
            _local_3.y = 170;
            addChild(_local_3);
            this._progressBarItemArr.push(_local_3);
            _local_6++;
         }
         this._progressCompleteItem = ComponentFactory.Instance.creat("wonderfulactivity.login.barCompleteItem");
         addChild(this._progressCompleteItem);
         this._progressCompleteItem.y = 170;
         _local_2 = 0;
         while(_local_2 < 7)
         {
            _local_1 = new Sprite();
            _local_1.buttonMode = true;
            _local_1.graphics.beginFill(0,0);
            if(_local_2 != 6)
            {
               _local_1.graphics.drawRect(this._progressBarItemArr[_local_2].x,170,this._progressBarItemArr[_local_2].width,this._progressBarItemArr[_local_2].height);
            }
            else
            {
               _local_1.graphics.drawRect(this._progressBarItemArr[5].x + 58,170,this._progressBarItemArr[5].width + 8,this._progressBarItemArr[5].height);
            }
            _local_1.graphics.endFill();
            _local_1.addEventListener("click",this.__showAwardHandler);
            addChild(_local_1);
            this._clickSpriteArr.push(_local_1);
            _local_2++;
         }
         this._hBox = ComponentFactory.Instance.creatComponentByStylename("wonderful.accumulativeLogin.Hbox");
         addChild(this._hBox);
         _local_8 = 0;
         while(_local_8 < this._movieStringArr.length)
         {
            _local_7 = new AccumulativeMovieSprite(this._movieStringArr[_local_8]);
            _local_7.addEventListener("click",this.__onClickHandler);
            addChild(_local_7);
            PositionUtils.setPos(_local_7,"wonderful.accumulativeLogin.moviePos" + (_local_8 + 1));
            this._movieVector.push(_local_7);
            _local_8++;
         }
         this._getButton = ComponentFactory.Instance.creatComponentByStylename("wonderful.ActivityState.GetButton");
         addChild(this._getButton);
         this._getButton.enable = false;
      }
      
      protected function __showAwardHandler(_arg_1:MouseEvent) : void
      {
         var _local_2:int = int(this._clickSpriteArr.indexOf(_arg_1.target));
         if(_local_2 != -1 && _local_2 + 1 != this.selectedDay)
         {
            this.selectedDay = _local_2 + 1;
         }
      }
      
      protected function __onClickHandler(_arg_1:MouseEvent) : void
      {
         var _local_2:AccumulativeMovieSprite = null;
         if(this._loginDayNum < 7)
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("wonderfulactivity.accumulativelogin.txt"));
            return;
         }
         if(_arg_1.currentTarget.state == 3)
         {
            return;
         }
         for each(_local_2 in this._movieVector)
         {
            if(_local_2 == _arg_1.currentTarget)
            {
               _local_2.state = 3;
               this._selectedFiveWeaponId = _local_2.data.ID;
            }
            else
            {
               _local_2.state = 1;
            }
         }
      }
      
      protected function __onOverHandler(_arg_1:MouseEvent) : void
      {
         (_arg_1.target as MovieClip).gotoAndPlay(2);
      }
      
      private function checkMovieCanClick() : void
      {
         var _local_1:AccumulativeMovieSprite = null;
         var _local_2:AccumulativeMovieSprite = null;
         if(this._loginDayNum >= 7 && this._awardDayNum >= 7)
         {
            for each(_local_1 in this._movieVector)
            {
               _local_1.removeEventListener("click",this.__onClickHandler);
               _local_1.state = 1;
            }
         }
         if(this._loginDayNum >= 7 && this._awardDayNum < 7)
         {
            for each(_local_2 in this._movieVector)
            {
               _local_2.state = 2;
            }
         }
      }
      
      private function initData() : void
      {
         this._loginDayNum = PlayerManager.Instance.Self.accumulativeLoginDays > 7 ? 7 : PlayerManager.Instance.Self.accumulativeLoginDays;
         this._awardDayNum = PlayerManager.Instance.Self.accumulativeAwardDays;
         if(this._awardDayNum < this._loginDayNum && this._awardDayNum < 7)
         {
            this._getButton.enable = true;
            this._getButton.addEventListener("click",this.__getAward);
         }
         else
         {
            this._getButton.enable = false;
         }
         this._dataDic = AccumulativeManager.instance.dataDic;
      }
      
      private function initViewWithData() : void
      {
         var _local_4:int = 0;
         var _local_5:int = 0;
         var _local_2:AccumulativeLoginRewardData = null;
         var _local_1:* = null;
         var _local_3:* = null;
         this.checkMovieCanClick();
         this._loginDayTxt.text = "" + this._loginDayNum;
         if(this._loginDayNum < 7)
         {
            this._progressBar.width = Boolean(this._progressBarItemArr[this._loginDayNum - 1]) ? this._progressBarItemArr[this._loginDayNum - 1].x - 265 : 0;
            this._progressCompleteItem.x = this._progressBar.width + 256;
         }
         else if(this._loginDayNum >= 7)
         {
            this._progressBar.width = this._progressBarItemArr[5].x - 265 + 55;
            this._progressCompleteItem.x = this._progressBar.width + 258;
         }
         if(!this._dataDic)
         {
            return;
         }
         _local_4 = 1;
         while(_local_4 < 8)
         {
            _local_1 = [];
            for each(_local_2 in this._dataDic[_local_4])
            {
               _local_3 = this.createBagCellSp(_local_2,_local_4);
               _local_1.push(_local_3);
            }
            this._dayGiftPackDic[_local_4] = _local_1;
            _local_4++;
         }
         _local_5 = 0;
         while(_local_5 < this._movieVector.length)
         {
            this._movieVector[_local_5].tipData = this._fiveWeaponArr[_local_5].tipData;
            this._movieVector[_local_5].data = this._dataDic[7][_local_5];
            _local_5++;
         }
      }
      
      private function __getAward(_arg_1:MouseEvent) : void
      {
         if(this._loginDayNum >= 7 && this._selectedFiveWeaponId == 0)
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("wonderfulactivity.accumulativelogin.txt2"));
            return;
         }
         SocketManager.Instance.out.sendAccumulativeLoginAward(this._selectedFiveWeaponId);
      }
      
      private function set selectedDay(_arg_1:int) : void
      {
         var _local_2:Sprite = null;
         this._selectedDay = _arg_1;
         if(this._selectedDay > 7)
         {
            this._selectedDay = 7;
         }
         this._hBox.removeAllChild();
         for each(_local_2 in this._dayGiftPackDic[this._selectedDay])
         {
            if(this._selectedDay <= this._awardDayNum)
            {
               this.graySp(_local_2);
            }
            else
            {
               _local_2.filters = null;
            }
            this._hBox.addChild(_local_2);
         }
      }
      
      private function graySp(_arg_1:Sprite) : void
      {
         _arg_1.filters = [this._filter];
      }
      
      private function get selectedDay() : int
      {
         return this._selectedDay;
      }
      
      private function createBagCellSp(_arg_1:AccumulativeLoginRewardData, _arg_2:int) : Sprite
      {
         var _local_5:Sprite = null;
         _local_5 = new Sprite();
         var _local_4:Bitmap = ComponentFactory.Instance.creat("wonderfulactivity.login.bagCellBg");
         var _local_7:* = 0.7;
         _local_4.scaleY = _local_7;
         _local_4.scaleX = _local_7;
         _local_5.addChild(_local_4);
         var _local_6:InventoryItemInfo = new InventoryItemInfo();
         _local_6.TemplateID = _arg_1.RewardItemID;
         _local_6 = ItemManager.fill(_local_6);
         _local_6.IsBinds = _arg_1.IsBind;
         _local_6.ValidDate = _arg_1.RewardItemValid;
         _local_6.StrengthenLevel = _arg_1.StrengthenLevel;
         _local_6.AttackCompose = _arg_1.AttackCompose;
         _local_6.DefendCompose = _arg_1.DefendCompose;
         _local_6.AgilityCompose = _arg_1.AgilityCompose;
         _local_6.LuckCompose = _arg_1.LuckCompose;
         var _local_3:BagCell = new BagCell(0);
         _local_3.info = _local_6;
         _local_3.setCount(_arg_1.RewardItemCount);
         _local_3.setBgVisible(false);
         _local_7 = 4;
         _local_3.y = _local_7;
         _local_3.x = _local_7;
         _local_5.addChild(_local_3);
         if(_arg_2 == 7)
         {
            this._fiveWeaponArr.push(_local_3);
         }
         return _local_5;
      }
      
      public function content() : Sprite
      {
         return this;
      }
      
      public function dispose() : void
      {
         var _local_6:int = 0;
         var _local_8:int = 0;
         var _local_4:Array = null;
         var _local_3:BagCell = null;
         var _local_2:Bitmap = null;
         var _local_1:Sprite = null;
         var _local_7:FilterFrameText = null;
         var _local_5:* = null;
         AccumulativeManager.instance.removeEventListener("accumulativeLoginAwardRefresh",this.__refreshAward);
         for each(_local_4 in this._dayGiftPackDic)
         {
            _local_6 = 0;
            while(_local_6 < _local_4.length)
            {
               _local_5 = _local_4[_local_6];
               ObjectUtils.disposeAllChildren(_local_5);
               ObjectUtils.disposeObject(_local_5);
               _local_6++;
            }
         }
         this._dayGiftPackDic = null;
         for each(_local_3 in this._fiveWeaponArr)
         {
            ObjectUtils.disposeObject(_local_3);
            _local_3 = null;
         }
         this._fiveWeaponArr = null;
         _local_8 = 0;
         while(_local_8 < this._movieVector.length)
         {
            this._movieVector[_local_8].removeEventListener("click",this.__onClickHandler);
            this._movieVector[_local_8].dispose();
            this._movieVector[_local_8] = null;
            _local_8++;
         }
         this._movieVector = null;
         for each(_local_2 in this._progressBarItemArr)
         {
            if(Boolean(_local_2))
            {
               ObjectUtils.disposeObject(_local_2);
            }
            _local_2 = null;
         }
         this._progressBarItemArr = null;
         for each(_local_1 in this._clickSpriteArr)
         {
            if(Boolean(_local_1))
            {
               _local_1.graphics.clear();
            }
            _local_1.removeEventListener("click",this.__showAwardHandler);
            _local_1 = null;
         }
         this._clickSpriteArr = null;
         for each(_local_7 in this._dayTxtArr)
         {
            if(Boolean(_local_7))
            {
               ObjectUtils.disposeObject(_local_7);
            }
            _local_7 = null;
         }
         this._dayTxtArr = null;
         if(Boolean(this._back))
         {
            ObjectUtils.disposeObject(this._back);
         }
         this._back = null;
         if(Boolean(this._hBox))
         {
            ObjectUtils.disposeObject(this._hBox);
         }
         this._hBox = null;
         if(Boolean(this._progressCompleteItem))
         {
            ObjectUtils.disposeObject(this._progressCompleteItem);
         }
         this._progressCompleteItem = null;
         if(Boolean(this._loginDayTxt))
         {
            ObjectUtils.disposeObject(this._loginDayTxt);
         }
         this._loginDayTxt = null;
         if(Boolean(this._progressBarBack))
         {
            ObjectUtils.disposeObject(this._progressBarBack);
         }
         this._progressBarBack = null;
         if(Boolean(this._progressBar))
         {
            ObjectUtils.disposeObject(this._progressBar);
         }
         this._progressBar = null;
         if(Boolean(this._getButton))
         {
            this._getButton.removeEventListener("click",this.__getAward);
         }
         ObjectUtils.disposeObject(this._getButton);
         this._getButton = null;
         if(Boolean(parent))
         {
            parent.removeChild(this);
         }
      }
   }
}

