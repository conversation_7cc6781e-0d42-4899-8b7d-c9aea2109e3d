using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EntityDatabase.PlayerModels
{
	// Token: 0x020000A6 RID: 166
	public class Sys_Users_RelicEquipInfo
	{
		// Token: 0x17000634 RID: 1588
		// (get) Token: 0x06000D10 RID: 3344 RVA: 0x00009282 File Offset: 0x00007482
		// (set) Token: 0x06000D11 RID: 3345 RVA: 0x0000928A File Offset: 0x0000748A
		[Key]
		[DatabaseGenerated(DatabaseGeneratedOption.Identity)]
		public int ID { get; set; }

		// Token: 0x17000635 RID: 1589
		// (get) Token: 0x06000D12 RID: 3346 RVA: 0x00009293 File Offset: 0x00007493
		// (set) Token: 0x06000D13 RID: 3347 RVA: 0x0000929B File Offset: 0x0000749B
		public int UserID { get; set; }

		// Token: 0x17000636 RID: 1590
		// (get) Token: 0x06000D14 RID: 3348 RVA: 0x000092A4 File Offset: 0x000074A4
		// (set) Token: 0x06000D15 RID: 3349 RVA: 0x000092AC File Offset: 0x000074AC
		public int RelicID { get; set; }

		// Token: 0x17000637 RID: 1591
		// (get) Token: 0x06000D16 RID: 3350 RVA: 0x000092B5 File Offset: 0x000074B5
		// (set) Token: 0x06000D17 RID: 3351 RVA: 0x000092BD File Offset: 0x000074BD
		public int Level { get; set; }

		// Token: 0x17000638 RID: 1592
		// (get) Token: 0x06000D18 RID: 3352 RVA: 0x000092C6 File Offset: 0x000074C6
		// (set) Token: 0x06000D19 RID: 3353 RVA: 0x000092CE File Offset: 0x000074CE
		public int Stage { get; set; }

		// Token: 0x17000639 RID: 1593
		// (get) Token: 0x06000D1A RID: 3354 RVA: 0x000092D7 File Offset: 0x000074D7
		// (set) Token: 0x06000D1B RID: 3355 RVA: 0x000092DF File Offset: 0x000074DF
		public int Exp { get; set; }

		// Token: 0x1700063A RID: 1594
		// (get) Token: 0x06000D1C RID: 3356 RVA: 0x000092E8 File Offset: 0x000074E8
		// (set) Token: 0x06000D1D RID: 3357 RVA: 0x000092F0 File Offset: 0x000074F0
		public int ShardNum { get; set; }

		// Token: 0x1700063B RID: 1595
		// (get) Token: 0x06000D1E RID: 3358 RVA: 0x000092F9 File Offset: 0x000074F9
		// (set) Token: 0x06000D1F RID: 3359 RVA: 0x00009301 File Offset: 0x00007501
		public string ProArr { get; set; }
	}
}
