package bagAndInfo.bag
{
   import com.pickgliss.events.FrameEvent;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.LayerManager;
   import com.pickgliss.ui.controls.Frame;
   import ddt.manager.PlayerManager;
   
   public class BagFrame extends Frame
   {
      
      protected var _bagView:BagView;
      
      protected var _isShow:Boolean;
      
      public function BagFrame()
      {
         super();
         this.initView();
         this.initEvent();
      }
      
      protected function initView() : void
      {
         this._bagView = ComponentFactory.Instance.creatCustomObject("bagFrameBagView");
         this._bagView.info = PlayerManager.Instance.Self;
      }
      
      public function graySortBtn() : void
      {
         this._bagView.sortBagEnable = false;
      }
      
      private function initEvent() : void
      {
         addEventListener("response",this.__responseHandler);
      }
      
      private function __responseHandler(_arg_1:FrameEvent) : void
      {
         switch(_arg_1.responseCode)
         {
            case 0:
            case 1:
               this.dispose();
         }
      }
      
      public function get bagView() : BagView
      {
         return this._bagView;
      }
      
      public function show() : void
      {
         LayerManager.Instance.addToLayer(this,3,false);
         this._isShow = true;
      }
      
      public function hide() : void
      {
         if(Boolean(parent))
         {
            parent.removeChild(this);
            this._isShow = false;
         }
      }
      
      public function get isShow() : Boolean
      {
         return this._isShow;
      }
      
      override public function dispose() : void
      {
         removeEventListener("response",this.__responseHandler);
         if(Boolean(this._bagView))
         {
            this._bagView.dispose();
         }
         super.dispose();
         if(Boolean(parent))
         {
            parent.removeChild(this);
         }
      }
   }
}

