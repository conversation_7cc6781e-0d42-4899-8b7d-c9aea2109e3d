package com.pickgliss.ui.controls
{
   import com.pickgliss.events.InteractiveEvent;
   import com.pickgliss.geom.InnerRectangle;
   import com.pickgliss.geom.IntDimension;
   import com.pickgliss.geom.IntPoint;
   import com.pickgliss.geom.IntRectangle;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.core.Component;
   import com.pickgliss.ui.core.IViewprot;
   import com.pickgliss.utils.ClassUtils;
   import com.pickgliss.utils.DisplayUtils;
   import com.pickgliss.utils.ObjectUtils;
   import flash.display.DisplayObject;
   import flash.events.MouseEvent;
   import flash.geom.Rectangle;
   
   public class ScrollPanel extends Component
   {
      
      public static const AUTO:int = 1;
      
      public static const OFF:int = 2;
      
      public static const ON:int = 0;
      
      public static const P_backgound:String = "backgound";
      
      public static const P_backgoundInnerRect:String = "backgoundInnerRect";
      
      public static const P_hScrollProxy:String = "hScrollProxy";
      
      public static const P_hScrollbar:String = "hScrollbar";
      
      public static const P_hScrollbarInnerRect:String = "hScrollbarInnerRect";
      
      public static const P_vScrollProxy:String = "vScrollProxy";
      
      public static const P_vScrollbar:String = "vScrollbar";
      
      public static const P_vScrollbarInnerRect:String = "vScrollbarInnerRect";
      
      public static const P_viewSource:String = "viewSource";
      
      public static const P_viewportInnerRect:String = "viewportInnerRect";
      
      protected var _backgound:DisplayObject;
      
      protected var _backgoundInnerRect:InnerRectangle;
      
      protected var _backgoundInnerRectString:String;
      
      protected var _backgoundStyle:String;
      
      protected var _hScrollProxy:int;
      
      protected var _hScrollbar:Scrollbar;
      
      protected var _hScrollbarInnerRect:InnerRectangle;
      
      protected var _hScrollbarInnerRectString:String;
      
      protected var _hScrollbarStyle:String;
      
      protected var _vScrollProxy:int;
      
      protected var _vScrollbar:Scrollbar;
      
      protected var _vScrollbarInnerRect:InnerRectangle;
      
      protected var _vScrollbarInnerRectString:String;
      
      protected var _vScrollbarStyle:String;
      
      protected var _viewSource:IViewprot;
      
      protected var _viewportInnerRect:InnerRectangle;
      
      protected var _viewportInnerRectString:String;
      
      protected var _isDisableMouseWheel:Boolean = false;
      
      public function ScrollPanel(_arg_1:Boolean = true)
      {
         super();
         if(_arg_1)
         {
            this._viewSource = new DisplayObjectViewport();
            this._viewSource.addStateListener(this.__onViewportStateChanged);
         }
      }
      
      public function set backgound(_arg_1:DisplayObject) : void
      {
         if(this._backgound == _arg_1)
         {
            return;
         }
         if(Boolean(this._backgound))
         {
            ObjectUtils.disposeObject(this._backgound);
         }
         this._backgound = _arg_1;
         onPropertiesChanged("backgound");
      }
      
      public function set backgoundInnerRectString(_arg_1:String) : void
      {
         if(this._backgoundInnerRectString == _arg_1)
         {
            return;
         }
         this._backgoundInnerRectString = _arg_1;
         this._backgoundInnerRect = ClassUtils.CreatInstance("com.pickgliss.geom.InnerRectangle",ComponentFactory.parasArgs(this._backgoundInnerRectString));
         onPropertiesChanged("backgoundInnerRect");
      }
      
      public function set backgoundStyle(_arg_1:String) : void
      {
         if(this._backgoundStyle == _arg_1)
         {
            return;
         }
         this._backgoundStyle = _arg_1;
         this.backgound = ComponentFactory.Instance.creat(this._backgoundStyle);
      }
      
      public function get displayObjectViewport() : DisplayObjectViewport
      {
         return this._viewSource as DisplayObjectViewport;
      }
      
      public function set disableMouseWheel(_arg_1:Boolean) : void
      {
         if(this._isDisableMouseWheel == _arg_1)
         {
            return;
         }
         this._isDisableMouseWheel = _arg_1;
      }
      
      public function get disableMouseWheel() : Boolean
      {
         return this._isDisableMouseWheel;
      }
      
      override public function dispose() : void
      {
         if(Boolean(this._vScrollbar))
         {
            this._vScrollbar.removeStateListener(this.__onScrollValueChange);
            ObjectUtils.disposeObject(this._vScrollbar);
         }
         this._vScrollbar = null;
         if(Boolean(this._hScrollbar))
         {
            this._hScrollbar.removeStateListener(this.__onScrollValueChange);
            ObjectUtils.disposeObject(this._hScrollbar);
         }
         this._hScrollbar = null;
         if(Boolean(this._backgound))
         {
            ObjectUtils.disposeObject(this._backgound);
         }
         this._backgound = null;
         if(Boolean(this._viewSource))
         {
            ObjectUtils.disposeObject(this._viewSource);
         }
         this._viewSource = null;
         removeEventListener("mouseWheel",this.__onMouseWheel);
         super.dispose();
      }
      
      public function getShowHScrollbarExtendHeight() : Number
      {
         var _local_3:* = null;
         if(_height == 0 || this._hScrollbarInnerRect == null)
         {
            return 0;
         }
         var _local_1:Rectangle = this._viewportInnerRect.getInnerRect(_width,_height);
         var _local_2:Rectangle = this._hScrollbarInnerRect.getInnerRect(_width,_height);
         _local_3 = _local_1.union(_local_2);
         return _local_1.height - _local_3.height;
      }
      
      public function getVisibleRect() : IntRectangle
      {
         var _local_1:int = 0;
         var _local_4:int = 0;
         var _local_5:int = 0;
         var _local_2:int = 0;
         var _local_3:IntDimension = this._viewSource.getViewSize();
         if(this._hScrollbar == null)
         {
            _local_4 = 0;
            _local_2 = _local_3.width;
         }
         else
         {
            _local_4 = this._hScrollbar.scrollValue;
            _local_2 = this._hScrollbar.visibleAmount;
         }
         if(this._vScrollbar == null)
         {
            _local_1 = 0;
            _local_5 = _local_3.height;
         }
         else
         {
            _local_1 = this._vScrollbar.scrollValue;
            _local_5 = this._vScrollbar.visibleAmount;
         }
         return new IntRectangle(_local_4,_local_1,_local_2,_local_5);
      }
      
      public function set hScrollProxy(_arg_1:int) : void
      {
         if(this._hScrollProxy == _arg_1)
         {
            return;
         }
         this._hScrollProxy = _arg_1;
         onPropertiesChanged("hScrollProxy");
      }
      
      public function get hScrollbar() : Scrollbar
      {
         return this._hScrollbar;
      }
      
      public function set hScrollbar(_arg_1:Scrollbar) : void
      {
         if(this._hScrollbar == _arg_1)
         {
            return;
         }
         if(Boolean(this._hScrollbar))
         {
            this._hScrollbar.removeStateListener(this.__onScrollValueChange);
            ObjectUtils.disposeObject(this._hScrollbar);
         }
         this._hScrollbar = _arg_1;
         this._hScrollbar.addStateListener(this.__onScrollValueChange);
         onPropertiesChanged("hScrollbar");
      }
      
      public function set hScrollbarInnerRectString(_arg_1:String) : void
      {
         if(this._hScrollbarInnerRectString == _arg_1)
         {
            return;
         }
         this._hScrollbarInnerRectString = _arg_1;
         this._hScrollbarInnerRect = ClassUtils.CreatInstance("com.pickgliss.geom.InnerRectangle",ComponentFactory.parasArgs(this._hScrollbarInnerRectString));
         onPropertiesChanged("hScrollbarInnerRect");
      }
      
      public function set hScrollbarStyle(_arg_1:String) : void
      {
         if(this._hScrollbarStyle == _arg_1)
         {
            return;
         }
         this._hScrollbarStyle = _arg_1;
         this.hScrollbar = ComponentFactory.Instance.creat(this._hScrollbarStyle);
      }
      
      public function set hUnitIncrement(_arg_1:int) : void
      {
         this._viewSource.horizontalUnitIncrement = _arg_1;
      }
      
      public function invalidateViewport(_arg_1:Boolean = false) : void
      {
         var _local_2:int = 0;
         var _local_3:* = null;
         if(this._viewSource is DisplayObjectViewport)
         {
            if(_arg_1)
            {
               this.displayObjectViewport.invalidateView();
               _local_2 = this.viewPort.getViewSize().height;
               _local_3 = new IntPoint(0,_local_2);
               this.viewPort.viewPosition = _local_3;
            }
            else
            {
               this.displayObjectViewport.invalidateView();
            }
         }
         else
         {
            trace("不允许对其他类型的viewport进行此操作");
         }
      }
      
      public function invalidateViewport_toTop(_arg_1:Boolean = false) : void
      {
         var _local_2:int = 0;
         var _local_3:* = null;
         if(this._viewSource is DisplayObjectViewport)
         {
            if(_arg_1)
            {
               this.displayObjectViewport.invalidateView();
               _local_2 = this.viewPort.getViewSize().height;
               _local_3 = new IntPoint(0,0);
               this.viewPort.viewPosition = _local_3;
            }
            else
            {
               this.displayObjectViewport.invalidateView();
            }
         }
         else
         {
            trace("不允许对其他类型的viewport进行此操作");
         }
      }
      
      public function setView(_arg_1:DisplayObject) : void
      {
         if(this._viewSource is DisplayObjectViewport)
         {
            this.displayObjectViewport.setView(_arg_1);
         }
         else
         {
            trace("不允许对其他类型的viewport进行此操作");
         }
      }
      
      public function set vScrollProxy(_arg_1:int) : void
      {
         if(this._vScrollProxy == _arg_1)
         {
            return;
         }
         this._vScrollProxy = _arg_1;
         onPropertiesChanged("vScrollProxy");
      }
      
      public function get vScrollbar() : Scrollbar
      {
         return this._vScrollbar;
      }
      
      public function set vScrollbar(_arg_1:Scrollbar) : void
      {
         if(this._vScrollbar == _arg_1)
         {
            return;
         }
         if(Boolean(this._vScrollbar))
         {
            this._vScrollbar.removeStateListener(this.__onScrollValueChange);
            ObjectUtils.disposeObject(this._vScrollbar);
         }
         this._vScrollbar = _arg_1;
         this._vScrollbar.addStateListener(this.__onScrollValueChange);
         onPropertiesChanged("vScrollbar");
      }
      
      public function set vScrollbarInnerRectString(_arg_1:String) : void
      {
         if(this._vScrollbarInnerRectString == _arg_1)
         {
            return;
         }
         this._vScrollbarInnerRectString = _arg_1;
         this._vScrollbarInnerRect = ClassUtils.CreatInstance("com.pickgliss.geom.InnerRectangle",ComponentFactory.parasArgs(this._vScrollbarInnerRectString));
         onPropertiesChanged("vScrollbarInnerRect");
      }
      
      public function set vScrollbarStyle(_arg_1:String) : void
      {
         if(this._vScrollbarStyle == _arg_1)
         {
            return;
         }
         this._vScrollbarStyle = _arg_1;
         this.vScrollbar = ComponentFactory.Instance.creat(this._vScrollbarStyle);
      }
      
      public function set vUnitIncrement(_arg_1:int) : void
      {
         this._viewSource.verticalUnitIncrement = _arg_1;
      }
      
      public function get viewPort() : IViewprot
      {
         return this._viewSource;
      }
      
      public function set viewPort(_arg_1:IViewprot) : void
      {
         if(this._viewSource == _arg_1)
         {
            return;
         }
         if(Boolean(this._viewSource))
         {
            this._viewSource.removeStateListener(this.__onViewportStateChanged);
            ObjectUtils.disposeObject(this._viewSource);
         }
         this._viewSource = _arg_1;
         this._viewSource.addStateListener(this.__onViewportStateChanged);
         onPropertiesChanged("viewSource");
      }
      
      public function set viewportInnerRectString(_arg_1:String) : void
      {
         if(this._viewportInnerRectString == _arg_1)
         {
            return;
         }
         this._viewportInnerRectString = _arg_1;
         this._viewportInnerRect = ClassUtils.CreatInstance("com.pickgliss.geom.InnerRectangle",ComponentFactory.parasArgs(this._viewportInnerRectString));
         onPropertiesChanged("viewportInnerRect");
      }
      
      protected function __onMouseWheel(_arg_1:MouseEvent) : void
      {
         var _local_2:* = null;
         if(!this._isDisableMouseWheel)
         {
            _local_2 = this._viewSource.viewPosition.clone();
            _local_2.y -= _arg_1.delta * this._viewSource.verticalUnitIncrement;
            this._viewSource.viewPosition = _local_2;
            _arg_1.stopImmediatePropagation();
         }
      }
      
      public function setViewPosition(_arg_1:int) : void
      {
         var _local_2:IntPoint = this._viewSource.viewPosition.clone();
         _local_2.y += _arg_1 * this._viewSource.verticalUnitIncrement;
         this._viewSource.viewPosition = _local_2;
      }
      
      protected function __onScrollValueChange(_arg_1:InteractiveEvent) : void
      {
         this.viewPort.scrollRectToVisible(this.getVisibleRect());
      }
      
      protected function __onViewportStateChanged(_arg_1:InteractiveEvent) : void
      {
         this.syncScrollPaneWithViewport();
      }
      
      override protected function addChildren() : void
      {
         super.addChildren();
         if(Boolean(this._backgound))
         {
            addChild(this._backgound);
         }
         if(Boolean(this._viewSource))
         {
            addChild(this._viewSource.asDisplayObject());
         }
         if(Boolean(this._vScrollbar))
         {
            addChild(this._vScrollbar);
         }
         if(Boolean(this._hScrollbar))
         {
            addChild(this._hScrollbar);
         }
      }
      
      override protected function init() : void
      {
         this.initEvent();
         super.init();
      }
      
      protected function initEvent() : void
      {
         addEventListener("mouseWheel",this.__onMouseWheel);
      }
      
      protected function layoutComponent() : void
      {
         if(Boolean(this._vScrollbar))
         {
            DisplayUtils.layoutDisplayWithInnerRect(this._vScrollbar,this._vScrollbarInnerRect,_width,_height);
         }
         if(Boolean(this._hScrollbar))
         {
            DisplayUtils.layoutDisplayWithInnerRect(this._hScrollbar,this._hScrollbarInnerRect,_width,_height);
         }
         if(Boolean(this._backgound))
         {
            DisplayUtils.layoutDisplayWithInnerRect(this._backgound,this._backgoundInnerRect,_width,_height);
         }
         if(Boolean(this._viewSource))
         {
            DisplayUtils.layoutDisplayWithInnerRect(this._viewSource.asDisplayObject(),this._viewportInnerRect,_width,_height);
         }
      }
      
      override protected function onProppertiesUpdate() : void
      {
         super.onProppertiesUpdate();
         if(Boolean(_changedPropeties["height"]) || Boolean(_changedPropeties["width"]) || Boolean(_changedPropeties["vScrollbarInnerRect"]) || Boolean(_changedPropeties["hScrollbarInnerRect"]) || Boolean(_changedPropeties["vScrollbar"]) || Boolean(_changedPropeties["hScrollbar"]) || Boolean(_changedPropeties["viewportInnerRect"]) || Boolean(_changedPropeties["viewSource"]))
         {
            this.layoutComponent();
         }
         if(Boolean(_changedPropeties["viewSource"]))
         {
            this.syncScrollPaneWithViewport();
         }
         if(Boolean(_changedPropeties["vScrollProxy"]) || Boolean(_changedPropeties["hScrollProxy"]))
         {
            if(Boolean(this._vScrollbar))
            {
               this._vScrollbar.visible = this._vScrollProxy == 0;
            }
            if(Boolean(this._hScrollbar))
            {
               this._hScrollbar.visible = this._hScrollProxy == 0;
            }
         }
      }
      
      protected function syncScrollPaneWithViewport() : void
      {
         var _local_1:int = 0;
         var _local_5:int = 0;
         var _local_3:int = 0;
         var _local_6:* = null;
         var _local_2:* = null;
         var _local_4:* = null;
         if(this._viewSource != null)
         {
            _local_6 = this._viewSource.getExtentSize();
            if(_local_6.width <= 0 || _local_6.height <= 0)
            {
               return;
            }
            _local_2 = this._viewSource.getViewSize();
            _local_4 = this._viewSource.viewPosition;
            if(this._vScrollbar != null)
            {
               _local_3 = int(_local_6.height);
               _local_1 = int(_local_2.height);
               _local_5 = Math.max(0,Math.min(_local_4.y,_local_1 - _local_3));
               this._vScrollbar.unitIncrement = this._viewSource.verticalUnitIncrement;
               this._vScrollbar.blockIncrement = this._viewSource.verticalBlockIncrement;
               this._vScrollbar.getModel().setRangeProperties(_local_5,_local_3,0,_local_1,false);
            }
            if(this._hScrollbar != null)
            {
               _local_3 = int(_local_6.width);
               _local_1 = int(_local_2.width);
               _local_5 = Math.max(0,Math.min(_local_4.x,_local_1 - _local_3));
               this._hScrollbar.unitIncrement = this._viewSource.horizontalUnitIncrement;
               this._hScrollbar.blockIncrement = this._viewSource.horizontalBlockIncrement;
               this._hScrollbar.getModel().setRangeProperties(_local_5,_local_3,0,_local_1,false);
            }
            this.updateAutoHiddenScroll();
         }
      }
      
      private function updateAutoHiddenScroll() : void
      {
         var _local_4:* = null;
         var _local_2:* = null;
         var _local_1:* = null;
         var _local_3:* = null;
         if(this._vScrollbar == null && this._hScrollbar == null)
         {
            return;
         }
         if(this._vScrollbar != null)
         {
            if(this._vScrollProxy == 1)
            {
               this._vScrollbar.visible = this._vScrollbar.getThumbVisible();
            }
            if(this._vScrollProxy == 1 && this._vScrollbar.maximum == 0)
            {
               this._vScrollbar.visible = false;
            }
         }
         if(Boolean(this._hScrollbar))
         {
            if(this._hScrollProxy == 1)
            {
               this._hScrollbar.visible = this._hScrollbar.getThumbVisible();
            }
            if(this._hScrollProxy == 1 && this._hScrollbar.maximum == 0)
            {
               this._hScrollbar.visible = false;
            }
         }
         if(this._vScrollProxy == 1 || this._hScrollProxy == 1)
         {
            _local_2 = this._viewportInnerRect.getInnerRect(_width,_height);
            if(Boolean(this._vScrollbarInnerRect))
            {
               _local_1 = this._vScrollbarInnerRect.getInnerRect(_width,_height);
            }
            if(Boolean(this._hScrollbarInnerRect))
            {
               _local_3 = this._hScrollbarInnerRect.getInnerRect(_width,_height);
            }
            if(this._vScrollbar != null)
            {
               if(!this._vScrollbar.getThumbVisible() || this._vScrollbar.visible == false)
               {
                  _local_4 = _local_2.union(_local_1);
               }
            }
            if(this._hScrollbar != null)
            {
               if(!this._hScrollbar.getThumbVisible() || this._hScrollbar.visible == false)
               {
                  if(_local_4)
                  {
                     _local_4 = _local_4.union(_local_3);
                  }
                  else
                  {
                     _local_4 = _local_2.union(_local_3);
                  }
               }
            }
            if(_local_4 == null)
            {
               _local_4 = _local_2;
            }
            this._viewSource.x = _local_4.x;
            this._viewSource.y = _local_4.y;
            this._viewSource.width = _local_4.width;
            this._viewSource.height = _local_4.height;
         }
      }
   }
}

