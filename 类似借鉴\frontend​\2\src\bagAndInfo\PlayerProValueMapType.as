package bagAndInfo
{
   public class PlayerProValueMapType
   {
      
      public static var Attack:int = 31;
      
      public static var Defence:int = 32;
      
      public static var Agility:int = 33;
      
      public static var Luck:int = 34;
      
      public static var MagicAttack:int = 101;
      
      public static var MagicDefence:int = 102;
      
      public static var HP:int = 37;
      
      public static var Arm:int = 35;
      
      public static var Damage:int = 36;
      
      public function PlayerProValueMapType()
      {
         super();
      }
   }
}

