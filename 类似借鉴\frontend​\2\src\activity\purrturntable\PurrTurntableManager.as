package activity.purrturntable
{
   import com.pickgliss.ui.LayerManager;
   import com.pickgliss.utils.ClassUtils;
   import ddt.data.player.PlayerInfo;
   import ddt.events.PkgEvent;
   import ddt.loader.LoaderCreate;
   import ddt.manager.SocketManager;
   import ddt.utils.AssetModuleLoader;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.EventDispatcher;
   import hallIcon.HallIconManager;
   import newActivity.NewActivityCnfInfo;
   import newActivity.NewActivityManager;
   import road7th.comm.PackageIn;
   import templateData.TemplateRankInfo;
   
   public class PurrTurntableManager extends EventDispatcher
   {
      
      private static var _ins:PurrTurntableManager;
      
      public static const PLAYERINFO:String = "playerInfo";
      
      public static const RANKINFO:String = "rankInfo";
      
      private var _state:int;
      
      private var _model:PurrTurntableModel = new PurrTurntableModel();
      
      public function PurrTurntableManager()
      {
         super();
      }
      
      public static function get ins() : PurrTurntableManager
      {
         if(!_ins)
         {
            _ins = new PurrTurntableManager();
         }
         return _ins;
      }
      
      public function get model() : PurrTurntableModel
      {
         return this._model;
      }
      
      public function addEvent() : void
      {
         SocketManager.Instance.addEventListener(PkgEvent.format(744,1),this.__onActiveInfo);
         NewActivityManager.instance.addEventListener("loadCNFXmlComplete",this.loadXmlComplete);
      }
      
      public function show() : void
      {
         SocketManager.Instance.addEventListener(PkgEvent.format(744,2),this.__playerInfo);
         SocketManager.Instance.addEventListener(PkgEvent.format(744,9),this.__getDrawResult);
         SocketManager.Instance.addEventListener(PkgEvent.format(744,33),this.__rankInfo);
         AssetModuleLoader.addModelLoader("purrturntable",5);
         AssetModuleLoader.addRequestLoader(LoaderCreate.Instance.createPurrtruntableShowLoader());
         AssetModuleLoader.addRequestLoader(LoaderCreate.Instance.createNewActivityCnfLoader());
         AssetModuleLoader.addRequestLoader(LoaderCreate.Instance.createPurrtruntableTypeLoader());
         AssetModuleLoader.startCodeLoader(this.showFrame);
      }
      
      private function showFrame(_arg_1:int = 0) : void
      {
         var _local_2:* = null;
         _local_2 = ClassUtils.CreatInstance("activity.purrturntable.view.PurrturntableMainView") as Sprite;
         LayerManager.Instance.addToLayer(_local_2,3,true,1);
         SocketManager.Instance.out.sendPurrTurntableRequestInfo();
         SocketManager.Instance.out.sendPersonalLimitShop(161);
      }
      
      public function removeEvent() : void
      {
         SocketManager.Instance.removeEventListener(PkgEvent.format(744,2),this.__playerInfo);
         SocketManager.Instance.removeEventListener(PkgEvent.format(744,9),this.__getDrawResult);
         SocketManager.Instance.removeEventListener(PkgEvent.format(744,33),this.__rankInfo);
      }
      
      private function __onActiveInfo(_arg_1:PkgEvent) : void
      {
         var _local_2:PackageIn = _arg_1.pkg;
         this._state = _local_2.readInt();
         this.checkOpen();
      }
      
      public function checkOpen() : void
      {
         if(this._state != 0)
         {
            HallIconManager.instance.updateSwitchHandler("purrturn",true);
         }
         else
         {
            HallIconManager.instance.updateSwitchHandler("purrturn",false);
         }
      }
      
      public function get State() : int
      {
         return this._state;
      }
      
      private function __playerInfo(_arg_1:PkgEvent) : void
      {
         var _local_2:PackageIn = _arg_1.pkg;
         this._model.playinfoTypeInt = _local_2.readInt();
         this._model.playinfoLevelInt = _local_2.readInt();
         this._model.playinfoSubscriptInt = _local_2.readInt();
         this._model.purrmoneyCountInt = _local_2.readInt();
         this._model.playinfoFreeCoinInt = _local_2.readInt();
         this._model.chipCountInt = _local_2.readInt();
         this._model.playinfoScoreInt = _local_2.readInt();
         this._model.setPlayinfoClaimed(_local_2.readUTF());
         this._model.NiuniumoneyRestrictInt = _local_2.readInt();
         this._model.AddStateInt = _local_2.readInt();
         dispatchEvent(new Event("playerInfo"));
      }
      
      private function __getDrawResult(_arg_1:PkgEvent) : void
      {
         var _local_2:PackageIn = _arg_1.pkg;
         this._model.romandTypeInt = _local_2.readInt();
         this._model.romandLevelInt = _local_2.readInt();
         this._model.issuccessfulBool = _local_2.readBoolean();
         this._model.recoverByteInt = _local_2.readByte();
         this._model.isdrawBool = true;
      }
      
      private function __rankInfo(_arg_1:PkgEvent) : void
      {
         var _local_7:int = 0;
         var _local_6:* = null;
         var _local_5:* = null;
         var _local_2:* = null;
         var _local_3:PackageIn = _arg_1.pkg;
         this._model.rankArr = [];
         var _local_4:int = _local_3.readInt();
         _local_7 = 0;
         while(_local_7 < _local_4)
         {
            _local_6 = new TemplateRankInfo();
            _local_6.areaName = _local_3.readUTF();
            _local_6.nickName = _local_3.readUTF();
            _local_6.score = _local_3.readInt();
            _local_6.rank = _local_3.readInt();
            _local_6.data = {};
            _local_5 = new PlayerInfo();
            _local_2 = _local_3.readUTF();
            _local_5.Sex = _local_3.readBoolean();
            _local_5.Style = _local_2;
            _local_6.data = _local_5;
            this._model.rankArr.push(_local_6);
            _local_7++;
         }
         this._model.myRankInt = _local_3.readInt();
         dispatchEvent(new Event("rankInfo"));
      }
      
      public function loadActivityXmlComplete(_arg_1:PurrTurntableShowAnalyzer) : void
      {
         this._model.showTempDic = _arg_1.data;
         this._model.DataTypeArr = _arg_1.dataType;
         this._model.rewardTempDic = _arg_1.dataReward;
      }
      
      public function loadTypeXmlComplete(_arg_1:PurrTurntableTypeAnalyzer) : void
      {
         this._model.TypeDic = _arg_1.data;
      }
      
      public function loadXmlComplete(_arg_1:Event = null) : void
      {
         NewActivityManager.instance.removeEventListener("loadCNFXmlComplete",this.loadXmlComplete);
         var _local_2:NewActivityCnfInfo = NewActivityManager.instance.getActCnfByNum(5);
         this._model.startDateStr = _local_2.StartDate.slice(0,4) + "." + _local_2.StartDate.slice(5,7) + "." + _local_2.StartDate.slice(8,10);
         this._model.endDateStr = _local_2.EndDate.slice(0,4) + "." + _local_2.EndDate.slice(5,7) + "." + _local_2.EndDate.slice(8,10);
         this._model.showDateStr = _local_2.ShowDate.slice(0,4) + "." + _local_2.ShowDate.slice(5,7) + "." + _local_2.ShowDate.slice(8,10);
         this._model.rankawardStr = _local_2.RankAreaAward;
         this._model.Params1Str = _local_2.Params1;
         this._model.Params2Str = _local_2.Params2;
         this._model.Params5Str = _local_2.Params5;
      }
   }
}

