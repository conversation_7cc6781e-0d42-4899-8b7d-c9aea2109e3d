package forcesbattle.model
{
   import ddt.manager.LanguageMgr;
   import ddt.manager.ServerConfigManager;
   import forcesbattle.data.relic.ForcesRelicZFCostCnfVo;
   import road7th.data.DictionaryData;
   
   public class ForcesRelicModel
   {
      
      public static const ATTACK_TYPE:int = 1;
      
      public static const DEFENCE_TYPE:int = 2;
      
      public static const TYPE_LIMTNUM:int = 3;
      
      public static const MANUAL_PROTYPE_ARR:Array = [1,2,3,4,5,6,8,9];
      
      public static const GENARL_SHARD_ITEMID:Array = [11025,386298,386299];
      
      public static const RELICNAME_COLOR:Array = ["0x59811b","0x4c7b95","0xCF612C","0xe99005","0xbb302e"];
      
      public static const RELICPRO_COLOR:Array = ["#59811b","#59811b","#59811b","#4c7b95","#7448b7","#e99005","#bb302e"];
      
      public static const RELICPRO_COLOR_TIPS:Array = ["#1eff00","#1eff00","#1eff00","#0282ff","#a335ee","#ff8000","#ff0000"];
      
      public var relicInfoDic:DictionaryData;
      
      public var curRelicHoleIndex:int;
      
      public var curRelicHoleArr:Array;
      
      public var relicShardDic:DictionaryData;
      
      public var commonShard1:int;
      
      public var commonShard2:int;
      
      public var relicSuitDic:DictionaryData;
      
      public var curZfProSelectIndex:int = -1;
      
      public var manualInfoDic:DictionaryData;
      
      public var shopScore:int = 12364;
      
      public var relicCnfDic:DictionaryData;
      
      public var relicLevelCnfDic:DictionaryData;
      
      public var relicExProCnfDic:DictionaryData;
      
      public var manualCnfDic:DictionaryData;
      
      public var manualTypeArr:Array = [0,1,2,3,4,5];
      
      public var manualMaxLeveDic:DictionaryData;
      
      public var relicCnfDicByFunType:DictionaryData;
      
      private var _zfCnfDic:DictionaryData;
      
      private var _zfProSelectIndex:int;
      
      private var _quaTypeArr:Array;
      
      public function ForcesRelicModel()
      {
         super();
         this.relicInfoDic = new DictionaryData();
         this.curRelicHoleArr = [-1,-1,-1,-1,-1,-1];
         this.relicSuitDic = new DictionaryData();
         this.manualInfoDic = new DictionaryData();
         this.relicShardDic = new DictionaryData();
         this.relicCnfDic = new DictionaryData();
         this.manualCnfDic = new DictionaryData();
         this.relicCnfDicByFunType = new DictionaryData();
         this.relicExProCnfDic = new DictionaryData();
      }
      
      public function get zfCnfDic() : DictionaryData
      {
         if(!this._zfCnfDic)
         {
            this.initZfCnf();
         }
         return this._zfCnfDic;
      }
      
      public function get quaNameArr() : Array
      {
         if(!this._quaTypeArr)
         {
            this._quaTypeArr = LanguageMgr.GetTranslation("tank.forceRelic.txt29").split(",");
         }
         return this._quaTypeArr;
      }
      
      private function initZfCnf() : void
      {
         var _local_9:int = 0;
         var _local_1:int = 0;
         var _local_7:int = 0;
         var _local_5:int = 0;
         var _local_4:int = 0;
         var _local_3:int = 0;
         var _local_8:* = null;
         var _local_2:* = null;
         this._zfCnfDic = new DictionaryData();
         var _local_6:Array = ServerConfigManager.instance.RelicBuffSubstatPrice;
         _local_9 = 0;
         while(_local_9 < _local_6.length)
         {
            _local_8 = new ForcesRelicZFCostCnfVo();
            _local_2 = (_local_6[_local_9] as String).split(",");
            _local_1 = int(_local_2[0]);
            _local_7 = int(_local_2[1]);
            _local_5 = int(_local_2[2]);
            _local_4 = int(_local_2[3]);
            _local_3 = int(_local_2[4]);
            _local_8.qua = _local_1;
            _local_8.place = _local_7;
            _local_8.costNum1 = _local_5;
            _local_8.costNum2 = _local_4;
            _local_8.costNum3 = _local_3;
            if(!this._zfCnfDic.hasKey(_local_8.qua))
            {
               this._zfCnfDic[_local_8.qua] = new DictionaryData();
            }
            (this._zfCnfDic[_local_8.qua] as DictionaryData).add(_local_8.place,_local_8);
            _local_9++;
         }
      }
   }
}

