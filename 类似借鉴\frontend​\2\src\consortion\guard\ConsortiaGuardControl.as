package consortion.guard
{
   import consortion.ConsortionModelManager;
   import consortion.data.ConsortiaBossDataVo;
   import ddt.data.player.PlayerInfo;
   import ddt.events.PkgEvent;
   import ddt.manager.ChatManager;
   import ddt.manager.CheckWeaponManager;
   import ddt.manager.GameInSocketOut;
   import ddt.manager.LanguageMgr;
   import ddt.manager.PlayerManager;
   import ddt.manager.ServerConfigManager;
   import ddt.manager.SocketManager;
   import ddt.manager.StateManager;
   import ddt.manager.TimeManager;
   import flash.events.EventDispatcher;
   import flash.events.TimerEvent;
   import flash.geom.Point;
   import flash.utils.Timer;
   import road7th.comm.PackageIn;
   import room.RoomManager;
   import starling.display.player.FightPlayerVo;
   
   public class ConsortiaGuardControl extends EventDispatcher
   {
      
      private static var _instance:ConsortiaGuardControl;
      
      public var notAlertAgain:Boolean = false;
      
      public var bossRankShow:Boolean = false;
      
      private var _showPlayer:Boolean = true;
      
      private var _timer:Timer;
      
      private var _model:ConsortiaGuardModel;
      
      public function ConsortiaGuardControl()
      {
         super();
         this._model = new ConsortiaGuardModel();
      }
      
      public static function get Instance() : ConsortiaGuardControl
      {
         if(_instance == null)
         {
            _instance = new ConsortiaGuardControl();
         }
         return _instance;
      }
      
      public function setup() : void
      {
         SocketManager.Instance.addEventListener(PkgEvent.format(316,0),this.__onOpenActivity);
      }
      
      public function enterGuradScene() : void
      {
         if(this.checkCanStartGame())
         {
            this._model.reset();
            GameInSocketOut.sendSingleRoomBegin(23);
         }
      }
      
      private function checkCanStartGame() : Boolean
      {
         var result:Boolean = true;
         if(CheckWeaponManager.instance.isNoWeapon())
         {
            CheckWeaponManager.instance.setFunction(this,function():void
            {
               _model.reset();
               GameInSocketOut.sendSingleRoomBegin(23);
            });
            CheckWeaponManager.instance.showAlert();
            result = false;
         }
         return result;
      }
      
      public function leaveGuardScene() : void
      {
         this.disposeTimer();
         StateManager.back();
         SocketManager.Instance.out.sendConsortiaGuradLeaveScene();
         RoomManager.Instance.reset();
      }
      
      private function initEvent() : void
      {
         SocketManager.Instance.addEventListener(PkgEvent.format(316,6),this.__onInitScene);
         SocketManager.Instance.addEventListener(PkgEvent.format(316,4),this.__onUpdateBossState);
         SocketManager.Instance.addEventListener(PkgEvent.format(316,1),this.__onPlayer);
         SocketManager.Instance.addEventListener(PkgEvent.format(316,8),this.__onUpdatePlayerState);
         SocketManager.Instance.addEventListener(PkgEvent.format(316,16),this.__onRankList);
         SocketManager.Instance.addEventListener(PkgEvent.format(316,12),this.__onRemovePlayer);
         SocketManager.Instance.addEventListener(PkgEvent.format(316,9),this.__onGameState);
         SocketManager.Instance.addEventListener(PkgEvent.format(316,17),this.__onBuyBuff);
         SocketManager.Instance.addEventListener(PkgEvent.format(316,19),this.__onBossRankList);
      }
      
      private function removeEvent() : void
      {
         SocketManager.Instance.removeEventListener(PkgEvent.format(316,6),this.__onInitScene);
         SocketManager.Instance.removeEventListener(PkgEvent.format(316,4),this.__onUpdateBossState);
         SocketManager.Instance.removeEventListener(PkgEvent.format(316,1),this.__onPlayer);
         SocketManager.Instance.removeEventListener(PkgEvent.format(316,8),this.__onUpdatePlayerState);
         SocketManager.Instance.removeEventListener(PkgEvent.format(316,16),this.__onRankList);
         SocketManager.Instance.removeEventListener(PkgEvent.format(316,12),this.__onRemovePlayer);
         SocketManager.Instance.removeEventListener(PkgEvent.format(316,9),this.__onGameState);
         SocketManager.Instance.removeEventListener(PkgEvent.format(316,17),this.__onBuyBuff);
         SocketManager.Instance.removeEventListener(PkgEvent.format(316,19),this.__onBossRankList);
      }
      
      private function __onBossRankList(_arg_1:PkgEvent) : void
      {
         var _local_5:int = 0;
         var _local_4:* = null;
         var _local_2:int = _arg_1.pkg.readInt();
         var _local_3:int = _arg_1.pkg.readInt();
         ConsortiaGuardControl.Instance.model.rankBossList.clear();
         _local_5 = 0;
         while(_local_5 < _local_3)
         {
            _local_4 = new ConsortiaBossDataVo();
            _local_4.rank = _arg_1.pkg.readInt();
            _local_4.name = _arg_1.pkg.readUTF();
            _local_4.damage = _arg_1.pkg.readInt();
            _local_4.attacksCount = _arg_1.pkg.readInt();
            ConsortiaGuardControl.Instance.model.rankBossList.add(_local_4.rank,_local_4);
            _local_5++;
         }
         dispatchEvent(new ConsortiaGuardEvent("showBossRank",_local_2));
      }
      
      public function bossLocation(_arg_1:Point) : void
      {
         dispatchEvent(new ConsortiaGuardEvent("clickBossIcon",_arg_1));
      }
      
      private function __onOpenActivity(_arg_1:PkgEvent) : void
      {
         this._model.isOpen = _arg_1.pkg.readBoolean();
         this._model.openTime = _arg_1.pkg.readDate();
         this._model.openLevel = _arg_1.pkg.readInt();
         this._model.isFight = _arg_1.pkg.readBoolean();
         this.removeEvent();
         if(this._model.isOpen)
         {
            ChatManager.Instance.sysChatConsortia(LanguageMgr.GetTranslation("tank.consortiaGurad.activityOpen"));
            this.initEvent();
            this.updateConsortia();
         }
         dispatchEvent(new ConsortiaGuardEvent("updateActivity"));
      }
      
      private function updateConsortia() : void
      {
         if(StateManager.currentStateType == "consortia")
         {
            ConsortionModelManager.Instance.getConsortionList(ConsortionModelManager.Instance.selfConsortionComplete,1,6,PlayerManager.Instance.Self.ConsortiaName,-1,-1,-1,PlayerManager.Instance.Self.ConsortiaID);
         }
      }
      
      private function __onBuyBuff(_arg_1:PkgEvent) : void
      {
         var _local_2:int = _arg_1.pkg.readInt();
         this._model.buffLevel = _local_2;
      }
      
      private function __onInitScene(_arg_1:PkgEvent) : void
      {
         var _local_5:int = 0;
         var _local_2:int = 0;
         var _local_3:PackageIn = _arg_1.pkg;
         _local_5 = 0;
         while(_local_5 < _local_3.extend1)
         {
            _local_2 = _local_3.readInt();
            this.addPlayer(_local_2,_local_3);
            _local_5++;
         }
         var _local_4:Boolean = _arg_1.pkg.readBoolean();
         if(_local_4 && StateManager.currentStateType != "consortiaGuard")
         {
            StateManager.setState("consortiaGuard");
         }
      }
      
      private function __onPlayer(_arg_1:PkgEvent) : void
      {
         var _local_3:PackageIn = _arg_1.pkg;
         var _local_2:int = _local_3.readInt();
         this.addPlayer(_local_2,_local_3);
         dispatchEvent(new ConsortiaGuardEvent("addPlayer",_local_2));
      }
      
      private function addPlayer(_arg_1:int, _arg_2:PackageIn) : void
      {
         var _local_4:* = null;
         var _local_3:Boolean = true;
         if(this._model.playerList.hasKey(_arg_1))
         {
            _local_4 = this._model.playerList[_arg_1];
            _local_3 = false;
         }
         else
         {
            _local_4 = new FightPlayerVo();
            _local_4.playerInfo = new PlayerInfo();
         }
         _local_4.playerInfo.beginChanges();
         _local_4.playerInfo.ID = _arg_1;
         _local_4.playerInfo.Sex = _arg_2.readBoolean();
         _local_4.playerInfo.Style = _arg_2.readUTF();
         _local_4.playerInfo.Colors = _arg_2.readUTF();
         _local_4.playerInfo.Skin = _arg_2.readUTF();
         _local_4.playerInfo.NickName = _arg_2.readUTF();
         _local_4.currentWalkStartPoint = new Point(_arg_2.readInt(),_arg_2.readInt());
         _local_4.state = _arg_2.readInt();
         _local_4.reviveTime = new Date(_arg_2.readDate().getTime() + ServerConfigManager.instance.consortiaGuardReviveTime * 1000);
         _local_4.playerInfo.IsVIP = _arg_2.readBoolean();
         _local_4.playerInfo.VIPLevel = _arg_2.readInt();
         _local_4.playerInfo.MountsType = _arg_2.readInt();
         _local_4.playerInfo.commitChanges();
         if(_local_3)
         {
            this._model.playerList.add(_arg_1,_local_4);
         }
      }
      
      private function __onUpdateBossState(_arg_1:PkgEvent) : void
      {
         var _local_2:int = 0;
         _local_2 = 0;
         while(_local_2 < 4)
         {
            this._model.setBossMaxHp(_local_2,_arg_1.pkg.readDouble());
            this._model.setBossHp(_local_2,_arg_1.pkg.readDouble());
            this._model.setBossState(_local_2,_arg_1.pkg.readInt());
            _local_2++;
         }
         this._model.statueHp = _arg_1.pkg.readDouble();
         this._model.statueMaxHp = _arg_1.pkg.readDouble();
         dispatchEvent(new ConsortiaGuardEvent("updateBossState"));
      }
      
      private function __onUpdatePlayerState(_arg_1:PkgEvent) : void
      {
         var _local_4:* = null;
         var _local_2:int = _arg_1.pkg.readInt();
         if(_local_2 == PlayerManager.Instance.Self.ID)
         {
            _local_4 = PlayerManager.Instance.fightVo;
         }
         else
         {
            _local_4 = this._model.playerList[_local_2] as FightPlayerVo;
         }
         if(_local_4 == null)
         {
            return;
         }
         _local_4.state = _arg_1.pkg.readInt();
         var _local_3:Number = ServerConfigManager.instance.consortiaGuardReviveTime * 1000;
         _local_4.reviveTime = new Date(TimeManager.Instance.NowTime() + _local_3);
         dispatchEvent(new ConsortiaGuardEvent("updatePlayerState",_local_2));
      }
      
      private function __onRemovePlayer(_arg_1:PkgEvent) : void
      {
         var _local_2:int = _arg_1.pkg.readInt();
         this._model.playerList.remove(_local_2);
         dispatchEvent(new ConsortiaGuardEvent("removePlayer",_local_2));
      }
      
      private function __onRankList(_arg_1:PkgEvent) : void
      {
         var _local_4:int = 0;
         var _local_3:Boolean = _arg_1.pkg.readBoolean();
         if(_local_3)
         {
            this.addRankVo(_arg_1.pkg,true);
         }
         else
         {
            ConsortiaGuardControl.Instance.model.rankList.remove(0);
         }
         var _local_2:int = _arg_1.pkg.readByte();
         _local_4 = 0;
         while(_local_4 < _local_2)
         {
            this.addRankVo(_arg_1.pkg,false);
            _local_4++;
         }
         dispatchEvent(new ConsortiaGuardEvent("updateRank"));
      }
      
      private function __onGameState(_arg_1:PkgEvent) : void
      {
         var _local_3:Boolean = _arg_1.pkg.readBoolean();
         var _local_2:Boolean = _arg_1.pkg.readBoolean();
         var _local_4:int = _arg_1.pkg.readInt();
         this._model.isFight = _local_3;
         this._model.isWin = _local_2;
         this._model.endTime = TimeManager.Instance.NowTime() + _local_4 * 60000;
         if(_local_3 == false)
         {
            if(_local_2)
            {
               ChatManager.Instance.sysChatConsortia(LanguageMgr.GetTranslation("tank.consortiaGurad.win"));
            }
            else
            {
               ChatManager.Instance.sysChatConsortia(LanguageMgr.GetTranslation("tank.consortiaGurad.fail1"));
               ChatManager.Instance.sysChatConsortia(LanguageMgr.GetTranslation("tank.consortiaGurad.fail2"));
            }
            ChatManager.Instance.sysChatConsortia(LanguageMgr.GetTranslation("tank.consortiaGurad.closeRoom",_local_4));
         }
         dispatchEvent(new ConsortiaGuardEvent("updateGameState"));
      }
      
      private function startCloseRoomTimer() : void
      {
         if(StateManager.currentStateType == "fighting" || StateManager.currentStateType == "consortiaGuard")
         {
            this._timer = new Timer(1000);
            this._timer.addEventListener("timer",this.__onTimer);
         }
      }
      
      private function __onTimer(_arg_1:TimerEvent) : void
      {
         if(TimeManager.Instance.NowTime() > this._model.endTime)
         {
            this.leaveGuardScene();
         }
      }
      
      private function disposeTimer() : void
      {
         if(Boolean(this._timer))
         {
            this._timer.stop();
            this._timer.removeEventListener("timer",this.__onTimer);
            this._timer = null;
         }
      }
      
      private function addRankVo(_arg_1:PackageIn, _arg_2:Boolean = false) : void
      {
         var _local_3:ConsortiaBossDataVo = new ConsortiaBossDataVo();
         if(_arg_2)
         {
            _local_3.name = PlayerManager.Instance.Self.NickName;
         }
         else
         {
            _local_3.name = _arg_1.readUTF();
         }
         _local_3.rank = _arg_1.readInt();
         _local_3.damage = _arg_1.readInt();
         _local_3.honor = _arg_1.readInt();
         _local_3.contribution = _arg_1.readInt();
         ConsortiaGuardControl.Instance.model.rankList.add(_local_3.rank,_local_3);
         if(_arg_2)
         {
            ConsortiaGuardControl.Instance.model.rankList.add(0,_local_3);
         }
      }
      
      public function get showPlayer() : Boolean
      {
         return this._showPlayer;
      }
      
      public function set showPlayer(_arg_1:Boolean) : void
      {
         if(this._showPlayer == _arg_1)
         {
            return;
         }
         this._showPlayer = _arg_1;
         dispatchEvent(new ConsortiaGuardEvent("updatePlayerView"));
      }
      
      public function get model() : ConsortiaGuardModel
      {
         return this._model;
      }
   }
}

