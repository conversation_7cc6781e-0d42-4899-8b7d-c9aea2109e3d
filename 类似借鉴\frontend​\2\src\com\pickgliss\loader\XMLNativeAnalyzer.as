package com.pickgliss.loader
{
   import com.pickgliss.ui.ComponentFactory;
   
   public class XMLNativeAnalyzer extends DataAnalyzer
   {
      
      public function XMLNativeAnalyzer(_arg_1:Function)
      {
         super(_arg_1);
      }
      
      override public function analyze(_arg_1:*) : void
      {
         var _local_2:XML = new XML(_arg_1);
         ComponentFactory.Instance.setup(_local_2);
         onAnalyzeComplete();
      }
   }
}

