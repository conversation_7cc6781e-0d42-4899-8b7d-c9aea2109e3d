package bagAndInfo.bag
{
   import ddt.manager.LanguageMgr;
   import ddt.manager.MessageTipManager;
   import ddt.manager.SoundManager;
   
   public class BreakGoodsRetrieveView extends BreakGoodsView
   {
      
      private var _okBackFun:Function;
      
      public function BreakGoodsRetrieveView()
      {
         super();
      }
      
      public function set backFun(_arg_1:Function) : void
      {
         this._okBackFun = _arg_1;
      }
      
      override protected function okFun() : void
      {
         SoundManager.instance.play("008");
         var _local_1:int = int(_input.text);
         if(_local_1 > 0 && _local_1 <= _cell.itemInfo.Count)
         {
            if(this._okBackFun != null)
            {
               this._okBackFun.call(this,_cell,_local_1);
            }
            dispose();
         }
         else if(_local_1 == 0)
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.view.bagII.BreakGoodsView.wrong2"));
            _input.text = "";
         }
         else
         {
            MessageTipManager.getInstance().show(LanguageMgr.GetTranslation("tank.view.bagII.BreakGoodsView.right"));
            _input.text = "";
         }
      }
   }
}

