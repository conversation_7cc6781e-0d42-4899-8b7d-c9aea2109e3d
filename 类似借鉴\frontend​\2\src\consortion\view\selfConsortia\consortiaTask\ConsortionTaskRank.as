package consortion.view.selfConsortia.consortiaTask
{
   import com.pickgliss.events.FrameEvent;
   import com.pickgliss.ui.ComponentFactory;
   import com.pickgliss.ui.controls.BaseButton;
   import com.pickgliss.ui.controls.Frame;
   import com.pickgliss.ui.image.Scale9CornerImage;
   import com.pickgliss.ui.text.FilterFrameText;
   import com.pickgliss.utils.ObjectUtils;
   import consortion.ConsortionModelManager;
   import consortion.event.ConsortionEvent;
   import ddt.manager.LanguageMgr;
   import ddt.manager.SoundManager;
   import ddt.utils.PositionUtils;
   import flash.display.Bitmap;
   import flash.events.MouseEvent;
   
   public class ConsortionTaskRank extends Frame
   {
      
      private var _bg:Bitmap;
      
      protected var _pageBack:Scale9CornerImage;
      
      protected var _rightBtn:BaseButton;
      
      protected var _leftBtn:BaseButton;
      
      protected var _pageNum:FilterFrameText;
      
      private var _itemList:Vector.<TaskRankItem>;
      
      private var _totalPage:Number = 1;
      
      private var _curPage:Number = 1;
      
      private var _itemLengthPerPage:Number = 12;
      
      private var _taskRankArr:Array;
      
      public function ConsortionTaskRank()
      {
         var _local_2:int = 0;
         var _local_1:* = null;
         super();
         titleText = LanguageMgr.GetTranslation("consortia.taskRank.title");
         this._bg = ComponentFactory.Instance.creatBitmap("asset.consortion.taskRankBG");
         addToContent(this._bg);
         this._pageBack = ComponentFactory.Instance.creatComponentByStylename("asset.consortion.upDownTextBgImgAsset");
         PositionUtils.setPos(this._pageBack,"taskRank.bg.pos");
         addToContent(this._pageBack);
         this._rightBtn = ComponentFactory.Instance.creatComponentByStylename("consortion.rank.nextPageBtn");
         PositionUtils.setPos(this._rightBtn,"taskRank.right.pos");
         addToContent(this._rightBtn);
         this._leftBtn = ComponentFactory.Instance.creatComponentByStylename("consortion.rank.prePageBtn");
         PositionUtils.setPos(this._leftBtn,"taskRank.left.pos");
         addToContent(this._leftBtn);
         this._pageNum = ComponentFactory.Instance.creatComponentByStylename("consortion.rank.pageNum");
         PositionUtils.setPos(this._pageNum,"taskRank.num.pos");
         this._pageNum.autoSize = "center";
         this._pageNum.text = "1/1";
         addToContent(this._pageNum);
         this._itemList = new Vector.<TaskRankItem>();
         _local_2 = 0;
         while(_local_2 < this._itemLengthPerPage)
         {
            _local_1 = new TaskRankItem(_local_2,"","","","");
            _local_1.x = 30;
            _local_1.y = (_local_1.height + 1) * _local_2 + 92;
            addToContent(_local_1);
            this._itemList.push(_local_1);
            _local_2++;
         }
         this._rightBtn.addEventListener("click",this.mouseClickHander);
         this._leftBtn.addEventListener("click",this.mouseClickHander);
         addEventListener("response",this._response);
         ConsortionModelManager.Instance.addEventListener("task_rank_list",this.taskRankListHander);
         ConsortionModelManager.Instance.getConsortionTaskRank();
      }
      
      protected function taskRankListHander(_arg_1:ConsortionEvent = null) : void
      {
         if(_arg_1 != null)
         {
            this._taskRankArr = _arg_1.data as Array;
         }
         this._totalPage = Math.max(Math.ceil(this._taskRankArr.length / this._itemLengthPerPage),1);
         this._pageNum.text = "1/" + this._totalPage.toString();
         this.setPageArr();
      }
      
      private function mouseClickHander(_arg_1:MouseEvent) : void
      {
         SoundManager.instance.playButtonSound();
         if(!this._taskRankArr)
         {
            return;
         }
         switch(_arg_1.currentTarget)
         {
            case this._rightBtn:
               ++this._curPage;
               if(this._curPage > this._totalPage)
               {
                  this._curPage = 1;
               }
               break;
            case this._leftBtn:
               --this._curPage;
               if(this._curPage < 1)
               {
                  this._curPage = this._totalPage;
               }
         }
         this._pageNum.text = this._curPage + "/" + this._totalPage;
         this.setPageArr();
      }
      
      private function setPageArr() : void
      {
         var _local_5:int = 0;
         var _local_2:* = null;
         var _local_1:int = (this._curPage - 1) * this._itemLengthPerPage;
         var _local_4:int = Math.min(this._curPage * this._itemLengthPerPage,this._taskRankArr.length);
         _local_2 = this._taskRankArr.slice(_local_1,_local_4);
         this.clearAllItemData();
         var _local_3:int = int(_local_2.length);
         _local_5 = 0;
         while(_local_5 < _local_3)
         {
            this._itemList[_local_5].update(_local_2[_local_5].name,_local_2[_local_5].rank,_local_2[_local_5].percent + "%",_local_2[_local_5].contribute);
            _local_5++;
         }
      }
      
      private function clearAllItemData() : void
      {
         var _local_2:int = 0;
         var _local_1:int = int(this._itemList.length);
         _local_2 = 0;
         while(_local_2 < _local_1)
         {
            this._itemList[_local_2].update();
            _local_2++;
         }
      }
      
      override public function dispose() : void
      {
         super.dispose();
         this._rightBtn.removeEventListener("click",this.mouseClickHander);
         this._leftBtn.removeEventListener("click",this.mouseClickHander);
         removeEventListener("response",this._response);
         ConsortionModelManager.Instance.removeEventListener("task_rank_list",this.taskRankListHander);
         ObjectUtils.disposeAllChildren(this);
         this._bg = null;
         this._pageBack = null;
         this._itemList.length = 0;
         this._itemList = null;
         this._rightBtn = null;
         this._leftBtn = null;
         this._pageNum = null;
         this._taskRankArr && (this._taskRankArr.length = 0);
         this._taskRankArr = null;
      }
      
      private function _response(_arg_1:FrameEvent) : void
      {
         if(_arg_1.responseCode == 0 || _arg_1.responseCode == 1)
         {
            ObjectUtils.disposeObject(this);
         }
      }
   }
}

import com.pickgliss.ui.ComponentFactory;
import com.pickgliss.ui.core.Disposeable;
import com.pickgliss.ui.image.ScaleFrameImage;
import com.pickgliss.ui.text.FilterFrameText;
import ddt.utils.PositionUtils;
import flash.display.Bitmap;
import flash.display.DisplayObject;
import flash.display.DisplayObjectContainer;
import flash.display.InteractiveObject;
import flash.display.Sprite;
import flash.events.EventDispatcher;

class TaskRankItem extends Sprite implements Disposeable
{
   
   private var _itemBg:Bitmap;
   
   private var _nameTxt:FilterFrameText;
   
   private var _rankTxt:FilterFrameText;
   
   private var _percentTxt:FilterFrameText;
   
   private var _contributeTxt:FilterFrameText;
   
   private var _topThreeIcon:ScaleFrameImage;
   
   public function TaskRankItem(_arg_1:int, _arg_2:String = "", _arg_3:String = "", _arg_4:String = "", _arg_5:String = "")
   {
      super();
      if(_arg_1 % 2 == 0)
      {
         this._itemBg = ComponentFactory.Instance.creat("asset.consortion.taskRankItem.bg1");
      }
      else
      {
         this._itemBg = ComponentFactory.Instance.creat("asset.consortion.taskRankItem.bg2");
      }
      addChild(this._itemBg);
      this._topThreeIcon = ComponentFactory.Instance.creat("consortion.rankThreeRink");
      PositionUtils.setPos(this._topThreeIcon,"taskRank.rankTop3.pos");
      addChild(this._topThreeIcon);
      this._topThreeIcon.visible = false;
      this._nameTxt = ComponentFactory.Instance.creat("consortion.taskRank.nameTxt");
      this._nameTxt.autoSize = "center";
      this._rankTxt = ComponentFactory.Instance.creat("consortion.taskRank.rankTxt");
      this._rankTxt.autoSize = "center";
      this._percentTxt = ComponentFactory.Instance.creat("consortion.taskRank.percentTxt");
      this._percentTxt.autoSize = "center";
      this._contributeTxt = ComponentFactory.Instance.creat("consortion.taskRank.contributeTxt");
      this._contributeTxt.autoSize = "center";
      addChild(this._nameTxt);
      addChild(this._rankTxt);
      addChild(this._percentTxt);
      addChild(this._contributeTxt);
      this._nameTxt.text = _arg_2;
      this._rankTxt.text = _arg_3;
      this._percentTxt.text = _arg_4;
      this._contributeTxt.text = _arg_5;
   }
   
   public function update(_arg_1:String = "", _arg_2:String = "", _arg_3:String = "", _arg_4:String = "") : void
   {
      this._nameTxt.text = _arg_1;
      if(int(_arg_2) < 4 && _arg_2 != "")
      {
         this._topThreeIcon.setFrame(int(_arg_2));
         this._topThreeIcon.visible = true;
         this._rankTxt.visible = false;
      }
      else
      {
         this._rankTxt.text = _arg_2;
         this._rankTxt.visible = true;
         this._topThreeIcon.visible = false;
      }
      this._percentTxt.text = _arg_3;
      this._contributeTxt.text = _arg_4;
   }
   
   public function dispose() : void
   {
   }
}
